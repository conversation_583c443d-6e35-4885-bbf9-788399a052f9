{"swagger": "2.0", "info": {"description": "API for common", "version": "1", "title": "common model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/common/common/v1/CapLtdTransitionTaskInfo": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/SubmitCaseConditionalProcessingCheck": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/absenceClaimClosureToOpenItems": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/adjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/approveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/capBalancePaymentToPayment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/PaymentDefinition_CapPaymentEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/capResolveActivePayments": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveActivePaymentsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/capResolveActiveSchedules": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveActiveSchedulesOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/capResolveCloseClaimReason": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/capResolveNonIssuedUnderpayments": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveNonIssuedUnderpaymentsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/capSelfBalanceCalculation": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapBalance_CapSelfBalanceGenerationRulesOutputSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/disapproveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/documentToResult": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DocumentToResultOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/generateReschedulePaymentsPayload": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentSchedule_CapReschedulePaymentsPayloadSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/initDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/loadPHPaymentIndex": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/originSourceClosureData": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/OriginSourceClosureDataOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/originSourceToPaymentScheduleInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/OriginSourceToPaymentScheduleInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/readjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/settlementToChildSettlementRefreshInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/verifyPaymentScheduleActivation": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/VerifyPaymentScheduleActivationInputsBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/verifyPaymentScheduleCompletion": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutputSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/common/v1/verifyUnderpaymentApproval": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/VerifyUnderpaymentApprovalInputsBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/PaymentDefinition_CapPaymentEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AbsenceClaimClosureToOpenItemsOutputs": {"properties": {"output": {"$ref": "#/definitions/CapLeave_CapLeaveClaimClosureOpenItemsOutput"}}, "title": "AbsenceClaimClosureToOpenItemsOutputs"}, "AbsenceClaimClosureToOpenItemsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputs"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "AbsenceClaimClosureToOpenItemsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputs"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "CapBalance_CapAllocationWithholdingCancelationDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAllocationWithholdingCancelationDetailsEntity"}, "paymentNumber": {"type": "string", "description": "Number of payment whose withholding was canceled (reversed)"}}, "title": "CapBalance CapAllocationWithholdingCancelationDetailsEntity", "description": "Allocation Withholding Cancelation Details "}, "CapBalance_CapBalancePaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBalancePaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_MessageType"}}, "paymentDetails": {"$ref": "#/definitions/CapBalance_CapPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}, "scheduledPaymentNumber": {"type": "string", "description": "Unique payment number in the scheduled used to match actual payment to the scheduled payments."}, "state": {"type": "string", "description": "Payment transaction state."}, "withholdingDetails": {"$ref": "#/definitions/CapBalance_CapPaymentWithholdingDetailsEntity"}}, "title": "CapBalance CapBalancePaymentEntity", "description": "Defines payment transaction information."}, "CapBalance_CapPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeTypeCd": {"type": "string"}, "paymentMethodDetails": {"type": "object", "description": "Payment method details."}}, "title": "CapBalance CapPayeeDetailsEntity", "description": "Defines payment payee details."}, "CapBalance_CapPayeeRoleDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeRoleDetailsEntity"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "registryId": {"type": "string", "description": "URI to CEM."}, "representBeneficiary": {"type": "string", "description": "Payee on beharf of Beneficiary's uri."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of the Payee"}}}, "title": "CapBalance CapPayeeRoleDetailsEntity", "description": "Payment Payee Role details."}, "CapBalance_CapPaymentAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCaseNumber": {"type": "string", "description": "Accumulator case number."}, "accumulatorClaimNumber": {"type": "string", "description": "Accumulator claim number."}, "accumulatorCoverage": {"type": "string", "description": "Accumulator coverage. Defines which type of accumulator is consumed."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapBalance_CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapBalance CapPaymentAccumulatorDetailsEntity", "description": "Accumulator details."}, "CapBalance_CapPaymentAccumulatorDetailsExtensionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapBalance_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapBalance CapPaymentAccumulatorDetailsExtensionEntity", "description": "Accumulator details extension."}, "CapBalance_CapPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsColaEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines cola consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines cola elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/CapBalance_Period"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "CapBalance CapPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapBalance_CapPaymentAdditionDetailsInterestEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsInterestEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDaysFromDolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOL."}, "interestDaysFromDosReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOS."}, "interestDaysFromPolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since POL was recieved."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Defines the date up to when interest were paid ."}, "interestRate": {"type": "number", "description": "Defines the rate based on which Interest was calculated."}, "interestState": {"type": "string", "description": "Interest state according to which interest are caculated."}, "interestThresholdAmount": {"$ref": "#/definitions/Money"}, "interestTimeThreshold": {"type": "integer", "format": "int64", "description": "Interest time threshold."}, "isCompound": {"type": "boolean", "description": "Identifies if Compound interest calculation formula was used."}}, "title": "CapBalance CapPaymentAdditionDetailsInterestEntity", "description": "Interest addition details are described in this entity."}, "CapBalance_CapPaymentAdditionDetailsRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabBenefitCd": {"type": "string", "description": "Rehab Benefit code taken from policy. Defines if rehab is included or not."}, "rehabBenefitPct": {"type": "number", "description": "Rehab percentage. Defines by how much to increase the AGBA."}, "rehabTerm": {"$ref": "#/definitions/CapBalance_Term"}}, "title": "CapBalance CapPaymentAdditionDetailsRehabEntity", "description": "Rehab details are described in this entity."}, "CapBalance_CapPaymentAdditionDetailsRestoredEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRestoredEntity"}, "restoredDeathBenefit": {"type": "number", "description": "Restored Death Benefit."}}, "title": "CapBalance CapPaymentAdditionDetailsRestoredEntity", "description": "Restored details are described in this entity."}, "CapBalance_CapPaymentAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionDetailsColaEntity"}, "paymentAdditionDetailsInterest": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionDetailsInterestEntity"}, "paymentAdditionDetailsRehab": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionDetailsRehabEntity"}, "paymentAdditionDetailsRestored": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionDetailsRestoredEntity"}}, "title": "CapBalance CapPaymentAdditionEntity", "description": "Entity for Payment Addition types (Cola, Interest Payment)."}, "CapBalance_CapPaymentAdditionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionSplitResultEntity"}, "additionNumber": {"type": "string", "description": "Number used to link split result to the payment addition."}, "appliedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentAdditionSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapBalance_CapPaymentAllocationBalanceAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBalanceAdjustmentEntity"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapBalance CapPaymentAllocationBalanceAdjustmentEntity", "description": "Details of the allocation with adjustment type."}, "CapBalance_CapPaymentAllocationBeneficiaryDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBeneficiaryDetailsEntity"}, "beneficiary": {"$ref": "#/definitions/EntityLink"}, "beneficiaryPct": {"type": "number", "description": "Percentage of the benefit amount that the beneficiary receives."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "CapBalance CapPaymentAllocationBeneficiaryDetailsEntity", "description": "Details of the customer the benefit was calcualted for."}, "CapBalance_CapPaymentAllocationBenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBenefitDurationEntity"}, "benefitDuration": {"type": "number", "description": "Benefit duration amount per defined frequency inside the period."}, "benefitDurationFrequencyCd": {"type": "string", "description": "The timeframe inside the period within which the benefit duration occurrences happen."}, "benefitDurationPeriod": {"$ref": "#/definitions/CapBalance_Period"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}}, "title": "CapBalance CapPaymentAllocationBenefitDurationEntity", "description": "Details of periods for which benefit is calculated."}, "CapBalance_CapPaymentAllocationDeductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDeductionDetailsEntity"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapBalance CapPaymentAllocationDeductionDetailsEntity", "description": "Details of 3rd party deduction payment allocation."}, "CapBalance_CapPaymentAllocationDisabilityDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDisabilityDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationBenefitDurationEntity"}}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "monthlyGbaAmount": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}, "taxableAmount": {"$ref": "#/definitions/Money"}, "taxablePercentage": {"type": "number", "description": "The percentage of the gross benefit amount that will taxable."}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentAllocationDisabilityDetailsEntity", "description": "Details of Disability LOB allocation."}, "CapBalance_CapPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationBeneficiaryDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationBeneficiaryDetailsEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationDeductionDetailsEntity"}, "allocationDisabilityDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationDisabilityDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLifeDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationLifeDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationLossInfo": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "allocationWithholdingCancelationDetails": {"$ref": "#/definitions/CapBalance_CapAllocationWithholdingCancelationDetailsEntity"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentTaxSplitResultEntity"}}}, "title": "CapBalance CapPaymentAllocationEntity", "description": "Entity for the payment allocation information."}, "CapBalance_CapPaymentAllocationLifeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLifeDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentAllocationLifeDetailsEntity", "description": "Details of Life LOB allocation."}, "CapBalance_CapPaymentAllocationLossInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfo"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapBalance CapPaymentAllocationLossInfo", "description": "Defines payment allocation loss info details."}, "CapBalance_CapPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfoEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "claimType": {"type": "string", "description": "Defines claim type."}, "dateOfLoss": {"type": "string", "format": "date-time"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Defines loss type."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}, "title": "CapBalance CapPaymentAllocationLossInfoEntity", "description": "The attribute to store all info that is needed from Claim for Payments."}, "CapBalance_CapPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationPayableItemEntity"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapBalance_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapBalance CapPaymentAllocationPayableItemEntity", "description": "Stores details for what it is paid."}, "CapBalance_CapPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentDetailsEntity"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Defines insured last work date prior to payment."}, "payeeDetails": {"$ref": "#/definitions/CapBalance_CapPayeeDetailsEntity"}, "payeeRoleDetails": {"$ref": "#/definitions/CapBalance_CapPayeeRoleDetailsEntity"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionEntity"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}, "paymentReductions": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentReductionEntity"}}, "paymentTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentTaxEntity"}}, "typicalWorkWeek": {"$ref": "#/definitions/CapBalance_CapPaymentTypicalWorkWeekEntity"}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentDetailsEntity", "description": "Payment details."}, "CapBalance_CapPaymentFinancialAdjustmentPayableSourcesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentFinancialAdjustmentPayableSourcesEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to the related policy."}}, "title": "CapBalance CapPaymentFinancialAdjustmentPayableSourcesEntity", "description": "Identifies sources for which financial adjustment is applied"}, "CapBalance_CapPaymentReductionDetailsDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type."}, "deductionBeneficiary": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "Defines the Deduction precentage."}, "deductionProvider": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/CapBalance_Term"}, "deductionType": {"type": "string", "description": "Deduction type."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "Is a Deduction applied before taxes."}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}}, "title": "CapBalance CapPaymentReductionDetailsDeductionEntity", "description": "Deduction details are described in this entity."}, "CapBalance_CapPaymentReductionDetailsIndebtednessEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsIndebtednessEntity"}, "indebtedness": {"type": "number", "description": "Indebtedness."}}, "title": "CapBalance CapPaymentReductionDetailsIndebtednessEntity", "description": "Indebtedness details are described in this entity."}, "CapBalance_CapPaymentReductionDetailsOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsOffsetEntity"}, "isAutoOffset": {"type": "boolean"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "Defines the prorating rate of the Offset."}, "offsetTerm": {"$ref": "#/definitions/CapBalance_Term"}, "offsetType": {"type": "string"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentReductionDetailsOffsetEntity", "description": "Offset details are described in this entity."}, "CapBalance_CapPaymentReductionDetailsWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsWithholdingEntity"}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentReductionDetailsWithholdingEntity", "description": "Withholding details are described in this entity."}, "CapBalance_CapPaymentReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionEntity"}, "paymentReductionDetailsDeduction": {"$ref": "#/definitions/CapBalance_CapPaymentReductionDetailsDeductionEntity"}, "paymentReductionDetailsIndebtedness": {"$ref": "#/definitions/CapBalance_CapPaymentReductionDetailsIndebtednessEntity"}, "paymentReductionDetailsOffset": {"$ref": "#/definitions/CapBalance_CapPaymentReductionDetailsOffsetEntity"}, "paymentReductionDetailsWithholding": {"$ref": "#/definitions/CapBalance_CapPaymentReductionDetailsWithholdingEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}}, "title": "CapBalance CapPaymentReductionEntity", "description": "Entity for Payment Reduction types (Offsets, Deductions)."}, "CapBalance_CapPaymentReductionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "reductionNumber": {"type": "string", "description": "Number used to link split result to the payment reduction."}}, "title": "CapBalance CapPaymentReductionSplitResultEntity", "description": "Defines payment allocation reduction split result."}, "CapBalance_CapPaymentTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxPercentageASO": {"type": "number"}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/CapBalance_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxW4Percentage": {"type": "number", "description": "Defines Federal tax percentage calculated according to the W4 parameters."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "federalTaxableWages": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapBalance CapPaymentTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "CapBalance_CapPaymentTaxDetailsFicaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFicaEntity"}, "ficaRate": {"type": "number"}, "ficaTaxableWages": {"$ref": "#/definitions/Money"}, "ficaType": {"type": "string", "description": "Defines the type of FICA."}}, "title": "CapBalance CapPaymentTaxDetailsFicaEntity", "description": "Defines Fica tax details."}, "CapBalance_CapPaymentTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/CapBalance_Term"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "stateTaxableWages": {"$ref": "#/definitions/Money"}}, "title": "CapBalance CapPaymentTaxDetailsStateEntity", "description": "Defines State tax details."}, "CapBalance_CapPaymentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxEntity"}, "paymentTaxDetailsFederal": {"$ref": "#/definitions/CapBalance_CapPaymentTaxDetailsFederalEntity"}, "paymentTaxDetailsFica": {"$ref": "#/definitions/CapBalance_CapPaymentTaxDetailsFicaEntity"}, "paymentTaxDetailsState": {"$ref": "#/definitions/CapBalance_CapPaymentTaxDetailsStateEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}}, "title": "CapBalance CapPaymentTaxEntity", "description": "Entity for Payment Tax types."}, "CapBalance_CapPaymentTaxSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "taxNumber": {"type": "string", "description": "Number used to link split result to the payment tax."}}, "title": "CapBalance CapPaymentTaxSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapBalance_CapPaymentTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapBalance CapPaymentTypicalWorkWeekEntity"}, "CapBalance_CapPaymentWithholdingAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}}, "title": "CapBalance CapPaymentWithholdingAdditionEntity", "description": "Entity for the withholding additions."}, "CapBalance_CapPaymentWithholdingAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationDeductionDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationLossInfo"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapBalance_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentReductionSplitResultEntity"}}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentTaxSplitResultEntity"}}}, "title": "CapBalance CapPaymentWithholdingAllocationEntity", "description": "Entity for the withholding allocation information."}, "CapBalance_CapPaymentWithholdingDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingDetailsEntity"}, "withholdingAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentWithholdingAdditionEntity"}}, "withholdingAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentWithholdingAllocationEntity"}}, "withholdingReductions": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentWithholdingReductionEntity"}}, "withholdingTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapPaymentWithholdingTaxEntity"}}}, "title": "CapBalance CapPaymentWithholdingDetailsEntity", "description": "Entity for the withholding details."}, "CapBalance_CapPaymentWithholdingReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingReductionEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}, "withholdingReductionDetailsDeduction": {"$ref": "#/definitions/CapBalance_CapPaymentReductionDetailsDeductionEntity"}}, "title": "CapBalance CapPaymentWithholdingReductionEntity", "description": "Entity for the withholding reductions."}, "CapBalance_CapPaymentWithholdingTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingTaxEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}, "withholdingTaxDetailsFederal": {"$ref": "#/definitions/CapBalance_CapPaymentTaxDetailsFederalEntity"}, "withholdingTaxDetailsFica": {"$ref": "#/definitions/CapBalance_CapPaymentTaxDetailsFicaEntity"}}, "title": "CapBalance CapPaymentWithholdingTaxEntity", "description": "Entity for the withholding taxes."}, "CapBalance_CapSelfBalanceGenerationRulesOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSelfBalanceGenerationRulesOutput"}, "selfBalanceTransactions": {"type": "array", "items": {"$ref": "#/definitions/CapBalance_CapBalancePaymentEntity"}}}, "title": "CapBalance CapSelfBalanceGenerationRulesOutput", "description": "Output of the self balance generation rules."}, "CapBalance_CapSelfBalanceGenerationRulesOutputSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapBalance_CapSelfBalanceGenerationRulesOutput"}}, "title": "CapBalance_CapSelfBalanceGenerationRulesOutputSuccess"}, "CapBalance_CapSelfBalanceGenerationRulesOutputSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapBalance_CapSelfBalanceGenerationRulesOutputSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapBalance_CapSelfBalanceGenerationRulesOutputSuccessBody"}, "CapBalance_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapBalance MessageType", "description": "Holds information of message type."}, "CapBalance_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapBalance Period"}, "CapBalance_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapBalance Term"}, "CapLeave_CapAbsenceClaimBalanceOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimBalanceOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeave CapAbsenceClaimBalanceOpenItemInfo"}, "CapLeave_CapAbsenceClaimCoverageOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimCoverageOpenItemInfo"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "dateRange": {"$ref": "#/definitions/CapLeave_Period"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "totalGBAorDuration": {"type": "number", "description": "Total amount to be paid"}, "unpaidGBAorDuration": {"type": "number", "description": "Total amount to be unpaid"}}, "title": "CapLeave CapAbsenceClaimCoverageOpenItemInfo"}, "CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Payment Number"}, "paymentState": {"type": "string", "description": "Payment State"}}, "title": "CapLeave CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "allocationPeriod": {"$ref": "#/definitions/CapLeave_Period"}, "countOfUnposted": {"type": "integer", "format": "int64", "description": "Count Of Unposted"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "frequencyType": {"type": "string", "description": "Frequency Type"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentScheduleNumber": {"type": "string", "description": "Payment Schedule Number"}}, "title": "CapLeave CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "CapLeave_CapLeaveClaimClosureOpenItemsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimClosureOpenItemsOutput"}, "actualPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "balanceInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimBalanceOpenItemInfo"}}, "coverageInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimCoverageOpenItemInfo"}}, "issuedPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "scheduledPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo"}}}, "title": "CapLeave CapLeaveClaimClosureOpenItemsOutput", "description": "Entity for closure claim"}, "CapLeave_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave Period"}, "CapPaymentSchedule_CapAllocationWithholdingCancelationDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAllocationWithholdingCancelationDetailsEntity"}, "paymentNumber": {"type": "string", "description": "Number of payment whose withholding was canceled (reversed)"}}, "title": "CapPaymentSchedule CapAllocationWithholdingCancelationDetailsEntity", "description": "Allocation Withholding Cancelation Details "}, "CapPaymentSchedule_CapBuildPaymentScheduleInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBuildPaymentScheduleInput"}, "description": {"type": "string", "description": "User input from the payment creation page."}, "exGratias": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleExGratiasInput"}}, "expenses": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleExpensesInput"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "settlements": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleSettlementInfoInput"}}, "withholdings": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleWithholdingsInput"}}}, "title": "CapPaymentSchedule CapBuildPaymentScheduleInput"}, "CapPaymentSchedule_CapMasterPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMasterPaymentDetailsEntity"}, "masterPayment": {"$ref": "#/definitions/EntityLink"}, "masterPaymentNumber": {"type": "string", "description": "A unique actual payment number of the master payment which sub payment belongs to."}, "masterPaymentScheduledNumber": {"type": "string", "description": "A unique scheduled payment number of the master payment which sub payment belongs to."}}, "title": "CapPaymentSchedule CapMasterPaymentDetailsEntity", "description": "Details of the master payment which sub payment belongs to."}, "CapPaymentSchedule_CapPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeTypeCd": {"type": "string"}, "paymentMethodDetails": {"type": "object", "description": "Payment method details."}}, "title": "CapPaymentSchedule CapPayeeDetailsEntity", "description": "Defines payment payee details."}, "CapPaymentSchedule_CapPayeeRoleDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeRoleDetailsEntity"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "registryId": {"type": "string", "description": "URI to CEM."}, "representBeneficiary": {"type": "string", "description": "Payee on beharf of Beneficiary's uri."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of the Payee"}}}, "title": "CapPaymentSchedule CapPayeeRoleDetailsEntity", "description": "Payment Payee Role details."}, "CapPaymentSchedule_CapPaymentAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCaseNumber": {"type": "string", "description": "Accumulator case number."}, "accumulatorClaimNumber": {"type": "string", "description": "Accumulator claim number."}, "accumulatorCoverage": {"type": "string", "description": "Accumulator coverage. Defines which type of accumulator is consumed."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapPaymentSchedule CapPaymentAccumulatorDetailsEntity", "description": "Accumulator details."}, "CapPaymentSchedule_CapPaymentAccumulatorDetailsExtensionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapPaymentSchedule CapPaymentAccumulatorDetailsExtensionEntity", "description": "Accumulator details extension."}, "CapPaymentSchedule_CapPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsColaEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines cola consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines cola elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionDetailsInterestEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsInterestEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDaysFromDolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOL."}, "interestDaysFromDosReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOS."}, "interestDaysFromPolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since POL was recieved."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Defines the date up to when interest were paid ."}, "interestRate": {"type": "number", "description": "Defines the rate based on which Interest was calculated."}, "interestState": {"type": "string", "description": "Interest state according to which interest are caculated."}, "interestThresholdAmount": {"$ref": "#/definitions/Money"}, "interestTimeThreshold": {"type": "integer", "format": "int64", "description": "Interest time threshold."}, "isCompound": {"type": "boolean", "description": "Identifies if Compound interest calculation formula was used."}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsInterestEntity", "description": "Interest addition details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionDetailsRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabBenefitCd": {"type": "string", "description": "Rehab Benefit code taken from policy. Defines if rehab is included or not."}, "rehabBenefitPct": {"type": "number", "description": "Rehab percentage. Defines by how much to increase the AGBA."}, "rehabTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsRehabEntity", "description": "Rehab details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionDetailsRestoredEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRestoredEntity"}, "restoredDeathBenefit": {"type": "number", "description": "Restored Death Benefit."}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsRestoredEntity", "description": "Restored details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsColaEntity"}, "paymentAdditionDetailsInterest": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsInterestEntity"}, "paymentAdditionDetailsRehab": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsRehabEntity"}, "paymentAdditionDetailsRestored": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsRestoredEntity"}}, "title": "CapPaymentSchedule CapPaymentAdditionEntity", "description": "Entity for Payment Addition types (Cola, Interest Payment)."}, "CapPaymentSchedule_CapPaymentAdditionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionSplitResultEntity"}, "additionNumber": {"type": "string", "description": "Number used to link split result to the payment addition."}, "appliedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentAdditionSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapPaymentSchedule_CapPaymentAllocationBalanceAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBalanceAdjustmentEntity"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapPaymentSchedule CapPaymentAllocationBalanceAdjustmentEntity", "description": "Details of the allocation with adjustment type."}, "CapPaymentSchedule_CapPaymentAllocationBeneficiaryDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBeneficiaryDetailsEntity"}, "beneficiary": {"$ref": "#/definitions/EntityLink"}, "beneficiaryPct": {"type": "number", "description": "Percentage of the benefit amount that the beneficiary receives."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "CapPaymentSchedule CapPaymentAllocationBeneficiaryDetailsEntity", "description": "Details of the customer the benefit was calcualted for."}, "CapPaymentSchedule_CapPaymentAllocationBenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBenefitDurationEntity"}, "benefitDuration": {"type": "number", "description": "Benefit duration amount per defined frequency inside the period."}, "benefitDurationFrequencyCd": {"type": "string", "description": "The timeframe inside the period within which the benefit duration occurrences happen."}, "benefitDurationPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}}, "title": "CapPaymentSchedule CapPaymentAllocationBenefitDurationEntity", "description": "Details of periods for which benefit is calculated."}, "CapPaymentSchedule_CapPaymentAllocationDeductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDeductionDetailsEntity"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentAllocationDeductionDetailsEntity", "description": "Details of 3rd party deduction payment allocation."}, "CapPaymentSchedule_CapPaymentAllocationDisabilityDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDisabilityDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBenefitDurationEntity"}}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "monthlyGbaAmount": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}, "taxableAmount": {"$ref": "#/definitions/Money"}, "taxablePercentage": {"type": "number", "description": "The percentage of the gross benefit amount that will taxable."}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentAllocationDisabilityDetailsEntity", "description": "Details of Disability LOB allocation."}, "CapPaymentSchedule_CapPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationBeneficiaryDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBeneficiaryDetailsEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationDeductionDetailsEntity"}, "allocationDisabilityDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationDisabilityDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLifeDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationLifeDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "allocationWithholdingCancelationDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapAllocationWithholdingCancelationDetailsEntity"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxSplitResultEntity"}}}, "title": "CapPaymentSchedule CapPaymentAllocationEntity", "description": "Entity for the payment allocation information."}, "CapPaymentSchedule_CapPaymentAllocationLifeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLifeDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentAllocationLifeDetailsEntity", "description": "Details of Life LOB allocation."}, "CapPaymentSchedule_CapPaymentAllocationLossInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfo"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentAllocationLossInfo", "description": "Defines payment allocation loss info details."}, "CapPaymentSchedule_CapPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfoEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "claimType": {"type": "string", "description": "Defines claim type."}, "dateOfLoss": {"type": "string", "format": "date-time"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Defines loss type."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentSchedule CapPaymentAllocationLossInfoEntity", "description": "The attribute to store all info that is needed from Claim for Payments."}, "CapPaymentSchedule_CapPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationPayableItemEntity"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapPaymentSchedule CapPaymentAllocationPayableItemEntity", "description": "Stores details for what it is paid."}, "CapPaymentSchedule_CapPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentDetailsEntity"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Defines insured last work date prior to payment."}, "payeeDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPayeeDetailsEntity"}, "payeeRoleDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPayeeRoleDetailsEntity"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionEntity"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}, "paymentReductions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionEntity"}}, "paymentTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxEntity"}}, "typicalWorkWeek": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTypicalWorkWeekEntity"}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentDetailsEntity", "description": "Payment details."}, "CapPaymentSchedule_CapPaymentFinancialAdjustmentPayableSourcesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentFinancialAdjustmentPayableSourcesEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to the related policy."}}, "title": "CapPaymentSchedule CapPaymentFinancialAdjustmentPayableSourcesEntity", "description": "Identifies sources for which financial adjustment is applied"}, "CapPaymentSchedule_CapPaymentReductionDetailsDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type."}, "deductionBeneficiary": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "Defines the Deduction precentage."}, "deductionProvider": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "deductionType": {"type": "string", "description": "Deduction type."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "Is a Deduction applied before taxes."}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsDeductionEntity", "description": "Deduction details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionDetailsIndebtednessEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsIndebtednessEntity"}, "indebtedness": {"type": "number", "description": "Indebtedness."}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsIndebtednessEntity", "description": "Indebtedness details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionDetailsOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsOffsetEntity"}, "isAutoOffset": {"type": "boolean"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "Defines the prorating rate of the Offset."}, "offsetTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "offsetType": {"type": "string"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsOffsetEntity", "description": "Offset details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionDetailsWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsWithholdingEntity"}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsWithholdingEntity", "description": "Withholding details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionEntity"}, "paymentReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsDeductionEntity"}, "paymentReductionDetailsIndebtedness": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsIndebtednessEntity"}, "paymentReductionDetailsOffset": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsOffsetEntity"}, "paymentReductionDetailsWithholding": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsWithholdingEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}}, "title": "CapPaymentSchedule CapPaymentReductionEntity", "description": "Entity for Payment Reduction types (Offsets, Deductions)."}, "CapPaymentSchedule_CapPaymentReductionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "reductionNumber": {"type": "string", "description": "Number used to link split result to the payment reduction."}}, "title": "CapPaymentSchedule CapPaymentReductionSplitResultEntity", "description": "Defines payment allocation reduction split result."}, "CapPaymentSchedule_CapPaymentScheduleActivationResult": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleActivationResult"}, "activationMessages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleMessageEntity"}}, "activationStatus": {"type": "string"}}, "title": "CapPaymentSchedule CapPaymentScheduleActivationResult"}, "CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleCompletionRulesOutput"}, "canCompletePaymentSchedule": {"type": "boolean", "description": "Identifies if payment schedule can be completed."}, "paymentSchedule": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntity"}}, "title": "CapPaymentSchedule CapPaymentScheduleCompletionRulesOutput", "description": "Payment Schedule completion rules output."}, "CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutputSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutput"}}, "title": "CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutputSuccess"}, "CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutputSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutputSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentSchedule_CapPaymentScheduleCompletionRulesOutputSuccessBody"}, "CapPaymentSchedule_CapPaymentScheduleEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPaymentSchedule"}, "_modelType": {"type": "string", "example": "CapPaymentSchedule"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentScheduleEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the schedule was created."}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentScheduleActivationResult": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleActivationResult"}, "paymentTemplate": {"$ref": "#/definitions/EntityLink"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapScheduledPaymentEntity"}}, "scheduleMessages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleMessageEntity"}}, "scheduleNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment schedule."}, "state": {"type": "string", "description": "State of a payment schedule lifecycle."}}, "title": "CapPaymentSchedule CapPaymentScheduleEntity", "description": "The Root Entity of CAP Payment Schedule Domain."}, "CapPaymentSchedule_CapPaymentScheduleEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntity"}}, "title": "CapPaymentSchedule_CapPaymentScheduleEntitySuccess"}, "CapPaymentSchedule_CapPaymentScheduleEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentSchedule_CapPaymentScheduleEntitySuccessBody"}, "CapPaymentSchedule_CapPaymentScheduleExGratiasInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleExGratiasInput"}, "exGratiaAmount": {"$ref": "#/definitions/Money"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia Description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentScheduleExGratiasInput", "description": "Additional details for allocation with ex gratia reserve type."}, "CapPaymentSchedule_CapPaymentScheduleExpensesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleExpensesInput"}, "expenseAmount": {"$ref": "#/definitions/Money"}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentScheduleExpensesInput", "description": "Additional details for allocation with expsene reserve type."}, "CapPaymentSchedule_CapPaymentScheduleMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentSchedule CapPaymentScheduleMessageEntity", "description": "Payment Schedule messages."}, "CapPaymentSchedule_CapPaymentScheduleSettlementFrequencyConfigInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementFrequencyConfigInput"}, "type": {"type": "string", "description": "Recurrent payments frequency type."}}, "title": "CapPaymentSchedule CapPaymentScheduleSettlementFrequencyConfigInput", "description": "Defines payment sequence frequency details."}, "CapPaymentSchedule_CapPaymentScheduleSettlementInfoInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementInfoInput"}, "allocationPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "frequencyConfiguration": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleSettlementFrequencyConfigInput"}, "interestDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleSettlementInterestsInput"}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "partialDisability": {"$ref": "#/definitions/CapPaymentSchedule_PartialDisabilityInput"}, "payeeDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleSettlementPayeeInput"}, "uri": {"type": "string", "description": "URI or the settlement used for retreiving financial data"}}, "title": "CapPaymentSchedule CapPaymentScheduleSettlementInfoInput"}, "CapPaymentSchedule_CapPaymentScheduleSettlementInterestsInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementInterestsInput"}, "interestBeneficiaryState": {"type": "string", "description": "Beneficiary state for interest calculation."}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDecedentState": {"type": "string", "description": "Decedent state for interest calculation."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Interest Paid Up To Date."}, "interestPlanHolderState": {"type": "string", "description": "Policy holder state for interest calculation."}, "interestStateOverrideCd": {"type": "string", "description": "Country state to calculate an interest for."}}, "title": "CapPaymentSchedule CapPaymentScheduleSettlementInterestsInput", "description": "Allocation interest details."}, "CapPaymentSchedule_CapPaymentScheduleSettlementPayeeInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementPayeeInput"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "payeeTypeCd": {"type": "string"}, "provider": {"$ref": "#/definitions/EntityLink"}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentScheduleSettlementPayeeInput", "description": "Defines payee details for payment template."}, "CapPaymentSchedule_CapPaymentScheduleWithholdingsInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleWithholdingsInput"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentScheduleWithholdingsInput"}, "CapPaymentSchedule_CapPaymentTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxPercentageASO": {"type": "number"}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxW4Percentage": {"type": "number", "description": "Defines Federal tax percentage calculated according to the W4 parameters."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "federalTaxableWages": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapPaymentSchedule CapPaymentTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "CapPaymentSchedule_CapPaymentTaxDetailsFicaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFicaEntity"}, "ficaRate": {"type": "number"}, "ficaTaxableWages": {"$ref": "#/definitions/Money"}, "ficaType": {"type": "string", "description": "Defines the type of FICA."}}, "title": "CapPaymentSchedule CapPaymentTaxDetailsFicaEntity", "description": "Defines Fica tax details."}, "CapPaymentSchedule_CapPaymentTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "stateTaxableWages": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentTaxDetailsStateEntity", "description": "Defines State tax details."}, "CapPaymentSchedule_CapPaymentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxEntity"}, "paymentTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFederalEntity"}, "paymentTaxDetailsFica": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFicaEntity"}, "paymentTaxDetailsState": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsStateEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}}, "title": "CapPaymentSchedule CapPaymentTaxEntity", "description": "Entity for Payment Tax types."}, "CapPaymentSchedule_CapPaymentTaxSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "taxNumber": {"type": "string", "description": "Number used to link split result to the payment tax."}}, "title": "CapPaymentSchedule CapPaymentTaxSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapPaymentSchedule_CapPaymentTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapPaymentSchedule CapPaymentTypicalWorkWeekEntity"}, "CapPaymentSchedule_CapPaymentWithholdingAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}}, "title": "CapPaymentSchedule CapPaymentWithholdingAdditionEntity", "description": "Entity for the withholding additions."}, "CapPaymentSchedule_CapPaymentWithholdingAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationDeductionDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationLossInfo"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionSplitResultEntity"}}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxSplitResultEntity"}}}, "title": "CapPaymentSchedule CapPaymentWithholdingAllocationEntity", "description": "Entity for the withholding allocation information."}, "CapPaymentSchedule_CapPaymentWithholdingDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingDetailsEntity"}, "withholdingAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingAdditionEntity"}}, "withholdingAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingAllocationEntity"}}, "withholdingReductions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingReductionEntity"}}, "withholdingTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingTaxEntity"}}}, "title": "CapPaymentSchedule CapPaymentWithholdingDetailsEntity", "description": "Entity for the withholding details."}, "CapPaymentSchedule_CapPaymentWithholdingReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingReductionEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}, "withholdingReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsDeductionEntity"}}, "title": "CapPaymentSchedule CapPaymentWithholdingReductionEntity", "description": "Entity for the withholding reductions."}, "CapPaymentSchedule_CapPaymentWithholdingTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingTaxEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}, "withholdingTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFederalEntity"}, "withholdingTaxDetailsFica": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFicaEntity"}}, "title": "CapPaymentSchedule CapPaymentWithholdingTaxEntity", "description": "Entity for the withholding taxes."}, "CapPaymentSchedule_CapReschedulePaymentsOutputConfig": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapReschedulePaymentsOutputConfig"}, "previousPaymentScheduleState": {"type": "string", "description": "The state of the previous payment schedule."}, "useBuildPaymentSchedule": {"type": "boolean", "description": "Defines if build payment schedule service should be invoked."}}, "title": "CapPaymentSchedule CapReschedulePaymentsOutputConfig", "description": "Rescheduling automation config."}, "CapPaymentSchedule_CapReschedulePaymentsPayload": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapReschedulePaymentsPayload"}, "buildPaymentScheduleInput": {"$ref": "#/definitions/CapPaymentSchedule_CapBuildPaymentScheduleInput"}, "reschedulingConfig": {"$ref": "#/definitions/CapPaymentSchedule_CapReschedulePaymentsOutputConfig"}}, "title": "CapPaymentSchedule CapReschedulePaymentsPayload", "description": "Reschedule payments service output."}, "CapPaymentSchedule_CapReschedulePaymentsPayloadSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentSchedule_CapReschedulePaymentsPayload"}}, "title": "CapPaymentSchedule_CapReschedulePaymentsPayloadSuccess"}, "CapPaymentSchedule_CapReschedulePaymentsPayloadSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentSchedule_CapReschedulePaymentsPayloadSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentSchedule_CapReschedulePaymentsPayloadSuccessBody"}, "CapPaymentSchedule_CapScheduledPaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapScheduledPaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "isIssuedOnScheduleCreation": {"type": "boolean", "description": "Defines if the payment has been issued by the time the schedule was created."}, "masterPaymentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapMasterPaymentDetailsEntity"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_MessageType"}}, "paymentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}, "paymentSubType": {"type": "string", "description": "Details of payment Sub Type"}, "scheduledPaymentNumber": {"type": "string", "description": "Unique payment number in the scheduled used to match actual and scheduled payments."}, "withholdingDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingDetailsEntity"}}, "title": "CapPaymentSchedule CapScheduledPaymentEntity", "description": "Defines payment transaction information."}, "CapPaymentSchedule_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentSchedule MessageType", "description": "Holds information of message type."}, "CapPaymentSchedule_PartialDisabilityInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PartialDisabilityInput"}, "currentEarningsAmount": {"$ref": "#/definitions/Money"}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation is for partial disability."}}, "title": "CapPaymentSchedule PartialDisabilityInput"}, "CapPaymentSchedule_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentSchedule Period"}, "CapPaymentSchedule_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentSchedule Term"}, "CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPaymentToPHPaymentIndex"}, "_modelType": {"type": "string", "example": "CapPaymentToPHPaymentIndex"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentToPHPaymentIdxEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "paymentHubPaymentUri": {"type": "string"}, "paymentUri": {"type": "string"}}, "title": "CapPaymentToPHPaymentIndex CapPaymentToPHPaymentIdxEntity"}, "CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntity"}}, "title": "CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntitySuccess"}, "CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentToPHPaymentIndex_CapPaymentToPHPaymentIdxEntitySuccessBody"}, "CapResolveActivePaymentsOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveActivePaymentsOutputs"}, "CapResolveActivePaymentsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveActivePaymentsOutputs"}}, "title": "CapResolveActivePaymentsOutputsSuccess"}, "CapResolveActivePaymentsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveActivePaymentsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveActivePaymentsOutputsSuccessBody"}, "CapResolveActiveSchedulesOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveActiveSchedulesOutputs"}, "CapResolveActiveSchedulesOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveActiveSchedulesOutputs"}}, "title": "CapResolveActiveSchedulesOutputsSuccess"}, "CapResolveActiveSchedulesOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveActiveSchedulesOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveActiveSchedulesOutputsSuccessBody"}, "CapResolveCloseClaimReasonOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveCloseClaimReasonOutputs"}, "CapResolveCloseClaimReasonOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputs"}}, "title": "CapResolveCloseClaimReasonOutputsSuccess"}, "CapResolveCloseClaimReasonOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveCloseClaimReasonOutputsSuccessBody"}, "CapResolveNonIssuedUnderpaymentsOutputs": {"properties": {"output": {"$ref": "#/definitions/PaymentDefinition_PaymentsContainer"}}, "title": "CapResolveNonIssuedUnderpaymentsOutputs"}, "CapResolveNonIssuedUnderpaymentsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveNonIssuedUnderpaymentsOutputs"}}, "title": "CapResolveNonIssuedUnderpaymentsOutputsSuccess"}, "CapResolveNonIssuedUnderpaymentsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveNonIssuedUnderpaymentsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveNonIssuedUnderpaymentsOutputsSuccessBody"}, "CommonFinancialInternal_CapUserCarryingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapUserCarryingEntity"}, "userId": {"type": "string"}}, "title": "CommonFinancialInternal CapUserCarryingEntity"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputs"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "DocumentToResultOutputs": {"properties": {"out": {"type": "object"}}, "title": "DocumentToResultOutputs"}, "DocumentToResultOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DocumentToResultOutputs"}}, "title": "DocumentToResultOutputsSuccess"}, "DocumentToResultOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DocumentToResultOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DocumentToResultOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputs"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "OriginSourceClosureDataOutputs": {"properties": {"output": {"type": "object"}}, "title": "OriginSourceClosureDataOutputs"}, "OriginSourceClosureDataOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/OriginSourceClosureDataOutputs"}}, "title": "OriginSourceClosureDataOutputsSuccess"}, "OriginSourceClosureDataOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/OriginSourceClosureDataOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "OriginSourceClosureDataOutputsSuccessBody"}, "OriginSourceToPaymentScheduleInputOutputs": {"properties": {"output": {"type": "object"}}, "title": "OriginSourceToPaymentScheduleInputOutputs"}, "OriginSourceToPaymentScheduleInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/OriginSourceToPaymentScheduleInputOutputs"}}, "title": "OriginSourceToPaymentScheduleInputOutputsSuccess"}, "OriginSourceToPaymentScheduleInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/OriginSourceToPaymentScheduleInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "OriginSourceToPaymentScheduleInputOutputsSuccessBody"}, "PaymentDefinition_CapAllocationWithholdingCancelationDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAllocationWithholdingCancelationDetailsEntity"}, "paymentNumber": {"type": "string", "description": "Number of payment whose withholding was canceled (reversed)"}}, "title": "PaymentDefinition CapAllocationWithholdingCancelationDetailsEntity", "description": "Allocation Withholding Cancelation Details "}, "PaymentDefinition_CapMasterPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMasterPaymentDetailsEntity"}, "masterPayment": {"$ref": "#/definitions/EntityLink"}, "masterPaymentNumber": {"type": "string", "description": "A unique actual payment number of the master payment which sub payment belongs to."}, "masterPaymentScheduledNumber": {"type": "string", "description": "A unique scheduled payment number of the master payment which sub payment belongs to."}}, "title": "PaymentDefinition CapMasterPaymentDetailsEntity", "description": "Details of the master payment which sub payment belongs to."}, "PaymentDefinition_CapPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeTypeCd": {"type": "string"}, "paymentMethodDetails": {"type": "object", "description": "Payment method details."}}, "title": "PaymentDefinition CapPayeeDetailsEntity", "description": "Defines payment payee details."}, "PaymentDefinition_CapPayeeRoleDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeRoleDetailsEntity"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "registryId": {"type": "string", "description": "URI to CEM."}, "representBeneficiary": {"type": "string", "description": "Payee on beharf of Beneficiary's uri."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of the Payee"}}}, "title": "PaymentDefinition CapPayeeRoleDetailsEntity", "description": "Payment Payee Role details."}, "PaymentDefinition_CapPaymentAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCaseNumber": {"type": "string", "description": "Accumulator case number."}, "accumulatorClaimNumber": {"type": "string", "description": "Accumulator claim number."}, "accumulatorCoverage": {"type": "string", "description": "Accumulator coverage. Defines which type of accumulator is consumed."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "PaymentDefinition CapPaymentAccumulatorDetailsEntity", "description": "Accumulator details."}, "PaymentDefinition_CapPaymentAccumulatorDetailsExtensionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/PaymentDefinition_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "PaymentDefinition CapPaymentAccumulatorDetailsExtensionEntity", "description": "Accumulator details extension."}, "PaymentDefinition_CapPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsColaEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines cola consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines cola elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/PaymentDefinition_Period"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "PaymentDefinition CapPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "PaymentDefinition_CapPaymentAdditionDetailsInterestEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsInterestEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDaysFromDolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOL."}, "interestDaysFromDosReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOS."}, "interestDaysFromPolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since POL was recieved."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Defines the date up to when interest were paid ."}, "interestRate": {"type": "number", "description": "Defines the rate based on which Interest was calculated."}, "interestState": {"type": "string", "description": "Interest state according to which interest are caculated."}, "interestThresholdAmount": {"$ref": "#/definitions/Money"}, "interestTimeThreshold": {"type": "integer", "format": "int64", "description": "Interest time threshold."}, "isCompound": {"type": "boolean", "description": "Identifies if Compound interest calculation formula was used."}}, "title": "PaymentDefinition CapPaymentAdditionDetailsInterestEntity", "description": "Interest addition details are described in this entity."}, "PaymentDefinition_CapPaymentAdditionDetailsRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabBenefitCd": {"type": "string", "description": "Rehab Benefit code taken from policy. Defines if rehab is included or not."}, "rehabBenefitPct": {"type": "number", "description": "Rehab percentage. Defines by how much to increase the AGBA."}, "rehabTerm": {"$ref": "#/definitions/PaymentDefinition_Term"}}, "title": "PaymentDefinition CapPaymentAdditionDetailsRehabEntity", "description": "Rehab details are described in this entity."}, "PaymentDefinition_CapPaymentAdditionDetailsRestoredEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRestoredEntity"}, "restoredDeathBenefit": {"type": "number", "description": "Restored Death Benefit."}}, "title": "PaymentDefinition CapPaymentAdditionDetailsRestoredEntity", "description": "Restored details are described in this entity."}, "PaymentDefinition_CapPaymentAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionDetailsColaEntity"}, "paymentAdditionDetailsInterest": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionDetailsInterestEntity"}, "paymentAdditionDetailsRehab": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionDetailsRehabEntity"}, "paymentAdditionDetailsRestored": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionDetailsRestoredEntity"}}, "title": "PaymentDefinition CapPaymentAdditionEntity", "description": "Entity for Payment Addition types (Cola, Interest Payment)."}, "PaymentDefinition_CapPaymentAdditionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionSplitResultEntity"}, "additionNumber": {"type": "string", "description": "Number used to link split result to the payment addition."}, "appliedAmount": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentAdditionSplitResultEntity", "description": "Defines payment allocation addition split result."}, "PaymentDefinition_CapPaymentAllocationBalanceAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBalanceAdjustmentEntity"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "PaymentDefinition CapPaymentAllocationBalanceAdjustmentEntity", "description": "Details of the allocation with adjustment type."}, "PaymentDefinition_CapPaymentAllocationBeneficiaryDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBeneficiaryDetailsEntity"}, "beneficiary": {"$ref": "#/definitions/EntityLink"}, "beneficiaryPct": {"type": "number", "description": "Percentage of the benefit amount that the beneficiary receives."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "PaymentDefinition CapPaymentAllocationBeneficiaryDetailsEntity", "description": "Details of the customer the benefit was calcualted for."}, "PaymentDefinition_CapPaymentAllocationBenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBenefitDurationEntity"}, "benefitDuration": {"type": "number", "description": "Benefit duration amount per defined frequency inside the period."}, "benefitDurationFrequencyCd": {"type": "string", "description": "The timeframe inside the period within which the benefit duration occurrences happen."}, "benefitDurationPeriod": {"$ref": "#/definitions/PaymentDefinition_Period"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}}, "title": "PaymentDefinition CapPaymentAllocationBenefitDurationEntity", "description": "Details of periods for which benefit is calculated."}, "PaymentDefinition_CapPaymentAllocationDeductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDeductionDetailsEntity"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "PaymentDefinition CapPaymentAllocationDeductionDetailsEntity", "description": "Details of 3rd party deduction payment allocation."}, "PaymentDefinition_CapPaymentAllocationDisabilityDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDisabilityDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationBenefitDurationEntity"}}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "monthlyGbaAmount": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}, "taxableAmount": {"$ref": "#/definitions/Money"}, "taxablePercentage": {"type": "number", "description": "The percentage of the gross benefit amount that will taxable."}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentAllocationDisabilityDetailsEntity", "description": "Details of Disability LOB allocation."}, "PaymentDefinition_CapPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationBeneficiaryDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationBeneficiaryDetailsEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationDeductionDetailsEntity"}, "allocationDisabilityDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationDisabilityDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLifeDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationLifeDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationLossInfo": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "allocationWithholdingCancelationDetails": {"$ref": "#/definitions/PaymentDefinition_CapAllocationWithholdingCancelationDetailsEntity"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxSplitResultEntity"}}}, "title": "PaymentDefinition CapPaymentAllocationEntity", "description": "Entity for the payment allocation information."}, "PaymentDefinition_CapPaymentAllocationLifeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLifeDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentAllocationLifeDetailsEntity", "description": "Details of Life LOB allocation."}, "PaymentDefinition_CapPaymentAllocationLossInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfo"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "PaymentDefinition CapPaymentAllocationLossInfo", "description": "Defines payment allocation loss info details."}, "PaymentDefinition_CapPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfoEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "claimType": {"type": "string", "description": "Defines claim type."}, "dateOfLoss": {"type": "string", "format": "date-time"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Defines loss type."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}, "title": "PaymentDefinition CapPaymentAllocationLossInfoEntity", "description": "The attribute to store all info that is needed from Claim for Payments."}, "PaymentDefinition_CapPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationPayableItemEntity"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/PaymentDefinition_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "PaymentDefinition CapPaymentAllocationPayableItemEntity", "description": "Stores details for what it is paid."}, "PaymentDefinition_CapPaymentDetailsEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "PaymentDefinition"}, "_modelType": {"type": "string", "example": "CapPayment"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentDetailsEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Defines insured last work date prior to payment."}, "payeeDetails": {"$ref": "#/definitions/PaymentDefinition_CapPayeeDetailsEntity"}, "payeeRoleDetails": {"$ref": "#/definitions/PaymentDefinition_CapPayeeRoleDetailsEntity"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionEntity"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}, "paymentReductions": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionEntity"}}, "paymentTaxes": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxEntity"}}, "typicalWorkWeek": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTypicalWorkWeekEntity"}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentDetailsEntity", "description": "Payment details."}, "PaymentDefinition_CapPaymentEntity": {"required": ["_modelName", "_type", "_variation"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "PaymentDefinition"}, "_modelType": {"type": "string", "example": "CapPayment"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentEntity"}, "_variation": {"type": "string", "example": "externalBalance"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "approvalDate": {"type": "string", "format": "date-time", "description": "Date when the payment was issued."}, "cancelationDate": {"type": "string", "format": "date-time", "description": "Date when the payment was canceled or issue failed."}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "issueDate": {"type": "string", "format": "date-time", "description": "Date when the payment was approved."}, "masterPaymentDetails": {"$ref": "#/definitions/PaymentDefinition_CapMasterPaymentDetailsEntity"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentMessageEntity"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentApprovalResult": {"$ref": "#/definitions/PaymentDefinition_PaymentApprovalResult"}, "paymentDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Unique payment number."}, "paymentSchedule": {"$ref": "#/definitions/EntityLink"}, "paymentSubType": {"type": "string", "description": "Details of payment Sub Type"}, "scheduledPaymentNumber": {"type": "string", "description": "Unique payment number within the payment schedule, used to link the actual payment to the scheduled payment during the balance calculation."}, "state": {"type": "string", "description": "Payment state in the lifecycle."}, "withholdingDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentWithholdingDetailsEntity"}}, "title": "PaymentDefinition CapPaymentEntity", "description": "Root Entity of CAP Payment Domain. Defines payment transaction information."}, "PaymentDefinition_CapPaymentEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/PaymentDefinition_CapPaymentEntity"}}, "title": "PaymentDefinition_CapPaymentEntitySuccess"}, "PaymentDefinition_CapPaymentEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/PaymentDefinition_CapPaymentEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "PaymentDefinition_CapPaymentEntitySuccessBody"}, "PaymentDefinition_CapPaymentFinancialAdjustmentPayableSourcesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentFinancialAdjustmentPayableSourcesEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to the related policy."}}, "title": "PaymentDefinition CapPaymentFinancialAdjustmentPayableSourcesEntity", "description": "Identifies sources for which financial adjustment is applied"}, "PaymentDefinition_CapPaymentMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "messageSource": {"type": "string"}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "PaymentDefinition CapPaymentMessageEntity", "description": "Holds information of message type."}, "PaymentDefinition_CapPaymentReductionDetailsDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type."}, "deductionBeneficiary": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "Defines the Deduction precentage."}, "deductionProvider": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/PaymentDefinition_Term"}, "deductionType": {"type": "string", "description": "Deduction type."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "Is a Deduction applied before taxes."}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}}, "title": "PaymentDefinition CapPaymentReductionDetailsDeductionEntity", "description": "Deduction details are described in this entity."}, "PaymentDefinition_CapPaymentReductionDetailsIndebtednessEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsIndebtednessEntity"}, "indebtedness": {"type": "number", "description": "Indebtedness."}}, "title": "PaymentDefinition CapPaymentReductionDetailsIndebtednessEntity", "description": "Indebtedness details are described in this entity."}, "PaymentDefinition_CapPaymentReductionDetailsOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsOffsetEntity"}, "isAutoOffset": {"type": "boolean"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "Defines the prorating rate of the Offset."}, "offsetTerm": {"$ref": "#/definitions/PaymentDefinition_Term"}, "offsetType": {"type": "string"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentReductionDetailsOffsetEntity", "description": "Offset details are described in this entity."}, "PaymentDefinition_CapPaymentReductionDetailsWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsWithholdingEntity"}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentReductionDetailsWithholdingEntity", "description": "Withholding details are described in this entity."}, "PaymentDefinition_CapPaymentReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionEntity"}, "paymentReductionDetailsDeduction": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionDetailsDeductionEntity"}, "paymentReductionDetailsIndebtedness": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionDetailsIndebtednessEntity"}, "paymentReductionDetailsOffset": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionDetailsOffsetEntity"}, "paymentReductionDetailsWithholding": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionDetailsWithholdingEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}}, "title": "PaymentDefinition CapPaymentReductionEntity", "description": "Entity for Payment Reduction types (Offsets, Deductions)."}, "PaymentDefinition_CapPaymentReductionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "reductionNumber": {"type": "string", "description": "Number used to link split result to the payment reduction."}}, "title": "PaymentDefinition CapPaymentReductionSplitResultEntity", "description": "Defines payment allocation reduction split result."}, "PaymentDefinition_CapPaymentTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxPercentageASO": {"type": "number"}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/PaymentDefinition_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxW4Percentage": {"type": "number", "description": "Defines Federal tax percentage calculated according to the W4 parameters."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "federalTaxableWages": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "PaymentDefinition CapPaymentTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "PaymentDefinition_CapPaymentTaxDetailsFicaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFicaEntity"}, "ficaRate": {"type": "number"}, "ficaTaxableWages": {"$ref": "#/definitions/Money"}, "ficaType": {"type": "string", "description": "Defines the type of FICA."}}, "title": "PaymentDefinition CapPaymentTaxDetailsFicaEntity", "description": "Defines Fica tax details."}, "PaymentDefinition_CapPaymentTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/PaymentDefinition_Term"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "stateTaxableWages": {"$ref": "#/definitions/Money"}}, "title": "PaymentDefinition CapPaymentTaxDetailsStateEntity", "description": "Defines State tax details."}, "PaymentDefinition_CapPaymentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxEntity"}, "paymentTaxDetailsFederal": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxDetailsFederalEntity"}, "paymentTaxDetailsFica": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxDetailsFicaEntity"}, "paymentTaxDetailsState": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxDetailsStateEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}}, "title": "PaymentDefinition CapPaymentTaxEntity", "description": "Entity for Payment Tax types."}, "PaymentDefinition_CapPaymentTaxSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "taxNumber": {"type": "string", "description": "Number used to link split result to the payment tax."}}, "title": "PaymentDefinition CapPaymentTaxSplitResultEntity", "description": "Defines payment allocation addition split result."}, "PaymentDefinition_CapPaymentTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "PaymentDefinition CapPaymentTypicalWorkWeekEntity"}, "PaymentDefinition_CapPaymentWithholdingAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}}, "title": "PaymentDefinition CapPaymentWithholdingAdditionEntity", "description": "Entity for the withholding additions."}, "PaymentDefinition_CapPaymentWithholdingAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationDeductionDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationLossInfo"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/PaymentDefinition_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionSplitResultEntity"}}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxSplitResultEntity"}}}, "title": "PaymentDefinition CapPaymentWithholdingAllocationEntity", "description": "Entity for the withholding allocation information."}, "PaymentDefinition_CapPaymentWithholdingDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingDetailsEntity"}, "withholdingAdditions": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentWithholdingAdditionEntity"}}, "withholdingAllocations": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentWithholdingAllocationEntity"}}, "withholdingReductions": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentWithholdingReductionEntity"}}, "withholdingTaxes": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentWithholdingTaxEntity"}}}, "title": "PaymentDefinition CapPaymentWithholdingDetailsEntity", "description": "Entity for the withholding details."}, "PaymentDefinition_CapPaymentWithholdingReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingReductionEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}, "withholdingReductionDetailsDeduction": {"$ref": "#/definitions/PaymentDefinition_CapPaymentReductionDetailsDeductionEntity"}}, "title": "PaymentDefinition CapPaymentWithholdingReductionEntity", "description": "Entity for the withholding reductions."}, "PaymentDefinition_CapPaymentWithholdingTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingTaxEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}, "withholdingTaxDetailsFederal": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxDetailsFederalEntity"}, "withholdingTaxDetailsFica": {"$ref": "#/definitions/PaymentDefinition_CapPaymentTaxDetailsFicaEntity"}}, "title": "PaymentDefinition CapPaymentWithholdingTaxEntity", "description": "Entity for the withholding taxes."}, "PaymentDefinition_PaymentApprovalResult": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PaymentApprovalResult"}, "approvalStatus": {"type": "string"}, "message": {"type": "string"}}, "title": "PaymentDefinition PaymentApprovalResult"}, "PaymentDefinition_PaymentsContainer": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PaymentsContainer"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/PaymentDefinition_CapPaymentEntity"}}}, "title": "PaymentDefinition PaymentsContainer"}, "PaymentDefinition_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "PaymentDefinition Period"}, "PaymentDefinition_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "PaymentDefinition Term"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}, "variation": {"type": "string"}}, "title": "RootEntityKey"}, "SettlementToChildSettlementRefreshInputOutputs": {"properties": {"output": {"type": "object"}}, "title": "SettlementToChildSettlementRefreshInputOutputs"}, "SettlementToChildSettlementRefreshInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputs"}}, "title": "SettlementToChildSettlementRefreshInputOutputsSuccess"}, "SettlementToChildSettlementRefreshInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SettlementToChildSettlementRefreshInputOutputsSuccessBody"}, "VerifyPaymentScheduleActivationInputs": {"properties": {"schedule": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntity"}, "userRequest": {"$ref": "#/definitions/CommonFinancialInternal_CapUserCarryingEntity"}}, "title": "VerifyPaymentScheduleActivationInputs"}, "VerifyPaymentScheduleActivationInputsBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/VerifyPaymentScheduleActivationInputs"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "VerifyPaymentScheduleActivationInputsBody"}, "VerifyUnderpaymentApprovalInputs": {"properties": {"payment": {"$ref": "#/definitions/PaymentDefinition_CapPaymentEntity"}, "userRequest": {"$ref": "#/definitions/CommonFinancialInternal_CapUserCarryingEntity"}}, "title": "VerifyUnderpaymentApprovalInputs"}, "VerifyUnderpaymentApprovalInputsBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/VerifyUnderpaymentApprovalInputs"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "VerifyUnderpaymentApprovalInputsBody"}}}