/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {Organization, OrgPerson, WorkQueueDetails, CrmEmail, CrmPhone} from '@eisgroup/cap-services'
import {LocalizationUtils} from '@eisgroup/i18n'

import {Has<PERSON><PERSON><PERSON>} from '@eisgroup/react-components'
import {Tooltip} from '@eisgroup/ui-kit'
import {CommunicationEnvelopeMedium, CommunicationPhoneMedium, MapPinMedium} from '@eisgroup/ui-kit-icons'
import {observer} from 'mobx-react'
import React from 'react'
import {
    ASSIGN_BANNER_CONTACT_INFO_TITLE,
    ASSIGN_BANNER_MEMBER_POPOVER,
    ASSIGN_CUSTOMER_CONTACT_INFO_ADDRESS,
    ASSIGN_CUSTOMER_CONTACT_INFO_EMAIL,
    ASSIGN_CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT,
    ASSIGN_CUSTOMER_CONTACT_INFO_PHONE_NUMBERS,
    ASSIGN_CUSTOMER_CONTACT_INFO_WRAPPER,
    ASSIGN_CUSTOMER_CONTACT_METHOD_ICON
} from '../../common/package-class-names'

const {translate: t} = LocalizationUtils

export interface ClaimBannerContactInformationLabels {
    /**
     * Contact information label.
     */
    contactInformationLabel?: string
}

export interface ClaimBannerContactInformationProps extends HasLabels<ClaimBannerContactInformationLabels> {
    queueInfo?: WorkQueueDetails
    userInfo?: OrgPerson
    organizationsInfo?: Organization
}

export interface RenderContactInfoPanelProps {
    phone: string | undefined
    phoneDetail: string | undefined
    email: string | undefined
    emailDetail: string | undefined
    address: string
}

export const RenderContactInfoPanel = observer((props: RenderContactInfoPanelProps) => {
    const {phone, email, address, phoneDetail, emailDetail} = props
    return (
        <div className={ASSIGN_CUSTOMER_CONTACT_INFO_WRAPPER}>
            <div className={ASSIGN_CUSTOMER_CONTACT_INFO_ADDRESS}>
                <MapPinMedium className={ASSIGN_CUSTOMER_CONTACT_METHOD_ICON} />
                <span>{address}</span>
            </div>
            <div className={ASSIGN_CUSTOMER_CONTACT_INFO_PHONE_NUMBERS}>
                <CommunicationPhoneMedium className={ASSIGN_CUSTOMER_CONTACT_METHOD_ICON} />
                <Tooltip title={phoneDetail}>{phone}</Tooltip>
            </div>
            <div className={ASSIGN_CUSTOMER_CONTACT_INFO_EMAIL}>
                <CommunicationEnvelopeMedium className={ASSIGN_CUSTOMER_CONTACT_METHOD_ICON} />
                <Tooltip title={emailDetail}>
                    <a className={ASSIGN_CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT} href={`mailto:${email}`}>
                        {email}
                    </a>
                </Tooltip>
            </div>
        </div>
    )
})

const managerInfoTool = (info: CrmPhone[] | CrmEmail[]) => {
    const preferred = info?.filter(element => element.preferred)
    const Unpreferred = info?.filter(element => !element.preferred)
    let item: string | undefined
    if (preferred?.length === 0) {
        item =
            Unpreferred?.length <= 2
                ? Unpreferred?.map(items => items.value).join(',')
                : `${Unpreferred?.slice(0, 2)
                      .map(items => items.value)
                      .join(',')}...`
    } else {
        item = preferred[0]?.value
    }

    const itemToolTipText =
        preferred?.length === 0 ? Unpreferred?.map(items => items.value).join(',') : preferred[0]?.value

    return {
        item,
        itemToolTipText
    }
}

export const ManagerInfoDetails = (props: ClaimBannerContactInformationProps) => {
    const {queueInfo, userInfo, organizationsInfo} = props
    const labels: ClaimBannerContactInformationLabels = {
        contactInformationLabel: t('cap-core:banner_contact_information')
    }
    const phone =
        !queueInfo && userInfo && userInfo.personInfo?.communicationInfo?.phones
            ? managerInfoTool(userInfo?.personInfo?.communicationInfo?.phones)
            : {item: '', itemToolTipText: ''}

    const email =
        !queueInfo && userInfo && userInfo.personInfo?.communicationInfo?.emails
            ? managerInfoTool(userInfo?.personInfo?.communicationInfo?.emails)
            : {item: '', itemToolTipText: ''}

    const address =
        organizationsInfo && organizationsInfo.details && organizationsInfo.details.organizationAddress
            ? [
                  organizationsInfo.details.organizationAddress[0].address.addressLine1,
                  `${organizationsInfo.details.organizationAddress[0].address.city},`,
                  organizationsInfo.details.organizationAddress[0].address.stateProvinceCd,
                  organizationsInfo.details.organizationAddress[0].address.postalCode
              ].join(' ')
            : ''
    return (
        <div className={ASSIGN_BANNER_MEMBER_POPOVER}>
            <div className={ASSIGN_BANNER_CONTACT_INFO_TITLE}>{labels.contactInformationLabel}</div>
            <RenderContactInfoPanel
                phone={phone.item}
                phoneDetail={phone.itemToolTipText}
                address={address}
                email={email.item}
                emailDetail={email.itemToolTipText}
            />
        </div>
    )
}
