import {claimService, IndividualCustomer, CrmLocation, backofficeCustomerCommonService} from '@eisgroup/cap-services'
import {CapAdjusterClaimSearchSearchClaimIndexEntitySearchEntityRequestV3} from '@eisgroup/cap-gateway-client'
import {formatDataFilter, getSortingParams} from '../../cases-claims-table'
import {CaseSearchResultType} from '../types'

export const searchCaseServiceCore = async (
    params: CapAdjusterClaimSearchSearchClaimIndexEntitySearchEntityRequestV3
) => {
    const casesClaimsInfo = await claimService.searchCasesClaims(params).toPromise()
    const {items = [], count = 0} = casesClaimsInfo.get()
    const subjectOfClaimLinks = [
        ...new Set(items.filter(item => item.subjects?.[0]).map(item => item.subjects[0]?.subject))
    ]
    const subjectOfClaimsMap = {}
    if (subjectOfClaimLinks.length > 0) {
        const subjectOfClaims = await backofficeCustomerCommonService()
            .searchCustomersByRegistryTypeIds(subjectOfClaimLinks)
            .then(res => res as IndividualCustomer[])
        subjectOfClaims.forEach(item => {
            const registryTypeId = item?.details?.person?.registryTypeId
            if (registryTypeId) {
                const addressInfo = item.communicationInfo.addresses?.[0]?.location || ({} as CrmLocation)
                subjectOfClaimsMap[registryTypeId] = {
                    customerName:
                        item && item._modelName === 'INDIVIDUALCUSTOMER'
                            ? [item.details.person.firstName, item.details.person.lastName].join(' ')
                            : '',
                    address: [addressInfo.addressLine1, addressInfo.city, addressInfo.stateProvinceCd].join(' '),
                    postalCode: addressInfo.postalCode
                }
            }
        })
    }

    const newItems = items.map(item => {
        return {
            ...item,
            subjectOfClaimInfo: subjectOfClaimsMap[item.subjects?.[0]?.subject]
        }
    }) as CaseSearchResultType[]

    return {
        count,
        items: newItems
    }
}

export type SearchCaseSuggestionServiceType = (
    searchValue: string
) => Promise<{count: number; items: CaseSearchResultType[]}>

export const searchCaseSuggestionsService = async (searchValue: string) => {
    const params = {
        // handle searchValue to match `*content*`
        singleLineQuery: `*${searchValue}*`,
        limit: 5,
        offset: 0
    }
    return searchCaseServiceCore(params)
}

export type SearchCaseTableServiceType = (props: {
    filters?: object
    sorter?: object
    pageNum?: number
    pageSize?: number
}) => Promise<{count: number; items: CaseSearchResultType[]}>

export const searchCaseTableService = async (props: {
    filters?: object
    sorter?: object
    pageNum?: number
    pageSize?: number
}) => {
    const {filters = {}, sorter, pageNum, pageSize} = props

    const offset = ((pageNum ?? 1) - 1) * (pageSize ?? 0)
    const limit = pageSize
    const sortingParams = getSortingParams(sorter)

    // handle filters.content to match `*content*`
    if ('content' in filters) {
        ;(filters as any).content = `*${(filters as any).content}*`
    }
    const params = {
        ...formatDataFilter(filters),
        offset,
        limit,
        sorting: sortingParams
    }
    return searchCaseServiceCore(params)
}
