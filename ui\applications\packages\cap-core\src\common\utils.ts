/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {UISchemaType} from '@eisgroup/builder'
import {CapLeaveSettlement} from '@eisgroup/cap-disability-models'
import {CapAdjusterPremiumWaiverSettlementCapSettlementReadjudicateInput} from '@eisgroup/cap-gateway-client'
import {
    AcceleratedSettlement,
    AccidentalDismembermentSettlement,
    BusinessTypes,
    CapEventCase,
    CISettlement,
    ClaimWrapper,
    DeathSettlement,
    HISettlement,
    PremiumWaiverSettlement
} from '@eisgroup/cap-event-case-models'
import {CapSpecialHandling} from '@eisgroup/cap-models'
import {
    BackOfficeLoginAuthenticationDetails,
    CapDisabilityLoss,
    CapGenericLoss,
    claimAcceleratedSettlementService,
    claimAccidentalDismembermentSettlementService,
    claimCISettlementService,
    claimDeathSettlementService,
    claimHISettlementService,
    ClaimLoss,
    claimPremiumWaiverSettlementService,
    CrmAddress,
    CustomerType,
    dateUtils,
    IndividualCustomer,
    OrganizationCustomer
} from '@eisgroup/cap-services'
import {capbasetypes} from '@eisgroup/claims-core'
import {LocalizationUtils, t} from '@eisgroup/i18n'
import {IoC} from '@eisgroup/ioc'
import {ErrorMessage, opt} from '@eisgroup/common-types'
import {PaymentMethod, PaymentMethodType} from '@eisgroup/common-business-components'
import {DatepickerInputProps} from '@eisgroup/form'
import {lookups} from '@eisgroup/lookups'
import * as MAPI from '@eisgroup/models-api'
import {Money, RootBusinessType} from '@eisgroup/models-api'
import {LookupOption} from '@eisgroup/react-components'
import {store} from '@eisgroup/state'
import {SidebarEntity} from '@eisgroup/ui-kit'
import {get, sum, uniq} from 'lodash'
import {toJS} from 'mobx'
import moment, {LongDateFormatKey} from 'moment'
import {useEffect, useState} from 'react'
import {hasAuthorities, Privileges} from '../utils/AuthoritiesUtils'
import {EntityLink} from '../utils/EntityLink'
import {ApprovalPeriodStates, ClaimTypesMap, PAYMENT_LEVEL, EVENT_TYPE_CD, SettlementModelName} from './constants'
import {ListIdxSearchParameters, LossDetailType, OptionValue, RelationshipParams} from './Types'
import AcceleratedPeriod = AcceleratedSettlement.Period
import AccidentalDismembermentPeriod = AccidentalDismembermentSettlement.Period
import CapAbsenceDetailEntity = CapEventCase.CapAbsenceDetailEntity
import CapAbsenceReasonEntity = CapEventCase.CapAbsenceReasonEntity
import CapAbsenceTypicalWorkWeekEntity = CapEventCase.CapAbsenceTypicalWorkWeekEntity
import CapAcceleratedDeathDetailEntity = CapEventCase.CapAcceleratedDeathDetailEntity
import CapAccidentDetailEntity = CapEventCase.CapAccidentDetailEntity
import CapBenefitRelatedEventEntity = CapEventCase.CapBenefitRelatedEventEntity
import CapCIDetailEntity = CapEventCase.CapCIDetailEntity
import CapClaimEventEntity = CapEventCase.CapClaimEventEntity
import CapDeathDetailEntity = CapEventCase.CapDeathDetailEntity
import CapDiagnosisInformationEntity = CapEventCase.CapDiagnosisInformationEntity
import CapEmploymentDetailEntity = CapEventCase.CapEmploymentDetailEntity
import CapEventCaseDetailEntity = CapEventCase.CapEventCaseDetailEntity
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapWellnessDetailEntity = CapEventCase.CapWellnessDetailEntity
import CapLeaveApprovalPeriodEntity = CapLeaveSettlement.CapLeaveApprovalPeriodEntity
import CapSpecialHandlingEntity = CapSpecialHandling.CapSpecialHandlingEntity
import CIPeriod = CISettlement.Period
import CapClaimWrapperEntity = ClaimWrapper.CapClaimWrapperEntity
import DeathPeriod = DeathSettlement.Period
import HIPeriod = HISettlement.Period
import locale = LocalizationUtils.locale
import localeToString = LocalizationUtils.localeToString
import CapPremiumWaiverApprovalPeriodEntity = PremiumWaiverSettlement.CapPremiumWaiverApprovalPeriodEntity
import PremiumWaiverPeriod = PremiumWaiverSettlement.Period
import CapPolicyEntity = capbasetypes.CapPolicyEntity
import PreExistingCoverableItemCondition = capbasetypes.PreExistingCoverableItemCondition
import moneyValue = LocalizationUtils.moneyValue

export type InputStateValidation = 'error' | undefined

/** use with caution when data contains Date string format */
export function deepClone(data: any): any {
    return data ? JSON.parse(JSON.stringify(data)) : data
}

export function isValidEmail(email: string): boolean {
    return /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        email
    )
}

export function isValidMoneyAmount(amount: string): boolean {
    return /^-?(0|[1-9][0-9]*)(\.[0-9]{0,2})?$/.test(amount)
}

export function moneyByLocale(amount: any): string {
    return moneyValue(amount).toString()
}

export function excludeLookupOptions(optionCodes: string[], option: LookupOption): boolean {
    return !optionCodes.includes(option.value)
}

export function findListIdxInStateObject(params: ListIdxSearchParameters): number {
    const {pathToList, pathToField, eisModel, searchedValue} = params
    if (!pathToList || !pathToField || !searchedValue) {
        return -1
    }
    const statesList = store.getValueOrDefault(pathToList, eisModel, [])
    return statesList.findIndex(v => {
        const foundValue = store.getValue(pathToField, v)
        return foundValue === searchedValue
    })
}

export function excludeLookupOptionsByFilter(
    filterName: string,
    filterOptionsToInclude: string[],
    option: any
): boolean {
    return !filterOptionsToInclude.includes(option.lookupValue.filters[filterName][0])
}

export function isNotEmpty(value?: string | number): boolean {
    return opt(value)
        .map(v => (typeof v === 'string' ? v.trim() !== '' : true))
        .orElse(false)
}

export function findAddressIdxByType(addresses: CrmAddress[], addressType: string): number {
    return addresses.findIndex(v => v.location.addressType === addressType)
}

export function filterOutListString(list: string[], valueToRemove: string): string[] {
    return list.filter(v => v !== valueToRemove)
}

export const getEarnings = (allValues: any, prefix: string) => get(allValues, `${prefix}.lossDetail.earnings`)

export const getLossEarningsValues = (allValues: any, prefix: string) => {
    const earnings = getEarnings(allValues, prefix)
    const {annualBonusAmount, annualCommissionAmount, annualSalaryAmount} = earnings
    return [annualBonusAmount, annualCommissionAmount, annualSalaryAmount]
}

export const calculateTotalEarnings = (earnings: (Money | undefined)[]): Money => {
    return toMoney(sumEarnings(earnings))
}

export const calculateMonthlyEarnings = (earnings: (Money | undefined)[]): Money => {
    return toMoney(sumEarnings(earnings) / 12)
}

export const calculateWeeklyEarnings = (earnings: (Money | undefined)[]): Money => {
    return toMoney(sumEarnings(earnings) / 52)
}

export const calculateAnnualSalary = (earnings: Money | undefined, mode: string | undefined): Money => {
    const amount: number = earnings?.amount != null && !Number.isNaN(earnings.amount) ? earnings.amount : 0
    switch (mode) {
        case 'Weekly':
            return toMoney(amount * 52)
        case 'BiWeekly':
            return toMoney(amount * 26)
        case 'SemiMonthly':
            return toMoney(amount * 24)
        case 'Monthly':
            return toMoney(amount * 12)
        default:
            return toMoney(amount)
    }
}

export const sumEarnings = (earnings: (Money | undefined)[]): number => {
    return sum(earnings.map(v => (v?.amount ? Number(v.amount) : 0)))
}

export const toMoney = (amount: number): Money => {
    return {amount, currency: LocalizationUtils.getCurrency().code}
}

export const lookupContextValue = (lookupName: string) => {
    return IoC.get<lookups.LookupAccessFacade>(lookups.TYPES.LookupAccessFacade)
        .getAvailableValues({lookupName})
        .map(either =>
            either.fold(
                error => error,
                values => values as any
            )
        )
        .toPromise()
}

export function getDateFormatByLocale(dateFormat: LongDateFormatKey = 'L'): string {
    return moment.localeData(localeToString(locale())).longDateFormat(dateFormat)
}

export const getDateFormattingByLocale = (dateFormat: LongDateFormatKey = 'L'): Partial<DatepickerInputProps> => {
    const format = getDateFormatByLocale(dateFormat)
    return {
        formatDate: format,
        placeholder: format
    }
}

export const localizeBuilderForm = (formConfig: UISchemaType): UISchemaType => {
    return JSON.parse(
        JSON.stringify(formConfig)
            .replace(/"formatDate":"L"/g, `"formatDate":"${getDateFormatByLocale()}"`)
            .replace(/"placeholder":"MM\/DD\/YYYY"/g, `"placeholder":"${getDateFormatByLocale()}"`)
    )
}

export const replaceComponentTitles = (formConfig: UISchemaType, oldTitles: {}, newTitles: {}): UISchemaType => {
    let modifiedFormConfig = JSON.stringify(formConfig)
    Object.keys(oldTitles).forEach(key => {
        modifiedFormConfig = modifiedFormConfig.replace(oldTitles[key], newTitles[key])
    })
    return JSON.parse(modifiedFormConfig)
}
/**
 * @param authoritiesArr
 * @param allPrivilegeFlag
 * if allPrivilegeFlag === true , it will return authoritiesArr related privilegeMap
 * if allPrivilegeFlag === false, it will return authoritiesArr related true value privilegeMap
 */
export const loadStorage = (authoritiesArr: string[] = [], allPrivilegeFlag = false) => {
    const genesisSsoSessionToken = localStorage.getItem('genesis-sso-session-token')
    const authoritiesResult = {}
    if (genesisSsoSessionToken) {
        const authoritiesMap = opt(
            (JSON.parse(genesisSsoSessionToken)?.userData as BackOfficeLoginAuthenticationDetails)?.authorities
        ).orElse([])
        authoritiesArr.forEach(item => {
            if (allPrivilegeFlag || authoritiesMap[item]) {
                authoritiesResult[item] = authoritiesMap[item]
            }
        })
    }
    return authoritiesResult
}

export const getSelectedLossesByClaimEvent = (
    selectedClaimEvent: string,
    lossesRelativeToCase: CapDisabilityLoss[]
): CapDisabilityLoss => {
    let selectedLoss: CapDisabilityLoss = {} as any as CapDisabilityLoss
    if (selectedClaimEvent && lossesRelativeToCase && lossesRelativeToCase.length > 0) {
        const losses = lossesRelativeToCase.filter(loss => selectedClaimEvent === loss.lossNumber)
        if (losses && losses.length) {
            selectedLoss = losses[0]
        }
    }
    return selectedLoss
}

export const validateUsedClaimEvent = (
    selectedClaimEvent: string,
    lossesRelativeToCase: CapDisabilityLoss[],
    ClaimInfo: CapClaimWrapperEntity
): boolean => {
    let isUsed = false
    if (selectedClaimEvent) {
        // match the selected claim event to the losses that are relative to event case
        const selectedLoss = getSelectedLossesByClaimEvent(selectedClaimEvent, lossesRelativeToCase)
        // get the rootIds from the damageLoss of Claim info
        const existedDamageLosses = ClaimInfo.damageLosses ?? []
        const rootIdsFromExistedDamageLosses: string[] = []
        if (existedDamageLosses && existedDamageLosses.length > 0) {
            existedDamageLosses.forEach(loss => {
                if (loss.lossSource) {
                    rootIdsFromExistedDamageLosses.push(EntityLink.from(loss.lossSource._uri).rootId)
                }
            })
        }
        if (rootIdsFromExistedDamageLosses.length > 0) {
            const filteredData = rootIdsFromExistedDamageLosses.filter(rootId => selectedLoss._key?.rootId === rootId)
            if (filteredData && filteredData.length > 0) {
                isUsed = true
            }
        }
    }
    return isUsed
}

export const getSpecialHandlingKeys = (
    specialHandling: CapSpecialHandlingEntity,
    specialHandlingTypes: Object
): string[] => {
    return Object.keys(specialHandling).filter(v => Object.values(specialHandlingTypes).indexOf(v) !== -1)
}

export function daysToPeriod(days = 0, periodDaysAmount = 7, useLongFormat?: boolean): string {
    const period = Math.trunc(days / periodDaysAmount)
    const day = Math.round((days % periodDaysAmount) * 100) / 100
    const isMonth = periodDaysAmount > 7
    let periodUnit = ''
    if (useLongFormat) {
        periodUnit = isMonth ? 'cap-core:time_unit_months' : 'cap-core:time_unit_weeks'
    } else {
        periodUnit = isMonth ? 'cap-core:time_unit_month_short' : 'cap-core:time_unit_week_short'
    }
    const periodString = String(period) + t(periodUnit)
    const dayString = String(day) + t(useLongFormat ? 'cap-core:time_unit_days' : 'cap-core:time_unit_day_short')
    if (period) {
        return day ? `${periodString} ${dayString}` : periodString
    }
    return dayString
}

export function isValidValue(value?: any): boolean {
    return !(value === null || typeof value === 'undefined')
}

export function checkValidity(...args: any[]): boolean {
    for (const arg of args) {
        if (isValidValue(arg)) {
            return false
        }
    }
    return true
}

export const hasPrivilege = (): boolean => {
    const hasAssignUserPrivilege = hasAuthorities([Privileges.CASE_ASSIGN_CASE_TO_USER])
    const hasAssignQueuePrivilege = hasAuthorities([Privileges.CASE_ASSIGN_CASE_TO_QUEUE])

    return hasAssignUserPrivilege || hasAssignQueuePrivilege
}

export const useQuery = <T>(
    request: () => Promise<T>
): {
    loading: boolean
    data: T | undefined
    error: Error | undefined
} => {
    const [loading, setLoading] = useState(false)
    const [data, setData] = useState<T | undefined>()
    const [error, setError] = useState<Error | undefined>()

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true)
                setData(undefined)
                setError(undefined)
                const response = await request()
                setData(response)
            } catch (err) {
                setError(err as Error)
                console.error(err)
            } finally {
                setLoading(false)
            }
        }
        fetchData()
    }, [request])

    return {
        loading,
        data,
        error
    }
}

export function getCompletedOrApprovedDays(
    status: ApprovalPeriodStates,
    approvalPeriodDetails?: CapPremiumWaiverApprovalPeriodEntity[] | CapLeaveApprovalPeriodEntity[]
): number {
    const approvedPeriods = approvalPeriodDetails ? approvalPeriodDetails.filter(v => v.approvalStatus === status) : []
    return approvedPeriods
        .map(v => {
            const startDateString = dateUtils(v.approvalPeriod!.startDate).format('YYYY-MM-DD')
            const endDateString = dateUtils(v.approvalPeriod!.endDate).format('YYYY-MM-DD')
            return moment(moment(endDateString).add(1, 'day').format('YYYY-MM-DD')).diff(startDateString, 'days')
        })
        .reduce((pre, curr) => pre + curr, 0)
}

export function formatBenefitDuration(duration: string): string {
    if (!duration) {
        return '0m'
    }
    let result = duration.slice(1, -1)
    result = parseInt(result, 10).toString()
    return `${result}m`
}

// Merged from event-case
const getLossDetail = (eventCase: CapEventCaseEntity) => {
    return eventCase.lossDetail
        ? eventCase.lossDetail
        : CapEventCase.factory.newByType<CapEventCaseDetailEntity>(CapEventCaseDetailEntity)
}

const getClaimEvent = (eventCase: CapEventCaseEntity) => {
    return eventCase.lossDetail?.claimEvent
        ? eventCase.lossDetail.claimEvent
        : CapEventCase.factory.newByType<CapClaimEventEntity>(CapClaimEventEntity)
}

const getDeathDetail = (theLossDetail: CapEventCaseDetailEntity) => {
    return theLossDetail?.claimEvent?.deathDetail === undefined
        ? CapEventCase.factory.newByType<CapDeathDetailEntity>(CapDeathDetailEntity)
        : theLossDetail?.claimEvent?.deathDetail
}

const getCIDetail = (theLossDetail: CapEventCaseDetailEntity) => {
    return theLossDetail?.claimEvent?.ciDetail === undefined
        ? CapEventCase.factory.newByType<CapCIDetailEntity>(CapCIDetailEntity)
        : theLossDetail?.claimEvent?.ciDetail
}

const getAcceleratedDeathDetail = (theLossDetail: CapEventCaseDetailEntity) => {
    return theLossDetail?.claimEvent?.acceleratedDeathDetail === undefined
        ? CapEventCase.factory.newByType<CapAcceleratedDeathDetailEntity>(CapAcceleratedDeathDetailEntity)
        : theLossDetail?.claimEvent?.acceleratedDeathDetail
}

const getAbsenceDetail = (theLossDetail: CapEventCaseDetailEntity) => {
    return theLossDetail?.claimEvent && theLossDetail?.claimEvent?.absenceDetail === undefined
        ? {
              ...CapEventCase.factory.newByType<CapAbsenceDetailEntity>(CapAbsenceDetailEntity),
              typicalWorkWeek: {
                  ...CapEventCase.factory.newByType<CapAbsenceTypicalWorkWeekEntity>(CapAbsenceTypicalWorkWeekEntity),
                  hoursFri: 8,
                  hoursMon: 8,
                  hoursSat: 0,
                  hoursSun: 0,
                  hoursThu: 8,
                  hoursTue: 8,
                  hoursWed: 8
              },
              absenceReason: {
                  ...CapEventCase.factory.newByType(CapAbsenceReasonEntity)
              },
              absencePeriods: []
          }
        : {...theLossDetail?.claimEvent?.absenceDetail}
}

const getAccidentDetail = (theLossDetail: CapEventCaseDetailEntity) => {
    return theLossDetail?.claimEvent?.accidentDetail === undefined
        ? CapEventCase.factory.newByType<CapAccidentDetailEntity>(CapAccidentDetailEntity)
        : theLossDetail?.claimEvent?.accidentDetail
}

const getWellnessDetail = (theLossDetail: CapEventCaseDetailEntity) => {
    return theLossDetail?.claimEvent?.wellnessDetail === undefined
        ? CapEventCase.factory.newByType<CapWellnessDetailEntity>(CapWellnessDetailEntity)
        : theLossDetail?.claimEvent?.wellnessDetail
}

const getDataOrUndefined = (data: LossDetailType, flag?: boolean) => {
    return flag ? {...data} : undefined
}

const getDiagnoses = (lossDetail: CapEventCaseDetailEntity): CapDiagnosisInformationEntity[] => {
    const claimEvents = lossDetail?.claimEvent?.claimEvents || []
    const {diagnoses} = lossDetail
    const wellnessExist = claimEvents.includes('Wellness')
    const notOMC = !lossDetail?.claimEvent?.absenceDetail?.absenceReason?.absenceReasons?.includes('OMC')
    if (claimEvents.length === 0) {
        return []
    }

    if (wellnessExist) {
        const defaultedDiagnosis: CapDiagnosisInformationEntity = {
            ...CapEventCase.factory.newByType<CapDiagnosisInformationEntity>(CapDiagnosisInformationEntity),
            primaryCode: true,
            icdCode: 'Z00.00',
            date: dateUtils(lossDetail.claimEvent?.wellnessDetail?.incidentDate).toDate
        }
        if (claimEvents.length === 1) {
            return [defaultedDiagnosis]
        }
        if (claimEvents.length === 2 && claimEvents.includes('Absence') && notOMC) {
            return [defaultedDiagnosis]
        }
        // reset previously defaulted value if more types selected
        return [...diagnoses.filter(diagnosis => diagnosis.icdCode !== 'Z00.00')]
    }
    return diagnoses
}

export const convertEventCaseByClaimEvents = (
    eventCase: CapEventCaseEntity,
    claimEvents: string[] | undefined
): CapEventCaseEntity => {
    const deathExist = eventCase.lossDetail?.claimEvent?.claimEvents?.includes('Death')
    const acceleratedDeathExist = eventCase.lossDetail?.claimEvent?.claimEvents?.includes('AcceleratedDeath')
    const absenceExist = eventCase.lossDetail?.claimEvent?.claimEvents?.includes('Absence')
    const accidentExist = eventCase.lossDetail?.claimEvent?.claimEvents?.includes('Accident')
    const wellnessExist = eventCase.lossDetail?.claimEvent?.claimEvents?.includes('Wellness')
    const ciExist = eventCase.lossDetail?.claimEvent?.claimEvents?.includes('CriticalIllness')

    const theLossDetail = getLossDetail(eventCase)
    const claimEvent = getClaimEvent(eventCase)
    const deathDetail = getDeathDetail(theLossDetail)
    const acceleratedDeathDetail = getAcceleratedDeathDetail(theLossDetail)
    const absenceDetail = getAbsenceDetail(theLossDetail) as CapAbsenceDetailEntity
    const accidentDetail = getAccidentDetail(theLossDetail)
    const wellnessDetail = getWellnessDetail(theLossDetail)
    const ciDetail = getCIDetail(theLossDetail)
    const events = clearEventDate(theLossDetail.events, e => !absenceExist && e.eventTypeCd === EVENT_TYPE_CD.DLW)

    const result = {
        ...eventCase,
        lossDetail: {
            ...theLossDetail,
            events,
            claimEvent: {
                ...claimEvent,
                deathDetail: getDataOrUndefined(deathDetail, deathExist),
                acceleratedDeathDetail: getDataOrUndefined(acceleratedDeathDetail, acceleratedDeathExist),
                absenceDetail: getDataOrUndefined(absenceDetail, absenceExist),
                accidentDetail: getDataOrUndefined(accidentDetail, accidentExist),
                wellnessDetail: getDataOrUndefined(wellnessDetail, wellnessExist),
                ciDetail: getDataOrUndefined(ciDetail, ciExist),
                claimEvents
            },
            diagnoses: getDiagnoses(toJS(theLossDetail))
        }
    }
    return result as CapEventCaseEntity
}

export const decimalsMultiply = (a: number, b: number): number => {
    const aStr = a.toString()
    const bStr = b.toString()
    const aInteger = Number(aStr.replace('.', ''))
    const bInteger = Number(bStr.replace('.', ''))
    const aNum = aStr.split('.')[1]?.length || 0
    const bNum = bStr.split('.')[1]?.length || 0
    return (aInteger * bInteger) / (10 ** aNum * 10 ** bNum)
}

export const isAbsence = (claimType: string) => {
    return [ClaimTypesMap.STD, ClaimTypesMap.SMP, ClaimTypesMap.LTD, ClaimTypesMap.Leave].includes(claimType)
}

export const isAbsenceNotLeave = (claimType: string) => {
    return [ClaimTypesMap.STD, ClaimTypesMap.SMP, ClaimTypesMap.LTD].includes(claimType)
}

export const isInactiveDateRange = (startDate?: Date, endDate?: Date) =>
    (startDate && dateUtils(startDate).toMoment.isAfter(moment(), 'day')) ||
    (endDate && dateUtils(endDate).toMoment.isBefore(moment(), 'day'))

export const isInactiveDateRangeNotIncludeEnd = (startDate?: Date, endDate?: Date) =>
    ((startDate && dateUtils(startDate).toMoment.isAfter(moment())) ||
        (endDate && dateUtils(endDate).toMoment.isBefore(moment()))) &&
    !moment(startDate).isSame(moment(), 'date')

export const fromEntityLink = (entity: RootBusinessType & {_modelType: string}) => {
    return `gentity://${entity._modelType}/${entity._modelName}//${entity._key.rootId}/${entity._key.revisionNo}`
}

export const createLossLink = (entity: RootBusinessType) => {
    return `gentity://CapLoss/${entity._modelName}//${entity._key.rootId}/${entity._key.revisionNo}`
}

export const isPaymentLevelClaim = (eventCase: CapEventCaseEntity): boolean => {
    return eventCase?.applicabilityResult?.paymentLevelCd === PAYMENT_LEVEL.Claim
}

export const isPaymentLevelEventCase = (eventCase: CapEventCaseEntity): boolean => {
    return eventCase?.applicabilityResult?.paymentLevelCd === PAYMENT_LEVEL.EventCase
}

export const getCloseClaimErrorMsg = (e: ErrorMessage): string => {
    let errorMsg = ''
    if (e.errors && e.errors.length) {
        errorMsg = e.errors?.[0]?.message
        if (e.errors?.[0]?.code === 'cllc001' || e.errors?.[0]?.code === 'clch001') {
            errorMsg = 'cap-core:manual_close_hard_stop_alert'
        }
        if (e.errors?.[0]?.code === 'cllc002' || e.errors?.[0]?.code === 'clch002') {
            errorMsg = 'cap-core:manual_close_existing_issued_payment_alert'
        }
    }
    return errorMsg
}

export const getLossTypesByClaims = (
    losses: ClaimLoss[],
    otherClaims: CapGenericLoss[],
    lossSources: MAPI.ExternalLink[]
): string | undefined => {
    const allLosses: string[][] = []

    if (losses?.length > 0 && lossSources) {
        allLosses.push(
            losses
                .filter(loss =>
                    lossSources
                        .filter(lossSource => EntityLink.from(lossSource._uri).rootId === loss._key.rootId)
                        .map(lossSource => lossSource._uri)
                        .join('')
                )
                .map(loss => `${loss.lossNumber}`)
        )
    }

    if (otherClaims?.length > 0 && lossSources) {
        allLosses.push(
            otherClaims
                .filter(loss =>
                    lossSources
                        .filter(lossSource => EntityLink.from(lossSource._uri).rootId === loss._key!.rootId)
                        .map(lossSource => lossSource._uri)
                        .join('')
                )
                .map(loss => `${loss.lossNumber}`)
        )
    }

    const result = uniq(allLosses.flatMap(loss => loss))

    return result.length > 0 ? result.join(', ') : undefined
}

export const getOptionsByClaims = (losses?: ClaimLoss[], otherClaims?: CapGenericLoss[]): OptionValue[] => {
    const options = [] as OptionValue[]

    losses!.forEach(loss =>
        options.push({
            code: `gentity://CapLoss/${loss._modelName}//${loss._key.rootId}/${loss._key.revisionNo}`,
            displayValue: `${loss.lossNumber}`
        })
    )

    if (otherClaims) {
        otherClaims.forEach(loss =>
            options.push({
                code: `gentity://CapLoss/${loss._modelName}//${loss._key!.rootId}/${loss._key!.revisionNo}`,
                displayValue: `${loss.lossNumber}`
            })
        )
    }

    return options
}

const clearEventDate = (
    events: CapBenefitRelatedEventEntity[],
    eventDateClearCondition: (event: CapBenefitRelatedEventEntity) => boolean
): CapBenefitRelatedEventEntity[] => {
    return events.map(e => {
        if (eventDateClearCondition(e)) {
            return {...e, eventDate: undefined}
        }
        return e
    })
}

export const setDateRange = (range: string[], term: BusinessTypes.Term) => {
    return range.length
        ? {
              ...term,
              startDate: range?.[0] ? range[0] : undefined,
              endDate: range[1] ? range[1] : undefined
          }
        : null
}

export const setInitSettlementRequestBody = (
    coverage: any
): CapAdjusterPremiumWaiverSettlementCapSettlementReadjudicateInput => {
    return {
        _updateStrategy: 'REPLACE',
        claimLossIdentification: coverage.claimLossIdentification,
        entity: {
            ...coverage.settlementDetail,
            _timestamp: coverage._timestamp
        },
        _key: coverage._key
    }
}

export const setServiceLoadApi = (coverage: any) => {
    let serviceLoadApi
    switch (coverage._modelName) {
        case SettlementModelName.PREMIUMWAIVER_SETTLEMENT:
            serviceLoadApi = claimPremiumWaiverSettlementService
            break
        case SettlementModelName.DEATH_SETTLEMENT:
            serviceLoadApi = claimDeathSettlementService
            break
        case SettlementModelName.ACCELERATED_SETTLEMENT:
            serviceLoadApi = claimAcceleratedSettlementService
            break
        case SettlementModelName.ACCIDENTALDISMEMBERMENT_SETTLEMENT:
            serviceLoadApi = claimAccidentalDismembermentSettlementService
            break
        case SettlementModelName.CI_SETTLEMENT:
            serviceLoadApi = claimCISettlementService
            break
        case SettlementModelName.HI_SETTLEMENT:
            serviceLoadApi = claimHISettlementService
            break
    }
    return serviceLoadApi
}

export const newPeriodBySeveralType = (settlementType: string): any => {
    switch (settlementType) {
        case 'CapPremiumWaiverSettlementEntity':
            return PremiumWaiverSettlement.factory.newByType<PremiumWaiverPeriod>(PremiumWaiverPeriod)
        case 'CapCISettlementEntity':
            return CISettlement.factory.newByType<CIPeriod>(CIPeriod)
        case 'CapDeathSettlementEntity':
            return DeathSettlement.factory.newByType<DeathPeriod>(DeathPeriod)
        case 'CapAcceleratedSettlementEntity':
            return AcceleratedSettlement.factory.newByType<AcceleratedPeriod>(AcceleratedPeriod)
        case 'CapAccidentalDismembermentSettlementEntity':
            return AccidentalDismembermentSettlement.factory.newByType<AccidentalDismembermentPeriod>(
                AccidentalDismembermentPeriod
            )
        case 'CapHISettlementEntity':
            return HISettlement.factory.newByType<HIPeriod>(HIPeriod)
        default:
            return undefined
    }
}

export const generateLossToSaveRequest = (
    eventCase: CapEventCaseEntity,
    employee: IndividualCustomer
): CapEventCaseEntity => {
    return {
        ...eventCase,
        lossDetail: {
            ...eventCase.lossDetail
        },
        memberRegistryTypeId: employee.details.person.registryTypeId
    } as CapEventCaseEntity
}

export const getAbsenceParties = eventCase => {
    const {
        bondings = [],
        careForFamilyMembers = [],
        military = []
    } = eventCase?.lossDetail?.claimEvent?.absenceDetail?.absenceReason || {}
    const bondingRegistryIds = bondings.map(v => v.registryTypeId)
    const careForFamilyMembersRegistryIds = careForFamilyMembers.map(v => v.registryTypeIds).flat()
    const militaryRegistryIds = military.map(v => v.registryTypeId)
    const absencePartyRegistryIds = [
        ...new Set([...bondingRegistryIds, ...careForFamilyMembersRegistryIds, ...militaryRegistryIds])
    ]
    return eventCase?.lossDetail?.additionalRole.filter(v => absencePartyRegistryIds.includes(v.registryId))
}

export const groupAddOrUpdateRelationship = (orgEventCase, eventCase, relationships) => {
    const memberRegistryId = eventCase?.memberRegistryTypeId
    const orgAbsenceParties = getAbsenceParties(orgEventCase)
    const absenceParties = getAbsenceParties(eventCase)
    const needAddRelationshipParams = [] as RelationshipParams[]
    const needUpdateRelationshipParams = [] as RelationshipParams[]
    absenceParties.forEach(absenceParty => {
        const orgExist = orgAbsenceParties.find(v => v.registryId)
        const relationshipRequest = {
            fromUri: memberRegistryId,
            toUri: absenceParty.registryId,
            relationshipToInsuredCd: absenceParty.relationshipToInsuredCd
        }
        if (!orgExist) {
            needAddRelationshipParams.push(relationshipRequest)
        }
        if (orgExist && orgExist.relationshipToInsuredCd !== absenceParty.relationshipToInsuredCd) {
            const curOrgRelationship = relationships.find(
                v =>
                    v.relationship?.from._uri === memberRegistryId &&
                    v.relationship?.to._uri === absenceParty.registryId &&
                    v.relationship?.relationshipRoleToCd === orgExist.relationshipToInsuredCd
            )
            if (curOrgRelationship) {
                needUpdateRelationshipParams.push({
                    ...relationshipRequest,
                    _key: curOrgRelationship.relationship?._key,
                    _timestamp: curOrgRelationship.relationship?._timestamp
                })
            } else {
                needAddRelationshipParams.push(relationshipRequest)
            }
        }
    })
    return {needAddRelationshipParams, needUpdateRelationshipParams}
}

export function createInitialEventCase(): any {
    return {
        ...CapEventCase.factory.newByType<CapEventCaseEntity>(CapEventCaseEntity),
        memberRegistryTypeId: ' ',
        lossDetail: {
            ...CapEventCase.factory.newByType<CapEventCaseDetailEntity>(CapEventCaseDetailEntity),
            claimEvent: {
                ...CapEventCase.factory.newByType<CapClaimEventEntity>(CapClaimEventEntity)
            },
            employmentDetail: {
                ...CapEventCase.factory.newByType<CapEmploymentDetailEntity>(CapEmploymentDetailEntity),
                workStateCodeCd: undefined
            }
        }
    }
}

export const extractValuesBetweenAngleBrackets = (message?: string): string[] => {
    const result = [] as string[]
    if (!message) {
        return result
    }
    for (let i = 0; i < message.length; i++) {
        if (message.charAt(i) === '<') {
            const endIdx = message.indexOf('>', i)
            if (endIdx !== -1) {
                result.push(message.substring(i + 1, endIdx))
            }
        }
    }
    return result
}

export const containsOrganization = (beneficiaryParty: string) => {
    return beneficiaryParty?.toLowerCase()?.includes(CustomerType.Organization.toLowerCase())
}

export const getCustomerName = (customer: IndividualCustomer | OrganizationCustomer) => {
    return opt(customer as IndividualCustomer)
        .map(v => v.details)
        .map(v => v.person)
        .map(v => [v.firstName, v.lastName].join(' '))
        .orElse(
            opt(customer as OrganizationCustomer)
                .map(v => v.details)
                .map(v => v.legalEntity)
                .map(v => v.legalName)
                .orElse('-')
        )
}

export const getAvailablePaymentMethodTypes = (paymentMethods: PaymentMethod[], excludeCheck?: boolean) => {
    const availablePaymentMethodTypes = [PaymentMethodType.EFT]
    const activeCheckPaymentMethods: PaymentMethod[] = []
    const now = moment()
    paymentMethods.forEach(paymentMethod => {
        if (
            paymentMethod._type === PaymentMethodType.CHECK &&
            now.isBetween(
                dateUtils(paymentMethod.effectiveDate).toMoment || undefined,
                dateUtils(paymentMethod.expirationDate).toMoment || undefined,
                'days',
                '[]'
            )
        ) {
            activeCheckPaymentMethods.push(paymentMethod)
        }
    })
    if (activeCheckPaymentMethods.length === 0 && !excludeCheck) {
        availablePaymentMethodTypes.push(PaymentMethodType.CHECK)
    }
    return availablePaymentMethodTypes
}

export const filterWorkbenchSetEntities = (entities: SidebarEntity[]) => {
    return entities.filter(entity => ['CapLoss', 'Customer'].includes(entity.type))
}

/**
 * Finds the specific PreExistingCoverableItemCondition based on plan and coverage codes.
 * If multiple items are found across policy model attributes, uses the first one.
 */
export const loadPreExistingCondition = (
    policyModel: CapPolicyEntity | undefined,
    planCd: string | undefined,
    coverageCd: string | undefined
): PreExistingCoverableItemCondition | undefined => {
    if (!policyModel?.coverableItems || !planCd || !coverageCd) {
        return undefined
    }
    const matchingPlanItems = policyModel.coverableItems?.filter(item => item.type === 'plan' && item.code === planCd)

    if (!matchingPlanItems || matchingPlanItems.length === 0) {
        return undefined
    }

    const matchingCoverageItems = matchingPlanItems[0].compositeCoverableItemCondition?.items?.filter(
        item => item.type === 'coverage' && item.code === coverageCd
    )

    if (!matchingCoverageItems || matchingCoverageItems.length === 0) {
        return undefined
    }

    return matchingCoverageItems[0].preExistingCoverableItemCondition
}

/**
 * Determines if pre-existing condition (Pre-Ex) is applied to policy.
 */
export const isPreExApplied = (
    policyModel: CapPolicyEntity | undefined,
    planCd: string | undefined,
    coverageCd: string | undefined
): boolean => {
    const preExistingCoverageItem = loadPreExistingCondition(policyModel, planCd, coverageCd)

    return preExistingCoverageItem?.isApplied ?? false
}

export function tryTranslateValue(value: string): string {
    const minimumWordLength = 2

    if (value.length <= minimumWordLength) {
        return value
    }
    // refer to：https://wiki.eisgroup.com/display/GRC/%5BQA%5D+How+to+test+UI+localization
    // ToDo Consider removing this logic, as amount values should not have a locale code attached to them.
    if (value.includes('US$')) {
        return `MT+${value}`
    }
    const keyFromValue = value.startsWith('history_table')
        ? value
        : value
              .replace(/\.$/, '')
              .replace(/([a-z])([A-Z])/g, '$1_$2')
              .replace(/[\s().,'’"&%-/]+/g, '_')
              .replace(/_$/, '')
              .toLowerCase()

    const translation = t(`cap-core:${keyFromValue}`)
    if (translation.startsWith('cap-core')) {
        // creating console output for missing keys to copy into i18n file
        console.debug(`'${keyFromValue}':'${value}',`)
        return value.startsWith('history_table')
            ? value.slice('history_table_unlabeled_entity_'.length).replace('_', ' ')
            : value
    }
    return translation
}
