/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useEffect, useState} from 'react'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import {Checkbox, Form} from '@eisgroup/ui-kit'
import {LocalizationUtils, t} from '@eisgroup/i18n'
import {useForm, Field, OnChange} from '@eisgroup/form'
import {CapBalance, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {multipleFieldRequired} from '../../utils/ValidationUtils'
import {PREFIX, PAYMENT_APPLIED_WITHHOLDINGS} from '../../common/package-class-names'
import {OptionValue} from '../../common/Types'
import CapPaymentWithholdingAdditionEntity = CapBalance.CapPaymentWithholdingAdditionEntity
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity

const {Select} = Field
const LOOKUP_DROPDOWN_CHECKBOX = `${PREFIX}-lookup-dropdown-checkbox`
const LOOKUP_DROPDOWN_CHECKBOX_LABEL = `${PREFIX}-lookup-dropdown-checkbox-label`

export interface PaymentAppliedWithholdingsProps {
    issuedPaymentsWhichContainsWithholdings: CapPaymentEntity[]
}

export const PaymentAppliedWithholdings: React.FC<PaymentAppliedWithholdingsProps> = observer(props => {
    const form = useForm()
    const [selected, setSelected] = useState<string[]>([])

    const {isCompensateWithholdingsSelected} = form.getState().values

    useEffect(() => {
        form.change('selectedPayments', [])
        setSelected([])
    }, [isCompensateWithholdingsSelected])

    const getOptions = () => {
        const optionList = [] as OptionValue[]
        props.issuedPaymentsWhichContainsWithholdings.forEach(v => {
            optionList.push({
                code: v._key?.id || '',
                displayValue: v.paymentNumber
            })
        })
        return optionList
    }

    const onSelect = (name, val) => {
        if (name !== 'selectedPayments') {
            return
        }
        setSelected(val || [])
        if (!val) {
            form.change('withholdingsAmount', {
                amount: 0,
                currency: LocalizationUtils.getCurrency().code
            })
            return
        }
        const selectedPayments: CapPaymentEntity[] = []
        let withholdingList: CapPaymentWithholdingAdditionEntity[] = []
        props.issuedPaymentsWhichContainsWithholdings.forEach(v => {
            if (val.findIndex(s => s === v._key?.id) > -1) {
                selectedPayments.push(v)
            }
        })
        selectedPayments.forEach(v => {
            withholdingList = withholdingList.concat(toJS(v.withholdingDetails?.withholdingAllocations) || [])
        })
        const withholdingSum = withholdingList.reduce((sum, curr) => {
            const amount: number = curr.allocationNetAmount.amount || 0
            return sum + amount
        }, 0)

        form.change('selectedPayments', val)
        form.change('withholdingsAmount', {
            amount: withholdingSum,
            currency: LocalizationUtils.getCurrency().code
        })
    }

    return (
        <Form.Item label={t('cap-core:payment_applied_withholdings')} className={PAYMENT_APPLIED_WITHHOLDINGS} required>
            <Select
                className={LOOKUP_DROPDOWN_CHECKBOX}
                name='selectedPayments'
                value={selected}
                required
                mode='multiple'
                menuItemSelectedIcon={() => <span />}
                options={getOptions()}
                optionRender={opt => (
                    <Checkbox
                        value={opt.value.code}
                        checked={selected.some(v => v === opt.value.code)}
                        className={LOOKUP_DROPDOWN_CHECKBOX_LABEL}
                        data-testid='checkbox'
                    >
                        {opt.value.displayValue}
                    </Checkbox>
                )}
                validate={multipleFieldRequired('cap-core:payment_applied_withholdings_is_required')}
                data-testid='select-input'
            />
            <OnChange>{(name: string, value: any) => onSelect(name, value)}</OnChange>
        </Form.Item>
    )
})
