import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 12,
            "name": "payeeDisplay",
            "label": "cap-core:payee",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "payeeDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "fa7ef7eb-f2a0-4e6b-9bd7-4fa715e1fc2e"
        }
      ],
      "id": "71872e2c-1615-41c0-ba08-d148a85f3774"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "CHECKBOX",
          "props": {
            "md": 12,
            "children": "cap-core:compensate_withholdings",
            "name": "isCompensateWithholdingsSelected",
            "initialValue": true
          },
          "id": "f2aad0a0-31ef-4108-9d53-dc4a8e790f30"
        }
      ],
      "id": "ea0e50e3-1846-4ec5-9487-9623dd3ec36f"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 12,
            "name": "amount",
            "allowDecimal": true,
            "required": true,
            "label": "cap-core:balance_actions_drawer_amount",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Amount"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "value-for-underpayment-amount",
                  "fieldValue": {
                    "type": "field",
                    "value": "~totalBalanceAmount"
                  }
                }
              ]
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isCompensateWithholdingsSelected"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": false
                      }
                    }
                  ]
                }
              }
            },
            "prohibitNegative": false
          },
          "id": "234eebd4-1b06-4646-b6f6-b42677c48f5d"
        }
      ],
      "id": "60d968b6-5698-4195-877f-7cf4c540c503"
    },
    {
      "type": "COMPONENT_SLOT",
      "props": {
        "md": 24,
        "span": 24,
        "slotId": "PAYMENT_APPLIED_WITHHOLDINGS",
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "isCompensateWithholdingsSelected"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": true
                  }
                }
              ]
            }
          }
        }
      },
      "components": [],
      "id": "363c5616-cba2-49ec-9f33-a81359423164"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 12,
            "name": "withholdingsAmount",
            "allowDecimal": true,
            "required": true,
            "label": "cap-core:balance_actions_drawer_amount",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Amount"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "amount-valid",
                  "fieldValue": {
                    "type": "field",
                    "value": "~totalBalance"
                  }
                }
              ]
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isCompensateWithholdingsSelected"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": true
                      }
                    }
                  ]
                }
              },
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isCompensateWithholdingsSelected"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": true
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "e9a73f82-b189-4fc2-a84b-65bf5279f9a9"
        }
      ],
      "id": "355d6838-95d8-4006-a2c0-48f7ea0c57bd"
    }
  ],
  "version": 89
}

export default config;
