/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapBalance, CapBalanceChangeLog, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {RxResult} from '@eisgroup/common-types'
import {
    CapAdjusterPaymentDefinitionCapGenerateOverpaymentWaiveRequest,
    CapAdjusterPaymentDefinitionCapUnderpaymentGenerationRequest,
    CapAdjusterPaymentDefinitionCapGenerateExternalBalanceRequest,
    CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery,
    CapAdjusterPaymentDefinitionCapWithholdingUnderpaymentGenerationRequest,
    CapAdjusterPaymentDefinitionPaymentDefinitionOverpaymentWaiveApprovalInput,
    CapAdjusterPaymentDefinitionCapRefExternalBalanceCancelInput,
    CapAdjusterPaymentDefinitionCapRefOverpaymentWaiveCancelInput
} from '@eisgroup/cap-gateway-client'
import {BaseRootStore, EventCaseStore} from '.'

import CapBalanceChangeLogEntity = CapBalanceChangeLog.CapBalanceChangeLogEntity
import CapBalanceEntity = CapBalance.CapBalanceEntity
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity
import PaymentApprovalResult = PaymentDefinition.PaymentApprovalResult

export interface BalanceStore extends BaseRootStore {
    balanceChangeLogCount: number
    eventCaseStore: EventCaseStore
    balanceSource: string
    balance: CapBalanceEntity
    balanceChangeLog: CapBalanceChangeLogEntity[]
    loadBalance: (payeeLink: string) => void
    getBalanceChangeLog: (
        params: CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery,
        nextCount?: number
    ) => void
    loadSettlementByType: (rootId: string, revisionNo: string, modelName: string) => any
    waiveOverpayment: (
        params: CapAdjusterPaymentDefinitionCapGenerateOverpaymentWaiveRequest
    ) => RxResult<CapPaymentEntity>
    payUnderpayment: (
        params: CapAdjusterPaymentDefinitionCapUnderpaymentGenerationRequest
    ) => RxResult<CapPaymentEntity>
    addExternalOverpayment: (
        params: CapAdjusterPaymentDefinitionCapGenerateExternalBalanceRequest
    ) => RxResult<CapPaymentEntity>
    generateWithholdingUnderpayment: (
        params: CapAdjusterPaymentDefinitionCapWithholdingUnderpaymentGenerationRequest
    ) => RxResult<CapPaymentEntity>
    validateOverpaymentWaiveAuthority: (
        body: CapAdjusterPaymentDefinitionPaymentDefinitionOverpaymentWaiveApprovalInput
    ) => RxResult<PaymentApprovalResult>
    cancelExternalOverpayment: (
        params: CapAdjusterPaymentDefinitionCapRefExternalBalanceCancelInput
    ) => RxResult<CapPaymentEntity>
    cancelOverpaymentWaive: (
        params: CapAdjusterPaymentDefinitionCapRefOverpaymentWaiveCancelInput
    ) => RxResult<CapPaymentEntity>
}
