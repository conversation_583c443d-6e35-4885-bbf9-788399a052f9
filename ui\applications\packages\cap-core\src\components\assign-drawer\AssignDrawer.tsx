/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {UIEngine} from '@eisgroup/builder'
import {AssigneeType} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {FormSpy} from '@eisgroup/form'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import * as React from 'react'
import {DrawerActions, DrawerFormStateType, DrawerWidth, FormDrawer} from '../../index'
import config from './builder/AssignForm.builder'
import {getAssignFormSlot} from './slots/AssignFormSlot'
import editorConfig from '../../builder/editor-config'

export const ASSIGN_FORM_ID = 'AssignForm'

export interface AssignDrawerProps {
    title?: string
    store: any
    readonly drawerFormState: DrawerFormStateType
    readonly caseId: string
    readonly assignFormDrawerKey: string
    readonly searchUserLabel: string
    refreshCaseInfo: (formValues: any) => void
}

@observer
export class AssignDrawer extends React.Component<AssignDrawerProps> {
    subscription = {submitting: true, modified: true, values: true, initialValues: true}

    private onClose = () => {
        this.props.store.formDrawerStore.closeDrawer()
        this.props.store.userStore.resetUserSearchResults()
        this.props.store.userStore.resetUserSearchTableResults()
        this.props.store.userStore.clearSelectedUser()
        this.props.store.queueStore.clearSelectQueue()
    }

    private onSave = values => {
        const caseId = this.props.caseId
        const userId = opt(this.props.store.userStore?.user?.securityIdentity).orElse('') as string
        const queueCd = opt(this.props.store.queueStore?.queue?.queueCd).orElse('') as string
        if (values.selectedAssignedType === AssigneeType.User) {
            this.props.store.assignStore.handleReassignCase(caseId, userId, '', () => {
                this.props.refreshCaseInfo(toJS(values))
                this.onClose()
            })
        }
        if (values.selectedAssignedType === AssigneeType.Queue) {
            this.props.store.assignStore.handleReassignCase(caseId, '', queueCd, () => {
                this.props.refreshCaseInfo(toJS(values))
                this.onClose()
            })
        }
    }

    render(): React.ReactNode {
        const {drawerFormState, store, searchUserLabel} = this.props
        const {userStore, queueStore} = store
        const {user} = userStore || {}
        const {queue} = queueStore || {}
        return (
            <>
                {store.formDrawerStore.openedDrawerKey === this.props.assignFormDrawerKey && (
                    <FormDrawer
                        formTitle={this.props.title || ''}
                        drawerWidth={DrawerWidth.SMALL}
                        onFormCancel={this.onClose}
                        formToRender={
                            <UIEngine
                                {...editorConfig}
                                formId={ASSIGN_FORM_ID}
                                config={config}
                                onNext={this.onSave}
                                initialValues={{
                                    drawerFormState,
                                    userStore: store.userStore,
                                    queueStore: store.queueStore,
                                    searchUserLabel
                                }}
                                slotComponents={{
                                    ASSIGN_FORM: getAssignFormSlot(userStore, queueStore)
                                }}
                            >
                                <FormSpy subscription={this.subscription}>
                                    {params => (
                                        <DrawerActions
                                            handleFormCancel={this.onClose}
                                            handleFormConfirm={this.onSave}
                                            labels={{
                                                editButtonLabel: t('cap-core:assign_form_save_btn'),
                                                createButtonLabel: t('cap-core:assign_form_save_btn')
                                            }}
                                            drawerFormState={drawerFormState}
                                            isDisabled={
                                                params.form.getState().values.selectedAssignedType === AssigneeType.User
                                                    ? !user?._key?.rootId
                                                    : !queue?.queueCd
                                            }
                                        />
                                    )}
                                </FormSpy>
                            </UIEngine>
                        }
                    />
                )}
            </>
        )
    }
}
