/**
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import * as React from 'react'
import {t} from '@eisgroup/i18n'
import {Pagination as UIKitPagination, PaginationProps} from '@eisgroup/ui-kit'

type IProps = PaginationProps

export function Pagination(props: IProps): JSX.Element {
    return (
        <UIKitPagination
            {...props}
            jumpToText={props.jumpToText ?? t('cap-core:table_pagination_jump_to_label')}
            locale={{
                jump_to: t('cap-core:table_pagination_jump_to_label'),
                items_per_page: t('cap-core:items_per_page'),
                ...(props.locale || {})
            }}
            key={props.current}
        />
    )
}
