/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {opt} from '@eisgroup/common-types'
import {LocalizationUtils} from '@eisgroup/i18n'
import {SelectInput} from '@eisgroup/form'
import {Form} from '@eisgroup/ui-kit'
import {observer} from 'mobx-react'
import React from 'react'
import {OptionValue} from '../../..'
import t = LocalizationUtils.translate

export interface QueueState {
    selectQueueCd: string
}
export interface QueueSelectProps {
    readonly store: any
}

@observer
export class QueueSelect extends React.Component<QueueSelectProps, QueueState> {
    state = {
        selectQueueCd: ''
    }

    private clearSelectedQueue(): void {
        if (this.props.store.queue) {
            this.props.store.selectQueue(undefined)
        }
    }

    private getSelectOptions = (): OptionValue[] => {
        const queues = this.props.store?.queueAllResults
        const options = [] as OptionValue[]
        if (queues && queues.length > 0) {
            queues.forEach(queue => {
                options.push({
                    code: opt(queue.queueCd).orElse(''),
                    displayValue: t(`cap-core:queue_${queue.queueCd}`)
                })
            })
        }
        return options
    }

    private handleChange = value => {
        const queues = this.props.store?.queueAllResults
        const selectedQueue = queues && queues.find(queue => `${queue.queueCd}` === value)
        this.props.store.selectQueue(selectedQueue)
        this.setState({
            selectQueueCd: value
        })
    }

    render(): React.ReactNode {
        const selectOptions = this.getSelectOptions()
        return (
            <Form.Item label={t('cap-core:assign_form_search_queue_input_label')}>
                <SelectInput
                    options={selectOptions}
                    name='queueCd'
                    onChange={val => this.handleChange(val)}
                    value={this.state.selectQueueCd}
                    required
                    placeholder={t('cap-core:assign_form_search_queue_input_placeholder')}
                />
            </Form.Item>
        )
    }
}
