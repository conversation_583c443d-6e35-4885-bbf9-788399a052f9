import {Table, TableProps} from '@eisgroup/ui-kit'
import classNames from 'classnames'
import {observer} from 'mobx-react'
import React, {PropsWithChildren} from 'react'
import {getSearchResultColumns} from '../CaseSearchConfigs'
import {CaseSearchResultType} from '../types'
import {CASE_SEARCH_RESULT_TABLE} from '../../../common/package-class-names'

export type CaseSearchResultTableProps = {
    className?: string
    searchInputValue?: string
    tableConfig?: TableProps<CaseSearchResultType>
}

export const CaseSearchResultTable = observer((props: PropsWithChildren<CaseSearchResultTableProps>) => {
    const {className, tableConfig} = props

    const columns = getSearchResultColumns<CaseSearchResultType>()
    return (
        <Table<CaseSearchResultType>
            className={classNames(CASE_SEARCH_RESULT_TABLE, className)}
            columns={columns}
            {...tableConfig}
        />
    )
})
