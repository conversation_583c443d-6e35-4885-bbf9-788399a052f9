{"swagger": "2.0", "info": {"description": "API for AcceleratedLoss", "version": "1", "title": "AcceleratedLoss model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/AcceleratedLoss/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/AcceleratedLossKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AcceleratedLossLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AcceleratedLossLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/transformation/AcceleratedLossToSettlementInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLossToSettlementInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/updateLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/reopenLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/initLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeLossInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/submitLoss": {"post": {"description": "The command that performs validation of the provided claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AcceleratedLoss/v1/command/closeLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"ClaimLossReopenInput": {"required": ["_key"], "properties": {"reasonDescription": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "AcceleratedLoss_CapAcceleratedLossEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntitySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AcceleratedLoss_CapAcceleratedLossEntitySuccessBody"}, "AcceleratedLossKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/AcceleratedLossKrakenDeprecatedBundleRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AcceleratedLossKrakenDeprecatedBundleRequestBody"}, "AcceleratedLoss_CapAcceleratedLossDiagnosisInfoEntity": {"required": ["_type"], "properties": {"isPrimaryCode": {"type": "boolean", "description": "If ICD code is primary ICD code."}, "icdCode": {"type": "string"}, "_type": {"type": "string", "example": "CapAcceleratedLossDiagnosisInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossDiagnosisInfoEntity"}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "AcceleratedLossLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AcceleratedLossLoadHistoryResult"}}, "title": "AcceleratedLossLoadHistoryResultSuccess"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "AcceleratedLossKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "AcceleratedLossKrakenDeprecatedBundleRequest"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "AcceleratedLoss_CapAcceleratedLossIncidentEntity": {"required": ["_type"], "properties": {"acceleratedReason": {"type": "string", "description": "reason for accelerated benefit, 3 options available as below."}, "disability": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossDisabilityEntity"}, "chronicIllness": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossChronicIllnessEntity"}, "terminalIllness": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossTerminalIllnessEntity"}, "locationDesc": {"type": "string"}, "incidentDate": {"type": "string", "format": "date"}, "_type": {"type": "string", "example": "CapAcceleratedLossIncidentEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossIncidentEntity"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "AcceleratedLoss_CapAcceleratedLossTerminalIllnessEntity": {"required": ["_type"], "properties": {"lifeExpectancy": {"type": "number", "description": "Life Expectancy(months)-remaining month of life to live"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Last date of insured's been at work"}, "dateOfLifeExpectancyPrescribed": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed with life expectancy prescribed with terminal illness"}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed"}, "requestedBenefitAmount": {"$ref": "#/definitions/Money"}, "dateOfDisability": {"type": "string", "format": "date", "description": "Date of insured's been disability"}, "dateOfFirstTreatment": {"type": "string", "format": "date", "description": "Date of insured's been first treated"}, "_type": {"type": "string", "example": "CapAcceleratedLossTerminalIllnessEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossTerminalIllnessEntity"}, "AcceleratedLoss_CapAcceleratedLossEntity": {"required": ["_modelName", "_type"], "properties": {"lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossPolicyInfoEntity"}}, "applicabilityResult": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossApplicabilityResult"}, "isGenerated": {"type": "boolean", "description": "Indicates if loss is auto generated."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "state": {"type": "string", "description": "Current status of the Loss. Updated each time a new status is gained through state machine."}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "policyIds": {"type": "array", "items": {"type": "string", "description": "Indicates all available policyIds."}}, "policyId": {"type": "string"}, "policy": {"$ref": "#/definitions/AcceleratedLoss_CapPolicyInfo"}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID."}, "lossDetail": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "_modelName": {"type": "string", "example": "AcceleratedLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_timestamp": {"type": "string", "example": "2024-02-02T10:14:47.814+02:00"}, "_archived": {"type": "boolean", "example": false}, "_version": {"type": "string", "example": "0_5ea3f00d-0a35-4200-9a49-d65941903f94"}, "_type": {"type": "string", "example": "CapAcceleratedLossEntity"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossEntity", "description": "Main object for the CAP Loss Domain."}, "LoadEntityRootRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityRootRequest"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossReopenInputBody"}, "AcceleratedLoss_CapAcceleratedLossDisabilityEntity": {"required": ["_type"], "properties": {"nameofFacility": {"type": "string", "description": "Name of Facility - name of confinement facility"}, "eliminationPeriod": {"type": "integer", "format": "int64", "description": "Elimination Period - waiting period"}, "isAccident": {"type": "boolean", "description": "Accident?  - whether or not claim is incurred by accident"}, "disabilityType": {"type": "string", "description": "Disability Type - the body part which no longer functions well"}, "releaseDate": {"type": "string", "format": "date", "description": "Date of release of  disability of the insured"}, "hoursNormallyWorkedperWeek": {"type": "number", "description": "Hours spent on work in week"}, "confinementDate": {"type": "string", "format": "date", "description": "Date of confinement of the insured"}, "requestedBenefitAmount": {"$ref": "#/definitions/Money"}, "workEducationHistory": {"type": "string", "description": "History of work and education of insured"}, "workRelated": {"type": "boolean", "description": "Work Related?"}, "dateOfDisability": {"type": "string", "format": "date", "description": "Date of insured's been disability"}, "dateOfFirstTreatment": {"type": "string", "format": "date", "description": "Date of insured's been first treated"}, "addressofFacility": {"type": "string", "description": "Address of Facility - address of confinement facility"}, "terminationDate": {"type": "string", "format": "date", "description": "Date of insured's terminated with disability"}, "typeofConfinement": {"type": "string", "description": "Type of Confinement - type of health care facility the insured is using. We will provide a list to start with, later on carriers will define more options."}, "dateOfHire": {"type": "string", "format": "date", "description": "Date of insured's been hired"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Last date of insured's been at work"}, "disabilityDegree": {"type": "string", "description": "Disability Grade - the degree of disability, eg. level I,II, III"}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed"}, "returnToWorkDate": {"type": "string", "format": "date", "description": "Date of insured expected to return to work"}, "_type": {"type": "string", "example": "CapAcceleratedLossDisabilityEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossDisabilityEntity"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "AcceleratedLoss_MessageType": {"required": ["_type"], "properties": {"severity": {"type": "string", "description": "Message severity."}, "code": {"type": "string", "description": "Message code."}, "source": {"type": "string", "description": "Message source."}, "message": {"type": "string", "description": "Message text."}, "_type": {"type": "string", "example": "MessageType"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss MessageType", "description": "Holds information of message type."}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityRootRequestBody"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "AcceleratedLoss_Term": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Term"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss Term"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "AcceleratedLoss_CapAcceleratedLossApplicabilityResult": {"required": ["_type"], "properties": {"messages": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedLoss_MessageType"}}, "applicability": {"type": "string", "description": "Applicability Code returned from Applicability OpenL rules"}, "_type": {"type": "string", "example": "CapAcceleratedLossApplicabilityResult"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossApplicabilityResult"}, "AcceleratedLoss_CapAcceleratedLossEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntity"}}, "title": "AcceleratedLoss_CapAcceleratedLossEntitySuccess"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossCloseInputBody"}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectBody"}, "AcceleratedLossToSettlementInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AcceleratedLossToSettlementInputOutputsSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AcceleratedLossToSettlementInputOutputsSuccessBody"}, "AcceleratedLoss_CapAcceleratedLossDetailEntity": {"required": ["_modelName", "_type"], "properties": {"icdCodes": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossDiagnosisInfoEntity"}}, "reportingMethod": {"type": "string", "description": "Indicate how the claim application is reported, e.g. by Phone Call,Email,Mail,Walk-In,Self-Service etc."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when claim application is reported."}, "incident": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossIncidentEntity"}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "_modelName": {"type": "string", "example": "AcceleratedLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_timestamp": {"type": "string", "example": "2024-02-02T10:14:47.814+02:00"}, "_archived": {"type": "boolean", "example": false}, "_version": {"type": "string", "example": "0_db9db21d-dfac-450a-8d64-8c1d90d908fc"}, "_type": {"type": "string", "example": "CapAcceleratedLossDetailEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossDetailEntity", "description": "Defines what loss it is and what happened."}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "CapLifeLossUpdateInput": {"required": ["_key", "entity"], "properties": {"_updateStrategy": {"type": "string"}, "policyId": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "entity": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossDetailEntity"}}, "title": "CapLifeLossUpdateInput"}, "AcceleratedLoss_CapAcceleratedLossPolicyInfoEntity": {"required": ["_type"], "properties": {"masterPolicyId": {"type": "string", "description": "master policy id"}, "masterPolicyNumber": {"type": "string", "description": "master policy number"}, "currencyCd": {"type": "string", "description": "Currency Code"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedLoss_CapInsuredInfo"}}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "term": {"$ref": "#/definitions/AcceleratedLoss_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "_type": {"type": "string", "example": "CapAcceleratedLossPolicyInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "AcceleratedLossLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AcceleratedLossLoadHistoryResultSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AcceleratedLossLoadHistoryResultSuccessBody"}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"reasonDescription": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "AcceleratedLoss_CapInsuredInfo": {"required": ["_type"], "properties": {"registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "AcceleratedLoss_CapPolicyInfo": {"required": ["_type"], "properties": {"currencyCd": {"type": "string", "description": "Currency Code"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedLoss_CapInsuredInfo"}}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "term": {"$ref": "#/definitions/AcceleratedLoss_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "_type": {"type": "string", "example": "CapPolicyInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapPolicyInfo", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"lossSubStatusCd": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "ClaimLossSubStatusInput"}, "ClaimLossSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossSubStatusInputBody"}, "AcceleratedLoss_CapAcceleratedLossChronicIllnessEntity": {"required": ["_type"], "properties": {"nameofFacility": {"type": "string", "description": "Name of Facility - name of confinement facility"}, "eliminationPeriod": {"type": "integer", "format": "int64", "description": "Elimination Period - waiting period"}, "releaseDate": {"type": "string", "format": "date", "description": "Date of release of  chronic illness of the insured"}, "confinementDate": {"type": "string", "format": "date", "description": "Date of confinement of the insured"}, "requestedBenefitAmount": {"$ref": "#/definitions/Money"}, "dateOfDisability": {"type": "string", "format": "date", "description": "Date of insured's been disability"}, "dateOfFirstTreatment": {"type": "string", "format": "date", "description": "Date of insured's been first treated"}, "lossofActivitiesofDailyLiving": {"type": "array", "items": {"type": "string", "description": "Loss of Activities of Daily Living - to define the loss of ADL causes by accelerated chronic illness"}}, "addressofFacility": {"type": "string", "description": "Address of Facility - address of confinement facility"}, "typeofConfinement": {"type": "string", "description": "Type of Confinement - type of health care facility the insured is using. We will provide a list to start with, later on carriers will define more options."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Last date of insured's been at work"}, "diagnosedChronicIllnessType": {"type": "string", "description": "Diagnosed Chronic Illness Type - type of chronic illness the insured is prescribed"}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed"}, "_type": {"type": "string", "example": "CapAcceleratedLossChronicIllnessEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AcceleratedLoss CapAcceleratedLossChronicIllnessEntity"}, "AcceleratedLossToSettlementInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AcceleratedLossToSettlementInputOutputs"}}, "title": "AcceleratedLossToSettlementInputOutputsSuccess"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "AcceleratedLossToSettlementInputOutputs": {"properties": {"out": {"type": "array", "items": {"type": "object"}}}, "title": "AcceleratedLossToSettlementInputOutputs"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "CapLifeLossInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeLossInitInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeLossInitInputBody"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "CapLifeLossInitInput": {"required": ["entity"], "properties": {"lossType": {"type": "string"}, "memberRegistryTypeId": {"type": "string"}, "policyIds": {"type": "array", "items": {"type": "string"}}, "entity": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLifeLossInitInput"}, "CapLifeLossUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeLossUpdateInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeLossUpdateInputBody"}, "AcceleratedLossLoadHistoryResult": {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedLoss_CapAcceleratedLossEntity"}}, "count": {"type": "integer", "format": "int64"}}, "title": "AcceleratedLossLoadHistoryResult"}}}