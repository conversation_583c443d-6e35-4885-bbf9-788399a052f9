/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {observer} from 'mobx-react'
import React from 'react'
import {FormSpy} from '@eisgroup/form'
import {RxResult} from '@eisgroup/common-types'
import {LocalizationUtils} from '@eisgroup/i18n'
import {UIEngine} from '@eisgroup/builder'
import {
    PaymentAmexSmallLight,
    PaymentCashSmall,
    PaymentCheckSmall,
    PaymentDinersClubSmallLight,
    PaymentDiscoverSmallLight,
    PaymentEftSmall,
    PaymentJcbSmallLight,
    PaymentMastercardSmallLight,
    PaymentVisaSmallLight
} from '@eisgroup/ui-kit-icons'
import {
    PaymentMethodDrawer,
    PaymentMethodCustomer,
    PaymentMethod,
    PaymentMethodType,
    CreditCardTypes
} from '@eisgroup/common-business-components'
import {IndividualCustomer} from '@eisgroup/cap-services'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {
    FormDrawer,
    isInactiveDateRange,
    DrawerActions,
    DrawerFormStateType,
    PAYMENT_METHOD_DRAWER,
    CheckAddressSection,
    ADDRESS_SECTION_TITLE,
    getAvailablePaymentMethodTypes
} from '../../..'
import {PREFERRED_PAYMENT_METHOD_DRAWER_KEY} from '../BannerDetailsMainInsured'
import config from './builder/PreferredPaymentMethodForm.builder'
import * as buildingBlocks from './blocks/global.blocks'
import {PreferredPaymentMethodSelect} from './PreferredPaymentMethodSelect'
import editorConfig from '../../../builder/editor-config'
import t = LocalizationUtils.translate
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity

export interface PreferredPaymentMethodEditorProps {
    customer: PaymentMethodCustomer
    drawerFormState: DrawerFormStateType
    paymentMethodDrawerVisible?: boolean
    addNewPaymentMethodDrawerVisible?: boolean
    showAddNewPaymentMethodForm: () => void
    showPaymentMethodDrawer: () => void
    onClose: () => void
    paymentMethods: PaymentMethod[]
    eventCase: CapEventCaseEntity
    updatePaymentMethods: (
        paymentMethods: PaymentMethod[],
        customer: PaymentMethodCustomer
    ) => RxResult<PaymentMethodCustomer>
    setMemberPaymentMethodId: (
        memberPaymentMethodId: string,
        eventCase: CapEventCaseEntity,
        checkAddressId?: string
    ) => RxResult<any>
    loadPaymentMethods: (customer: PaymentMethodCustomer) => RxResult<PaymentMethod[]>
    onPreferredPaymentMethodChange: (id?: string) => void
    getMainInsured: () => Promise<IndividualCustomer>
    updateMainInsured?: (customer: IndividualCustomer) => Promise<IndividualCustomer>
    memberPaymentMethodId?: string
    beneficiaryPaymentMethodId?: string
}

export interface PreferredPaymentMethodEditorState {
    preferredPaymentMethod?: string
    checkAddressId?: string
    saveButtonLoading: boolean
    addressSectionVisible: boolean
    isAddressRequired: boolean
}

type PreferredPaymentInfo = {
    displayValue: string
    icon: React.ReactElement | null
}

export const getPaymentMethodInfo = (paymentMethod: PaymentMethod): PreferredPaymentInfo => {
    const CREDIT_CARD_ICON_MAPPING = {
        [CreditCardTypes.AMERICAN_EXPRESS]: <PaymentAmexSmallLight />,
        [CreditCardTypes.DINERS]: <PaymentDinersClubSmallLight />,
        [CreditCardTypes.DISCOVER]: <PaymentDiscoverSmallLight />,
        [CreditCardTypes.JCB]: <PaymentJcbSmallLight />,
        [CreditCardTypes.MASTER_CARD]: <PaymentMastercardSmallLight />,
        [CreditCardTypes.VISA]: <PaymentVisaSmallLight />
    }
    const maskSymbol = '****'
    switch (paymentMethod._type) {
        case PaymentMethodType.EFT:
            return {
                displayValue: `${maskSymbol} ${paymentMethod.accountNumber.slice(-4)}`,
                icon: <PaymentEftSmall />
            }
        case PaymentMethodType.CREDIT_CARD:
            return {
                displayValue: `${maskSymbol} ${paymentMethod.cardNumber}`,
                icon: CREDIT_CARD_ICON_MAPPING[paymentMethod.cardType]
            }
        case PaymentMethodType.CASH:
            return {
                displayValue: t('cap-core:preferred_payment_method_one_time_cash'),
                icon: <PaymentCashSmall />
            }
        case PaymentMethodType.CHECK:
            return {
                displayValue: t('cap-core:preferred_payment_method_one_time_check'),
                icon: <PaymentCheckSmall />
            }
        default:
            return {
                displayValue: t('cap-core:not_available'),
                icon: null
            }
    }
}

/**
 * Preferred payment method editor for life intake loss
 */
@observer
export class PreferredPaymentMethodEditor extends React.Component<
    PreferredPaymentMethodEditorProps,
    PreferredPaymentMethodEditorState
> {
    state = {
        preferredPaymentMethod: '',
        checkAddressId: '',
        saveButtonLoading: false,
        addressSectionVisible: false,
        isAddressRequired: false
    }

    componentDidMount(): void {
        const preferredPaymentMethod = this.props.memberPaymentMethodId
            ? this.props.memberPaymentMethodId
            : this.props.beneficiaryPaymentMethodId
        this.setState({preferredPaymentMethod})
        this.updateRelatedState()
    }

    componentDidUpdate(previousProps, previousState) {
        if (previousState.preferredPaymentMethod !== this.state.preferredPaymentMethod) {
            this.updateRelatedState()
        }
    }

    private updateRelatedState = () => {
        const checkAddressId = this.props.eventCase?.lossDetail?.memberCheckAddressId
        const selectedPaymentMethod = this.props.paymentMethods.filter(
            v => v._key?.id === this.state.preferredPaymentMethod
        )?.[0]
        if (selectedPaymentMethod?._type === PaymentMethodType.CHECK) {
            this.setState({
                addressSectionVisible: true,
                checkAddressId: checkAddressId ?? '',
                isAddressRequired: true
            })
        }
    }

    private cancelAddNewPaymentMethod = () => {
        this.props.showPaymentMethodDrawer()
    }

    private saveAddNewPaymentMethod = async result => {
        this.props.loadPaymentMethods(result.customer).subscribe(() => {
            this.setState({
                preferredPaymentMethod: result?.paymentMethod._key?.id
            })
            this.props.showPaymentMethodDrawer()
        })
    }

    private savePreferredPaymentMethod = (preferredPaymentMethod, memberCheckAddressId) => {
        this.setState({
            saveButtonLoading: true
        })
        this.props
            .setMemberPaymentMethodId(preferredPaymentMethod, this.props.eventCase, memberCheckAddressId)
            .subscribe(() => {
                this.setState({
                    saveButtonLoading: false
                })
                this.props.onClose()
            })
    }

    private handleChangePreferredPaymentMethod = preferredPaymentMethod => {
        const selectedPaymentMethod = this.props.paymentMethods.filter(v => v._key?.id === preferredPaymentMethod)?.[0]
        if (selectedPaymentMethod?._type === PaymentMethodType.CHECK) {
            this.setState({
                addressSectionVisible: true,
                isAddressRequired: true
            })
        } else {
            this.setState({
                addressSectionVisible: false,
                isAddressRequired: false
            })
        }
        this.setState({
            preferredPaymentMethod
        })
    }

    private checkAddressIdOnChange = value => {
        this.setState({
            checkAddressId: value
        })
    }

    private preferredPaymentMethod = () => {
        return (
            <>
                <PreferredPaymentMethodSelect
                    memberBeneficiaryPaymentMethodId={this.state.preferredPaymentMethod}
                    paymentMethods={this.props.paymentMethods}
                    onPreferredPaymentMethodChange={this.handleChangePreferredPaymentMethod}
                    onAddButtonClick={this.props.showAddNewPaymentMethodForm}
                />
                {this.state.addressSectionVisible && (
                    <>
                        <h3 className={ADDRESS_SECTION_TITLE}>{t('cap-core:title_address')}</h3>
                        <CheckAddressSection
                            paymentMethods={this.props.paymentMethods}
                            customer={this.props.customer}
                            checkAddressIdOnChange={this.checkAddressIdOnChange}
                            checkAddressId={this.state.checkAddressId}
                            updateIndividualCustomer={this.props.updateMainInsured}
                            isAddressRequired={this.state.isAddressRequired}
                        />
                    </>
                )}
            </>
        )
    }

    private renderDrawerContent = (): React.ReactNode => {
        return (
            <UIEngine
                {...editorConfig}
                formId={PREFERRED_PAYMENT_METHOD_DRAWER_KEY}
                config={config}
                initialValues={{
                    preferredPaymentMethod: this.state.preferredPaymentMethod,
                    checkAddressId: this.state.checkAddressId
                }}
                buildingBlocks={buildingBlocks}
                slotComponents={{
                    PREFERRED_PAYMENT_METHOD: this.preferredPaymentMethod
                }}
            >
                <FormSpy>
                    {params => {
                        const savedPreferredPaymentMethod = params.form.getState().values.preferredPaymentMethod
                        const savedCheckAddressId = params.form.getState().values.checkAddressId
                        const findPaymentMethod = this.props.paymentMethods.find(
                            paymentMethod => paymentMethod._key?.id === this.state.preferredPaymentMethod
                        )
                        return (
                            <DrawerActions
                                handleFormCancel={this.props.onClose}
                                labels={{
                                    editButtonLabel: t('cap-core:save'),
                                    createButtonLabel: t('cap-core:save')
                                }}
                                drawerFormState={this.props.drawerFormState}
                                isLoading={this.state.saveButtonLoading}
                                isDisabled={
                                    !this.state.preferredPaymentMethod ||
                                    isInactiveDateRange(
                                        findPaymentMethod?.effectiveDate,
                                        findPaymentMethod?.expirationDate
                                    )
                                }
                                handleFormConfirm={() => {
                                    if (params.form.getState().hasValidationErrors) {
                                        return
                                    }
                                    this.savePreferredPaymentMethod(savedPreferredPaymentMethod, savedCheckAddressId)
                                }}
                            />
                        )
                    }}
                </FormSpy>
            </UIEngine>
        )
    }

    render(): React.ReactNode {
        const {paymentMethodDrawerVisible, addNewPaymentMethodDrawerVisible, paymentMethods} = this.props
        const availablePaymentMethodTypes = getAvailablePaymentMethodTypes(paymentMethods)
        return (
            <>
                {addNewPaymentMethodDrawerVisible && (
                    <PaymentMethodDrawer
                        paymentMethodFormProps={{
                            paymentMethodTypes: availablePaymentMethodTypes
                        }}
                        visible={this.props.addNewPaymentMethodDrawerVisible}
                        customerNumber={this.props.customer?.customerNumber!}
                        isEditMode={false}
                        onCancel={this.cancelAddNewPaymentMethod}
                        onClose={this.props.onClose}
                        onSuccess={result => this.saveAddNewPaymentMethod(result)}
                    />
                )}
                {paymentMethodDrawerVisible && (
                    <FormDrawer
                        className={PAYMENT_METHOD_DRAWER}
                        formTitle={t('cap-core:preferred_payment_method_drawer_title')}
                        formToRender={this.renderDrawerContent()}
                        onFormCancel={this.props.onClose}
                    />
                )}
            </>
        )
    }
}
