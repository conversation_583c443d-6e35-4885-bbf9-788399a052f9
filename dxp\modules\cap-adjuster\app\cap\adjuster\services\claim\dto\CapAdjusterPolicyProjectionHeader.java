/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.dto;

import java.util.List;

import core.services.ApiDTO;
import core.services.PartialUpdateApiDTO;

public class CapAdjusterPolicyProjectionHeader extends PartialUpdateApiDTO implements ApiDTO {
    public Boolean isVerified;
    public String productCd;
    public String policyNumber;
    public String policyStatus;
    public String riskStateCd;
    public String capPolicyId;
    public String capPolicyVersionId;
    public String policyType;
    public List<String> insuredRegistryTypeIds;
    public String masterPolicyId;
    public String masterPolicyNumber;
    public String masterPolicyProductCd;
    public String orgCustomerNumber;
}
