/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.eventcase.dto;

import java.util.List;

import cap.adjuster.services.common.dto.GenesisLink;
import cap.adjuster.services.common.dto.GenesisRootApiModel;

public class CapGenericLoss extends GenesisRootApiModel {

    public String lossType;
    public String claimType;
    public String memberRegistryTypeId;
    public String coverageType;
    public String lossNumber;
    public GenesisLink absence;
    public GenesisLink eventCaseLink;
    public String state;
    public String reasonCd;
    public String reasonDescription;

    public String lossSubStatusCd;
    public String policyId;
    public Object lossDetail;
    public Object eventCaseInfo;
    public Object policy;
    public Object eligibilityResult;
    public Object subjectOfClaim;
    public List<Object> damageLosses;
    public Boolean isGenerated;
    public List<Object> policies;
    public Object applicabilityResult;
    public String claimWrapperUpdateUIAction;

}
