/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.policy.impl;

import cap.adjuster.services.policy.CapAdjusterPoliciesService;
import com.eisgroup.dxp.services.capadjusterpolicysearch.CapAdjusterPolicySearchCapAdjusterv1policiesService;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchCapPolicySearchRequestBody;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchHeaderProjectionModel_HeaderProjection;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse;
import core.services.pagination.PageData;
import dataproviders.common.dto.GenesisLinkDTO;
import dataproviders.common.utils.GenesisPaginationUtils;
import dataproviders.common.utils.GenesisReferenceUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

public class CapAdjusterPoliciesServiceImpl implements CapAdjusterPoliciesService {
    private static final Integer PAGE_LIMIT_DEFAULT = 100;
    private static final Integer OFFSET_DEFAULT = 0;
    private static final String PREFIX_OF_CAP_POLICY_ID  = "capPolicy://PAS/";

    private CapAdjusterPolicySearchCapAdjusterv1policiesService capAdjusterPolicySearchCapAdjusterv1policiesService;
    private GenesisPaginationUtils paginationUtils;

    @Override
    public CompletionStage<CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse> searchPolicyHeaders(CapAdjusterPolicySearchCapPolicySearchRequestBody requestBody, String fields, PageData pageData) {
        return capAdjusterPolicySearchCapAdjusterv1policiesService.apiCommonSearchV1ClaimPolicyProjectionHeaderPost(requestBody, fields, PAGE_LIMIT_DEFAULT, OFFSET_DEFAULT)
                .thenApply(result -> {
                    CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse response = result.body.success;
                    double page = Math.ceil(response.count / PAGE_LIMIT_DEFAULT.doubleValue());
                    for (int i = 1; i < page; i++) {
                        capAdjusterPolicySearchCapAdjusterv1policiesService.apiCommonSearchV1ClaimPolicyProjectionHeaderPost(requestBody, fields, PAGE_LIMIT_DEFAULT, i * PAGE_LIMIT_DEFAULT)
                                .thenApply(pageResponse -> response.result.addAll(pageResponse.body.success.result));
                    }
                    return response;
                })
                .thenApply(response -> filterLatestPolicies(response, pageData));
    }

    private CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse filterLatestPolicies(CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse response, PageData pageData) {
        if (CollectionUtils.isEmpty(response.result)) {
            return response;
        }
        Map<String, CapAdjusterPolicySearchHeaderProjectionModel_HeaderProjection> latestPolicies = response.result.parallelStream()
                .filter(policy -> StringUtils.isNotEmpty(policy.masterPolicyId) || !validateMasterPolicy(policy, response.result))
                .collect(
                        Collectors.groupingBy(policy -> policy.capPolicyId, Collectors.collectingAndThen(
                                Collectors.reducing((c1, c2) -> getPolicyRevisionNo(c1.capPolicyVersionId) > getPolicyRevisionNo(c2.capPolicyVersionId) ? c1 : c2), Optional::get))
                );

        response.result.clear();
        response.count = Long.valueOf(latestPolicies.size());
        response.result.addAll(paginationUtils.propagatePaginationMetadata(pageData, latestPolicies.values().stream().collect(Collectors.toList())));

        return response;
    }

    /**
     * Check if master policy has individual(certificate) policy
     *
     * @param masterPolicy
     * @param result
     *
     * @return true if master policy has individual(certificate) policy, false if not
     */
    private boolean validateMasterPolicy(CapAdjusterPolicySearchHeaderProjectionModel_HeaderProjection masterPolicy, List<CapAdjusterPolicySearchHeaderProjectionModel_HeaderProjection> result) {
        return result.stream()
                .filter(policy -> StringUtils.isNotEmpty(policy.masterPolicyId) && StringUtils.equals(policy.masterPolicyId, masterPolicy.capPolicyVersionId))
                .findAny().isPresent();

    }

    private Integer getPolicyRevisionNo(String policyVersionId) {
        GenesisLinkDTO policyLink = new GenesisLinkDTO(policyVersionId.substring(PREFIX_OF_CAP_POLICY_ID.length()));
        return GenesisReferenceUtils.getRevision(policyLink);
    }

    @Inject
    public void setCapAdjusterPolicySearchCapAdjusterv1policiesService(CapAdjusterPolicySearchCapAdjusterv1policiesService capAdjusterPolicySearchCapAdjusterv1policiesService) {
        this.capAdjusterPolicySearchCapAdjusterv1policiesService = capAdjusterPolicySearchCapAdjusterv1policiesService;
    }

    @Inject
    public void setPaginationUtils(GenesisPaginationUtils paginationUtils) {
        this.paginationUtils = paginationUtils;
    }
}
