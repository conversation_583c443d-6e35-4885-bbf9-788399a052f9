{"swagger": "2.0", "info": {"description": "API for CapPaymentTemplate", "version": "1", "title": "CapPaymentTemplate model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/cappaymenttemplate/CapPaymentTemplate/v1/command/addPaymentTemplateMessages": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRefPaymentTemplateMessagesInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/command/closePaymentTemplate": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/command/initPaymentTemplate": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRefPaymentTemplateInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/command/startBuildPaymentScheduleFlow": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRefBuildPaymentScheduleInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/command/updatePaymentTemplate": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRefPaymentTemplateUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRefPaymentTemplateUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/entities/loadPaymentTemplates": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapLoadByOriginSourceRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapPaymentTemplate_LoadPaymentTemplateResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/financialData/getFinancialData": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapFinancialDataRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapPaymentTemplateLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapPaymentTemplateLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/paymentTemplateIndex/loadPaymentTemplateIndexes": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPaymentTemplateIndexLoadRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapPaymentTemplateIndex_CapPaymentTemplateIdxEntityListSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPaymentTemplateKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappaymenttemplate/CapPaymentTemplate/v1/transformation/calculatePaymentSchedule": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapFinancialDataRequest": {"required": ["originSource", "settlements"], "properties": {"description": {"type": "string"}, "exGratias": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleExGratiasInput"}}, "expenses": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleExpensesInput"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "settlements": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleSettlementInfoInput"}}, "withholdings": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleWithholdingsInput"}}}, "title": "CapFinancialDataRequest"}, "CapFinancialDataRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapFinancialDataRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapFinancialDataRequestBody"}, "CapLoadByOriginSourceRequest": {"properties": {"fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "originSourceUris": {"type": "array", "items": {"type": "string"}}}, "title": "CapLoadByOriginSourceRequest"}, "CapLoadByOriginSourceRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLoadByOriginSourceRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLoadByOriginSourceRequestBody"}, "CapPaymentSchedule_CapAllocationWithholdingCancelationDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAllocationWithholdingCancelationDetailsEntity"}, "paymentNumber": {"type": "string", "description": "Number of payment whose withholding was canceled (reversed)"}}, "title": "CapPaymentSchedule CapAllocationWithholdingCancelationDetailsEntity", "description": "Allocation Withholding Cancelation Details "}, "CapPaymentSchedule_CapMasterPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMasterPaymentDetailsEntity"}, "masterPayment": {"$ref": "#/definitions/EntityLink"}, "masterPaymentNumber": {"type": "string", "description": "A unique actual payment number of the master payment which sub payment belongs to."}, "masterPaymentScheduledNumber": {"type": "string", "description": "A unique scheduled payment number of the master payment which sub payment belongs to."}}, "title": "CapPaymentSchedule CapMasterPaymentDetailsEntity", "description": "Details of the master payment which sub payment belongs to."}, "CapPaymentSchedule_CapPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeTypeCd": {"type": "string"}, "paymentMethodDetails": {"type": "object", "description": "Payment method details."}}, "title": "CapPaymentSchedule CapPayeeDetailsEntity", "description": "Defines payment payee details."}, "CapPaymentSchedule_CapPayeeRoleDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeRoleDetailsEntity"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "registryId": {"type": "string", "description": "URI to CEM."}, "representBeneficiary": {"type": "string", "description": "Payee on beharf of Beneficiary's uri."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of the Payee"}}}, "title": "CapPaymentSchedule CapPayeeRoleDetailsEntity", "description": "Payment Payee Role details."}, "CapPaymentSchedule_CapPaymentAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCaseNumber": {"type": "string", "description": "Accumulator case number."}, "accumulatorClaimNumber": {"type": "string", "description": "Accumulator claim number."}, "accumulatorCoverage": {"type": "string", "description": "Accumulator coverage. Defines which type of accumulator is consumed."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapPaymentSchedule CapPaymentAccumulatorDetailsEntity", "description": "Accumulator details."}, "CapPaymentSchedule_CapPaymentAccumulatorDetailsExtensionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapPaymentSchedule CapPaymentAccumulatorDetailsExtensionEntity", "description": "Accumulator details extension."}, "CapPaymentSchedule_CapPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsColaEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines cola consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines cola elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionDetailsInterestEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsInterestEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDaysFromDolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOL."}, "interestDaysFromDosReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOS."}, "interestDaysFromPolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since POL was recieved."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Defines the date up to when interest were paid ."}, "interestRate": {"type": "number", "description": "Defines the rate based on which Interest was calculated."}, "interestState": {"type": "string", "description": "Interest state according to which interest are caculated."}, "interestThresholdAmount": {"$ref": "#/definitions/Money"}, "interestTimeThreshold": {"type": "integer", "format": "int64", "description": "Interest time threshold."}, "isCompound": {"type": "boolean", "description": "Identifies if Compound interest calculation formula was used."}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsInterestEntity", "description": "Interest addition details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionDetailsRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabBenefitCd": {"type": "string", "description": "Rehab Benefit code taken from policy. Defines if rehab is included or not."}, "rehabBenefitPct": {"type": "number", "description": "Rehab percentage. Defines by how much to increase the AGBA."}, "rehabTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsRehabEntity", "description": "Rehab details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionDetailsRestoredEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRestoredEntity"}, "restoredDeathBenefit": {"type": "number", "description": "Restored Death Benefit."}}, "title": "CapPaymentSchedule CapPaymentAdditionDetailsRestoredEntity", "description": "Restored details are described in this entity."}, "CapPaymentSchedule_CapPaymentAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsColaEntity"}, "paymentAdditionDetailsInterest": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsInterestEntity"}, "paymentAdditionDetailsRehab": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsRehabEntity"}, "paymentAdditionDetailsRestored": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionDetailsRestoredEntity"}}, "title": "CapPaymentSchedule CapPaymentAdditionEntity", "description": "Entity for Payment Addition types (Cola, Interest Payment)."}, "CapPaymentSchedule_CapPaymentAdditionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionSplitResultEntity"}, "additionNumber": {"type": "string", "description": "Number used to link split result to the payment addition."}, "appliedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentAdditionSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapPaymentSchedule_CapPaymentAllocationBalanceAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBalanceAdjustmentEntity"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapPaymentSchedule CapPaymentAllocationBalanceAdjustmentEntity", "description": "Details of the allocation with adjustment type."}, "CapPaymentSchedule_CapPaymentAllocationBeneficiaryDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBeneficiaryDetailsEntity"}, "beneficiary": {"$ref": "#/definitions/EntityLink"}, "beneficiaryPct": {"type": "number", "description": "Percentage of the benefit amount that the beneficiary receives."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "CapPaymentSchedule CapPaymentAllocationBeneficiaryDetailsEntity", "description": "Details of the customer the benefit was calcualted for."}, "CapPaymentSchedule_CapPaymentAllocationBenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBenefitDurationEntity"}, "benefitDuration": {"type": "number", "description": "Benefit duration amount per defined frequency inside the period."}, "benefitDurationFrequencyCd": {"type": "string", "description": "The timeframe inside the period within which the benefit duration occurrences happen."}, "benefitDurationPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}}, "title": "CapPaymentSchedule CapPaymentAllocationBenefitDurationEntity", "description": "Details of periods for which benefit is calculated."}, "CapPaymentSchedule_CapPaymentAllocationDeductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDeductionDetailsEntity"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentAllocationDeductionDetailsEntity", "description": "Details of 3rd party deduction payment allocation."}, "CapPaymentSchedule_CapPaymentAllocationDisabilityDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDisabilityDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBenefitDurationEntity"}}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "monthlyGbaAmount": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}, "taxableAmount": {"$ref": "#/definitions/Money"}, "taxablePercentage": {"type": "number", "description": "The percentage of the gross benefit amount that will taxable."}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentAllocationDisabilityDetailsEntity", "description": "Details of Disability LOB allocation."}, "CapPaymentSchedule_CapPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationBeneficiaryDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBeneficiaryDetailsEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationDeductionDetailsEntity"}, "allocationDisabilityDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationDisabilityDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLifeDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationLifeDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "allocationWithholdingCancelationDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapAllocationWithholdingCancelationDetailsEntity"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxSplitResultEntity"}}}, "title": "CapPaymentSchedule CapPaymentAllocationEntity", "description": "Entity for the payment allocation information."}, "CapPaymentSchedule_CapPaymentAllocationLifeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLifeDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentAllocationLifeDetailsEntity", "description": "Details of Life LOB allocation."}, "CapPaymentSchedule_CapPaymentAllocationLossInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfo"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentSchedule CapPaymentAllocationLossInfo", "description": "Defines payment allocation loss info details."}, "CapPaymentSchedule_CapPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfoEntity"}, "asoType": {"type": "string", "description": "Administrative services only type"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "claimType": {"type": "string", "description": "Defines claim type."}, "dateOfLoss": {"type": "string", "format": "date-time"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Defines loss type."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentSchedule CapPaymentAllocationLossInfoEntity", "description": "The attribute to store all info that is needed from Claim for Payments."}, "CapPaymentSchedule_CapPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationPayableItemEntity"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapPaymentSchedule CapPaymentAllocationPayableItemEntity", "description": "Stores details for what it is paid."}, "CapPaymentSchedule_CapPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentDetailsEntity"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Defines insured last work date prior to payment."}, "payeeDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPayeeDetailsEntity"}, "payeeRoleDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPayeeRoleDetailsEntity"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionEntity"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}, "paymentReductions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionEntity"}}, "paymentTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxEntity"}}, "typicalWorkWeek": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTypicalWorkWeekEntity"}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentDetailsEntity", "description": "Payment details."}, "CapPaymentSchedule_CapPaymentFinancialAdjustmentPayableSourcesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentFinancialAdjustmentPayableSourcesEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to the related policy."}}, "title": "CapPaymentSchedule CapPaymentFinancialAdjustmentPayableSourcesEntity", "description": "Identifies sources for which financial adjustment is applied"}, "CapPaymentSchedule_CapPaymentReductionDetailsDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type."}, "deductionBeneficiary": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "Defines the Deduction precentage."}, "deductionProvider": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "deductionType": {"type": "string", "description": "Deduction type."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "Is a Deduction applied before taxes."}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsDeductionEntity", "description": "Deduction details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionDetailsIndebtednessEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsIndebtednessEntity"}, "indebtedness": {"type": "number", "description": "Indebtedness."}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsIndebtednessEntity", "description": "Indebtedness details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionDetailsOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsOffsetEntity"}, "isAutoOffset": {"type": "boolean"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "Defines the prorating rate of the Offset."}, "offsetTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "offsetType": {"type": "string"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsOffsetEntity", "description": "Offset details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionDetailsWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsWithholdingEntity"}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentReductionDetailsWithholdingEntity", "description": "Withholding details are described in this entity."}, "CapPaymentSchedule_CapPaymentReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionEntity"}, "paymentReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsDeductionEntity"}, "paymentReductionDetailsIndebtedness": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsIndebtednessEntity"}, "paymentReductionDetailsOffset": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsOffsetEntity"}, "paymentReductionDetailsWithholding": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsWithholdingEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}}, "title": "CapPaymentSchedule CapPaymentReductionEntity", "description": "Entity for Payment Reduction types (Offsets, Deductions)."}, "CapPaymentSchedule_CapPaymentReductionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "reductionNumber": {"type": "string", "description": "Number used to link split result to the payment reduction."}}, "title": "CapPaymentSchedule CapPaymentReductionSplitResultEntity", "description": "Defines payment allocation reduction split result."}, "CapPaymentSchedule_CapPaymentScheduleActivationResult": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleActivationResult"}, "activationMessages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleMessageEntity"}}, "activationStatus": {"type": "string"}}, "title": "CapPaymentSchedule CapPaymentScheduleActivationResult"}, "CapPaymentSchedule_CapPaymentScheduleEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPaymentSchedule"}, "_modelType": {"type": "string", "example": "CapPaymentSchedule"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentScheduleEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the schedule was created."}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentScheduleActivationResult": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleActivationResult"}, "paymentTemplate": {"$ref": "#/definitions/EntityLink"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapScheduledPaymentEntity"}}, "scheduleMessages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleMessageEntity"}}, "scheduleNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment schedule."}, "state": {"type": "string", "description": "State of a payment schedule lifecycle."}}, "title": "CapPaymentSchedule CapPaymentScheduleEntity", "description": "The Root Entity of CAP Payment Schedule Domain."}, "CapPaymentSchedule_CapPaymentScheduleEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntity"}}, "title": "CapPaymentSchedule_CapPaymentScheduleEntitySuccess"}, "CapPaymentSchedule_CapPaymentScheduleEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentScheduleEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentSchedule_CapPaymentScheduleEntitySuccessBody"}, "CapPaymentSchedule_CapPaymentScheduleMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentSchedule CapPaymentScheduleMessageEntity", "description": "Payment Schedule messages."}, "CapPaymentSchedule_CapPaymentTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxPercentageASO": {"type": "number"}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxW4Percentage": {"type": "number", "description": "Defines Federal tax percentage calculated according to the W4 parameters."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "federalTaxableWages": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapPaymentSchedule CapPaymentTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "CapPaymentSchedule_CapPaymentTaxDetailsFicaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFicaEntity"}, "ficaRate": {"type": "number"}, "ficaTaxableWages": {"$ref": "#/definitions/Money"}, "ficaType": {"type": "string", "description": "Defines the type of FICA."}}, "title": "CapPaymentSchedule CapPaymentTaxDetailsFicaEntity", "description": "Defines Fica tax details."}, "CapPaymentSchedule_CapPaymentTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/CapPaymentSchedule_Term"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "stateTaxableWages": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentSchedule CapPaymentTaxDetailsStateEntity", "description": "Defines State tax details."}, "CapPaymentSchedule_CapPaymentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxEntity"}, "paymentTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFederalEntity"}, "paymentTaxDetailsFica": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFicaEntity"}, "paymentTaxDetailsState": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsStateEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}}, "title": "CapPaymentSchedule CapPaymentTaxEntity", "description": "Entity for Payment Tax types."}, "CapPaymentSchedule_CapPaymentTaxSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "taxNumber": {"type": "string", "description": "Number used to link split result to the payment tax."}}, "title": "CapPaymentSchedule CapPaymentTaxSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapPaymentSchedule_CapPaymentTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapPaymentSchedule CapPaymentTypicalWorkWeekEntity"}, "CapPaymentSchedule_CapPaymentWithholdingAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}}, "title": "CapPaymentSchedule CapPaymentWithholdingAdditionEntity", "description": "Entity for the withholding additions."}, "CapPaymentSchedule_CapPaymentWithholdingAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationDeductionDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationLossInfo"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Allocation reserve type."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxSplitResultEntity"}}}, "title": "CapPaymentSchedule CapPaymentWithholdingAllocationEntity", "description": "Entity for the withholding allocation information."}, "CapPaymentSchedule_CapPaymentWithholdingDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingDetailsEntity"}, "withholdingAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingAdditionEntity"}}, "withholdingAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingAllocationEntity"}}, "withholdingReductions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingReductionEntity"}}, "withholdingTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingTaxEntity"}}}, "title": "CapPaymentSchedule CapPaymentWithholdingDetailsEntity", "description": "Entity for the withholding details."}, "CapPaymentSchedule_CapPaymentWithholdingReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingReductionEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}, "withholdingReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentReductionDetailsDeductionEntity"}}, "title": "CapPaymentSchedule CapPaymentWithholdingReductionEntity", "description": "Entity for the withholding reductions."}, "CapPaymentSchedule_CapPaymentWithholdingTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingTaxEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}, "withholdingTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFederalEntity"}, "withholdingTaxDetailsFica": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsFicaEntity"}, "withholdingTaxDetailsState": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentTaxDetailsStateEntity"}}, "title": "CapPaymentSchedule CapPaymentWithholdingTaxEntity", "description": "Entity for the withholding taxes."}, "CapPaymentSchedule_CapScheduledPaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapScheduledPaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "isIssuedOnScheduleCreation": {"type": "boolean", "description": "Defines if the payment has been issued by the time the schedule was created."}, "masterPaymentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapMasterPaymentDetailsEntity"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentSchedule_CapScheduledPaymentMessageEntity"}}, "paymentDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}, "paymentSubType": {"type": "string", "description": "Details of payment Sub Type"}, "scheduledPaymentNumber": {"type": "string", "description": "Unique payment number in the scheduled used to match actual and scheduled payments."}, "withholdingDetails": {"$ref": "#/definitions/CapPaymentSchedule_CapPaymentWithholdingDetailsEntity"}}, "title": "CapPaymentSchedule CapScheduledPaymentEntity", "description": "Defines payment transaction information."}, "CapPaymentSchedule_CapScheduledPaymentMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapScheduledPaymentMessageEntity"}, "allocationPeriod": {"$ref": "#/definitions/CapPaymentSchedule_Period"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentSchedule CapScheduledPaymentMessageEntity", "description": "Schedule Payment Message"}, "CapPaymentSchedule_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentSchedule Period"}, "CapPaymentSchedule_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentSchedule Term"}, "CapPaymentTemplateIndexLoadRequest": {"properties": {"originSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplateIndexLoadRequest"}, "CapPaymentTemplateIndexLoadRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapPaymentTemplateIndexLoadRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentTemplateIndexLoadRequestBody"}, "CapPaymentTemplateIndex_CapPaymentTemplateIdxEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPaymentTemplateIndex"}, "_modelType": {"type": "string", "example": "CapPaymentTemplateIndex"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentTemplateIdxEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time"}, "originSource": {"type": "string"}, "paymentTemplateId": {"type": "string"}}, "title": "CapPaymentTemplateIndex CapPaymentTemplateIdxEntity"}, "CapPaymentTemplateIndex_CapPaymentTemplateIdxEntityListSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplateIndex_CapPaymentTemplateIdxEntity"}}}, "title": "CapPaymentTemplateIndex_CapPaymentTemplateIdxEntityListSuccess"}, "CapPaymentTemplateIndex_CapPaymentTemplateIdxEntityListSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentTemplateIndex_CapPaymentTemplateIdxEntityListSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentTemplateIndex_CapPaymentTemplateIdxEntityListSuccessBody"}, "CapPaymentTemplateKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapPaymentTemplateKrakenDeprecatedBundleRequest"}, "CapPaymentTemplateKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapPaymentTemplateKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentTemplateKrakenDeprecatedBundleRequestBody"}, "CapPaymentTemplateLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntity"}}}, "title": "CapPaymentTemplateLoadHistoryResult"}, "CapPaymentTemplateLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentTemplateLoadHistoryResult"}}, "title": "CapPaymentTemplateLoadHistoryResultSuccess"}, "CapPaymentTemplateLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentTemplateLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentTemplateLoadHistoryResultSuccessBody"}, "CapPaymentTemplate_CapAllocationWithholdingCancelationDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAllocationWithholdingCancelationDetailsEntity"}, "paymentNumber": {"type": "string", "description": "Number of payment whose withholding was canceled (reversed)"}}, "title": "CapPaymentTemplate CapAllocationWithholdingCancelationDetailsEntity", "description": "Allocation Withholding Cancelation Details "}, "CapPaymentTemplate_CapBuildPaymentScheduleInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBuildPaymentScheduleInput"}, "description": {"type": "string", "description": "User input from the payment creation page."}, "exGratias": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleExGratiasInput"}}, "expenses": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleExpensesInput"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "settlements": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleSettlementInfoInput"}}, "withholdings": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleWithholdingsInput"}}}, "title": "CapPaymentTemplate CapBuildPaymentScheduleInput"}, "CapPaymentTemplate_CapManualEOBRemarksEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapManualEOBRemarksEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentTemplate CapManualEOBRemarksEntity", "description": "Entity for manual EOB remarks."}, "CapPaymentTemplate_CapManualEOBRemarksInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapManualEOBRemarksInput"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentTemplate CapManualEOBRemarksInput", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapPaymentTemplate_CapMasterPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMasterPaymentDetailsEntity"}, "masterPayment": {"$ref": "#/definitions/EntityLink"}, "masterPaymentNumber": {"type": "string", "description": "A unique actual payment number of the master payment which sub payment belongs to."}, "masterPaymentScheduledNumber": {"type": "string", "description": "A unique scheduled payment number of the master payment which sub payment belongs to."}}, "title": "CapPaymentTemplate CapMasterPaymentDetailsEntity", "description": "Details of the master payment which sub payment belongs to."}, "CapPaymentTemplate_CapPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeTypeCd": {"type": "string"}, "paymentMethodDetails": {"type": "object", "description": "Payment method details."}}, "title": "CapPaymentTemplate CapPayeeDetailsEntity", "description": "Defines payment payee details."}, "CapPaymentTemplate_CapPayeeRoleDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPayeeRoleDetailsEntity"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "registryId": {"type": "string", "description": "URI to CEM."}, "representBeneficiary": {"type": "string", "description": "Payee on beharf of Beneficiary's uri."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of the Payee"}}}, "title": "CapPaymentTemplate CapPayeeRoleDetailsEntity", "description": "Payment Payee Role details."}, "CapPaymentTemplate_CapPaymentAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCaseNumber": {"type": "string", "description": "Accumulator case number."}, "accumulatorClaimNumber": {"type": "string", "description": "Accumulator claim number."}, "accumulatorCoverage": {"type": "string", "description": "Accumulator coverage. Defines which type of accumulator is consumed."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapPaymentTemplate CapPaymentAccumulatorDetailsEntity", "description": "Accumulator details."}, "CapPaymentTemplate_CapPaymentAccumulatorDetailsExtensionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapPaymentTemplate CapPaymentAccumulatorDetailsExtensionEntity", "description": "Accumulator details extension."}, "CapPaymentTemplate_CapPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsColaEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines cola consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines cola elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "CapPaymentTemplate CapPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapPaymentTemplate_CapPaymentAdditionDetailsInterestEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsInterestEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDaysFromDolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOL."}, "interestDaysFromDosReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOS."}, "interestDaysFromPolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since POL was recieved."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Defines the date up to when interest were paid ."}, "interestRate": {"type": "number", "description": "Defines the rate based on which Interest was calculated."}, "interestState": {"type": "string", "description": "Interest state according to which interest are caculated."}, "interestThresholdAmount": {"$ref": "#/definitions/Money"}, "interestTimeThreshold": {"type": "integer", "format": "int64", "description": "Interest time threshold."}, "isCompound": {"type": "boolean", "description": "Identifies if Compound interest calculation formula was used."}}, "title": "CapPaymentTemplate CapPaymentAdditionDetailsInterestEntity", "description": "Interest addition details are described in this entity."}, "CapPaymentTemplate_CapPaymentAdditionDetailsRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabBenefitCd": {"type": "string", "description": "Rehab Benefit code taken from policy. Defines if rehab is included or not."}, "rehabBenefitPct": {"type": "number", "description": "Rehab percentage. Defines by how much to increase the AGBA."}, "rehabTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}}, "title": "CapPaymentTemplate CapPaymentAdditionDetailsRehabEntity", "description": "Rehab details are described in this entity."}, "CapPaymentTemplate_CapPaymentAdditionDetailsRestoredEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionDetailsRestoredEntity"}, "restoredDeathBenefit": {"type": "number", "description": "Restored Death Benefit."}}, "title": "CapPaymentTemplate CapPaymentAdditionDetailsRestoredEntity", "description": "Restored details are described in this entity."}, "CapPaymentTemplate_CapPaymentAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionDetailsColaEntity"}, "paymentAdditionDetailsInterest": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionDetailsInterestEntity"}, "paymentAdditionDetailsRehab": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionDetailsRehabEntity"}, "paymentAdditionDetailsRestored": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionDetailsRestoredEntity"}}, "title": "CapPaymentTemplate CapPaymentAdditionEntity", "description": "Entity for Payment Addition types (Cola, Interest Payment)."}, "CapPaymentTemplate_CapPaymentAdditionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionSplitResultEntity"}, "additionNumber": {"type": "string", "description": "Number used to link split result to the payment addition."}, "appliedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentAdditionSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapPaymentTemplate_CapPaymentAdditionTemplateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAdditionTemplateEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}, "paymentTemplateAdditionDetailsCola": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdditionDetailsColaEntity"}, "paymentTemplateAdditionDetailsInterest": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdditionDetailsInterestEntity"}, "paymentTemplateAdditionDetailsRehab": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdditionDetailsRehabEntity"}, "paymentTemplateAdditionDetailsRestored": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdditionDetailsRestoredEntity"}}, "title": "CapPaymentTemplate CapPaymentAdditionTemplateEntity", "description": "Entity for Payment Addition types (Cola, Interest Payment)."}, "CapPaymentTemplate_CapPaymentAllocationBalanceAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBalanceAdjustmentEntity"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapPaymentTemplate CapPaymentAllocationBalanceAdjustmentEntity", "description": "Details of the allocation with adjustment type."}, "CapPaymentTemplate_CapPaymentAllocationBeneficiaryDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBeneficiaryDetailsEntity"}, "beneficiary": {"$ref": "#/definitions/EntityLink"}, "beneficiaryPct": {"type": "number", "description": "Percentage of the benefit amount that the beneficiary receives."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "CapPaymentTemplate CapPaymentAllocationBeneficiaryDetailsEntity", "description": "Details of the customer the benefit was calcualted for."}, "CapPaymentTemplate_CapPaymentAllocationBenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationBenefitDurationEntity"}, "benefitDuration": {"type": "number", "description": "Benefit duration amount per defined frequency inside the period."}, "benefitDurationFrequencyCd": {"type": "string", "description": "The timeframe inside the period within which the benefit duration occurrences happen."}, "benefitDurationPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}}, "title": "CapPaymentTemplate CapPaymentAllocationBenefitDurationEntity", "description": "Details of periods for which benefit is calculated."}, "CapPaymentTemplate_CapPaymentAllocationDeductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDeductionDetailsEntity"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplate CapPaymentAllocationDeductionDetailsEntity", "description": "Details of 3rd party deduction payment allocation."}, "CapPaymentTemplate_CapPaymentAllocationDisabilityDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationDisabilityDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationBenefitDurationEntity"}}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "monthlyGbaAmount": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}, "taxableAmount": {"$ref": "#/definitions/Money"}, "taxablePercentage": {"type": "number", "description": "The percentage of the gross benefit amount that will taxable."}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentAllocationDisabilityDetailsEntity", "description": "Details of Disability LOB allocation."}, "CapPaymentTemplate_CapPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationBeneficiaryDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationBeneficiaryDetailsEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationDeductionDetailsEntity"}, "allocationDisabilityDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationDisabilityDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLifeDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationLifeDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "allocationWithholdingCancelationDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapAllocationWithholdingCancelationDetailsEntity"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxSplitResultEntity"}}}, "title": "CapPaymentTemplate CapPaymentAllocationEntity", "description": "Entity for the payment allocation information."}, "CapPaymentTemplate_CapPaymentAllocationLifeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLifeDetailsEntity"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate."}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "proratedGrossBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentAllocationLifeDetailsEntity", "description": "Details of Life LOB allocation."}, "CapPaymentTemplate_CapPaymentAllocationLossInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfo"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplate CapPaymentAllocationLossInfo", "description": "Defines payment allocation loss info details."}, "CapPaymentTemplate_CapPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationLossInfoEntity"}, "asoType": {"type": "string", "description": "Administrative services only type"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "claimType": {"type": "string", "description": "Defines claim type."}, "dateOfLoss": {"type": "string", "format": "date-time"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Defines loss type."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentTemplate CapPaymentAllocationLossInfoEntity", "description": "The attribute to store all info that is needed from Claim for Payments."}, "CapPaymentTemplate_CapPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationPayableItemEntity"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapPaymentTemplate CapPaymentAllocationPayableItemEntity", "description": "Stores details for what it is paid."}, "CapPaymentTemplate_CapPaymentAllocationTemplateAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateAccumulatorDetailsEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCaseNumber": {"type": "string", "description": "Accumulator case number."}, "accumulatorClaimNumber": {"type": "string", "description": "Accumulator claim number."}, "accumulatorCoverage": {"type": "string", "description": "Accumulator coverage. Defines which type of accumulator is consumed."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAccumulatorDetailsExtensionEntity"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateAccumulatorDetailsEntity", "description": "Accumulator details the allocaiton uses."}, "CapPaymentTemplate_CapPaymentAllocationTemplateBeneficiaryDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateBeneficiaryDetailsEntity"}, "beneficiary": {"$ref": "#/definitions/EntityLink"}, "beneficiaryPct": {"type": "number", "description": "Percentage of the benefit amount that the beneficiary receives."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateBeneficiaryDetailsEntity", "description": "Details of the customer the benefit is calcualted for."}, "CapPaymentTemplate_CapPaymentAllocationTemplateDisabilityDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateDisabilityDetailsEntity"}, "allocationPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "allocationProratingRate": {"type": "string", "description": "Defines allocation prorating rate"}, "benefitTypeCd": {"type": "string"}, "calculatedLastWorkDate": {"type": "string", "format": "date-time"}, "frequencyConfiguration": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateFrequencyConfigurationEntity"}, "maxMonthlyBenefitAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "minMonthlyBenefitAmount": {"$ref": "#/definitions/Money"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "monthlyGbaAmount": {"$ref": "#/definitions/Money"}, "partialDisability": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplatePartialDisabilityEntity"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}, "taxablePercentage": {"type": "number", "description": "The percentage of the gross benefit amount that will taxable."}, "tieredGrossBenefitAmount": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateTierGbaEntity"}}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateDisabilityDetailsEntity", "description": "Specific Disability LOB details."}, "CapPaymentTemplate_CapPaymentAllocationTemplateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateEntity"}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateAccumulatorDetailsEntity"}}, "allocationBeneficiaryDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateBeneficiaryDetailsEntity"}, "allocationDisabilityDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateDisabilityDetailsEntity"}, "allocationExGratiaDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateExGratiaDetailsEntity"}, "allocationExpenseDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateExpenseDetailsEntity"}, "allocationInterestDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateInterestDetailsEntity"}, "allocationLifeDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateLifeDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateLossInfoEntity"}, "allocationPayeeDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplatePayeeDetailsEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "coverageCd": {"type": "string", "description": "Coverage code from the policy."}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if the Claim, related to the allocation is the subject to Auto Adjudication."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "manualEOBRemarks": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapManualEOBRemarksEntity"}}, "reserveType": {"type": "string", "description": "Defines if paid via coverage or expense."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateEntity", "description": "Defines payment allocation amount for payment template."}, "CapPaymentTemplate_CapPaymentAllocationTemplateExGratiaDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateExGratiaDetailsEntity"}, "exGratiaAmount": {"$ref": "#/definitions/Money"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia Description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateExGratiaDetailsEntity", "description": "Additional details for allocation with ex gratia reserve type."}, "CapPaymentTemplate_CapPaymentAllocationTemplateExpenseDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateExpenseDetailsEntity"}, "expenseAmount": {"$ref": "#/definitions/Money"}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateExpenseDetailsEntity", "description": "Additional details for allocation with expsene reserve type."}, "CapPaymentTemplate_CapPaymentAllocationTemplateFrequencyConfigurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateFrequencyConfigurationEntity"}, "type": {"type": "string", "description": "Recurrent payments frequency type."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateFrequencyConfigurationEntity", "description": "Defines payment sequence frequency details."}, "CapPaymentTemplate_CapPaymentAllocationTemplateInterestDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateInterestDetailsEntity"}, "interestBeneficiaryState": {"type": "string", "description": "Beneficiary state for interest calculation."}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDecedentState": {"type": "string", "description": "Decedent state for interest calculation."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Interest Paid Up To Date."}, "interestPlanHolderState": {"type": "string", "description": "Policy holder state for interest calculation."}, "interestStateOverrideCd": {"type": "string", "description": "Country state to calculate an interest for."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateInterestDetailsEntity", "description": "Allocation interest details."}, "CapPaymentTemplate_CapPaymentAllocationTemplateLifeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateLifeDetailsEntity"}, "ageReductionAppliedPct": {"type": "number", "description": "Age Reduction Applied Percentage."}, "allocationPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "allocationProratingRate": {"type": "string"}, "beneficiaryPct": {"type": "number", "description": "Beneficiary percentage."}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "benefitCd": {"type": "string", "description": "Benefit Code."}, "coveredPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "frequencyConfiguration": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateFrequencyConfigurationEntity"}, "grossBenefitAmount": {"$ref": "#/definitions/Money"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Date of the incident."}, "initiatePaymentAutomatic": {"type": "boolean", "description": "auto-generated payment determined by claim adjudication rules."}, "isFaceValueOverrided": {"type": "boolean", "description": "Is Face Value Overrided"}, "isRecurringPaid": {"type": "boolean", "description": "Is Recurring Paid"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateLifeDetailsEntity", "description": "Specific Life LOB details."}, "CapPaymentTemplate_CapPaymentAllocationTemplateLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateLossInfoEntity"}, "asoType": {"type": "string", "description": "Administrative services only type"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "claimState": {"type": "string", "description": "Claim State"}, "claimType": {"type": "string", "description": "Defines claim type."}, "dateOfLoss": {"type": "string", "format": "date-time", "description": "Loss Date and Time."}, "lossOriginSource": {"$ref": "#/definitions/EntityLink"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Defines loss type."}, "policySource": {"type": "string", "description": "Link to the related policy"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of loss received date and time."}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateLossInfoEntity", "description": "The attribute to store all required claim loss details."}, "CapPaymentTemplate_CapPaymentAllocationTemplatePartialDisabilityEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplatePartialDisabilityEntity"}, "coveredEarningsAmount": {"$ref": "#/definitions/Money"}, "currentEarningsAmount": {"$ref": "#/definitions/Money"}, "currentEarningsPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "currentEarningsReducedPct": {"type": "number"}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation is for partial disability."}, "maxPartialEarningsPct": {"type": "number", "description": "Defines maximum partial earnings percentage."}, "minPartialEarningsPct": {"type": "number", "description": "Defines minimum partial earnings percentage."}, "partialDisabilityCd": {"type": "string", "description": "Partial disability benefit type."}, "workIncentiveBenefitCd": {"type": "string", "description": "Defines if work incentive benefit is included."}, "workIncentiveBenefitDuration": {"type": "integer", "format": "int64", "description": "Work Incentive benefit duration in months"}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplatePartialDisabilityEntity", "description": "Entity that stores all partial disability information."}, "CapPaymentTemplate_CapPaymentAllocationTemplatePayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplatePayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplatePayeeDetailsEntity", "description": "Defines payee details for payment template."}, "CapPaymentTemplate_CapPaymentAllocationTemplateTierGbaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentAllocationTemplateTierGbaEntity"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "tierDuration": {"type": "integer", "format": "int64"}, "tierLevel": {"type": "integer", "format": "int64"}, "weeklyGbaAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentAllocationTemplateTierGbaEntity", "description": "Tiered benefitd details."}, "CapPaymentTemplate_CapPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentDetailsEntity"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Defines insured last work date prior to payment."}, "payeeDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPayeeDetailsEntity"}, "payeeRoleDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPayeeRoleDetailsEntity"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionEntity"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}, "paymentReductions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionEntity"}}, "paymentTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxEntity"}}, "typicalWorkWeek": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTypicalWorkWeekEntity"}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentDetailsEntity", "description": "Payment details."}, "CapPaymentTemplate_CapPaymentDetailsTemplateEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapPaymentTemplate"}, "_modelType": {"type": "string", "example": "CapPaymentTemplate"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentDetailsTemplateEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "absencePeriods": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAbsencePeriodEntity"}}, "description": {"type": "string", "description": "Indicates text of the payment description."}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "lastWorkDates": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAbsenceLastWorkDateEntity"}}, "paymentAdditionTemplates": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionTemplateEntity"}}, "paymentAllocationTemplates": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationTemplateEntity"}}, "paymentReductionTemplates": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionTemplateEntity"}}, "paymentTaxTemplates": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxTemplateEntity"}}, "typicalWorkWeek": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateTypicalWorkWeekEntity"}, "ytdEarnings": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentDetailsTemplateYTDEarningsEntity"}}}, "title": "CapPaymentTemplate CapPaymentDetailsTemplateEntity", "description": "Object for payment template details."}, "CapPaymentTemplate_CapPaymentDetailsTemplateYTDEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentDetailsTemplateYTDEarningsEntity"}, "year": {"type": "integer", "format": "int64"}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentDetailsTemplateYTDEarningsEntity", "description": "Year to date earnings of the Insured."}, "CapPaymentTemplate_CapPaymentFinancialAdjustmentPayableSourcesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentFinancialAdjustmentPayableSourcesEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to the related policy."}}, "title": "CapPaymentTemplate CapPaymentFinancialAdjustmentPayableSourcesEntity", "description": "Identifies sources for which financial adjustment is applied"}, "CapPaymentTemplate_CapPaymentReductionDetailsDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type."}, "deductionBeneficiary": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "Defines the Deduction precentage."}, "deductionProvider": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "deductionType": {"type": "string", "description": "Deduction type."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "Is a Deduction applied before taxes."}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}}, "title": "CapPaymentTemplate CapPaymentReductionDetailsDeductionEntity", "description": "Deduction details are described in this entity."}, "CapPaymentTemplate_CapPaymentReductionDetailsIndebtednessEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsIndebtednessEntity"}, "indebtedness": {"type": "number", "description": "Indebtedness."}}, "title": "CapPaymentTemplate CapPaymentReductionDetailsIndebtednessEntity", "description": "Indebtedness details are described in this entity."}, "CapPaymentTemplate_CapPaymentReductionDetailsOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsOffsetEntity"}, "isAutoOffset": {"type": "boolean"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "Defines the prorating rate of the Offset."}, "offsetTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "offsetType": {"type": "string"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentReductionDetailsOffsetEntity", "description": "Offset details are described in this entity."}, "CapPaymentTemplate_CapPaymentReductionDetailsWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionDetailsWithholdingEntity"}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentReductionDetailsWithholdingEntity", "description": "Withholding details are described in this entity."}, "CapPaymentTemplate_CapPaymentReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionEntity"}, "paymentReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionDetailsDeductionEntity"}, "paymentReductionDetailsIndebtedness": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionDetailsIndebtednessEntity"}, "paymentReductionDetailsOffset": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionDetailsOffsetEntity"}, "paymentReductionDetailsWithholding": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionDetailsWithholdingEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}}, "title": "CapPaymentTemplate CapPaymentReductionEntity", "description": "Entity for Payment Reduction types (Offsets, Deductions)."}, "CapPaymentTemplate_CapPaymentReductionSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "reductionNumber": {"type": "string", "description": "Number used to link split result to the payment reduction."}}, "title": "CapPaymentTemplate CapPaymentReductionSplitResultEntity", "description": "Defines payment allocation reduction split result."}, "CapPaymentTemplate_CapPaymentReductionTemplateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentReductionTemplateEntity"}, "paymentTemplateReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateReductionDetailsDeductionEntity"}, "paymentTemplateReductionDetailsIndebtedness": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateReductionDetailsIndebtednessEntity"}, "paymentTemplateReductionDetailsOffset": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateReductionDetailsOffsetEntity"}, "paymentTemplateReductionDetailsWithholding": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateReductionDetailsWithholdingEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}}, "title": "CapPaymentTemplate CapPaymentReductionTemplateEntity", "description": "Entity for Payment Reduction types (Offsets, Deductions, Withholding)."}, "CapPaymentTemplate_CapPaymentScheduleExGratiasInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleExGratiasInput"}, "exGratiaAmount": {"$ref": "#/definitions/Money"}, "exGratiaDescription": {"type": "string", "description": "Ex gratia Description."}, "exGratiaNumber": {"type": "string", "description": "Generated ex gratia number."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "manualEOBRemarks": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapManualEOBRemarksInput"}}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplate CapPaymentScheduleExGratiasInput", "description": "Additional details for allocation with ex gratia reserve type."}, "CapPaymentTemplate_CapPaymentScheduleExpensesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleExpensesInput"}, "expenseAmount": {"$ref": "#/definitions/Money"}, "expenseDescription": {"type": "string", "description": "Expense Description."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "manualEOBRemarks": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapManualEOBRemarksInput"}}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplate CapPaymentScheduleExpensesInput", "description": "Additional details for allocation with expsene reserve type."}, "CapPaymentTemplate_CapPaymentScheduleSettlementFrequencyConfigInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementFrequencyConfigInput"}, "type": {"type": "string", "description": "Recurrent payments frequency type."}}, "title": "CapPaymentTemplate CapPaymentScheduleSettlementFrequencyConfigInput", "description": "Defines payment sequence frequency details."}, "CapPaymentTemplate_CapPaymentScheduleSettlementInfoInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementInfoInput"}, "allocationPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "frequencyConfiguration": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleSettlementFrequencyConfigInput"}, "interestDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleSettlementInterestsInput"}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "manualEOBRemarks": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapManualEOBRemarksInput"}}, "partialDisability": {"$ref": "#/definitions/CapPaymentTemplate_PartialDisabilityInput"}, "payeeDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleSettlementPayeeInput"}, "uri": {"type": "string", "description": "URI or the settlement used for retreiving financial data"}}, "title": "CapPaymentTemplate CapPaymentScheduleSettlementInfoInput"}, "CapPaymentTemplate_CapPaymentScheduleSettlementInterestsInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementInterestsInput"}, "interestBeneficiaryState": {"type": "string", "description": "Beneficiary state for interest calculation."}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDecedentState": {"type": "string", "description": "Decedent state for interest calculation."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Interest Paid Up To Date."}, "interestPlanHolderState": {"type": "string", "description": "Policy holder state for interest calculation."}, "interestStateOverrideCd": {"type": "string", "description": "Country state to calculate an interest for."}}, "title": "CapPaymentTemplate CapPaymentScheduleSettlementInterestsInput", "description": "Allocation interest details."}, "CapPaymentTemplate_CapPaymentScheduleSettlementPayeeInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleSettlementPayeeInput"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeCheckAddressId": {"type": "string", "description": "Payee check address Id."}, "payeePaymentMethodId": {"type": "string", "description": "Payee preferred payment method Id."}, "provider": {"$ref": "#/definitions/EntityLink"}, "representBeneficiary": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPaymentTemplate CapPaymentScheduleSettlementPayeeInput", "description": "Defines payee details for payment template."}, "CapPaymentTemplate_CapPaymentScheduleWithholdingsInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentScheduleWithholdingsInput"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentScheduleWithholdingsInput"}, "CapPaymentTemplate_CapPaymentTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxPercentageASO": {"type": "number"}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxW4Percentage": {"type": "number", "description": "Defines Federal tax percentage calculated according to the W4 parameters."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "federalTaxableWages": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapPaymentTemplate CapPaymentTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "CapPaymentTemplate_CapPaymentTaxDetailsFicaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsFicaEntity"}, "ficaRate": {"type": "number"}, "ficaTaxableWages": {"$ref": "#/definitions/Money"}, "ficaType": {"type": "string", "description": "Defines the type of FICA."}}, "title": "CapPaymentTemplate CapPaymentTaxDetailsFicaEntity", "description": "Defines Fica tax details."}, "CapPaymentTemplate_CapPaymentTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "stateTaxableWages": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentTaxDetailsStateEntity", "description": "Defines State tax details."}, "CapPaymentTemplate_CapPaymentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxEntity"}, "paymentTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxDetailsFederalEntity"}, "paymentTaxDetailsFica": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxDetailsFicaEntity"}, "paymentTaxDetailsState": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxDetailsStateEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}}, "title": "CapPaymentTemplate CapPaymentTaxEntity", "description": "Entity for Payment Tax types."}, "CapPaymentTemplate_CapPaymentTaxSplitResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxSplitResultEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "taxNumber": {"type": "string", "description": "Number used to link split result to the payment tax."}}, "title": "CapPaymentTemplate CapPaymentTaxSplitResultEntity", "description": "Defines payment allocation addition split result."}, "CapPaymentTemplate_CapPaymentTaxTemplateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTaxTemplateEntity"}, "paymentTemplateTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateTaxDetailsFederalEntity"}, "paymentTemplateTaxDetailsFica": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateTaxDetailsFicaEntity"}, "paymentTemplateTaxDetailsState": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateTaxDetailsStateEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}}, "title": "CapPaymentTemplate CapPaymentTaxTemplateEntity", "description": "Entity for payment template taxes."}, "CapPaymentTemplate_CapPaymentTemplateAbsenceLastWorkDateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAbsenceLastWorkDateEntity"}, "lastWorkDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentTemplate CapPaymentTemplateAbsenceLastWorkDateEntity", "description": "Insured last date at work before to the absnce."}, "CapPaymentTemplate_CapPaymentTemplateAbsencePeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAbsencePeriodEntity"}, "absencePeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "absenceTypeCd": {"type": "string"}, "actualRtwDate": {"type": "string", "format": "date-time"}, "estimatedRtwDate": {"type": "string", "format": "date-time"}, "intermittentPeriodDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateIntermittentPeriodDetailsEntity"}, "reducedPeriodDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateReducedPeriodDetailsEntity"}}, "title": "CapPaymentTemplate CapPaymentTemplateAbsencePeriodEntity", "description": "Absence period details."}, "CapPaymentTemplate_CapPaymentTemplateActualPaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateActualPaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "masterPaymentDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapMasterPaymentDetailsEntity"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_MessageType"}}, "paymentDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}, "paymentSubType": {"type": "string", "description": "Details of payment Sub Type."}, "paymentUri": {"$ref": "#/definitions/EntityLink"}, "scheduledPaymentNumber": {"type": "string", "description": "Unique payment number within the payment schedule, used to link the actual payment to the scheduled payment during the balance calculation."}, "state": {"type": "string", "description": "Actual payment state."}, "withholdingDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentWithholdingDetailsEntity"}}, "title": "CapPaymentTemplate CapPaymentTemplateActualPaymentEntity", "description": "Actual Payment details."}, "CapPaymentTemplate_CapPaymentTemplateAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAdditionDetailsColaEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines cola consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines cola elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/CapPaymentTemplate_Period"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "CapPaymentTemplate CapPaymentTemplateAdditionDetailsColaEntity", "description": "Entity for cola details."}, "CapPaymentTemplate_CapPaymentTemplateAdditionDetailsInterestEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAdditionDetailsInterestEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestDaysFromDolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOL."}, "interestDaysFromDosReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since DOS."}, "interestDaysFromPolReceivedNumber": {"type": "integer", "format": "int64", "description": "Number of days that has passed since POL was recieved."}, "interestOverrideAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Defines the date up to when interest were paid ."}, "interestRate": {"type": "number", "description": "Defines the rate based on which Interest was calculated."}, "interestState": {"type": "string", "description": "Interest state according to which interest are caculated."}, "interestThresholdAmount": {"$ref": "#/definitions/Money"}, "interestTimeThreshold": {"type": "integer", "format": "int64", "description": "Interest time threshold."}, "isCompound": {"type": "boolean", "description": "Identifies if Compound interest calculation formula was used."}}, "title": "CapPaymentTemplate CapPaymentTemplateAdditionDetailsInterestEntity", "description": "Entity for interest details."}, "CapPaymentTemplate_CapPaymentTemplateAdditionDetailsRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAdditionDetailsRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabBenefitCd": {"type": "string", "description": "Rehab Benefit code taken from policy. Defines if rehab is included or not."}, "rehabBenefitPct": {"type": "number", "description": "Rehab percentage. Defines by how much to increase the AGBA."}, "rehabTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}}, "title": "CapPaymentTemplate CapPaymentTemplateAdditionDetailsRehabEntity", "description": "Entity for rehab details."}, "CapPaymentTemplate_CapPaymentTemplateAdditionDetailsRestoredEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAdditionDetailsRestoredEntity"}, "restoredDeathBenefit": {"type": "number", "description": "Restored Death Benefit."}}, "title": "CapPaymentTemplate CapPaymentTemplateAdditionDetailsRestoredEntity", "description": "Entity for restored details."}, "CapPaymentTemplate_CapPaymentTemplateAdjustmentPaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateAdjustmentPaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_MessageType"}}, "paymentDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}, "state": {"type": "string", "description": "Actual payment state."}}, "title": "CapPaymentTemplate CapPaymentTemplateAdjustmentPaymentEntity", "description": "Balance Adjustment Payment details."}, "CapPaymentTemplate_CapPaymentTemplateEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPaymentTemplate"}, "_modelType": {"type": "string", "example": "CapPaymentTemplate"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPaymentTemplateEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "actualPayments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateActualPaymentEntity"}}, "adjustmentPayments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdjustmentPaymentEntity"}}, "buildPaymentScheduleInput": {"$ref": "#/definitions/CapPaymentTemplate_CapBuildPaymentScheduleInput"}, "creationDate": {"type": "string", "format": "date-time", "description": "The date when the template was created."}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateMessageEntity"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentDetailsTemplate": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentDetailsTemplateEntity"}, "state": {"type": "string", "description": "Payment Template state in the lifecycle."}}, "title": "CapPaymentTemplate CapPaymentTemplateEntity", "description": "Main object for the CAP Payment Template Domain."}, "CapPaymentTemplate_CapPaymentTemplateEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntity"}}, "title": "CapPaymentTemplate_CapPaymentTemplateEntitySuccess"}, "CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentTemplate_CapPaymentTemplateEntitySuccessBody"}, "CapPaymentTemplate_CapPaymentTemplateIntermittentActualAbsencesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateIntermittentActualAbsencesEntity"}, "absenceDate": {"type": "string", "format": "date-time"}, "absenceSeconds": {"type": "integer", "format": "int64"}}, "title": "CapPaymentTemplate CapPaymentTemplateIntermittentActualAbsencesEntity", "description": "Actual absences details during the Intermittent absence."}, "CapPaymentTemplate_CapPaymentTemplateIntermittentPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateIntermittentPeriodDetailsEntity"}, "actualAbsences": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateIntermittentActualAbsencesEntity"}}}, "title": "CapPaymentTemplate CapPaymentTemplateIntermittentPeriodDetailsEntity", "description": "Intermittent absence period details."}, "CapPaymentTemplate_CapPaymentTemplateMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}, "technicalMessage": {"type": "string", "description": "Technical details of the message."}}, "title": "CapPaymentTemplate CapPaymentTemplateMessageEntity", "description": "Payment Template messages."}, "CapPaymentTemplate_CapPaymentTemplateReducedPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateReducedPeriodDetailsEntity"}, "secondsFri": {"type": "integer", "format": "int64"}, "secondsMon": {"type": "integer", "format": "int64"}, "secondsSat": {"type": "integer", "format": "int64"}, "secondsSun": {"type": "integer", "format": "int64"}, "secondsThu": {"type": "integer", "format": "int64"}, "secondsTue": {"type": "integer", "format": "int64"}, "secondsWed": {"type": "integer", "format": "int64"}}, "title": "CapPaymentTemplate CapPaymentTemplateReducedPeriodDetailsEntity", "description": "Reduced absence period details."}, "CapPaymentTemplate_CapPaymentTemplateReductionDetailsDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateReductionDetailsDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type."}, "deductionBeneficiary": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "Defines the Deduction precentage."}, "deductionProvider": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "deductionType": {"type": "string", "description": "Deduction type."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "Is a Deduction applied before taxes."}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}}, "title": "CapPaymentTemplate CapPaymentTemplateReductionDetailsDeductionEntity", "description": "Entity for deduction details."}, "CapPaymentTemplate_CapPaymentTemplateReductionDetailsIndebtednessEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateReductionDetailsIndebtednessEntity"}, "indebtedness": {"type": "number", "description": "Indebtedness."}}, "title": "CapPaymentTemplate CapPaymentTemplateReductionDetailsIndebtednessEntity", "description": "Entity for indebtedness details."}, "CapPaymentTemplate_CapPaymentTemplateReductionDetailsOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateReductionDetailsOffsetEntity"}, "isAutoOffset": {"type": "boolean"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "Defines the prorating rate of the Offset."}, "offsetTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "offsetType": {"type": "string"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentTemplateReductionDetailsOffsetEntity", "description": "Entity for offset details."}, "CapPaymentTemplate_CapPaymentTemplateReductionDetailsWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateReductionDetailsWithholdingEntity"}, "payableSources": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentFinancialAdjustmentPayableSourcesEntity"}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentTemplateReductionDetailsWithholdingEntity", "description": "Entity for withholding details."}, "CapPaymentTemplate_CapPaymentTemplateTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxPercentageASO": {"type": "number"}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxW4Percentage": {"type": "number", "description": "Defines Federal tax percentage calculated according to the W4 parameters."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "federalTaxableWages": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapPaymentTemplate CapPaymentTemplateTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "CapPaymentTemplate_CapPaymentTemplateTaxDetailsFicaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateTaxDetailsFicaEntity"}, "ficaRate": {"type": "number"}, "ficaTaxableWages": {"$ref": "#/definitions/Money"}, "ficaType": {"type": "string", "description": "Defines the type of FICA."}}, "title": "CapPaymentTemplate CapPaymentTemplateTaxDetailsFicaEntity", "description": "Defines FICA tax details."}, "CapPaymentTemplate_CapPaymentTemplateTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/CapPaymentTemplate_Term"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "stateTaxableWages": {"$ref": "#/definitions/Money"}}, "title": "CapPaymentTemplate CapPaymentTemplateTaxDetailsStateEntity", "description": "Defines State tax details."}, "CapPaymentTemplate_CapPaymentTemplateTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTemplateTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapPaymentTemplate CapPaymentTemplateTypicalWorkWeekEntity", "description": "Insured typical work week details."}, "CapPaymentTemplate_CapPaymentTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapPaymentTemplate CapPaymentTypicalWorkWeekEntity"}, "CapPaymentTemplate_CapPaymentWithholdingAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAdditionEntity"}, "additionNumber": {"type": "string", "description": "Unique number to link with split results."}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type."}}, "title": "CapPaymentTemplate CapPaymentWithholdingAdditionEntity", "description": "Entity for the withholding additions."}, "CapPaymentTemplate_CapPaymentWithholdingAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingAllocationEntity"}, "additionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAdditionSplitResultEntity"}}, "allocationAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAccumulatorDetailsEntity"}}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationBalanceAdjustmentEntity"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationDeductionDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationLossInfo"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "reductionSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionSplitResultEntity"}}, "reserveType": {"type": "string", "description": "Allocation reserve type."}, "taxSplitResults": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxSplitResultEntity"}}}, "title": "CapPaymentTemplate CapPaymentWithholdingAllocationEntity", "description": "Entity for the withholding allocation information."}, "CapPaymentTemplate_CapPaymentWithholdingDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingDetailsEntity"}, "withholdingAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentWithholdingAdditionEntity"}}, "withholdingAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentWithholdingAllocationEntity"}}, "withholdingReductions": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentWithholdingReductionEntity"}}, "withholdingTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentWithholdingTaxEntity"}}}, "title": "CapPaymentTemplate CapPaymentWithholdingDetailsEntity", "description": "Entity for the withholding details."}, "CapPaymentTemplate_CapPaymentWithholdingReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingReductionEntity"}, "reductionNumber": {"type": "string", "description": "Unique number to link with split results."}, "reductionSource": {"$ref": "#/definitions/EntityLink"}, "reductionType": {"type": "string", "description": "Reduction type."}, "withholdingReductionDetailsDeduction": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentReductionDetailsDeductionEntity"}}, "title": "CapPaymentTemplate CapPaymentWithholdingReductionEntity", "description": "Entity for the withholding reductions."}, "CapPaymentTemplate_CapPaymentWithholdingTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPaymentWithholdingTaxEntity"}, "taxNumber": {"type": "string", "description": "Unique number to link with split results."}, "taxSource": {"$ref": "#/definitions/EntityLink"}, "taxType": {"type": "string", "description": "Tax type."}, "withholdingTaxDetailsFederal": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxDetailsFederalEntity"}, "withholdingTaxDetailsFica": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxDetailsFicaEntity"}, "withholdingTaxDetailsState": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTaxDetailsStateEntity"}}, "title": "CapPaymentTemplate CapPaymentWithholdingTaxEntity", "description": "Entity for the withholding taxes."}, "CapPaymentTemplate_LoadPaymentTemplateResponse": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "LoadPaymentTemplateResponse"}, "count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateEntity"}}}, "title": "CapPaymentTemplate LoadPaymentTemplateResponse"}, "CapPaymentTemplate_LoadPaymentTemplateResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPaymentTemplate_LoadPaymentTemplateResponse"}}, "title": "CapPaymentTemplate_LoadPaymentTemplateResponseSuccess"}, "CapPaymentTemplate_LoadPaymentTemplateResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPaymentTemplate_LoadPaymentTemplateResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPaymentTemplate_LoadPaymentTemplateResponseSuccessBody"}, "CapPaymentTemplate_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapPaymentTemplate MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapPaymentTemplate_PartialDisabilityInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PartialDisabilityInput"}, "currentEarningsAmount": {"$ref": "#/definitions/Money"}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation is for partial disability."}}, "title": "CapPaymentTemplate PartialDisabilityInput"}, "CapPaymentTemplate_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentTemplate Period"}, "CapPaymentTemplate_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapPaymentTemplate Term"}, "CapRefBuildPaymentScheduleInput": {"properties": {"description": {"type": "string"}, "exGratias": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleExGratiasInput"}}, "expenses": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleExpensesInput"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "settlements": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleSettlementInfoInput"}}, "withholdings": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentScheduleWithholdingsInput"}}}, "title": "CapRefBuildPaymentScheduleInput"}, "CapRefBuildPaymentScheduleInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRefBuildPaymentScheduleInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRefBuildPaymentScheduleInputBody"}, "CapRefPaymentTemplateInitInput": {"required": ["entity"], "properties": {"actualPayments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateActualPaymentEntity"}}, "adjustmentPayments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdjustmentPaymentEntity"}}, "buildPaymentScheduleInput": {"$ref": "#/definitions/CapPaymentTemplate_CapBuildPaymentScheduleInput"}, "entity": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentDetailsTemplateEntity"}, "originSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapRefPaymentTemplateInitInput"}, "CapRefPaymentTemplateInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRefPaymentTemplateInitInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRefPaymentTemplateInitInputBody"}, "CapRefPaymentTemplateMessagesInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateMessageEntity"}}}, "title": "CapRefPaymentTemplateMessagesInput"}, "CapRefPaymentTemplateMessagesInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRefPaymentTemplateMessagesInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRefPaymentTemplateMessagesInputBody"}, "CapRefPaymentTemplateUpdateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "_version": {"type": "string"}, "actualPayments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateActualPaymentEntity"}}, "adjustmentPayments": {"type": "array", "items": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentTemplateAdjustmentPaymentEntity"}}, "buildPaymentScheduleInput": {"$ref": "#/definitions/CapPaymentTemplate_CapBuildPaymentScheduleInput"}, "entity": {"$ref": "#/definitions/CapPaymentTemplate_CapPaymentDetailsTemplateEntity"}}, "title": "CapRefPaymentTemplateUpdateInput"}, "CapRefPaymentTemplateUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRefPaymentTemplateUpdateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRefPaymentTemplateUpdateInputBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}