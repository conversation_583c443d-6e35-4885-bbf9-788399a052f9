{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.premium.waiver.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Premium Waiver Loss API", "version": "1", "title": "CAP Adjuster: Premium Waiver Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-premium-waiver", "description": "CAP Adjuster: Premium Waiver Loss API"}], "paths": {"/losses-premium-waiver/{rootId}/{revisionNo}": {"get": {"summary": "Get premium waive loss by rootId and revisionNo", "x-dxp-path": "/api/caploss/PremiumWaiverLoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-premium-waiver"]}}, "/losses-premium-waiver/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for premium waiver loss", "x-dxp-path": "/api/caploss/PremiumWaiverLoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-premium-waiver"]}}, "/losses-premium-waiver": {"post": {"summary": "Create premium waive loss", "x-dxp-path": "/api/caploss/PremiumWaiverLoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-premium-waiver"]}, "put": {"summary": "Update premium waive loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/PremiumWaiverLoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-premium-waiver"]}}, "/losses-premium-waiver/draft": {"post": {"summary": "Init premium waive loss", "x-dxp-path": "/api/caploss/PremiumWaiverLoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-premium-waiver"]}}}}