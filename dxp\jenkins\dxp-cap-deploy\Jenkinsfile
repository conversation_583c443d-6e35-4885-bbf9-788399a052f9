@Library("ci-config@master")
@Library("cd-library@master") _

def ENVIRONMENTS = k8s.environments()

pipeline {

    parameters {
        choice(name: 'ENVIRONMENT', choices: ENVIRONMENTS, description: 'Environment name to deploy component')
        string(name: 'VERSION', description: 'component version to deploy')
        string(name: 'BRANCH', defaultValue: 'master', description: 'Optional. Component branch name to use for deployment. Default is master.')
        booleanParam(name: 'CLEAN', defaultValue: false, description: '<b style="color:gray">Optional:</b> If set deletes previous deployment before installing new one')
    }

    environment {
        COMPONENT_NAME = "dxp-cap"
        PROPERTIES_FILE_NAME = "dxp/jenkins/dxp-cap-deploy/deployProperties.yaml"
    }

    options {
        skipStagesAfterUnstable()
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        timeout(time: 2, unit: 'HOURS')
    }

    agent { label k8s.getWorkerLabel(params.ENVIRONMENT) }
    
    stages {
        stage('Deploy component') {
            steps {
                script {
                    k8s.deploy(
                            env: params.ENVIRONMENT,
                            name: COMPONENT_NAME,
                            version: params.VERSION,
                            yaml: PROPERTIES_FILE_NAME,
                            clean: params.CLEAN
                    )

                    writeFile(file: "startup.log", text: k8s.logs(env: params.ENVIRONMENT, labels: [component: COMPONENT_NAME]))
                    archiveArtifacts artifacts: "startup.log"
                }
            }
        }
        
    }

    post {
        always {
            script {
                cleanWs()
            }
        }
    } 
}
