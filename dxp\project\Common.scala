import com.typesafe.sbt.packager.archetypes.JavaAppPackaging.autoImport.scriptClasspath
import sbt.Keys._
import sbt._

object Common {

  lazy val dxpCoreVersion = "25.6-RC0" // DXP Core version

  // Common settings
  def projectSettings = {

    Seq(
      organization := "com.eisgroup.dxp",
      scalaVersion := "3.3.3",

      // Setting sbt log level from environment variable (otherwise set "info" level)
      logLevel := Level.withName(setLogLevel(sys.env.getOrElse("LOG_LEVEL", "info").toLowerCase)),

      // Nexus links with artifacts
      resolvers += "DXP Group Sonatype Nexus Repository Manager" at sys.env.getOrElse("DXP_NEXUS_DEPENDENCIES_MVN_REPO_URL", "https://sfoeisgennexus01.exigengroup.com/repository/GENESIS_MVN"),
      credentials += Credentials(Path.userHome / ".ivy2" / ".credentials.genesis"),

      // Enable super shell to display the current tasks in progress
      ThisBuild / useSuperShell := true,

      // Removes Scala version from artifact names
      crossPaths := false,

      // Speed-up dependency resolution for multi-module products
      updateOptions := updateOptions.value.withCachedResolution(true),

      // Turn off "Resolving" log messages that clutter build logs
      ThisBuild / ivyLoggingLevel := UpdateLogging.Quiet,

      // Suppress dependency eviction warnings
      update / evictionWarningOptions := EvictionWarningOptions.default.withWarnTransitiveEvictions(false),
      Global / excludeLintKeys += update / evictionWarningOptions,
      Global / excludeLintKeys += update / ivyLoggingLevel,

      // Filter routes files during "sbt test"
      Test / managedSources := {
        val files: Seq[File] = (Test / managedSources).value
        val filter: FileFilter = "*.routes"
        files.filterNot(file => filter.accept(file))
      },

      // Automatically using "application.local.conf" file when running application locally
      initialize ~= { _ =>
        System.setProperty("config.file", file(".") / "envs" / "application.local.conf" absolutePath)
      },

      // Settings for SBT development options for compilation performance
      javaOptions ++= Seq(
        "-Xmx10G", "-XX:+UseG1GC", "-Dlog.level=" + sys.env.getOrElse("LOG_LEVEL", "TRACE")
      ),

      // Settings for javac options
      Compile / compile / javacOptions ++= Seq(
        "-encoding", "UTF-8",
        "-source", "21",
        "-target", "21",
        "-parameters",
        "-Xlint:unchecked",
        "-Xlint:deprecation"
      ),

      // Fix symbol count problem (<256) for the generated "gateway.bat" (need for Windows script only)
      scriptClasspath := Seq("../conf", "*"),

      // Dependency on core module (with basic functionality)
      libraryDependencies += "com.eisgroup.dxp" % "genesiscore" % Common.dxpCoreVersion
    ) ++
      PlaySettings.playSettings ++
      JacocoPluginSettings.jacocoPluginReportSettings ++
      BuildPluginsSettings.javadocSettings ++
      Release.publishSettings
  }

  def setLogLevel(logLevel: String): String = {
    if (logLevel.equals("off")) {
      return "error"
    } else if (logLevel.equals("trace")) {
      return "debug"
    }
    logLevel;
  }
}
