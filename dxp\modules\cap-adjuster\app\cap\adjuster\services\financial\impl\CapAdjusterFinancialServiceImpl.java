/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial.impl;

import cap.adjuster.services.financial.CapAdjusterFinancialService;
import cap.adjuster.services.financial.converters.CapAdjusterActualPaymentConverter;
import cap.adjuster.services.financial.converters.CapAdjusterSchedulePaymentConverter;
import cap.adjuster.services.financial.dto.CapFinancialPayment;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.GenesisCapEventCaseDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.dto.CapEventCase_CapEventCaseEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.dto.EntityLinkDTO;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.dto.EntityLinkRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.dto.EntityLinkRequestDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.GenesisCapLossSearchDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapPaymentSchedule_CapPaymentScheduleEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapPaymentSchedule_CapScheduledPaymentEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.PaymentDefinition_CapPaymentEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapPaymentEntitySearchEntityResponseV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapPaymentEntitySearchEntityResponseV3SuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapPaymentScheduleEntitySearchEntityResponseV3SuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapLossSearchEntityRequestV3BodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapLossSearchEntityRequestV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapLossSearchQueryDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapLossSearchValueMatcherDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentEntitySearchEntityRequestV3BodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentEntitySearchEntityRequestV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentEntitySearchQueryDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentEntitySearchValueMatcherDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentScheduleEntitySearchEntityRequestV3BodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentScheduleEntitySearchEntityRequestV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentScheduleEntitySearchQueryDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapPaymentScheduleEntitySearchValueMatcherDTO;
import com.eisgroup.dxp.dataproviders.genesiscappaymentschedule.GenesisCapPaymentScheduleDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscappaymentschedule.dto.CapLoadByOriginSourceRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscappaymentschedule.dto.CapLoadByOriginSourceRequestDTO;
import com.eisgroup.dxp.dataproviders.genesiscappaymentschedule.dto.CapPaymentSchedule_LoadPaymentScheduleResponseSuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscappaymentschedule.dto.CapPaymentSchedule_LoadPaymentScheduleResponseSuccessDTO;
import com.google.common.collect.Lists;
import core.services.pagination.SortParam.Direction;
import dataproviders.dto.CapLossDTO;
import genesis.core.utils.GenesisJsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static core.dataproviders.impl.ContextPreservingCompletionStageFactory.completedFuture;
import static java.lang.String.format;

public class CapAdjusterFinancialServiceImpl implements CapAdjusterFinancialService {

    private static final String LOSS_URI = "gentity://CapLoss/%s//%s/%s";
    private static final String CAP_EVENT_CASE_MODEL = "CapEventCase";
    private static final String[] AVAILABLE_PAYMENT_SCHEDULE_STATE = new String[]{"Open", "Active", "Completed", "Suspended", "Processing"};
    private static final String[] ACTUAL_PAYMENT_VARIATIONS = new String[]{"underpayment", "recovery"};
    private static final Integer ACTUAL_PAYMENTS_LIMIT_PER_PAGE = 100;
    private static final String PAYMENT_VARIATION = "payment";
    private static final String PAYMENT_STATE_CANCELED = "Canceled";
    private static final String AUTO_CANCELED_MESSAGE_CD = "BuildPaymentScheduleFlow-CancelActivePayments";
    private static final String PAYMENT_LEVEL_CLAIM = "Claim";
    private static final String PAYMENT_LEVEL_EVENT_CASE = "EventCase";

    private GenesisCapLossSearchDataProvider paymentSearchDataProvider;
    private GenesisCapEventCaseDataProvider caseDataProvider;
    private GenesisJsonUtils genesisJsonUtils;
    private GenesisCapPaymentScheduleDataProvider paymentScheduleDataProvider;

    private CapAdjusterSchedulePaymentConverter<CapPaymentSchedule_CapScheduledPaymentEntityDTO, CapFinancialPayment> schedulePaymentConverter;
    private CapAdjusterActualPaymentConverter<PaymentDefinition_CapPaymentEntityDTO, CapFinancialPayment> actualPaymentConverter;

    @Override
    public CompletionStage<List<CapFinancialPayment>> getPaymentsWithScheduled(String rootId, Integer revisionNo, String modelName) {
        String lossURI = format(LOSS_URI, modelName, rootId, revisionNo);
        CompletionStage<String> paymentLevelCd;
        if (StringUtils.equals(CAP_EVENT_CASE_MODEL, modelName)) {
            paymentLevelCd = getCasePaymentLevelCd(lossURI);
        } else {
            paymentLevelCd = getLossByRootId(rootId)
                    .thenCompose(capGenericLoss -> getCasePaymentLevelCd(capGenericLoss.eventCaseLink.uri));
        }
        return paymentLevelCd.thenCompose(levelCd -> getScheduledPayments(lossURI, modelName, levelCd)
                .thenCombine(getActualPayments(lossURI, modelName, levelCd), this::combinePayments));
    }

    private CompletionStage<String> getCasePaymentLevelCd(String caseUri) {
        return caseDataProvider.apiCaplossCapEventCaseV1LinkPost(prepareLinkRequest(caseUri), null, null, null, null)
                .thenApply(response -> {
                    if (response == null || response.body == null || response.body.success == null) {
                        return PAYMENT_LEVEL_EVENT_CASE;
                    }
                    Object eventCaseObj;
                    if (response.body.success instanceof Collection) {
                        eventCaseObj = new ArrayList<>((Collection<?>) response.body.success).get(0);
                    } else {
                        return PAYMENT_LEVEL_EVENT_CASE;
                    }
                    CapEventCase_CapEventCaseEntityDTO eventCase = genesisJsonUtils.convertObjectToDto(eventCaseObj, CapEventCase_CapEventCaseEntityDTO.class);
                    if (eventCase.applicabilityResult == null || StringUtils.isEmpty(eventCase.applicabilityResult.paymentLevelCd)) {
                        return PAYMENT_LEVEL_EVENT_CASE;
                    }
                    return eventCase.applicabilityResult.paymentLevelCd;
                });
    }

    private CompletionStage<List<CapFinancialPayment>> getScheduledPayments(String lossURI, String modelName, String paymentLevelCd) {
        List<CapFinancialPayment> schedulePayments = Lists.newArrayList();
        return getLatestPaymentSchedule(lossURI, modelName, paymentLevelCd).thenApply(paymentSchedule -> {
            if (paymentSchedule == null) {
                return schedulePayments;
            }
            if (paymentSchedule != null && paymentSchedule.payments != null) {
                List<CapFinancialPayment> convertPayments = paymentSchedule.payments.stream()
                        .map(payment -> {
                            CapFinancialPayment paymentApiDTO = schedulePaymentConverter.convertToApiDTO(payment);
                            paymentApiDTO.state = paymentSchedule.state;
                            if (StringUtils.isEmpty(paymentApiDTO.paymentNumber)) {
                                paymentApiDTO.paymentNumber = paymentSchedule.scheduleNumber;
                            }
                            return paymentApiDTO;
                        })
                        .collect(Collectors.toList());
                schedulePayments.addAll(convertPayments);
            }
            return schedulePayments;
        });
    }

    private CapPaymentSchedule_CapPaymentScheduleEntityDTO convertSearchResponseToPaymentSchedule(ResultCapPaymentScheduleEntitySearchEntityResponseV3SuccessBodyDTO response) {
        return genesisJsonUtils.convertObjectToDto(response.body.success.result.stream().findFirst().orElse(null), CapPaymentSchedule_CapPaymentScheduleEntityDTO.class);
    }

    private CapPaymentSchedule_CapPaymentScheduleEntityDTO convertLoadResponseToPaymentSchedule(CapPaymentSchedule_LoadPaymentScheduleResponseSuccessBodyDTO response) {
        return genesisJsonUtils.convertObjectToDto(response.body.success.result.stream().findFirst().orElse(null), CapPaymentSchedule_CapPaymentScheduleEntityDTO.class);
    }

    private CompletionStage<CapPaymentSchedule_CapPaymentScheduleEntityDTO> getLatestPaymentSchedule(String lossURI, String modelName, String paymentLevelCd) {
        if (!StringUtils.equals(CAP_EVENT_CASE_MODEL, modelName) && !StringUtils.equals(PAYMENT_LEVEL_CLAIM, paymentLevelCd)) {
            return paymentSearchDataProvider.apiCommonSearchV3CapPaymentSchedulePost(preparePaymentScheduleSearchRequest(null, null, lossURI), null, null, 1, null)
                    .thenCombine(paymentSearchDataProvider.apiCommonSearchV3CapPaymentSchedulePost(preparePaymentScheduleSearchRequest(null, lossURI, null), null, null, 1, null), (regularSchedule, expenseSchedule) -> {
                        if (expenseSchedule != null && expenseSchedule.body != null && expenseSchedule.body.success.count > 0) {
                            return convertSearchResponseToPaymentSchedule(expenseSchedule);
                        }
                        return convertSearchResponseToPaymentSchedule(regularSchedule);
                    });
        }
        return paymentScheduleDataProvider.apiCappaymentscheduleCapPaymentScheduleV1EntitiesLoadPaymentSchedulesPost(prepareLoadPaymentScheduleRequest(lossURI), null, 1, null)
                .thenApply(result -> convertLoadResponseToPaymentSchedule(result));
    }

    private CompletionStage<CapLossDTO> getLossByRootId(String rootId) {
        return paymentSearchDataProvider.apiCommonSearchV3CapLossPost(prepareLossSearchRequest(rootId), null, null, 1, null)
                .thenApply(response -> {
                    if (response == null || response.body == null || response.body.success == null) {
                        return new CapLossDTO();
                    }
                    return genesisJsonUtils.convertObjectToDto(response.body.success.result.get(0), CapLossDTO.class);
                });
    }

    private CompletionStage<List<CapFinancialPayment>> getActualPayments(String lossURI, String modelName, String paymentLevelCd) {
        if (!StringUtils.equals(CAP_EVENT_CASE_MODEL, modelName) && !StringUtils.equals(PAYMENT_LEVEL_CLAIM, paymentLevelCd)) {
            return searchAllActualPayments(prepareActualPaymentSearchRequest(null, null, lossURI))
                    .thenCombine(getActualExpensePayments(lossURI), (regularPayments, expensePayments) -> {
                        List<CapFinancialPayment> result = Lists.newArrayList();
                        result.addAll(convertActualPaymentsResult(regularPayments));
                        result.addAll(convertActualPaymentsResult(expensePayments));
                        return result;
                    });
        }
        SearchCapPaymentEntitySearchEntityRequestV3BodyDTO request = prepareActualPaymentSearchRequest(lossURI, null, null);
        return searchAllActualPayments(request)
                .thenApply(result -> convertActualPaymentsResult(result));
    }

    /**
     * Convert actual payments result
     *
     * @param result
     * @return
     */
    private List<CapFinancialPayment> convertActualPaymentsResult(ResultCapPaymentEntitySearchEntityResponseV3DTO result) {
        if (result == null || CollectionUtils.isEmpty(result.result)) {
            return Lists.newArrayList();
        }
        return result.result
                .stream()
                .map(payment -> genesisJsonUtils.convertObjectToDto(payment, PaymentDefinition_CapPaymentEntityDTO.class))
                .filter(actualPayment -> Arrays.asList(ACTUAL_PAYMENT_VARIATIONS).contains(actualPayment._variation)
                        || (StringUtils.equals(PAYMENT_VARIATION, actualPayment._variation)
                        && !checkAutoCanceledActualPayment(actualPayment)))
                .map(actualPaymentConverter::convertToApiDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get actual expense payments
     *
     * @param lossURI
     * @return
     */
    private CompletionStage<ResultCapPaymentEntitySearchEntityResponseV3DTO> getActualExpensePayments(String lossURI) {
        SearchCapPaymentEntitySearchEntityRequestV3BodyDTO request = prepareActualPaymentSearchRequest(null, lossURI,
                null);
        return searchAllActualPayments(request);
    }

    /**
     * Check if actual payment is auto canceled during payment rescheduling
     *
     * @param actualPayment
     * @return true if actual payment is auto canceled
     */
    private boolean checkAutoCanceledActualPayment(PaymentDefinition_CapPaymentEntityDTO actualPayment) {
        if (!StringUtils.equals(PAYMENT_STATE_CANCELED, actualPayment.state) || CollectionUtils.isEmpty(actualPayment.messages)) {
            return false;
        }
        return actualPayment.messages.stream()
                .anyMatch(message -> StringUtils.equals(message.code, AUTO_CANCELED_MESSAGE_CD));
    }

    /**
     * Search All actual payment without pagination
     *
     * @param request
     * @return
     */
    private CompletionStage<ResultCapPaymentEntitySearchEntityResponseV3DTO> searchAllActualPayments(SearchCapPaymentEntitySearchEntityRequestV3BodyDTO request) {
        return paymentSearchDataProvider.apiCommonSearchV3CapPaymentPost(request, null, null, ACTUAL_PAYMENTS_LIMIT_PER_PAGE, null)
                .thenCompose(result -> {
                    if (result == null || result.body == null) {
                        return completedFuture(null);
                    }
                    if (result.body.success.count == 0 || result.body.success.count <= result.body.success.result.size()) {
                        return completedFuture(result.body.success);
                    }
                    Integer pageCount = Math.toIntExact(result.body.success.count / result.body.success.result.size());
                    if (Math.toIntExact(result.body.success.count % result.body.success.result.size()) > 0) {
                        pageCount++;
                    }

                    List<CompletableFuture<ResultCapPaymentEntitySearchEntityResponseV3SuccessBodyDTO>> futuresList = Lists.newArrayList();
                    for (int i = 1; i < pageCount; i++) {
                        futuresList.add(paymentSearchDataProvider.apiCommonSearchV3CapPaymentPost(request, null, null, ACTUAL_PAYMENTS_LIMIT_PER_PAGE, i * ACTUAL_PAYMENTS_LIMIT_PER_PAGE).toCompletableFuture());
                    }
                    return CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[futuresList.size()]))
                            .thenApply(v -> futuresList.stream().map(future -> future.join()).collect(Collectors.toList()))
                            .thenApply(resultList -> {
                                result.body.success.result.addAll(resultList.stream()
                                        .flatMap(response -> response.body.success.result.stream())
                                        .collect(Collectors.toList()));
                                return result.body.success;
                            });
                });
    }

    /**
     * Combine scheduled and actual payments.
     * If matched actual payment found from scheduled payments, then only return actual payment.
     * Else if scheduled payment state is Active, then return scheduled payment with Pending state
     *
     * @param scheduledPayments
     * @param actualPayments
     * @return
     */
    private List<CapFinancialPayment> combinePayments(List<CapFinancialPayment> scheduledPayments, List<CapFinancialPayment> actualPayments) {
        List<CapFinancialPayment> incomingSchedulePayments = Lists.newArrayList();
        scheduledPayments.forEach(scheduledPayment -> {
            CapFinancialPayment matchedActualPayment = actualPayments.stream()
                    .filter(actualPayment -> StringUtils.equals(scheduledPayment.paymentNumber, actualPayment.paymentNumber) ||
                            (StringUtils.equals(scheduledPayment.payee, actualPayment.payee)
                                    && actualPayment.paymentDate != null
                                    && scheduledPayment.paymentDate != null
                                    && scheduledPayment.paymentDate.isEqual(actualPayment.paymentDate)))
                    .findFirst().orElse(null);
            if (matchedActualPayment == null && Arrays.asList(AVAILABLE_PAYMENT_SCHEDULE_STATE).contains(scheduledPayment.state)) {
                scheduledPayment.state = "Pending";
                incomingSchedulePayments.add(scheduledPayment);
            }
        });
        actualPayments.addAll(incomingSchedulePayments);
        return actualPayments;
    }

    private CapLoadByOriginSourceRequestBodyDTO prepareLoadPaymentScheduleRequest(String originSource) {
        CapLoadByOriginSourceRequestBodyDTO request = new CapLoadByOriginSourceRequestBodyDTO();
        request.body = new CapLoadByOriginSourceRequestDTO();
        request.body.originSourceUris = Lists.newArrayList(originSource);
        return request;
    }

    private SearchCapPaymentScheduleEntitySearchEntityRequestV3BodyDTO preparePaymentScheduleSearchRequest(String originSource, String lossSource, String claimSource) {
        SearchCapPaymentScheduleEntitySearchEntityRequestV3BodyDTO request = new SearchCapPaymentScheduleEntitySearchEntityRequestV3BodyDTO();
        request.body = new SearchCapPaymentScheduleEntitySearchEntityRequestV3DTO();
        request.body.query = new SearchCapPaymentScheduleEntitySearchQueryDTO();
        if (StringUtils.isNotEmpty(originSource)) {
            request.body.query.originSource = new SearchCapPaymentScheduleEntitySearchValueMatcherDTO();
            request.body.query.originSource.matches = new ArrayList<>();
            request.body.query.originSource.matches.add(originSource);
        }
        if (StringUtils.isNotEmpty(lossSource)) {
            request.body.query.lossSource = new SearchCapPaymentScheduleEntitySearchValueMatcherDTO();
            request.body.query.lossSource.matches = new ArrayList<>();
            request.body.query.lossSource.matches.add(lossSource);
        }
        if (StringUtils.isNotEmpty(claimSource)) {
            request.body.query.claimSource = new SearchCapPaymentScheduleEntitySearchValueMatcherDTO();
            request.body.query.claimSource.matches = new ArrayList<>();
            request.body.query.claimSource.matches.add(claimSource);
        }
        request.body.resolveEntity = true;
        request.body.sorting = new ArrayList<>();
        Map<String, Object> creationDateSort = new HashMap<>();
        creationDateSort.put("creationDate", Direction.DESC.name());
        request.body.sorting.add(creationDateSort);
        return request;
    }

    private SearchCapLossSearchEntityRequestV3BodyDTO prepareLossSearchRequest(String rootId) {
        SearchCapLossSearchEntityRequestV3BodyDTO request = new SearchCapLossSearchEntityRequestV3BodyDTO();
        request.body = new SearchCapLossSearchEntityRequestV3DTO();
        request.body.query = new SearchCapLossSearchQueryDTO();
        request.body.query.rootId = new SearchCapLossSearchValueMatcherDTO();
        request.body.query.rootId.matches = List.of(rootId);
        return request;
    }

    private EntityLinkRequestBodyDTO prepareLinkRequest(String uri) {
        EntityLinkRequestBodyDTO request = new EntityLinkRequestBodyDTO();
        EntityLinkDTO linkDTO = new EntityLinkDTO();
        linkDTO._uri = uri;
        request.body = new EntityLinkRequestDTO();
        request.body.links = List.of(linkDTO);
        return request;
    }

    private SearchCapPaymentEntitySearchEntityRequestV3BodyDTO prepareActualPaymentSearchRequest(String originSource, String lossSource, String claimSource) {
        SearchCapPaymentEntitySearchEntityRequestV3BodyDTO request = new SearchCapPaymentEntitySearchEntityRequestV3BodyDTO();
        request.body = new SearchCapPaymentEntitySearchEntityRequestV3DTO();
        request.body.query = new SearchCapPaymentEntitySearchQueryDTO();
        if (StringUtils.isNotEmpty(originSource)) {
            request.body.query.originSource = new SearchCapPaymentEntitySearchValueMatcherDTO();
            request.body.query.originSource.matches = new ArrayList<>();
            request.body.query.originSource.matches.add(originSource);
        }
        if (StringUtils.isNotEmpty(lossSource)) {
            request.body.query.lossSource = new SearchCapPaymentEntitySearchValueMatcherDTO();
            request.body.query.lossSource.matches = new ArrayList<>();
            request.body.query.lossSource.matches.add(lossSource);
        }
        if (StringUtils.isNotEmpty(claimSource)) {
            request.body.query.claimSource = new SearchCapPaymentEntitySearchValueMatcherDTO();
            request.body.query.claimSource.matches = new ArrayList<>();
            request.body.query.claimSource.matches.add(claimSource);
        }
        request.body.resolveEntity = true;
        return request;
    }

    @Inject
    public void setPaymentScheduleDataProvider(GenesisCapPaymentScheduleDataProvider paymentScheduleDataProvider) {
        this.paymentScheduleDataProvider = paymentScheduleDataProvider;
    }

    @Inject
    public void setPaymentSearchDataProvider(GenesisCapLossSearchDataProvider paymentSearchDataProvider) {
        this.paymentSearchDataProvider = paymentSearchDataProvider;
    }

    @Inject
    public void setGenesisJsonUtils(GenesisJsonUtils genesisJsonUtils) {
        this.genesisJsonUtils = genesisJsonUtils;
    }

    @Inject
    public void setSchedulePaymentConverter(CapAdjusterSchedulePaymentConverter schedulePaymentConverter) {
        this.schedulePaymentConverter = schedulePaymentConverter;
    }

    @Inject
    public void setActualPaymentConverter(CapAdjusterActualPaymentConverter actualPaymentConverter) {
        this.actualPaymentConverter = actualPaymentConverter;
    }

    @Inject
    public void setCaseDataProvider(GenesisCapEventCaseDataProvider caseDataProvider) {
        this.caseDataProvider = caseDataProvider;
    }
}
