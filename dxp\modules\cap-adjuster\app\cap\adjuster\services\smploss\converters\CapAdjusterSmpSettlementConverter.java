/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.smploss.converters;

import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.smploss.dto.CapSmpSettlement;
import dataproviders.dto.CapSmpSettlementDTO;
import genesis.core.utils.GenesisJsonUtils;

import java.util.Map;
import javax.inject.Inject;

public class CapAdjusterSmpSettlementConverter<I extends CapSmpSettlementDTO, A extends CapSmpSettlement>
        extends GenesisRootApiModelConverter<I, A> {

    private GenesisJsonUtils genesisJsonUtils;

    @SuppressWarnings("unchecked")
    private Map<String, Object> convertToMap(Object object) {
        return object != null ? genesisJsonUtils.convertObjectToMap(object) : null;
    }

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.policy = convertToMap(intDTO.policy);
        apiDTO.policyId = intDTO.policyId;
        apiDTO.settlementDetail = convertToMap(intDTO.settlementDetail);
        apiDTO.settlementNumber = intDTO.settlementNumber;
        apiDTO.settlementResult = convertToMap(intDTO.settlementResult);
        apiDTO.settlementType = intDTO.settlementType;
        apiDTO.state = intDTO.state;
        apiDTO.settlementAbsenceInfo = convertToMap(intDTO.settlementAbsenceInfo);
        apiDTO.settlementLossInfo = convertToMap(intDTO.settlementLossInfo);
        return apiDTO;
    }

    @Inject
    public void setGenesisJsonUtils(GenesisJsonUtils genesisJsonUtils) {
        this.genesisJsonUtils = genesisJsonUtils;
    }
}
