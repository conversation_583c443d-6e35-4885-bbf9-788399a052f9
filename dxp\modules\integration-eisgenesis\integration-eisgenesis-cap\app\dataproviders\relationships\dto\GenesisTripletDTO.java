/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.relationships.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import core.dataproviders.dto.InternalDTO;

@JsonInclude(Include.NON_NULL)
public class GenesisTripletDTO implements InternalDTO {

    private String subject;
    private String predicate;
    private String object;

    private GenesisTripletDTO subjectRef;

    public GenesisTripletDTO(String predicate, String object, GenesisTripletDTO subjectRef) {
        this(null, predicate, object);
        this.subjectRef = subjectRef;
    }

    public GenesisTripletDTO(String subject, String predicate, String object) {
        this.subject = subject;
        this.predicate = predicate;
        this.object = object;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getPredicate() {
        return predicate;
    }

    public void setPredicate(String predicate) {
        this.predicate = predicate;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public GenesisTripletDTO getSubjectRef() {
        return subjectRef;
    }

    public void setSubjectRef(GenesisTripletDTO subjectRef) {
        this.subjectRef = subjectRef;
    }
}

