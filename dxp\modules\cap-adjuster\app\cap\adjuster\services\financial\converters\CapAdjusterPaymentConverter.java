/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial.converters;

import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.financial.dto.CapPayment;
import dataproviders.dto.CapPaymentDTO;

public class CapAdjusterPaymentConverter<I extends CapPaymentDTO, A extends CapPayment>
        extends GenesisRootApiModelConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.paymentAmount = intDTO.paymentAmount;
        apiDTO.paymentDetails = intDTO.paymentDetails;
        apiDTO.creationDate = intDTO.creationDate;
        apiDTO.direction = intDTO.direction;
        apiDTO.variation = intDTO.variation;
        apiDTO.state = intDTO.state;
        apiDTO.paymentNumber = intDTO.paymentNumber;
        return apiDTO;
    }
}
