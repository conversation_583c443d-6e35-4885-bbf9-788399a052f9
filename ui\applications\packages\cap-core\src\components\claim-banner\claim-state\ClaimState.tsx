/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {useTranslate} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {AntBadge} from '@eisgroup/ui-kit'
import * as React from 'react'
import {
    CLAIM_STATUS,
    CLAIM_SUBSTATUS,
    STATUS_MARKER_CLOSED,
    STATUS_MARKER_INCOMPLETE,
    STATUS_MARKER_OPEN,
    STATUS_MARKER_PENDING
} from '../../../common/package-class-names'
import FC = React.FunctionComponent

export interface ClaimStatusProps {
    /**
     * Claim status.
     */
    readonly claimStatus?: string
    readonly showClaimSubStatus?: boolean
    readonly claimSubStatusCd?: string
    readonly reasonCd?: string
    readonly subStatusLookupName?: string
}

const claimStatusMarkerClasses = new Map<string, string>([
    ['Incomplete', STATUS_MARKER_INCOMPLETE],
    ['Pending', STATUS_MARKER_PENDING],
    ['Open', STATUS_MARKER_OPEN],
    ['Closed', STATUS_MARKER_CLOSED]
])

export enum ClaimStateCd {
    INCOMPLETE = 'Incomplete',
    PENDING = 'Pending',
    OPEN = 'Open',
    CLOSED = 'Closed'
}
export enum ClaimStatesColor {
    OPEN = '#57ad68',
    PENDING = '#ecae3d',
    CLOSED = '#096eb6',
    INCOMPLETE = '#868e96'
}

export const ClaimState: FC<ClaimStatusProps> = ({
    claimStatus,
    showClaimSubStatus = true,
    claimSubStatusCd,
    reasonCd,
    subStatusLookupName
}) => {
    const {t} = useTranslate()
    const renderLookup = () => {
        if (claimStatus === ClaimStateCd.CLOSED) {
            return <LookupLabel lookup='Reason' code={reasonCd} />
        }
        return claimSubStatusCd ? (
            <LookupLabel lookup={subStatusLookupName || 'LossSubStatus'} code={claimSubStatusCd} />
        ) : (
            t('cap-core:not_available')
        )
    }
    return claimStatus ? (
        <>
            <span className={CLAIM_STATUS}>
                <AntBadge color={ClaimStatesColor[ClaimStateCd[claimStatus]]} text='stateText' />
                <span className={claimStatusMarkerClasses.get(claimStatus)} />

                <LookupLabel
                    lookup='DisabilityClaimStatus'
                    code={claimStatus}
                    emptyLabel={t('cap-core:not_available')}
                />
            </span>
            {showClaimSubStatus ? <div className={CLAIM_SUBSTATUS}>{renderLookup()}</div> : null}
        </>
    ) : null
}
