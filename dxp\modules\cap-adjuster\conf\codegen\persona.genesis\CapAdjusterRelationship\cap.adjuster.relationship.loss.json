{"swagger": "2.0", "x-dxp-spec": {"imports": {"death": {"schema": "integration.cap.relationship.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Relationship API", "version": "1", "title": "CAP Adjuster: Relationship API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/relationship", "description": "CAP Adjuster: Relationship API"}], "paths": {"/relationship": {"post": {"summary": "Initialize and create the relationship", "x-dxp-method": "post", "x-dxp-path": "/api/caprelationship/CapRelationship/v1/command/createCapRelationship", "tags": ["/cap-adjuster/v1/relationship"]}, "put": {"summary": "Update relationship", "x-dxp-method": "post", "x-dxp-path": "/api/caprelationship/CapRelationship/v1/command/updateCapRelationship", "tags": ["/cap-adjuster/v1/relationship"]}}, "/relationship/deleteRelationship": {"post": {"summary": "Delete relationship", "x-dxp-path": "/api/caprelationship/CapRelationship/v1/command/deleteCapRelationship", "tags": ["/cap-adjuster/v1/relationship"]}}, "/relationship/loadRelationships": {"post": {"summary": "Load relationships", "x-dxp-path": "/api/caprelationship/CapRelationship/v1/relationship/loadRelationships", "tags": ["/cap-adjuster/v1/relationship"]}}, "/relationship/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for relationship", "x-dxp-path": "/api/caprelationship/CapRelationship/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/relationship"]}}, "/relationship/rules/bundle": {"post": {"summary": "Rules bundle for relationship", "x-dxp-path": "/api/caprelationship/CapRelationship/v1/rules/bundle", "tags": ["/cap-adjuster/v1/relationship"]}}}}