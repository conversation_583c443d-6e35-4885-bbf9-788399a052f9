import React, {FC} from 'react'
import {observer} from 'mobx-react'
import {t} from '@eisgroup/i18n'
import {CaseSystemService, CSClaimWrapperService} from '@eisgroup/cap-services'
import {FormApi} from '@eisgroup/form'
import {ActionFormBase} from './ActionFormBase'
import {BALANCE_ACTION_CANCEL_EXTERNAL_OVERPAYMENT_FORM_ID, BALANCE_ACTIONS_MAP} from '../../../../common/constants'
import cancelExternalOverPaymentConfig from '../../../../builder/BalanceCancelExternalOverpayment.builder'
import {DrawerFormStateType, ICaseSystem} from '../../../..'

import {BalanceActionDrawerProps} from '../BalanceActionDrawer'

type CancelExternalOverpaymentActionFormProps = BalanceActionDrawerProps<
    ICaseSystem,
    CaseSystemService | CSClaimWrapperService<ICaseSystem>
>

const OVERPAYMENT_SELECT_INPUT_ID = '7e46667f-6823-48af-b451-ef6d9415c517'

export const CancelExternalOverpaymentActionForm: FC<CancelExternalOverpaymentActionFormProps> = observer(props => {
    const {actionKey: key, balanceStore, paymentsStore, closeDrawer, getBalanceChangeLog, getLoadPayments} = props
    const isLoading = balanceStore.actionsStore.isRunning(BALANCE_ACTIONS_MAP.CANCEL_EXTERNAL_OVERPAYMENT)
    const onExternalOverpaymentChange = (
        _values: unknown,
        _props: unknown,
        _eventValue: string,
        _prefix: string,
        formApi: FormApi
    ) => {
        const externalOverpayment = formApi.getState().values.externalOverpayment
        const selectedPayment = paymentsStore.paymentList.find(p => p._key.rootId === externalOverpayment)
        formApi.change('externalBalanceAmount', selectedPayment?.paymentNetAmount)
    }

    const getExternalPaymentsOptions = () => {
        const externalPayments1 = paymentsStore.paymentList.filter(
            e => e._variation === 'externalBalance' && e.state === 'Issued'
        )
        return externalPayments1.map(ex => {
            return {
                displayValue: ex.paymentNumber,
                code: ex._key.rootId
            }
        })
    }

    const onFormConfirm = values => {
        const nextBalanceCount = balanceStore.balanceChangeLogCount + 1
        const externalPayment = paymentsStore.paymentList.find(p => p._key.rootId === values.externalOverpayment)
        const externalPaymentKey = externalPayment._key
        const saveParams = {
            _key: externalPaymentKey,
            cancellationReason: values.comment
        }
        balanceStore.cancelExternalOverpayment(saveParams).subscribe(() => {
            getLoadPayments()
            getBalanceChangeLog(nextBalanceCount)
            closeDrawer()
        })
    }
    const propsCustomizerMap = {
        [OVERPAYMENT_SELECT_INPUT_ID]: (properties, {prefix, prefixIdx}) => ({
            ...properties,
            options: getExternalPaymentsOptions()
        })
    }

    return (
        <ActionFormBase
            uiEngineProps={{
                formId: BALANCE_ACTION_CANCEL_EXTERNAL_OVERPAYMENT_FORM_ID,
                config: cancelExternalOverPaymentConfig,
                initialValues: {
                    action: key
                },
                propsCustomizerMap,
                apiServices: {
                    onOverpaymentChanged: onExternalOverpaymentChange
                },
                onNext: onFormConfirm
            }}
            drawerActionProps={{
                drawerFormState: DrawerFormStateType.Create,
                handleFormCancel: closeDrawer,
                isLoading,
                labels: {
                    createButtonLabel: t('cap-core:balance_actions_drawer_confirm_button')
                }
            }}
        />
    )
})
