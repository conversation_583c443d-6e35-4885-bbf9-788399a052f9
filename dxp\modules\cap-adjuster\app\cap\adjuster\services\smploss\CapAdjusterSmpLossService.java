/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.smploss;

import java.util.List;
import java.util.concurrent.CompletionStage;

import cap.adjuster.services.smploss.dto.CapSmpSettlement;
import com.google.inject.ImplementedBy;

import cap.adjuster.services.smploss.impl.CapAdjusterSmpLossServiceImpl;
import cap.adjuster.services.financial.dto.CapPayment;

/**
 * Service that provides methods for CAP SMP
 */
@ImplementedBy(CapAdjusterSmpLossServiceImpl.class)
public interface CapAdjusterSmpLossService {

    /**
     * Get payments associated with SMP loss
     *
     * @param rootId SMP identifier
     * @param revisionNo SMP revision number
     * @return list of payments related to absence loss
     */
    CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo);

    /**
     * Get settlements associated with SMP loss
     *
     * @param rootId     SMP loss identifier
     * @param revisionNo SMP loss revision number
     * @return SMP loss settlements
     */
    CompletionStage<List<CapSmpSettlement>> getSettlements(String rootId, Integer revisionNo);
}
