resolvers += "SBT Release Group Sonatype Nexus Repository Manager" at "https://repo.scala-sbt.org/scalasbt/sbt-plugin-releases/"
resolvers += "DXP Group Sonatype Nexus Repository Manager" at sys.env.getOrElse("DXP_NEXUS_DEPENDENCIES_MVN_REPO_URL", "https://sfoeisgennexus01.exigengroup.com/repository/GENESIS_MVN")
credentials += Credentials(Path.userHome / ".ivy2" / ".credentials.genesis")

// Used for upgrading "scala-xml" to 2.x version (SBT >1.8.0)
ThisBuild / libraryDependencySchemes += "org.scala-lang.modules" %% "scala-xml" % VersionScheme.Always

// Play Framework plugin
addSbtPlugin("com.typesafe.play" % "sbt-plugin" % "2.9.7")

// Plugin to add build info to a project
addSbtPlugin("com.eed3si9n" % "sbt-buildinfo" % "0.11.0")

// Plugin to check dependency updates
addSbtPlugin("com.timushev.sbt" % "sbt-updates" % "0.6.4")

// Creates tags in VCS and publishes artifacts to nexus repository (See publishSettings in Common.scala)
addSbtPlugin("com.github.sbt" % "sbt-release" % "1.0.15")

// Plugin to provide project dependency tree
addSbtPlugin("net.virtual-void" % "sbt-dependency-graph" % "0.10.0-RC1")

// Jacoco plugin
addSbtPlugin("com.github.sbt" % "sbt-jacoco" % "3.5.0")

// Sonar plugin
addSbtPlugin("com.sonar-scala" % "sbt-sonar" % "2.3.0")

// DXP API Codegen plugin
addSbtPlugin("com.eisgroup.dxp" % "dxp-api-codegen" % "24.16")
