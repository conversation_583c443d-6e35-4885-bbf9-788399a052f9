/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {
    backofficeCensusService,
    backofficeCustomerCommonService,
    backofficeIndCustomerService,
    backofficeOrgCustomerService,
    CensusClass,
    createCustomerUri,
    EmploymentDetails,
    IndividualCustomer,
    IOCustomer,
    OrganizationCustomer,
    SearchByPartyTypeCriteria,
    SearchCustomersRequest
} from '@eisgroup/cap-services'
import {applyDate, applyDateAndDateTime} from '@eisgroup/common'
import {opt} from '@eisgroup/common-types'
import {FetchOptionsResult} from '@eisgroup/form'
import {DataSourceItemType} from '@eisgroup/ui-kit'
import {action, computed, observable, runInAction, toJS} from 'mobx'
import {PreferredContactTypes} from '../../../components/preferred-contact-info/PreferredContactInfo'
import {createIndividualCustomer, getEmployerRootIdFromMainInsured, occupationCategoryValueToCode} from '../../../utils'
import {ORGANIZATION_CUSTOMER_STATE} from '../../constants'
import {EmployeeFormTypes} from '../../Types'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {CustomerStore} from '../CustomerStore'
import {EmploymentStore} from '../EmploymentStore'
import {EventCaseStore} from '../EventCaseStore'
import CapEmployerRole = CapEventCase.CapEmployerRole
import CapEmploymentDetailEntity = CapEventCase.CapEmploymentDetailEntity
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity

export const LOAD_EMPLOYEE = 'loadEmployee'

export class EmploymentStoreImpl extends BaseRootStoreImpl implements EmploymentStore {
    eventCaseStore: EventCaseStore

    customerStore: CustomerStore | undefined

    @observable companySearchResults: OrganizationCustomer[] = []

    @observable selectedCompany?: OrganizationCustomer

    @observable tempSelectedCompany?: OrganizationCustomer

    @observable employee?: IndividualCustomer

    addNewMemberEmployee?: IndividualCustomer

    @observable employeeFormType: EmployeeFormTypes = EmployeeFormTypes.SearchEmployee

    @observable employeeSearchResults: IndividualCustomer[] = []

    @observable censusClasses: CensusClass[] = []

    @observable givenEmployee?: IndividualCustomer

    @observable workDetailVisible: boolean

    @observable isClearWorkDetailAutocompletable = false

    @observable tempEmployee?: IndividualCustomer

    @observable isOrganizationCustomerIntake = false

    constructor(eventCaseStore: EventCaseStore, customerStore?: CustomerStore) {
        super()
        this.eventCaseStore = eventCaseStore
        this.customerStore = customerStore
    }

    @computed
    get eventCase(): any {
        return this.eventCaseStore.eventCase || ({} as CapEventCaseEntity)
    }

    @action
    setEmployee = (employee?: IndividualCustomer) => {
        this.employee = employee
    }

    @action
    searchEmployerByRootId = (rootId: string) => {
        this.promiseCall(() =>
            (this.customerStore
                ? this.customerStore.getOrLoadCustomer<OrganizationCustomer>(rootId)
                : backofficeCustomerCommonService().searchCustomerByRootId(rootId)
            ).then(response => {
                runInAction(() => {
                    this.companySearchResults = [response as OrganizationCustomer]
                    this.selectedCompany = response as OrganizationCustomer
                    this.loadCensusClassForSelectedCompany()
                })
            })
        )
    }

    employerHasGroupSponsor = async (registryTypeId: string) => {
        const searchCriteria = {
            body: {
                registryTypeId: {
                    matches: [registryTypeId]
                },
                groupSponsor: {
                    matches: ['true']
                }
            }
        }

        return backofficeOrgCustomerService()
            .searchByPartyType(searchCriteria)
            .then(response => {
                return !!response.length
            })
            .catch(error => {
                console.error(error)
                return false
            })
    }

    @action
    searchEmployeeByRootIdSubscribe = (rootId: string): Promise<IndividualCustomer> => {
        return this.promiseCall(() =>
            backofficeCustomerCommonService()
                .searchCustomerByRootId(rootId)
                .then(payload => {
                    runInAction(() => {
                        this.employee = payload as IndividualCustomer
                        this.workDetailVisible = true
                    })
                    return payload as IndividualCustomer
                })
        )
    }

    @action
    addEmploymentDetailsToEventCase = (
        eventCase: CapEventCaseEntity = this.eventCase,
        company: OrganizationCustomer | undefined = this.selectedCompany
    ): any => {
        const rootId = company?._key?.rootId || ''
        const customerLink = rootId ? createCustomerUri('ORGANIZATIONCUSTOMER', rootId) : undefined
        const employment =
            this.employee?.participationInfo?.employments?.find(v => v.customer?.link?._uri?.includes(rootId)) ||
            ({} as EmploymentDetails)
        const {originalHireDate, workStateCodeCd} = applyDateAndDateTime(toJS(employment), ['originalHireDate'])
        const {occupationCategory} = toJS(employment)
        const formatHireDate =
            eventCase.lossDetail?.employmentDetail?.hireDate &&
            typeof eventCase.lossDetail?.employmentDetail?.hireDate === 'object'
                ? applyDate({hireDate: eventCase.lossDetail?.employmentDetail?.hireDate}, ['hireDate']).hireDate
                : eventCase.lossDetail?.employmentDetail?.hireDate
        const {employmentDetail} = eventCase.lossDetail!
        return {
            ...eventCase,
            lossDetail: {
                ...eventCase.lossDetail!,
                employmentDetail: {
                    ...CapEventCase.factory.newByType<CapEmploymentDetailEntity>(CapEmploymentDetailEntity),
                    ...employmentDetail,
                    workStateCodeCd: workStateCodeCd ?? employmentDetail?.workStateCodeCd,
                    occupationClassCd:
                        eventCase.lossDetail?.employmentDetail?.occupationClassCd ??
                        this.employee?.employmentDetails?.[0]?.occupationCd,
                    jobClassificationCd:
                        eventCase.lossDetail?.employmentDetail?.jobClassificationCd ??
                        occupationCategoryValueToCode(occupationCategory),
                    hireDate: formatHireDate ?? originalHireDate,
                    employerRole: {
                        ...CapEventCase.factory.newByType<CapEmployerRole>(CapEmployerRole),
                        ...employmentDetail?.employerRole,
                        registryId: customerLink,
                        roleCd: ['Employer']
                    }
                }
            }
        }
    }

    @action
    selectCompany = (company?: OrganizationCustomer) => {
        if (company?.details.legalEntity.legalName && company?.details.legalEntity.registryTypeId) {
            this.selectedCompany = company
            this.eventCaseStore.updateEventCase(this.addEmploymentDetailsToEventCase())
            this.loadCensusClassForSelectedCompany()
        } else {
            this.selectedCompany = undefined
            this.removeSelectedCompany()
        }
    }

    @action
    loadCensusClassForSelectedCompany = () => {
        if (this.selectedCompany) {
            backofficeCensusService()
                .loadAllCensusClassesForCustomer(this.selectedCompany?._key?.rootId || '')
                .then(result =>
                    runInAction(() => {
                        this.censusClasses = result
                    })
                )
        }
    }

    @action
    selectEmployee = (employee?: IndividualCustomer) => {
        this.employee = employee
        if (!this.isOrganizationCustomerIntake) {
            if (employee?.participationInfo?.employments?.length) {
                this.searchEmployerByRootId(getEmployerRootIdFromMainInsured(employee))
                this.eventCaseStore.updateEventCase({
                    ...this.addEmploymentDetailsToEventCase(),
                    memberRegistryTypeId: employee?.details.person.registryTypeId
                })
            } else {
                this.removeSelectedCompany()
            }
        }
        this.workDetailVisible = true
    }

    @action
    searchCompany = (searchCriteria: SearchByPartyTypeCriteria) => {
        this.promiseCall<OrganizationCustomer[]>(() =>
            backofficeOrgCustomerService()
                .searchByPartyType(searchCriteria)
                .then(response => {
                    runInAction(() => {
                        this.companySearchResults = response
                    })
                    return response
                })
        )
    }

    @action
    removeSelectedCompany = () => {
        const {employmentDetail, ...rest} = this.eventCase.lossDetail
        this.eventCaseStore.updateEventCase({
            ...this.eventCase,
            lossDetail: rest
        })
        this.selectedCompany = undefined
        this.censusClasses = []
    }

    @action
    changeEmployeeFormType = (type: EmployeeFormTypes, addMemberEmployeeForm?: IndividualCustomer) => {
        this.employeeFormType = type
        if (type === EmployeeFormTypes.AddEmployee) {
            this.employee = this.addNewMemberEmployee ?? createIndividualCustomer(PreferredContactTypes.PHONE)
            this.tempSelectedCompany = this.selectedCompany
            if (!this.isOrganizationCustomerIntake) {
                this.removeSelectedCompany()
            }
        } else {
            this.addNewMemberEmployee = addMemberEmployeeForm
            this.employee = this.givenEmployee || undefined
            if (!this.isOrganizationCustomerIntake) {
                this.removeSelectedCompany()
            }
            if (this.tempSelectedCompany) {
                this.selectCompany(this.tempSelectedCompany)
            }
        }

        this.workDetailVisible = type === EmployeeFormTypes.AddEmployee || !!this.givenEmployee
    }

    @action
    searchEmployee = (searchCriteria: SearchCustomersRequest) => {
        this.promiseCall<IndividualCustomer[]>(() =>
            backofficeIndCustomerService().searchCustomerByCriteria(searchCriteria)
        ).then(response =>
            runInAction(() => {
                this.employeeSearchResults = response
            })
        )
    }

    @action
    saveUpdateEmployee = (): Promise<IndividualCustomer> => {
        return this.employee ? this.promiseCall(() => this.saveUpdate()) : Promise.resolve({} as IndividualCustomer)
    }

    @action
    searchEmployeeOrEmployerByCustomerNumber = (customerNumber: string, isOrganizationCustomer?: boolean) => {
        this.promiseCall<IOCustomer>(
            () => backofficeCustomerCommonService().searchCustomerByCustomerNumber(customerNumber),
            LOAD_EMPLOYEE
        ).then(r =>
            runInAction(() => {
                if (!isOrganizationCustomer) {
                    this.isOrganizationCustomerIntake = false
                    this.setGivenEmployee(r as IndividualCustomer)
                    this.selectEmployee(r as IndividualCustomer)
                } else {
                    this.isOrganizationCustomerIntake = true
                    this.selectCompany(r as OrganizationCustomer)
                }
                this.workDetailVisible = true
            })
        )
    }

    @action
    searchEmployeeByRegistryTypeId = (registryTypeId: string) => {
        this.promiseCall<IOCustomer[]>(
            () => backofficeCustomerCommonService().searchCustomersByRegistryTypeIds([registryTypeId]),
            LOAD_EMPLOYEE
        ).then(customers => {
            const foundCustomer = customers.find(customer => 'person' in customer.details)
            if (foundCustomer) {
                runInAction(() => {
                    this.setGivenEmployee(foundCustomer as IndividualCustomer)
                    this.employee = foundCustomer as IndividualCustomer
                    this.workDetailVisible = true
                })
            }
        })
    }

    @action
    setAutocompletableSearchClear = (isClear: boolean): void => {
        this.isClearWorkDetailAutocompletable = isClear
    }

    @action
    searchEmployeeByRootId = (rootId: string) => {
        this.promiseCall<IOCustomer>(
            () => backofficeCustomerCommonService().searchCustomerByRootId(rootId),
            LOAD_EMPLOYEE
        ).then(r =>
            runInAction(() => {
                this.employee = r[0]
                this.workDetailVisible = true
            })
        )
    }

    @action
    retrieveEmployeeByRootIds = (rootIds: string[]) => {
        return this.promiseCall<IndividualCustomer[]>(
            () => backofficeCustomerCommonService().searchCustomersByRootId(rootIds) as Promise<IndividualCustomer[]>,
            LOAD_EMPLOYEE
        )
    }

    @action
    setGivenEmployee = (employee?: IndividualCustomer) => {
        this.givenEmployee = employee
    }

    @action
    updateEmployee = (employee?: IndividualCustomer) => {
        this.employee = employee
    }

    private saveUpdate = (): Promise<IndividualCustomer> => {
        const employee = this.employee ? this.employee : ({} as IndividualCustomer)
        return (
            this.employee?.details.person.registryTypeId
                ? backofficeIndCustomerService().updateIndividualCustomer(employee)
                : backofficeIndCustomerService().saveIndividualCustomer(employee)
        ).then(response => {
            runInAction(() => {
                this.employee = response
            })
            return response
        })
    }

    searchCompanyByName = async (searchText: string): Promise<FetchOptionsResult<DataSourceItemType>> => {
        const searchCriteria = {
            limit: 10,
            enablePhoneticSearch: false,
            text: searchText,
            body: {
                state: {
                    matches: [
                        ORGANIZATION_CUSTOMER_STATE.UNQUALIFIED,
                        ORGANIZATION_CUSTOMER_STATE.IN_QUALIFICATION,
                        ORGANIZATION_CUSTOMER_STATE.QUALIFIED,
                        ORGANIZATION_CUSTOMER_STATE.CUSTOMER,
                        ORGANIZATION_CUSTOMER_STATE.ARCHIVED,
                        ORGANIZATION_CUSTOMER_STATE.INVALID,
                        ORGANIZATION_CUSTOMER_STATE.ANONYMIZED
                    ]
                },
                groupSponsor: {
                    matches: ['true']
                }
            }
        }

        const results = await backofficeOrgCustomerService().searchByPartyType(searchCriteria)

        const options = results.flatMap(company => ({
            ...company,
            value: opt(company._key?.rootId).orElse(''),
            text: opt(company.details.legalEntity.legalName).orElse('')
        }))

        return {
            options,
            pathToCode: 'value',
            pathToDisplayValue: 'text'
        }
    }
}
