import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "block",
  "blockId": "PersonDetails",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 9,
            "name": "firstName",
            "label": "cap-core:person_base_detail_first_name",
            "maxLength": 50,
            "disabled": false,
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "disabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:person_base_detail_first_name"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "4530533e-c828-47f5-9293-e4fd938e817c"
        },
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 6,
            "name": "middleName",
            "label": "cap-core:person_base_detail_middle_name",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "operator": "$and",
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "personDetailsDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "5fa69de3-8efd-4f16-a59a-a238074d1fb3"
        },
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 9,
            "name": "lastName",
            "label": "cap-core:person_base_detail_last_name",
            "maxLength": 50,
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "disabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:person_base_detail_last_name"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "c19aa35c-217e-4345-9fdb-b9e9803ef46d"
        }
      ],
      "id": "b4747ca7-a9aa-4bb6-9c6a-86f7018396a4"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "SSN_INPUT",
          "props": {
            "md": 8,
            "name": "taxId",
            "label": "cap-core:persion_base_detail_social_security_number",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "socialSecurityNumber"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              },
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "personDetailsDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": [
                {
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 9
                  },
                  "skipOnEmpty": false,
                  "type": "min-length"
                },
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:persion_base_detail_social_security_number"
                  }
                }
              ]
            }
          },
          "id": "55fc0c11-50ba-46b6-99aa-8b2dbe5f71cc"
        },
        {
          "type": "DATEPICKER_INPUT",
          "props": {
            "md": 8,
            "name": "birthDate",
            "label": "cap-core:date_of_birth_full",
            "dateType": "date-string",
            "valueType": "DATE",
            "timezoneFromProvider": false,
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "disabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "placeholder": "cap-core:default_date_format",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:date_of_birth_full"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "dob_validate"
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "d837598f-980a-4dfe-b564-e9bec7217286"
        },
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 8,
            "name": "genderCd",
            "label": "cap-core:gender",
            "lookupName": "Gender",
            "valueType": "string",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "disabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "placeholder": "cap-core:select_placeholder"
          },
          "id": "0f6067c3-53ca-4d7f-8fda-24c30925a609"
        }
      ],
      "id": "c6a82f62-bcf8-43ca-a79c-d34ae130ff9d"
    }
  ],
  "version": 65
}

export default config;
