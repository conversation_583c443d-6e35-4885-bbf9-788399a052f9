# Default values for dxp-api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
deployStrategy: Recreate
 
image:
  repository: ""
  pullPolicy: Always
  tag: ""

  dxp-cap:
    name: dxp-cap-app
    ports:
      http-app: 9191
      http-jmxjolokia: 8778
      tcp-jacoco: 6300
      
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  istio: false
  useClusterGateway: true # If false namespace gateway will be used
  domain_prefix: ""
  domain: ""
  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: dxp-cap-app # FQDN template: {{ .host }}-{{ $namespace }}.{{ $domain }}
      paths:
        - path: "/"
          serviceName: "dxp-cap-app"
          servicePort: 9191 # service port which will be exposed
  tls:
    enabled: false
    secretName: ""
  tcp: {}

resources:
  dxp-cap:
    limits:
      cpu: "2"
      memory: "2000Mi"
    requests:
      cpu: "0.1"
      memory: "1000Mi"

readinessProbe:
  dxp-cap:
    httpGet:
      path: /core/v1/version
      port: 9191
    failureThreshold: 3
    successThreshold: 1
    timeoutSeconds: 5
    periodSeconds: 10

livenessProbe:
  dxp-cap:
    httpGet:
      path: /core/v1/version
      port: 9191
    failureThreshold: 3
    successThreshold: 1
    timeoutSeconds: 5
    periodSeconds: 10

startupProbe:
  dxp-cap:
    httpGet:
      path: /core/v1/version
      port: 9191
    failureThreshold: 100
    periodSeconds: 5

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

externalSecrets:
  enabled: true

secretsData: {}

jprofiler:
  enabled: false
  capName: ""

secretBackend:
  backendType: ""
  vaultMountPoint: ""
  vaultRole: ""
secretPath: ""

envs:
  LOG_LEVEL: "{{ default \"INFO\" .Values.log_level }}"
  "CORE_SWAGGER_FILTER_PATHS_VISIBLE.0": "/cap-adjuster.*"
  "CORE_SWAGGER_FILTER_PATHS_VISIBLE.1": "/core.*"
  CONFIG_RESOURCE: "application.envs.genesis.conf"
  JAVA_OPTS:
    jacocoagent: -javaagent:/opt/jacocoagent/jacocoagent.jar=port=6300,destfile=jacoco-e2e.exec,output=tcpserver,address=*
    global_java_opts: $(global_java_opts)
    log_format: -Dlog.format=STDOUTJSON
    app_java_opts: "-Xms1024M -Xmx1024M -XX:+UseParallelGC"
  MAX_MEMORY_BUFFER: 50M
  MAX_DISK_BUFFER: 100M

ms_envs: {}
