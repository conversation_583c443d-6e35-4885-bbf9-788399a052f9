/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.specialHandling.impl;

import cap.adjuster.services.eventcase.CapAdjusterEventCasesService;
import cap.adjuster.services.eventcase.dto.CapGenericLoss;
import cap.adjuster.services.specialHandling.CapAdjusterSpecialHandlingService;
import cap.adjuster.services.specialHandling.converters.CapAdjusterGenericSpecialHandlingConverter;
import cap.adjuster.services.specialHandling.dto.CapGenericSpecialHandling;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.dto.CapSpecialHandlingDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static java.lang.String.format;

/**
 * Implement that implements service {@link CapAdjusterSpecialHandlingService}
 */
public class CapAdjusterSpecialHandlingServiceImpl implements CapAdjusterSpecialHandlingService {

    private static final String CAP_SPECIAL_HANDLING = "CapSpecialHandling";
    private static final String PREDICATE = "is/handled";
    private static final String CLAIM_URI = "gentity://CapLoss/%s//%s/%s";

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private CapAdjusterEventCasesService eventCasesService;
    private CapAdjusterGenericSpecialHandlingConverter<CapSpecialHandlingDTO, CapGenericSpecialHandling> genericSpecialHandlingConverter;

    /**
     * {@inheritDoc}
     */
    @Override
    public CompletionStage<List<CapGenericSpecialHandling>> getSpecialHandling(String rootId, Integer revisionNo, String modelName) {
        return getSpecialHandlingAssociatedWithClaimLoss(rootId, revisionNo, modelName)
                .thenApply(specialHandling -> specialHandling.stream()
                        .map(genericSpecialHandlingConverter::convertToApiDTO)
                        .collect(Collectors.toList()));
    }

    @Override
    public CompletionStage<List<CapGenericSpecialHandling>> getAllSpecialHandlingInCase(String rootId, Integer revisionNo) {
        return eventCasesService.getAbsenceLosses(rootId, revisionNo)
                .thenCombine(eventCasesService.getLifeClaims(rootId, revisionNo), (absences, claims) -> {
                    List<CapGenericLoss> list = new ArrayList();
                    list.addAll(absences);
                    list.addAll(claims);
                    return list;
                })
                .thenApply(losses -> losses.stream()
                        .flatMap(loss -> getSpecialHandlingAssociatedWithClaimLoss(loss.key.rootId, loss.key.revisionNo, loss.modelName)
                                .thenApply(specialHandling -> specialHandling
                                        .stream()
                                        .map(genericSpecialHandlingConverter::convertToApiDTO)
                                        .collect(Collectors.toList()))
                                .toCompletableFuture()
                                .join()
                                .stream())
                        .collect(Collectors.toList()));
    }

    /**
     * Get special handling associated with claim
     *
     * @param rootId     claim identifier
     * @param revisionNo claim revision number
     * @param modelName  model name
     * @return list of special handling related to claim
     */
    private CompletionStage<List<? extends CapSpecialHandlingDTO>> getSpecialHandlingAssociatedWithClaimLoss(String rootId, Integer revisionNo, String modelName) {
        String claimUri = format(CLAIM_URI, modelName, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(CAP_SPECIAL_HANDLING, CapAdjusterSpecialHandlingServiceImpl.PREDICATE, claimUri);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapSpecialHandlingDTO.class);
    }

    @Inject
    public void setSearchRelationshipsDataProvider(GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    public void setGenericSpecialHandlingConverter(
            CapAdjusterGenericSpecialHandlingConverter<CapSpecialHandlingDTO, CapGenericSpecialHandling> genericSpecialHandlingConverter) {
        this.genericSpecialHandlingConverter = genericSpecialHandlingConverter;
    }

    @Inject
    public void setEventCasesService(CapAdjusterEventCasesService eventCasesService) {
        this.eventCasesService = eventCasesService;
    }
}
