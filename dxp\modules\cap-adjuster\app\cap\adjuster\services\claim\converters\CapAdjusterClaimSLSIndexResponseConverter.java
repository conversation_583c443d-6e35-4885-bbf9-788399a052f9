/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.converters;

import javax.inject.Inject;

import cap.adjuster.services.claim.dto.CapAdjusterClaimIndex;
import cap.adjuster.services.claim.dto.CapAdjusterClaimIndexResponse;
import core.services.converters.CommonDTOConverter;
import dataproviders.common.dto.GenesisSearchResponseDTO;
import dataproviders.dto.CapLossDTO;

public class CapAdjusterClaimSLSIndexResponseConverter<I extends GenesisSearchResponseDTO, A extends CapAdjusterClaimIndexResponse>
        extends CommonDTOConverter<I, A> {

    private CapAdjusterClaimSLSIndexEntityConverter<CapLossDTO, CapAdjusterClaimIndex> claimSLSIndexEntityConverter;

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO.count = Long.valueOf(String.valueOf(intDTO.count));
        apiDTO.result = claimSLSIndexEntityConverter.convertToApiDTOs(intDTO.result);
        return apiDTO;
    }

    @Inject
    public void setClaimSLSIndexEntityConverter(
        CapAdjusterClaimSLSIndexEntityConverter<CapLossDTO, CapAdjusterClaimIndex> claimSLSIndexEntityConverter) {
        this.claimSLSIndexEntityConverter = claimSLSIndexEntityConverter;
    }
}
