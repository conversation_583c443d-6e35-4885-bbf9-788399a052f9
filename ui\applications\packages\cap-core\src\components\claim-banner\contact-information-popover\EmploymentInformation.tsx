/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {noop} from '@eisgroup/common'
import {LocalizationUtils} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import * as React from 'react'
import {useState} from 'react'
import {ClaimLossState, dateUtils, IndividualCustomer, OrganizationCustomer} from '@eisgroup/cap-services'
import classNames from 'classnames'
import {SettingEditMedium} from '@eisgroup/ui-kit-icons'
import {CapEventCase, ClaimWrapper} from '@eisgroup/cap-event-case-models'
import {Field, Form, FormApi} from '@eisgroup/form'
import {
    CLAIM_BANNER_MEMBER_OCCUPATION_EDIT,
    CLAIM_POPOVER_EMPLOYMENT_INFO_ROW,
    INPUT_FORM_ROW_3X1
} from '../../../common/package-class-names'
import {LabelValueInfo} from './LabelValueInfo'
import {LossUpdateHelper} from '../ClaimBannerContactInformation'
import {CAP_EVENT_CASE} from '../../../common/constants'
import {isValidValue} from '../../../common/utils'
import {hasAuthorities, Privileges} from '../../../utils/AuthoritiesUtils'
import FC = React.FunctionComponent
import ReactNode = React.ReactNode
import t = LocalizationUtils.translate
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapEmploymentDetailEntity = CapEventCase.CapEmploymentDetailEntity
import CapClaimWrapperEntity = ClaimWrapper.CapClaimWrapperEntity

const {LookupSelect} = Field

export interface EmploymentInformationProps {
    /**
     * Main insured information
     */
    mainInsured?: IndividualCustomer | OrganizationCustomer
    /**
     * Is life product
     */
    isLifeProduct?: boolean
    /**
     * Absence loss or eventCase
     */
    loss?: CapEventCaseEntity | CapClaimWrapperEntity
    /**
     * Loss update helper
     */
    lossHelper?: LossUpdateHelper

    showPreferredPaymentMethodEditor: () => void

    isSubClaim?: boolean
}

export const EmploymentInformation: FC<EmploymentInformationProps> = (props: EmploymentInformationProps) => {
    const [isOccupationClassEdit, setIsOccupationClassEdit] = useState(false)
    const {mainInsured, loss} = props
    const createInfo = (label: string, info: string | ReactNode): ReactNode => (
        <LabelValueInfo key={label} label={label}>
            {info}
        </LabelValueInfo>
    )

    const createInfoWithACheck = (
        label: string,
        info: string | ReactNode,
        testValue?: any,
        defaultValue?: string
    ): ReactNode => {
        if (!isValidValue(testValue)) {
            info = defaultValue || null
        }
        return (
            <LabelValueInfo key={label} label={label}>
                {info}
            </LabelValueInfo>
        )
    }
    const handleEdit = (): void => {
        setIsOccupationClassEdit(true)
    }
    const insuredEmploymentDetails = (mainInsured as IndividualCustomer)?.employmentDetails
        ? (mainInsured as IndividualCustomer)?.employmentDetails?.[0]
        : undefined
    const eventCaseEmploymentDetails = loss?.lossDetail?.employmentDetail as CapEmploymentDetailEntity
    const occupationClassHandleChange = (form: FormApi<CapEventCaseEntity>, value: any): void => {
        form.change('loss.lossDetail.employmentDetail.jobClassificationCd', value)
        const formLoss = form.getState().values.loss
        const saveEventCase = {
            ...formLoss,
            lossDetail: {
                ...formLoss.lossDetail,
                employmentDetail: {
                    ...formLoss.lossDetail.employmentDetail,
                    _type: 'CapEmploymentDetailEntity'
                }
            }
        }
        props.lossHelper?.updateEventCase!(saveEventCase).subscribe(() => setIsOccupationClassEdit(false))
    }
    const occupationClassEdit = (): ReactNode => {
        return (
            <div>
                <Form<CapEventCaseEntity> onSubmit={noop} initialValues={{loss}}>
                    {({form}) => {
                        return (
                            <LookupSelect
                                name='loss.lossDetail.employmentDetail.jobClassificationCd'
                                lookupName='JobClassificationCd'
                                onSelect={value => occupationClassHandleChange(form as FormApi, value)}
                            />
                        )
                    }}
                </Form>
            </div>
        )
    }

    const updateCasePrivilegeWithEditPen = () => {
        return hasAuthorities([Privileges.EVENT_CASE_UPDATE_CASE]) && props.loss?.state !== ClaimLossState.Closed ? (
            <SettingEditMedium className={CLAIM_BANNER_MEMBER_OCCUPATION_EDIT} onClick={handleEdit} />
        ) : null
    }

    const renderEmploymentInfo = (): ReactNode[] => {
        const customerNumberTitle = props.isSubClaim
            ? t('cap-core:claim_subject_member_id_custom_label')
            : t('cap-core:claim_subject_member_id_label')
        const employmentInfoAreas: ReactNode[] = [
            // birth date
            createInfoWithACheck(
                t('cap-core:claim_subject_info_dob'),
                dateUtils((mainInsured as IndividualCustomer)?.details?.person.birthDate).render,
                (mainInsured as IndividualCustomer)?.details?.person?.birthDate
            ),
            // customer number
            createInfoWithACheck(customerNumberTitle, mainInsured?.customerNumber, mainInsured?.customerNumber)
        ]
        if (loss?._modelName === CAP_EVENT_CASE) {
            employmentInfoAreas.push([
                createInfoWithACheck(
                    t('cap-core:claim_subject_job_title_label'),
                    <LookupLabel lookup='JobTitle' code={insuredEmploymentDetails?.jobTitleCd} />,
                    insuredEmploymentDetails?.jobTitleCd,
                    t('cap-core:not_available').toUpperCase()
                ),
                createInfoWithACheck(
                    t('cap-core:claim_subject_occupation_label'),
                    <LookupLabel lookup='Occupation' code={eventCaseEmploymentDetails?.occupationClassCd} />,
                    eventCaseEmploymentDetails?.occupationClassCd
                ),
                createInfoWithACheck(
                    t('cap-core:claim_subject_work_state_label'),
                    <LookupLabel lookup='StateProv' code={eventCaseEmploymentDetails?.workStateCodeCd} />,
                    eventCaseEmploymentDetails?.workStateCodeCd
                )
            ])
        }

        if (props.isLifeProduct && loss?._modelName === CAP_EVENT_CASE) {
            employmentInfoAreas.push([
                eventCaseEmploymentDetails?.jobClassificationCd
                    ? createInfo(
                          t('cap-core:claim_subject_occupation_class_label'),
                          isOccupationClassEdit ? (
                              <span>{occupationClassEdit()}</span>
                          ) : (
                              <>
                                  <span>
                                      <LookupLabel
                                          lookup='JobClassificationCd'
                                          code={eventCaseEmploymentDetails?.jobClassificationCd}
                                      />
                                  </span>
                                  {updateCasePrivilegeWithEditPen()}
                              </>
                          )
                      )
                    : createInfo(
                          t('cap-core:claim_subject_occupation_class_label'),
                          isOccupationClassEdit ? (
                              <span>{occupationClassEdit()}</span>
                          ) : (
                              updateCasePrivilegeWithEditPen()
                          )
                      )
            ])
        }

        return employmentInfoAreas
    }

    return (
        <section>
            <div className={classNames(CLAIM_POPOVER_EMPLOYMENT_INFO_ROW, INPUT_FORM_ROW_3X1)}>
                {renderEmploymentInfo()}
            </div>
        </section>
    )
}
