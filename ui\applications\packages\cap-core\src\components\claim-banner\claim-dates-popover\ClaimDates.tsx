/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {opt} from '@eisgroup/common-types'
import {LocalizationUtils} from '@eisgroup/i18n'
import * as React from 'react'
import {ClaimLoss, dateUtils} from '@eisgroup/cap-services'
import {
    CLAIM_DATES_POPOVER,
    POPOVER_CLAIM_DATES_LABEL,
    POPOVER_CLAIM_DATES_TEXT
} from '../../../common/package-class-names'

import FC = React.FunctionComponent
import t = LocalizationUtils.translate

export interface ClaimDatesProps {
    readonly claim: ClaimLoss
}

interface ClaimDatesSection {
    readonly label: string
    readonly date: string
}

export const ClaimDates: FC<ClaimDatesProps> = (props: ClaimDatesProps) => {
    const {claim} = props

    const claimDateSections: ClaimDatesSection[] = [
        {
            label: t('cap-core:claim_dates_actively_at_work_label'),
            date: opt(claim.lossDetail?.activelyAtWorkDate)
                .map(d => dateUtils(d).render)
                .orElse('')
        },
        {
            label: t('cap-core:claim_dates_last_worked_label'),
            date: opt(claim.lossDetail?.lastWorkDate)
                .map(d => dateUtils(d).render)
                .orElse('')
        },
        {
            label: t('cap-core:claim_dates_proof_of_loss_received'),
            date: opt(claim.lossDetail?.proofReceivedDate)
                .map(d => dateUtils(d).render)
                .orElse('')
        },
        {
            label: t('cap-core:rtw_estimatedPTRTW_label'),
            date: opt(claim.lossDetail?.returnToWorkDateDetail?.estimatedPTRTW)
                .map(d => dateUtils(d).render)
                .orElse('')
        },
        {
            label: t('cap-core:rtw_actualPTRTW_label'),
            date: opt(claim.lossDetail?.returnToWorkDateDetail.actualPTRTW)
                .map(d => dateUtils(d).render)
                .orElse('')
        },
        {
            label: t('cap-core:rtw_estimatedFTRTW_label'),
            date: opt(claim.lossDetail?.returnToWorkDateDetail?.estimatedFTRTW)
                .map(d => dateUtils(d).render)
                .orElse('')
        },
        {
            label: t('cap-core:rtw_actualFTRTW_label'),
            date: opt(claim.lossDetail?.returnToWorkDateDetail.actualFTRTW)
                .map(d => dateUtils(d).render)
                .orElse('')
        }
    ]

    const renderDates = (claimDatesSection: ClaimDatesSection, idx: number): React.ReactNode => {
        const {label, date} = claimDatesSection
        return (
            <section key={label}>
                <label className={POPOVER_CLAIM_DATES_LABEL}>{label}</label>
                <p className={POPOVER_CLAIM_DATES_TEXT}>{date}</p>
            </section>
        )
    }

    return (
        <div className={CLAIM_DATES_POPOVER}>
            {claimDateSections.filter(v => v.date).map((v, idx) => renderDates(v, idx))}
        </div>
    )
}
