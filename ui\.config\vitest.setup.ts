import {cleanup} from '@testing-library/react'
import {afterEach, vi} from 'vitest'
import '@testing-library/jest-dom'

import {Localization} from '@eisgroup/i18n'
import {IoC} from '@eisgroup/ioc'

mockLocalizationProvider()
suppressMobXWarnings()
suppressComponentWillReceivePropsWarning()
suppresssWitchScrollingEffectTest()

afterEach(() => {
    cleanup()
})

function mockLocalizationProvider() {
    const mockEnLocale: Localization.Locale = {
        country: 'US',
        language: 'en'
    }

    IoC.get<Localization.LocalizerProvider>(Localization.TYPES.LocalizerProvider).initLocalizer({
        supportedLocales: [mockEnLocale],
        currentLocale: mockEnLocale,
        resources: [
            {
                locale: mockEnLocale,
                ns: 'cap',
                resources: {}
            }
        ]
    })
}

function suppressMobXWarnings() {
    const originalWarn = console.warn
    vi.spyOn(console, 'warn').mockImplementation(message => {
        if (message.startsWith('The MobX package does not have a default export.')) {
            return undefined
        }
        return originalWarn(message)
    })
}

function suppressComponentWillReceivePropsWarning() {
    const originalWarn = console.warn
    vi.spyOn(console, 'warn').mockImplementation(message => {
        if (message.includes('componentWillReceiveProps')) {
            return undefined
        }
        return originalWarn(message)
    })
}

function suppresssWitchScrollingEffectTest() {
    const originalError = console.error
    vi.spyOn(console, 'error').mockImplementation((...args) => {
        const message = args.join(' ')
        if (message.includes('switchScrollingEffect')) {
            return undefined
        }
        return originalError(...args)
    })
}
