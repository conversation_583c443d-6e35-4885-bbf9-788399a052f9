/**
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import React from 'react'
import {Link} from 'react-router'
import {environment} from '@eisgroup/environment'
import {opt} from '@eisgroup/common-types'
import {t} from '@eisgroup/i18n'
import {ColumnProps, Tooltip} from '@eisgroup/ui-kit'
import {DisabilityClaimTypesMap, P_AND_C_CLAIM_TYPE_OPTIONS, DENTAL_CLAIM_TYPE_OPTIONS} from '../../common/constants'
import {CaseStates, ClaimStates} from '../../common/Types'
import {PREFIX} from '../../common/package-class-names'
import {SubjectOfClaimInfo} from './components/subject-of-claim-info/SubjectOfClaimInfo'

const CASES_CLAIMS_TABLE_NUMBER_HEADER = `${PREFIX}-cases-claims-table-number-header`
const CASES_CLAIMS_TABLE_STATE_HEADER = `${PREFIX}-cases-claims-table-state-header`
const CASES_CLAIMS_TABLE_CLAIM_TYPE_HEADER = `${PREFIX}-cases-claims-table-claim-type-header`
const CASES_CLAIMS_TABLE_CLAIM_DATE_HEADER = `${PREFIX}-cases-claims-table-claim-date-header`
const CASES_CLAIMS_TABLE_POLICY_NUMBER_HEADER = `${PREFIX}-cases-claims-table-policy-number-header`
const CASES_CLAIMS_TABLE_SUBJECT_OF_CLAIM_HEADER = `${PREFIX}-cases-claims-table-subject-of-claim-header`
const CASES_CLAIMS_TABLE_TOOLTIP = `${PREFIX}-cases-claims-table-tooltip`

export const createColumns = (claimTypeList, caseClaimList, filters): ColumnProps<any>[] => {
    const splitArray = (arr, subGroupLength: number) => {
        const result = [] as string[][]
        for (let i = 0; i < arr.length; i += subGroupLength) {
            result.push(arr.slice(i, i + subGroupLength))
        }
        return result
    }
    const PAndCClaimTypes = P_AND_C_CLAIM_TYPE_OPTIONS.map(item => item.code)

    const buildClaimsClassicUrl = (claimNumber: string) => {
        const eisCoreUrl = opt(environment)
            .map(v => v.dxp)
            .map(v => v.eisCore)
            .orElse('')
        return `${eisCoreUrl}/flow?_flowId=cf-claims-entry-flow&claimNumber=${claimNumber}`
    }

    return [
        {
            title: t('cap-core:cases-claims-table-columns-case-claim-number'),
            key: 'caseClaimNumber',
            dataIndex: 'caseClaimNumber',
            className: CASES_CLAIMS_TABLE_NUMBER_HEADER,
            sorter: true,
            render: (text, record, index) => {
                let url = ''
                const {rootId, revisionNo} = record._key
                if (record.claimNumber) {
                    if (PAndCClaimTypes.includes(record.claimType)) {
                        url = buildClaimsClassicUrl(record.claimNumber)
                        url = record.claimType !== 'dental' ? url : ''
                        return <a href={url}>{record.claimNumber || record.caseNumber}</a>
                    }
                    if (Object.keys(DisabilityClaimTypesMap).includes(record.claimType)) {
                        url = `/${record.claimModelName}/${rootId}/${revisionNo}`
                    } else if (record.claimType === DENTAL_CLAIM_TYPE_OPTIONS[0]?.code) {
                        if (record.state === ClaimStates.INCOMPLETE) {
                            url = `/dental/intake/${rootId}/${revisionNo}`
                        } else {
                            url = `/dental/${rootId}/${revisionNo}`
                        }
                    } else {
                        url = `/claim/claim-overview/${rootId}/${revisionNo}`
                    }
                } else if (record.state === CaseStates.INCOMPLETE) {
                    url = `/event-case/intake?rootId=${rootId}&revisionNo=${revisionNo}`
                } else {
                    url = `/CapEventCase/${rootId}/${revisionNo}`
                }
                const label = record.claimNumber || record.caseNumber

                return <Link to={url}>{label}</Link>
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-status'),
            key: 'state',
            dataIndex: 'state',
            className: CASES_CLAIMS_TABLE_STATE_HEADER,
            render: (text, record) => {
                return text ? t(`cap-core:claim_state:${text.toLowerCase()}`) : '-'
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-claim-type'),
            key: 'claimType',
            dataIndex: 'claimType',
            className: CASES_CLAIMS_TABLE_CLAIM_TYPE_HEADER,
            filters: claimTypeList,
            render: (text, record) => {
                return claimTypeList.filter(item => item.value === text)[0]?.text
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-date-of-loss'),
            key: 'claimDate',
            dataIndex: 'claimDate',
            className: CASES_CLAIMS_TABLE_CLAIM_DATE_HEADER,
            sorter: true,
            render: (text, record, index) => {
                if (record.claimModelName === 'CapEventCase') {
                    return '-'
                }
                return opt(record.claimDate)
                    .map(d => dateUtils(d).toString())
                    .orElse('-')
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-policy-number'),
            key: 'policyNumber',
            dataIndex: 'policyNumber',
            className: CASES_CLAIMS_TABLE_POLICY_NUMBER_HEADER,
            render: (text, record, index) => {
                const policyNumbers = record.policies?.map(policy => policy.policyNumber)
                if (Array.isArray(policyNumbers) && record.claimModelName !== 'CapEventCase') {
                    const splitPolicyNumbers = splitArray(policyNumbers, 4)
                    return (
                        <div style={{whiteSpace: 'pre-line'}}>
                            {splitPolicyNumbers.map(itemArr => itemArr.join(', ')).join(`, \n `)}
                        </div>
                    )
                }
                return '-'
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-subject-of-claim'),
            key: 'subjectOfClaim',
            dataIndex: 'subjectOfClaim',
            className: CASES_CLAIMS_TABLE_SUBJECT_OF_CLAIM_HEADER,
            render: (text, record, index) => {
                const isPAndCClaim = PAndCClaimTypes.includes(record.claimType)
                if (isPAndCClaim) {
                    const contentList = [
                        ...new Set(record.subjects?.filter(v => v.subject)?.map(item => item.subject) || [])
                    ]
                    if (contentList.length > 1) {
                        return (
                            <Tooltip
                                placement='topLeft'
                                title={contentList.join('\n')}
                                mouseEnterDelay={1}
                                maxRows={100}
                                overlayClassName={CASES_CLAIMS_TABLE_TOOLTIP}
                            >
                                <span>{`${contentList[0]}...`}</span>
                            </Tooltip>
                        )
                    }
                    return <>{contentList?.[0] || '-'}</>
                }
                return <SubjectOfClaimInfo customerInfo={record.subjectOfClaimInfo} />
            }
        }
    ]
}
