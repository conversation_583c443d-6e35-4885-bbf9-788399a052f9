/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {LocalizationUtils} from '@eisgroup/i18n'
import React, {useCallback, useState, useMemo, useEffect} from 'react'
import {observer} from 'mobx-react'
import {PaymentMethod, PaymentMethodCustomer} from '@eisgroup/common-business-components'
import {Field, OnChange} from '@eisgroup/form'
import {Button, Row, Col} from '@eisgroup/ui-kit'
import {IndividualCustomer, OrganizationCustomer, CrmAddress} from '@eisgroup/cap-services'
import {CheckAddressTypes, OptionValueWithClassType} from '../../common/Types'
import {ADD_NEW_ADDRESS, ADDRESS_EDIT_BTN, ADDRESS_EDIT_BTN_CONTENT} from '../../common/package-class-names'
import {AddressDrawer} from './AddressDrawer'
import {isInactiveDateRange} from '../../common/utils'
import {required} from '../..'
import t = LocalizationUtils.translate

const {Select} = Field

export interface CheckAddressSectionProps {
    customer: PaymentMethodCustomer
    checkAddressIdOnChange: (value?: string) => void
    checkAddressId?: string
    isPartyCustomer?: boolean
    updateIndividualCustomer?: (customer: IndividualCustomer) => Promise<IndividualCustomer>
    updateOrganizationCustomer?: (customer: OrganizationCustomer) => Promise<OrganizationCustomer>
    isAddressRequired?: boolean
    paymentMethods?: PaymentMethod[]
    // this function is used for addParty, useeffect can be refresh customerValue
    setCustomerWhenSuccess?: (customer: PaymentMethodCustomer) => void
}

export interface CheckAddressSectionState {
    addressDrawerVisible: boolean
    isAddNew: boolean
    customerValue: IndividualCustomer | OrganizationCustomer
    idx?: number
}

export const CheckAddressSection: React.FC<CheckAddressSectionProps> = observer(props => {
    const {
        customer,
        checkAddressId,
        checkAddressIdOnChange,
        updateIndividualCustomer,
        updateOrganizationCustomer,
        isAddressRequired,
        setCustomerWhenSuccess
    } = props
    const [addressDrawerVisible, setAddressDrawerVisible] = useState(false)
    const [isAddNew, setIsAddNew] = useState(false)
    const [idx, setIdx] = useState(0)
    const [customerValue, setCustomerValue] = useState(customer)

    useEffect(() => {
        setCustomerValue(customer)
    }, [customer])

    const getOptions = useMemo(() => {
        const options = [] as OptionValueWithClassType[]
        const optionDisplay = v =>
            `${v.location?.addressLine1} ${v.location?.city}, ${v.location?.stateProvinceCd ?? ''} ${
                v.location?.postalCode
            }`
        const addresses = customerValue?.communicationInfo?.addresses
        addresses?.forEach(v => {
            if (
                v.communicationPreferences &&
                v.communicationPreferences.filter(w => w === 'MailingAddressForCheck').length > 0 &&
                v._key?.id
            ) {
                options.push({
                    code: v._key.id,
                    displayValue: `${CheckAddressTypes.MAILING} - ${optionDisplay(v)}`,
                    classType: 1
                })
            } else if (v.preferred && v._key?.id) {
                options.push({
                    code: v._key.id,
                    displayValue: `${CheckAddressTypes.PREFERRED} - ${optionDisplay(v)}`,
                    classType: 2
                })
            } else if (v.schedulingContactInfo?.temporary && v._key?.id) {
                let displayValue = `${CheckAddressTypes.TEMPORARY} - ${optionDisplay(v)}`
                if (isInactiveDateRange(v.schedulingContactInfo?.effectiveFrom, v.schedulingContactInfo?.effectiveTo)) {
                    displayValue = `${displayValue} (Expired)`
                }
                options.push({
                    code: v._key.id,
                    displayValue,
                    classType: 3
                })
            } else if (v._key?.id) {
                options.push({
                    code: v._key.id,
                    displayValue: optionDisplay(v),
                    classType: 4
                })
            }
        })
        options.sort((a, b) => a.classType - b.classType)
        return options
    }, [customer, customerValue])

    const onAddNewAddress = () => {
        setIsAddNew(true)
        setCustomerValue(prevState => {
            const addresses = prevState.communicationInfo.addresses || []
            const newAddress = {
                _type: 'GenesisCrmAddress',
                location: {
                    _type: 'GenesisLocation'
                },
                schedulingContactInfo: {
                    _type: 'GenesisCrmSchedulingContactInfo'
                }
            }
            addresses.push(newAddress as CrmAddress)
            setIdx(addresses.length > 0 ? addresses.length - 1 : 0)
            return {
                ...prevState,
                communicationInfo: {
                    ...prevState.communicationInfo,
                    addresses
                }
            }
        })
        setAddressDrawerVisible(true)
    }

    const onEditAddress = () => {
        setCustomerValue(prevState => {
            const addresses = prevState?.communicationInfo.addresses || []
            const addressesValle = addresses.map(address => {
                return {
                    ...address,
                    schedulingContactInfo: {
                        ...address.schedulingContactInfo,
                        _type: 'GenesisCrmSchedulingContactInfo'
                    }
                }
            })
            setIdx(prevState.communicationInfo.addresses?.findIndex(v => v._key?.id === checkAddressId) ?? 0)
            return {
                ...prevState,
                communicationInfo: {
                    ...prevState.communicationInfo,
                    addresses: addressesValle
                }
            }
        })
        setAddressDrawerVisible(true)
        setIsAddNew(false)
    }

    const handleChange = (name, value) => {
        if (name === 'checkAddressId') {
            checkAddressIdOnChange(value)
        }
    }

    const onSuccess = useCallback(
        backCustomer => {
            setAddressDrawerVisible(false)
            const addresses = backCustomer.communicationInfo.addresses
            const savedCheckAddressId = addresses?.[idx]?._key.id
            if (setCustomerWhenSuccess) {
                const newCustomer = {
                    ...customerValue,
                    communicationInfo: {
                        ...customerValue.communicationInfo,
                        addresses
                    },
                    _timestamp: backCustomer._timestamp
                }
                setCustomerWhenSuccess(newCustomer)
            }
            checkAddressIdOnChange(savedCheckAddressId)
        },
        [idx]
    )

    const onClose = () => {
        setCustomerValue(prevState => {
            const addresses = prevState.communicationInfo.addresses || []
            const length = addresses.length
            if (isAddNew && length > 0) {
                addresses.splice(length - 1, 1)
            }
            setIdx(prevState.communicationInfo.addresses?.findIndex(v => v._key?.id === checkAddressId) ?? 0)
            return {
                ...prevState,
                communicationInfo: {
                    ...prevState.communicationInfo,
                    addresses
                }
            }
        })
        setAddressDrawerVisible(false)
    }

    const updateCustomerCEM = (savedCustomer): Promise<PaymentMethodCustomer> => {
        if (customer._type === 'IndividualCustomer') {
            return updateIndividualCustomer!(savedCustomer)
        }
        return updateOrganizationCustomer!(savedCustomer)
    }

    const getIdx = useCallback((): number => {
        return idx ?? 0
    }, [idx, customer])

    return (
        <>
            <Row gutter={24}>
                <Col span={21}>
                    <Select
                        label={t('cap-core:address')}
                        options={getOptions}
                        name='checkAddressId'
                        required={isAddressRequired}
                        validate={required('cap-core:address_is_required')}
                    />
                </Col>
                <Col span={3} className={ADDRESS_EDIT_BTN}>
                    <Button
                        className={ADDRESS_EDIT_BTN_CONTENT}
                        type='secondary'
                        icon='setting-edit'
                        disabled={!checkAddressId}
                        onClick={onEditAddress}
                    />
                </Col>
            </Row>
            <Button className={ADD_NEW_ADDRESS} type='link' icon='action-add-medium' onClick={() => onAddNewAddress()}>
                {t('cap-core:add_address_btn')}
            </Button>
            <OnChange>{(name: string, value: any) => handleChange(name, value)}</OnChange>
            <AddressDrawer
                paymentMethods={props.paymentMethods ?? []}
                customer={customerValue}
                idx={getIdx()}
                addressDrawerVisible={addressDrawerVisible}
                onClose={onClose}
                onSuccess={onSuccess}
                formTitle={isAddNew ? t('cap-core:add_new_address_title') : t('cap-core:edit_address_title')}
                updateCustomerCEM={updateCustomerCEM}
            />
        </>
    )
})
