{"swagger": "2.0", "x-dxp-spec": {"imports": {"ltd": {"schema": "integration.cap.ltd.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: LTD Loss API", "version": "1", "title": "CAP Adjuster: LTD Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-ltd", "description": "CAP Adjuster: LTD Loss API"}], "paths": {"/losses-ltd/{rootId}/{revisionNo}": {"get": {"summary": "Search LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/caploss/CapLtd/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/rules/bundle": {"post": {"summary": "Rules bundle for LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/draft": {"post": {"summary": "Init LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd": {"post": {"summary": "Create LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-ltd"]}, "put": {"summary": "Update LTD loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapLtd/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/closeLoss": {"post": {"summary": "Close LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/command/closeLoss", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/reopenLoss": {"post": {"summary": "Reopen LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/command/reopenLoss", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/submitLoss": {"post": {"summary": "Submit LTD loss", "x-dxp-path": "/api/caploss/CapLtd/v1/command/submitLoss", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/setLossSubStatus": {"post": {"summary": "Set LTD loss sub-status", "x-dxp-path": "/api/caploss/CapLtd/v1/command/setLossSubStatus", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/updateReturnToWork": {"post": {"summary": "Updates Return To Work Entity", "x-dxp-path": "/api/caploss/CapLtd/v1/command/updateReturnToWork", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/retrieve-closure-open-items": {"post": {"summary": "Retrieve LTD loss closure open items", "x-dxp-path": "/api/caploss/CapLtd/v1/transformation/absenceClaimClosureToOpenItems", "tags": ["/cap-adjuster/v1/losses-ltd"]}}}}