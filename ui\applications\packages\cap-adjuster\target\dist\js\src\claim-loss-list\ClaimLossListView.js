"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClaimLossListViewLoader = exports.ClaimLossListViewComponent = void 0;
/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
const React = __importStar(require("react"));
const cap_core_1 = require("@eisgroup/cap-core");
const mobx_1 = require("mobx");
const ClaimLossListStore_1 = require("./store/ClaimLossListStore");
class ClaimLossListViewComponent extends React.Component {
    render() {
        return React.createElement(cap_core_1.ClaimLossSearch, { onSearch: this.props.onSearch, data: this.props.data });
    }
}
exports.ClaimLossListViewComponent = ClaimLossListViewComponent;
const ClaimLossListView = ClaimLossListStore_1.connectToStore({
    component: ClaimLossListViewComponent,
    mapStoreToProps: (store, props) => ({
        data: mobx_1.toJS(store.claimLossList),
        onSearch: lossNumber => store.searchClaimLoss(lossNumber)
    })
});
exports.ClaimLossListViewLoader = ClaimLossListStore_1.createViewLoader({
    isLoaded: store => store.actionsStore.isCompleted(ClaimLossListStore_1.LOAD_INITIAL_DATA),
    startLoading: (store, routingData) => {
        store.initLossListStore();
        store.routingStore.setRoutingData(routingData);
    },
    component: ClaimLossListView
});
//# sourceMappingURL=ClaimLossListView.js.map