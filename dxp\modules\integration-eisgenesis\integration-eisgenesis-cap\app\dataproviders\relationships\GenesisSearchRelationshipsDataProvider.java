/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.relationships;

import com.google.inject.ImplementedBy;
import dataproviders.common.dto.GenesisRootDTO;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.relationships.impl.GenesisSearchRelationshipsDataProviderImpl;

import java.util.List;
import java.util.concurrent.CompletionStage;

@ImplementedBy(GenesisSearchRelationshipsDataProviderImpl.class)
public interface GenesisSearchRelationshipsDataProvider<T extends GenesisRootDTO> {

    /**
     * Generic method to get related entities
     *
     * @param tripletDTO search triplet
     * @param responseType type of response
     * @return list of related entities
     */
    CompletionStage<List<T>> getRelationships(GenesisTripletDTO tripletDTO, Class<T> responseType);
}
