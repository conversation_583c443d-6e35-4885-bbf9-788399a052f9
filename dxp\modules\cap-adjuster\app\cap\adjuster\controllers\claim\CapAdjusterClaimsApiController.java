/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.controllers.claim;

import static core.swagger.SwaggerConstants.PARAM_DATATYPE_INTEGER;
import static core.swagger.SwaggerConstants.PARAM_DATATYPE_STRING;
import static core.swagger.SwaggerConstants.PARAM_TYPE_BODY;
import static core.swagger.SwaggerConstants.PARAM_TYPE_QUERY;

import java.util.Optional;
import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequest;
import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequestBody;

import cap.adjuster.services.claim.CapAdjusterClaimsService;
import cap.adjuster.services.claim.dto.CapAdjusterClaimIndexResponse;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import core.services.pagination.PageData;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ResponseHeader;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterClaimsApiController.TAG_API_CAP_ADJUSTER_CLAIMS,
                description = "CAP Adjuster: Claims API")})
@Api(value = CapAdjusterClaimsApiController.TAG_API_CAP_ADJUSTER_CLAIMS,
        tags = CapAdjusterClaimsApiController.TAG_API_CAP_ADJUSTER_CLAIMS)
public class CapAdjusterClaimsApiController extends ApiController {
    protected static final String TAG_API_CAP_ADJUSTER_CLAIMS = "/cap-adjuster/v1/claims";

    private CapAdjusterClaimsService capAdjusterClaimsService;

    @ApiOperation(value = "Search claim indexes with additional data by criteria",
            notes = "Cross claim domain search with intersection",
            response = CapAdjusterClaimIndexResponse.class,
            responseHeaders = {
                    @ResponseHeader(name = "X-Total-Count",
                            response = Long.class,
                            description = "Total number of entries") },
            tags = { TAG_API_CAP_ADJUSTER_CLAIMS })
    @ApiImplicitParams({
            @ApiImplicitParam(
                    dataType = PARAM_DATATYPE_INTEGER,
                    paramType = PARAM_TYPE_QUERY,
                    name = PARAM_OFFSET),
            @ApiImplicitParam(
                    dataType = PARAM_DATATYPE_INTEGER,
                    paramType = PARAM_TYPE_QUERY,
                    name = PARAM_LIMIT),
            @ApiImplicitParam(
                    dataType = PARAM_DATATYPE_STRING,
                    paramType = PARAM_TYPE_QUERY,
                    name = PARAM_FIELDS),
            @ApiImplicitParam(
                    value = "Search criteria",
                    dataType = "com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequest",
                    paramType = PARAM_TYPE_BODY,
                    name = PARAM_TYPE_BODY) })
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> searchClaims() {
        CapAdjusterClaimSearchCapDynamicSearchRequestBody body = new CapAdjusterClaimSearchCapDynamicSearchRequestBody();
        body.addFieldInRequest(PARAM_TYPE_BODY);
        body.body = parseRequestBody(CapAdjusterClaimSearchCapDynamicSearchRequest.class, false);

        String fields = getQueryParam("fields");
        PageData pageData = getPageData();

        return completeOk(capAdjusterClaimsService.searchClaims(body, fields, pageData)
            .thenApply(response -> {
                response().withHeader("X-Total-Count", String.valueOf(Optional.ofNullable(response).map(r -> r.count).orElse(null)));
                return response;
            }));
    }

    /**
     * @deprecated since 24.1
     */
    @ApiOperation(value = "Advanced Search claim indexes with additional data by criteria",
        notes = "claim domain advanced search with intersection",
        response = CapAdjusterClaimIndexResponse.class,
        responseHeaders = {
            @ResponseHeader(name = "X-Total-Count",
                response = Long.class,
                description = "Total number of entries") },
        tags = { TAG_API_CAP_ADJUSTER_CLAIMS })
    @ApiImplicitParams({
        @ApiImplicitParam(
            dataType = PARAM_DATATYPE_INTEGER,
            paramType = PARAM_TYPE_QUERY,
            name = PARAM_OFFSET),
        @ApiImplicitParam(
            dataType = PARAM_DATATYPE_INTEGER,
            paramType = PARAM_TYPE_QUERY,
            name = PARAM_LIMIT),
        @ApiImplicitParam(
            dataType = PARAM_DATATYPE_STRING,
            paramType = PARAM_TYPE_QUERY,
            name = PARAM_FIELDS),
        @ApiImplicitParam(
            value = "Search criteria",
            dataType = "com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequest",
            paramType = PARAM_TYPE_BODY,
            name = PARAM_TYPE_BODY) })
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
            message = HttpStatusDescription.BAD_REQUEST,
            response = GenesisCommonExceptionDTO.class),
        @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
            message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
            response = GenesisCommonExceptionDTO.class)})
    @Deprecated(since = "24.1", forRemoval = true)
    public CompletionStage<Result> advancedSearchClaims(@ApiParam(name = "text", value = "Query for full-text search") @Parameter(name = "text", description = "Query for full-text search") String text) {
        CapAdjusterClaimSearchCapDynamicSearchRequestBody claimSearchRequestBody = new CapAdjusterClaimSearchCapDynamicSearchRequestBody();
        claimSearchRequestBody.addFieldInRequest(PARAM_TYPE_BODY);
        claimSearchRequestBody.body = parseRequestBody(CapAdjusterClaimSearchCapDynamicSearchRequest.class, false);

        String fields = getQueryParam("fields");
        PageData pageData = getPageData();

        return completeOk(capAdjusterClaimsService.advancedSearchClaims(text, claimSearchRequestBody, fields, pageData)
            .thenApply(response -> {
                response().withHeader("X-Total-Count", String.valueOf(Optional.ofNullable(response).map(r -> r.count).orElse(null)));
                return response;
            }));
    }

    @Inject
    public void setCapAdjusterClaimsService(CapAdjusterClaimsService capAdjusterClaimsService) {
        this.capAdjusterClaimsService = capAdjusterClaimsService;
    }
}
