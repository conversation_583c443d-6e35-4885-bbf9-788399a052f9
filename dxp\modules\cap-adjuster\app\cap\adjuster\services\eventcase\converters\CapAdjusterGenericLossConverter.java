/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.eventcase.converters;

import javax.inject.Inject;

import cap.adjuster.services.common.converters.GenesisLinkConverter;
import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.common.dto.GenesisLink;
import cap.adjuster.services.eventcase.dto.CapGenericLoss;
import dataproviders.common.dto.GenesisLinkDTO;
import dataproviders.dto.CapLossDTO;

public class CapAdjusterGenericLossConverter<I extends CapLossDTO, A extends CapGenericLoss>
        extends GenesisRootApiModelConverter<I, A> {

    private GenesisLinkConverter<GenesisLinkDTO, GenesisLink> genesisLinkConverter;

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO = super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.lossDetail = intDTO.lossDetail;
        apiDTO.state = intDTO.state;
        apiDTO.lossSubStatusCd = intDTO.lossSubStatusCd;
        apiDTO.lossNumber = intDTO.lossNumber;
        apiDTO.reasonCd = intDTO.reasonCd;
        apiDTO.reasonDescription = intDTO.reasonDescription;
        apiDTO.lossType = intDTO.lossType;
        apiDTO.absence = genesisLinkConverter.convertToApiDTO(intDTO.absence);
        apiDTO.eventCaseLink = genesisLinkConverter.convertToApiDTO(intDTO.eventCaseLink);
        apiDTO.policyId = intDTO.policyId;
        apiDTO.policy = intDTO.policy;
        apiDTO.claimType = intDTO.claimType;
        apiDTO.eventCaseInfo = intDTO.eventCaseInfo;
        apiDTO.memberRegistryTypeId = intDTO.memberRegistryTypeId;
        apiDTO.coverageType = intDTO.coverageType;
        apiDTO.eligibilityResult = intDTO.eligibilityResult;
        apiDTO.subjectOfClaim = intDTO.subjectOfClaim;
        apiDTO.damageLosses = intDTO.damageLosses;
        return apiDTO;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setGenesisLinkConverter(GenesisLinkConverter genesisLinkConverter) {
        this.genesisLinkConverter = genesisLinkConverter;
    }
}
