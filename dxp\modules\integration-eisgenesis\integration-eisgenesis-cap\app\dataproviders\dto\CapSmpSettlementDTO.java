/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.dto;

import com.eisgroup.dxp.dataproviders.genesiscapsmpsettlement.dto.CapSmpSettlement_CapSMPSettlementPolicyInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapsmpsettlement.dto.CapSmpSettlement_CapSMPSettlementAbsenceInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapsmpsettlement.dto.CapSmpSettlement_CapSMPSettlementDetailEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapsmpsettlement.dto.CapSmpSettlement_CapSMPSettlementLossInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapsmpsettlement.dto.CapSmpSettlement_CapSMPSettlementResultEntityDTO;
import dataproviders.common.dto.GenesisRootDTO;

public class CapSmpSettlementDTO extends GenesisRootDTO {

    public String settlementType;
    public CapSmpSettlement_CapSMPSettlementResultEntityDTO settlementResult;
    public String settlementNumber;
    public CapSmpSettlement_CapSMPSettlementDetailEntityDTO settlementDetail;
    public String policyId;
    public String state;
    public CapSmpSettlement_CapSMPSettlementAbsenceInfoEntityDTO settlementAbsenceInfo;
    public CapSmpSettlement_CapSMPSettlementPolicyInfoEntityDTO policy;
    public CapSmpSettlement_CapSMPSettlementLossInfoEntityDTO settlementLossInfo;
}
