{"swagger": "2.0", "info": {"description": "API for CapLeaveSettlement", "version": "1", "title": "CapLeaveSettlement model API facade"}, "basePath": "/", "schemes": ["http"], "paths": {"/api/capsettlement/CapLeaveSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/approveSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapApproveSettlementRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/closeSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/initApprovalPeriod": {"post": {"description": "The command that creates leave approval period.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementApprovalPeriodInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/initSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRequestLeaveSettlementAdjudicationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/command/updateApprovalPeriod": {"post": {"description": "The command that updates leave period submitted for approval.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementApprovalPeriodInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLeaveSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLeaveSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapLeaveSettlementKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/transformation/CapLeaveSettlementAdjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveSettlementAdjudicationInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/transformation/adjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/transformation/approveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/transformation/disapproveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/transformation/initDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLeaveSettlement/v1/transformation/readjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AdjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputs"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "CapApproveSettlementRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "userId": {"type": "string"}}, "title": "CapApproveSettlementRequest"}, "CapApproveSettlementRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapApproveSettlementRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapApproveSettlementRequestBody"}, "CapLeaveSettlementAdjudicationInputOutputs": {"properties": {"output": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementRulesInput"}}, "title": "CapLeaveSettlementAdjudicationInputOutputs"}, "CapLeaveSettlementAdjudicationInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLeaveSettlementAdjudicationInputOutputs"}}, "title": "CapLeaveSettlementAdjudicationInputOutputsSuccess"}, "CapLeaveSettlementAdjudicationInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLeaveSettlementAdjudicationInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveSettlementAdjudicationInputOutputsSuccessBody"}, "CapLeaveSettlementKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapLeaveSettlementKrakenDeprecatedBundleRequest"}, "CapLeaveSettlementKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLeaveSettlementKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveSettlementKrakenDeprecatedBundleRequestBody"}, "CapLeaveSettlementLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntity"}}}, "title": "CapLeaveSettlementLoadHistoryResult"}, "CapLeaveSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLeaveSettlementLoadHistoryResult"}}, "title": "CapLeaveSettlementLoadHistoryResultSuccess"}, "CapLeaveSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLeaveSettlementLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveSettlementLoadHistoryResultSuccessBody"}, "CapLeaveSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapLeaveSettlement AccessTrackInfo"}, "CapLeaveSettlement_BenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BenefitDurationEntity"}, "benefitDuration": {"type": "integer", "format": "int64", "description": "Benefit Duration defined in Leave Policy"}, "benefitDurationTypeCd": {"type": "string", "description": "Benefit Duration Type Cd defined in Leave Policy"}, "benefitDurationUnitsCd": {"type": "string", "description": "Benefit Duration Units Cd in Leave Policy"}}, "title": "CapLeaveSettlement BenefitDurationEntity", "description": "Benefit Durations defined in Leave Policy."}, "CapLeaveSettlement_CapApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapApprovalPeriodEntity"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLeaveSettlement CapApprovalPeriodEntity"}, "CapLeaveSettlement_CapBaseDisabilityFormulaCalculationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaCalculationDetails"}, "calculatedResult": {"$ref": "#/definitions/Money"}, "formulaAppliedAttribute": {"type": "string", "description": "Attribute name which formula calculation applied"}, "formulaContent": {"type": "string", "description": "Formula Content."}, "formulaId": {"type": "string", "description": "Formula Id."}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapBaseDisabilityFormulaParameter"}}, "policyRoundingAmount": {"type": "string", "description": "Policy Rounding Amount"}, "policyRoundingFactorCd": {"type": "string", "description": "Policy Rounding Factor Cd"}, "roundingResult": {"$ref": "#/definitions/Money"}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}}, "title": "CapLeaveSettlement CapBaseDisabilityFormulaCalculationDetails", "description": "An entity for formula calculation details."}, "CapLeaveSettlement_CapBaseDisabilityFormulaParameter": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaParameter"}, "paramExtension": {"type": "object", "description": "Extension of parameter. E.g currency"}, "paramName": {"type": "string", "description": "Parameter name."}, "paramValue": {"type": "number", "description": "Value of parameter."}}, "title": "CapLeaveSettlement CapBaseDisabilityFormulaParameter", "description": "An entity for formula parameter."}, "CapLeaveSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapLeaveSettlement CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapLeaveSettlement_CapLeaveApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveApprovalPeriodEntity"}, "approvalPeriod": {"$ref": "#/definitions/CapLeaveSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string"}, "approverPerson": {"type": "string"}, "cancelReason": {"type": "string"}, "dateOfStatusChange": {"type": "string", "format": "date-time"}}, "title": "CapLeaveSettlement CapLeaveApprovalPeriodEntity"}, "CapLeaveSettlement_CapLeaveBenefitWeeklyAmt": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveBenefitWeeklyAmt"}, "maxWeeklyBenefitPct": {"type": "number", "description": "Maximum percentage of weekly earnings covered by policy"}, "weeklyBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeaveSettlement CapLeaveBenefitWeeklyAmt", "description": "Defines details of 'Weekly Amount' benefit type"}, "CapLeaveSettlement_CapLeaveBenefitWeeklySalaryPct": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveBenefitWeeklySalaryPct"}, "benefitPct": {"type": "number", "description": "Percentage of weekly earnings covered by policy."}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeaveSettlement CapLeaveBenefitWeeklySalaryPct", "description": "An entity that stores details of 'Percentage' benefit type."}, "CapLeaveSettlement_CapLeaveCertInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveCertInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveCoverageInfoEntity"}}}, "title": "CapLeaveSettlement CapLeaveCertInfoEntity", "description": "An entity for policy information when policy type is individual (certificate) policy."}, "CapLeaveSettlement_CapLeaveClaimDetailSelectedCoverageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimDetailSelectedCoverageEntity"}, "coverageName": {"type": "string"}, "planName": {"type": "string"}}, "title": "CapLeaveSettlement CapLeaveClaimDetailSelectedCoverageEntity"}, "CapLeaveSettlement_CapLeaveCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveCoverageInfoEntity"}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_BenefitDurationEntity"}}, "benefitTypeCd": {"type": "string", "description": "Defines type of which formula to apply to calculate gross benefit amount"}, "coverageCd": {"type": "string"}, "earningsDefinitionCd": {"type": "string", "description": "Earning definition Cd defined in policy"}, "eliminationPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEliminationPeriods"}}, "employmentStatus": {"type": "string", "description": "Employment Status in Policy"}, "employmentTerminiationDate": {"type": "string", "format": "date", "description": "Employment Terminiation Date in Policy"}, "individualRecordTypeCd": {"type": "string", "description": "Employment Status in Leave Policy"}, "isSelfBill": {"type": "boolean", "description": "Is Self Bill defined in Leave policy"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "partyTypeCd": {"type": "string", "description": "Party Type Cd defined in Policy"}, "payrollFrequencyCd": {"type": "string", "description": "Payroll Frequency Cd in Leave Policy"}, "planCd": {"type": "string", "description": "Describes planCd defined in Leave policy"}, "planName": {"type": "string", "description": "Describes planName defined in Leave policy"}, "riskStateCd": {"type": "string", "description": "Risk State Cd defined in Leave Policy"}, "roundingAmount": {"type": "integer", "format": "int64", "description": "Rounding Amount defined in Leave policy"}, "roundingFactorCd": {"type": "string", "description": "Rounding Factor Cd defined in Leave policy"}, "taxabilityCd": {"type": "string", "description": "For selected coverage defines if weekly benefits are taxable or tax-fr"}, "tieredWeeklySalaryPct": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementTieredWeeklySalaryPctEntity"}, "timeUnitGranularity": {"type": "string", "description": "Time Unit Granularity"}, "weeklyAmtDetails": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveBenefitWeeklyAmt"}, "weeklyPctDetails": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveBenefitWeeklySalaryPct"}, "workweekTypeCd": {"type": "integer", "format": "int64", "description": "Member work week type defined in Policy"}}, "title": "CapLeaveSettlement CapLeaveCoverageInfoEntity", "description": "An entity for coverage information."}, "CapLeaveSettlement_CapLeaveGrossBenefitAmount": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveGrossBenefitAmount"}, "coreCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "totalApprovedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeaveSettlement CapLeaveGrossBenefitAmount"}, "CapLeaveSettlement_CapLeaveMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveCoverageInfoEntity"}}}, "title": "CapLeaveSettlement CapLeaveMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "CapLeaveSettlement_CapLeaveSettlementAbsenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementAbsenceInfoEntity"}, "absencePeriods": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementAbsencePeriodInfoEntity"}}, "absenceReason": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementAbsenceReasonInfoEntity"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes. Inherited from Leave Claim."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax. Inherited from Leave Claim."}, "isOtherIncome": {"type": "boolean"}, "lastWorkDates": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementAbsenceInfoLastWorkDateEntity"}}, "returnToWorkDate": {"type": "string", "format": "date-time", "description": "Return to Work date"}, "typicalWorkWeek": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementLossInfoTypicalWorkWeekEntity"}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}}, "title": "CapLeaveSettlement CapLeaveSettlementAbsenceInfoEntity", "description": "The object that includes information taken from Absence case."}, "CapLeaveSettlement_CapLeaveSettlementAbsenceInfoLastWorkDateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementAbsenceInfoLastWorkDateEntity"}, "lastWorkDate": {"type": "string", "format": "date-time"}}, "title": "CapLeaveSettlement CapLeaveSettlementAbsenceInfoLastWorkDateEntity"}, "CapLeaveSettlement_CapLeaveSettlementAbsencePeriodInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementAbsencePeriodInfoEntity"}, "absenceTypeCd": {"type": "string", "description": "This attribute describes the type of absence in time perspective."}, "actualRtwDate": {"type": "string", "format": "date-time"}, "estimatedRtwDate": {"type": "string", "format": "date-time"}, "period": {"$ref": "#/definitions/CapLeaveSettlement_Period"}}, "title": "CapLeaveSettlement CapLeaveSettlementAbsencePeriodInfoEntity", "description": "Entity that contains Absence periods information"}, "CapLeaveSettlement_CapLeaveSettlementAbsenceReasonInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementAbsenceReasonInfoEntity"}, "absenceReasons": {"type": "array", "items": {"type": "string", "description": "Absence reasons"}}}, "title": "CapLeaveSettlement CapLeaveSettlementAbsenceReasonInfoEntity", "description": "Entity that contains Absence reason information"}, "CapLeaveSettlement_CapLeaveSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapLeaveSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLeaveSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveApprovalPeriodEntity"}}, "approvedAmountOverride": {"$ref": "#/definitions/Money"}, "approvedAmountOverrideReason": {"type": "string"}, "benefitOverridePctReason": {"type": "string"}, "benefitOverridePercent": {"type": "number"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string", "description": "The attribute represents the eligibility rules decision override reason."}, "eliminationPeriodOverrideEndDate": {"type": "string", "format": "date-time", "description": "The attribute that represents the overridden end date of elimination period."}, "eliminationPeriodOverrideReason": {"type": "string", "description": "The attribute that represents the reason of elimination period end date override."}, "endDateOverride": {"type": "string", "format": "date-time"}, "endDateOverrideReason": {"type": "string"}, "grossAmountOverride": {"$ref": "#/definitions/Money"}, "grossAmountOverrideReason": {"type": "string"}, "leavePlanDefinitionId": {"type": "string"}, "maxBenefitOverridePeriod": {"$ref": "#/definitions/CapLeaveSettlement_Period"}, "maxBenefitOverrideReason": {"type": "string"}, "maxWeeklyBenefitOverrideAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitOverrideAmountReason": {"type": "string"}, "startDateOverride": {"type": "string", "format": "date-time"}, "startDateOverrideReason": {"type": "string"}, "taxablePercentageOverride": {"type": "number", "description": "The taxable percentage override defined by user manually that will be taxable."}, "weeklyEarningsOverrideAmount": {"$ref": "#/definitions/Money"}, "weeklyEarningsOverrideReason": {"type": "string"}}, "title": "CapLeaveSettlement CapLeaveSettlementDetailEntity", "description": "This Business entity houses the detail of the Leave Settlement."}, "CapLeaveSettlement_CapLeaveSettlementEliminationPeriods": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementEliminationPeriods"}, "eliminationPeriodCd": {"type": "integer", "format": "int64", "description": "Defines elimination period of time (in calendar days) between the injury and receiving benefits payments from an insurer."}, "eliminationPeriodTypeCd": {"type": "string", "description": "Defines elimination period type"}}, "title": "CapLeaveSettlement CapLeaveSettlementEliminationPeriods", "description": "An entity for elimination period of time (in calendar days) between the injury and receiving benefits payments from an insurer."}, "CapLeaveSettlement_CapLeaveSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapLeaveSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLeaveSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "absenceReasonCd": {"type": "string"}, "accessTrackInfo": {"$ref": "#/definitions/CapLeaveSettlement_AccessTrackInfo"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "externalReference": {"$ref": "#/definitions/CapLeaveSettlement_ExternalReference"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "policy": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "settlementAbsenceInfo": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementAbsenceInfoEntity"}, "settlementDetail": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementLossInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}}, "title": "CapLeaveSettlement CapLeaveSettlementEntity", "description": "The object that encompasses attributes set for Leave Settlement."}, "CapLeaveSettlement_CapLeaveSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntity"}}, "title": "CapLeaveSettlement_CapLeaveSettlementEntitySuccess"}, "CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveSettlement_CapLeaveSettlementEntitySuccessBody"}, "CapLeaveSettlement_CapLeaveSettlementFinancialAdjustmentOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementFinancialAdjustmentOffsetEntity"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapLeaveSettlement_Term"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the claim."}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapLeaveSettlement CapLeaveSettlementFinancialAdjustmentOffsetEntity", "description": "Business entity that describes the Offsets of the claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapLeaveSettlement_CapLeaveSettlementLossInfoClaimPayeeDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementLossInfoClaimPayeeDetails"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLeaveSettlement CapLeaveSettlementLossInfoClaimPayeeDetails"}, "CapLeaveSettlement_CapLeaveSettlementLossInfoEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementLossInfoEarningsEntity"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "baseSalaryAmount": {"$ref": "#/definitions/Money"}, "salaryMode": {"type": "string"}}, "title": "CapLeaveSettlement CapLeaveSettlementLossInfoEarningsEntity"}, "CapLeaveSettlement_CapLeaveSettlementLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementLossInfoEntity"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations. Value is copied from Leave Claim domain."}, "claimPayeeDetails": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementLossInfoClaimPayeeDetails"}, "coverageType": {"type": "string", "description": "Type of coverage"}, "disabilityReasonCd": {"type": "string", "description": "Disability reason. Value is copied from Leave Claim domain."}, "earnings": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementLossInfoEarningsEntity"}, "eligibilityVerifiedCd": {"type": "string"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time. Value is copied from Leave Claim domain."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date and time when the incident happened. Value is copied from Leave Claim domain."}, "lossNumber": {"type": "string"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Type of loss."}, "offsets": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementFinancialAdjustmentOffsetEntity"}}, "reportedDate": {"type": "string", "format": "date-time"}, "selectedCoverage": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveClaimDetailSelectedCoverageEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable. Inherited from Claim."}}, "title": "CapLeaveSettlement CapLeaveSettlementLossInfoEntity", "description": "Business entity that houses the information from Leave claim to use in settlement."}, "CapLeaveSettlement_CapLeaveSettlementLossInfoTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementLossInfoTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapLeaveSettlement CapLeaveSettlementLossInfoTypicalWorkWeekEntity"}, "CapLeaveSettlement_CapLeaveSettlementMessagesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementMessagesEntity"}, "message": {"type": "string"}, "messageCode": {"type": "string"}, "messageSeverity": {"type": "string"}}, "title": "CapLeaveSettlement CapLeaveSettlementMessagesEntity"}, "CapLeaveSettlement_CapLeaveSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementPolicyInfoEntity"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "leaveCertInfo": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveCertInfoEntity"}, "leaveMasterInfo": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveMasterInfoEntity"}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (LeaveMaster, LeaveIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/CapLeaveSettlement_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapLeaveSettlement CapLeaveSettlementPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (Leave, SMP, etc.) & Settlement (Absence, Leave, SMP, etc.)."}, "CapLeaveSettlement_CapLeaveSettlementResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementResultEntity"}, "approvedAmount": {"$ref": "#/definitions/Money"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "calculatedLastWorkDate": {"type": "string", "format": "date-time"}, "checkVersionCd": {"type": "string"}, "coverageCd": {"type": "string"}, "coverageName": {"type": "string"}, "coverageWeeklyLimitAmount": {"$ref": "#/definitions/Money"}, "coveredEarningsAmount": {"$ref": "#/definitions/Money"}, "coveredWeeklyEarningsAmount": {"$ref": "#/definitions/Money"}, "currentEarningsReducedPct": {"type": "number"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "eliminationPeriodDuration": {"type": "integer", "format": "int64"}, "eliminationPeriodThroughDate": {"type": "string", "format": "date-time", "description": "\tThis attribute defines end date of elimination period. Elimination period is time (starting from date of loss) an Insured must be disabled before qualifying for Leave benefits. No benefits are payable during the elimination period."}, "employmentStatus": {"type": "string"}, "employmentTerminiationDate": {"type": "string", "format": "date-time"}, "formulaCalculationDetails": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_CapBaseDisabilityFormulaCalculationDetails"}}, "frequencyTypeCd": {"type": "string"}, "grossBenefitAmount": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveGrossBenefitAmount"}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if this Claim is the subject to Auto Adjudication."}, "isEligibilityEvaluationCdOverriden": {"type": "boolean", "description": "This attribute describes if Eligibility evaluation Cd is overriden"}, "isFicaApplicable": {"type": "boolean"}, "isFitApplicable": {"type": "boolean"}, "isPaymentOnHold": {"type": "boolean"}, "maxBenefitDuration": {"type": "string"}, "maxBenefitEndDate": {"type": "string", "format": "date-time"}, "maxBenefitStartDate": {"type": "string", "format": "date-time"}, "maxPartialEarningsPct": {"type": "number"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapLeaveSettlement_MessageType"}}, "minPartialEarningsPct": {"type": "number"}, "partyTypeCd": {"type": "string"}, "planCd": {"type": "string"}, "planName": {"type": "string"}, "proratingRate": {"type": "string", "description": "Prorating rate, used to prorate AGBA, deductions and taxes. The values of the prorating rate can be 1/5 or 1/7."}, "reserve": {"type": "number"}, "roundingAmount": {"type": "integer", "format": "int64"}, "roundingFactorCd": {"type": "string"}, "settlementResultMessages": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementMessagesEntity"}, "taxablePercentage": {"type": "number", "description": "The calculated percentage of gross benefit amount that will be taxable."}, "timeUnitGranularity": {"type": "string", "description": "Time Unit Granularity"}, "typicalWorkWeek": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementResultTypicalWorkWeekEntity"}, "weeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "workIncentiveBenefitCd": {"type": "string"}}, "title": "CapLeaveSettlement CapLeaveSettlementResultEntity", "description": "Business entity defines Leave settlement result."}, "CapLeaveSettlement_CapLeaveSettlementResultTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementResultTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapLeaveSettlement CapLeaveSettlementResultTypicalWorkWeekEntity"}, "CapLeaveSettlement_CapLeaveSettlementRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementRulesInput"}, "absence": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementAbsenceInfoEntity"}, "details": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementLossInfoEntity"}, "policy": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementPolicyInfoEntity"}}, "title": "CapLeaveSettlement CapLeaveSettlementRulesInput"}, "CapLeaveSettlement_CapLeaveSettlementTieredWeeklySalaryPctEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveSettlementTieredWeeklySalaryPctEntity"}, "benefitPctOnPortionOfWeeklySalaryAboveHalfOfSAWW": {"type": "number", "description": "Benefit Pct On Portion Of Weekly Salary Above Half Of SAWW defined in Leave Policy"}, "benefitPctOnPortionOfWeeklySalaryUpToHalfOfSAWW": {"type": "number", "description": "Benefit Pct On Portion Of Weekly Salary Up To Half Of SAWW defined in Leave Policy"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "stateAverageWeeklyWageAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeaveSettlement CapLeaveSettlementTieredWeeklySalaryPctEntity", "description": "Cap Leave Settlement Tiered Weekly Salary Pct Entity defined in Leave Policy."}, "CapLeaveSettlement_ExternalReference": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ExternalReference"}, "reference": {"$ref": "#/definitions/EntityLink"}, "sourceId": {"type": "string", "description": "Entity ID in external system"}, "sourceSubType": {"type": "string", "description": "Entity subtype in external system"}, "sourceSystem": {"type": "string", "description": "External system name/description"}, "sourceType": {"type": "string", "description": "Entity type in external system"}}, "title": "CapLeaveSettlement ExternalReference", "description": "Holds external(3rd party) system reference related information"}, "CapLeaveSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapLeaveSettlement MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapLeaveSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLeaveSettlement Period"}, "CapLeaveSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapLeaveSettlement Term"}, "CapRequestLeaveSettlementAdjudicationInput": {"required": ["entity"], "properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementDetailEntity"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapRequestLeaveSettlementAdjudicationInput"}, "CapRequestLeaveSettlementAdjudicationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRequestLeaveSettlementAdjudicationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRequestLeaveSettlementAdjudicationInputBody"}, "CapSettlementApprovalPeriodInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "endDate": {"type": "string", "format": "date-time"}, "periodId": {"type": "string", "format": "uuid"}, "reason": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "status": {"type": "string"}}, "title": "CapSettlementApprovalPeriodInput"}, "CapSettlementApprovalPeriodInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementApprovalPeriodInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementApprovalPeriodInputBody"}, "CapSettlementReadjudicateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapLeaveSettlement_CapLeaveSettlementDetailEntity"}}, "title": "CapSettlementReadjudicateInput"}, "CapSettlementReadjudicateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementReadjudicateInputBody"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputs"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputs"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}