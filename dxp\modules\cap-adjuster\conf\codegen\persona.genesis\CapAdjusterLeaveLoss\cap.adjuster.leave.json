{"swagger": "2.0", "x-dxp-spec": {"imports": {"leave": {"schema": "integration.cap.leave.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Leave Loss API", "version": "1", "title": "CAP Adjuster: Leave Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-leave", "description": "CAP Adjuster: Leave Loss API"}], "paths": {"/losses-leave/{rootId}/{revisionNo}": {"get": {"summary": "Search Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/caploss/CapLeave/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/rules/bundle": {"post": {"summary": "Rules bundle for Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/draft": {"post": {"summary": "Init Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave": {"post": {"summary": "Create Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-leave"]}, "put": {"summary": "Update Leave loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapLeave/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/closeLoss": {"post": {"summary": "Close Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/command/closeLoss", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/reopenLoss": {"post": {"summary": "Reopen Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/command/reopenLoss", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/submitLoss": {"post": {"summary": "Submit Leave loss", "x-dxp-path": "/api/caploss/CapLeave/v1/command/submitLoss", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/setLossSubStatus": {"post": {"summary": "Set Leave loss sub-status", "x-dxp-path": "/api/caploss/CapLeave/v1/command/setLossSubStatus", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/updateReturnToWork": {"post": {"summary": "Updates Return To Work Entity", "x-dxp-path": "/api/caploss/CapLeave/v1/command/updateReturnToWork", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/retrieve-closure-open-items": {"post": {"summary": "Retrieve leave loss closure open items", "x-dxp-path": "/api/caploss/CapLeave/v1/transformation/absenceClaimClosureToOpenItems", "tags": ["/cap-adjuster/v1/losses-leave"]}}}}