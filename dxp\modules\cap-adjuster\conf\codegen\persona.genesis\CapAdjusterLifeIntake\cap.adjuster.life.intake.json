{"swagger": "2.0", "x-dxp-spec": {"imports": {"life.intake": {"schema": "integration.cap.life.intake.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Life Intake API", "version": "1", "title": "CAP Adjuster: Life Intake API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/life-intake", "description": "CAP Adjuster: Life Intake API"}], "paths": {"/life-intake/{rootId}/{revisionNo}": {"get": {"summary": "Get Life Intake Loss", "x-dxp-path": "/api/caploss/LifeIntakeLoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/life-intake"]}}, "/life-intake/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/caploss/LifeIntakeLoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/life-intake"]}}, "/life-intake/create": {"post": {"summary": "Create Life Intake Loss", "x-dxp-path": "/api/caploss/LifeIntakeLoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/life-intake"]}}, "/life-intake/init": {"post": {"summary": "Init Life Intake Loss", "x-dxp-path": "/api/caploss/LifeIntakeLoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/life-intake"]}}, "/life-intake": {"put": {"summary": "Update Life Intake Loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/LifeIntakeLoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/life-intake"]}}}}