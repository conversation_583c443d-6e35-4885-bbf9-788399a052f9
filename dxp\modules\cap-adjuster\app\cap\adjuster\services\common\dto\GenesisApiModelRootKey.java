/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import core.services.ApiDTO;

@JsonInclude(Include.NON_NULL)
public class GenesisApiModelRootKey implements ApiDTO {

    public String rootId;
    public Integer revisionNo;
}
