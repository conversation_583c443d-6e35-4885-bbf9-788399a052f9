{"swagger": "2.0", "x-dxp-spec": {"imports": {"smp.settlement": {"schema": "integration.cap.smp.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: SMP Settlement API", "version": "1", "title": "CAP Adjuster: SMP Settlement API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-smp", "description": "CAP Adjuster: SMP Loss API"}], "paths": {"/losses-smp/settlements/readjudicate": {"patch": {"summary": "Readjudicate SMP settlement", "x-dxp-path": "/api/capsettlement/CapSmpSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/settlements/rules/bundle": {"post": {"summary": "Bundle SMP settlement", "x-dxp-path": "/api/capsettlement/CapSmpSettlement/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-smp"]}}}}