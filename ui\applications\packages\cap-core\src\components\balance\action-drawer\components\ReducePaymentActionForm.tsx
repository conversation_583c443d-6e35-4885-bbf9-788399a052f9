import {observer} from 'mobx-react'
import React, {FC} from 'react'
import {ExternalLink, Money} from '@eisgroup/models-api'
import {isEmpty} from 'lodash'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {noop} from '@eisgroup/common'
import {t} from '@eisgroup/i18n'
import {useForm} from '@eisgroup/form'
import {CaseSystemService, CSClaimWrapperService} from '@eisgroup/cap-services'
import reducePaymentConfig from '../../../../builder/BalanceReducePayment.builder'
import {EntityLink} from '../../../../utils'
import {ICaseSystem, SelectInputOptions} from '../../../../common/Types'
import {ActionFormBase} from './ActionFormBase'
import {ADD_OR_EDIT, BALANCE_ACTION_REDUCE_PAYMENT_FORM_ID, UPDATE_EVENT_CASE} from '../../../../common/constants'
import {DrawerFormStateType, SelectCustomProps} from '../../../..'
import {getWithholdingLossSources} from '../../utils'
import {WithholdingAmount} from '../../WithholdingAmount'
import {WithholdingPercentage} from '../../WithholdingPercentage'

import CapFinancialAdjustmentWithholdingEntity = CapEventCase.CapFinancialAdjustmentWithholdingEntity
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapFinancialAdjustmentEntity = CapEventCase.CapFinancialAdjustmentEntity
import {BalanceActionDrawerProps} from '../BalanceActionDrawer'

type ReducePaymentActionFormProps = BalanceActionDrawerProps<
    ICaseSystem,
    CaseSystemService | CSClaimWrapperService<ICaseSystem>
>

const withBalance = (Component, balanceStore) => () => <Component balanceStore={balanceStore} />
const withPercentage = Component => () => <Component />

const getDataOrUndefined = (flag: boolean, data) => {
    return flag ? data : undefined
}

export const ReducePaymentActionForm: FC<ReducePaymentActionFormProps> = observer(props => {
    const {
        actionKey: key,
        balanceStore,
        currentWithholding,
        claims,
        payeeLink,
        reducePaymentMode,
        eventCase,
        onUpdateDeleteWithHolding,
        closeDrawer
    } = props
    const getTotalBalanceAmount = () => balanceStore.balance?.totalBalanceAmount?.amount ?? 0

    const getBtnText = (withholding: CapFinancialAdjustmentWithholdingEntity) =>
        isEmpty(withholding) ? t('cap-core:add') : t('cap-core:save')

    const getAmountClaimOptions = () => {
        const form = useForm()
        const reduceFormData = form.getState().values
        const remainBalanceType = !currentWithholding.withholdingPct

        let currentEditOptions: SelectInputOptions[] = []
        if (reducePaymentMode === ADD_OR_EDIT.EDIT && remainBalanceType === reduceFormData.remainingBalanceType) {
            currentEditOptions =
                claims
                    .filter(v => currentWithholding.lossSources?.findIndex(s => s._uri.includes(v.rootId)) > -1)
                    .map(v => {
                        return {
                            code: v.rootId,
                            displayValue: v.lossNumber
                        }
                    }) || []
        }

        const lossSrcs = getWithholdingLossSources(
            eventCase.lossDetail?.financialAdjustment?.withholdings.filter(
                v => v.withholdingPayee?._uri === payeeLink
            ) || []
        )

        return claims
            .filter(claim => !['CI', 'HI', 'LIFE', 'ACC', 'TL', 'PL'].includes(claim.claimType))
            .filter(v => lossSrcs.findIndex(s => s._uri.includes(v.rootId)) === -1)
            .map(v => {
                return {
                    code: v.rootId,
                    displayValue: v.lossNumber
                }
            })
            .concat(currentEditOptions)
    }

    const getPCTClaimOptions = () => {
        let currentEditOptions: SelectInputOptions[] = []
        if (reducePaymentMode === ADD_OR_EDIT.EDIT) {
            currentEditOptions =
                claims
                    .filter(v => currentWithholding.lossSources?.findIndex(s => s._uri.includes(v.rootId)) > -1)
                    .map(v => {
                        return {
                            code: v.rootId,
                            displayValue: v.lossNumber
                        }
                    }) || []
        }
        const lossSrc = getWithholdingLossSources(
            eventCase.lossDetail?.financialAdjustment?.withholdings.filter(
                v => v.withholdingPayee?._uri === payeeLink
            ) || []
        )

        return claims
            .filter(v => lossSrc.findIndex(s => s._uri.includes(v.rootId)) === -1)
            .map(v => {
                return {
                    code: v.rootId,
                    displayValue: v.lossNumber
                }
            })
            .concat(currentEditOptions)
    }

    const isLoading = balanceStore.eventCaseStore.actionsStore.isRunning(UPDATE_EVENT_CASE)

    const totalBalanceAmount = getTotalBalanceAmount()
    let numberOfEstimatedWeekly: number | undefined
    let selectedClaim = ''
    let selectedClaims: string[] = []
    let remainingBalanceType: boolean | undefined
    let withholdingAmount: Money | undefined

    if (currentWithholding.withholdingPct || currentWithholding.withholdingPct === 0) {
        remainingBalanceType = false
    }
    if (currentWithholding.withholdingMonthlyAmount || currentWithholding.withholdingWeeklyAmount) {
        remainingBalanceType = true
        withholdingAmount = currentWithholding.withholdingMonthlyAmount || currentWithholding.withholdingWeeklyAmount
        if (totalBalanceAmount < 0) {
            const entireBalance = Math.abs(totalBalanceAmount)
            numberOfEstimatedWeekly = Number((entireBalance / withholdingAmount!.amount).toFixed(1))
        }
    }
    const withholdingLossSources = currentWithholding?.lossSources || []
    const lossSources = withholdingLossSources
        .filter(s => {
            const {rootId} = EntityLink.from(s._uri)
            return claims.find(v => v.rootId === rootId)
        })
        .map(v => {
            const {rootId} = EntityLink.from(v._uri)
            return rootId
        })

    if (remainingBalanceType) {
        selectedClaim = lossSources?.[0] || ''
    } else {
        selectedClaims = lossSources
    }

    const getLossSourceAndType = values => {
        const currentLosssSources: ExternalLink[] = []
        const lossTypes: string[] = []
        claims.forEach(v => {
            values.selectedClaims.forEach(claim => {
                if (v.rootId === claim) {
                    currentLosssSources.push({
                        _uri: `gentity://CapLoss/${v?._modelName}//${v?.rootId}/${v?.revisionNo}`
                    })
                    lossTypes.push(v.claimType)
                }
            })
        })
        return {
            lossSources: currentLosssSources,
            lossTypes
        }
    }

    const onFormConfirm = values => {
        let currentLosssSources: ExternalLink[] = []
        let lossTypes: string[] = []
        let withholding: CapFinancialAdjustmentWithholdingEntity = {} as CapFinancialAdjustmentWithholdingEntity
        if (values.remainingBalanceType) {
            const currentSelectedClaims = claims.find(v => v.rootId === values.selectedClaim)
            currentLosssSources = [
                {
                    _uri: `gentity://CapLoss/${currentSelectedClaims?._modelName}//${currentSelectedClaims?.rootId}/${currentSelectedClaims?.revisionNo}`
                }
            ]
            lossTypes = [currentSelectedClaims?.claimType || '']
            withholding = {
                ...CapEventCase.factory.newByType<CapFinancialAdjustmentWithholdingEntity>(
                    CapFinancialAdjustmentWithholdingEntity
                ),
                withholdingPayee: {
                    _uri: payeeLink
                },
                withholdingMonthlyAmount: getDataOrUndefined(lossTypes.includes('LTD'), values.withholdingAmount),
                withholdingWeeklyAmount: getDataOrUndefined(!lossTypes.includes('LTD'), values.withholdingAmount),
                lossSources: currentLosssSources,
                lossTypes: Array.from(new Set(lossTypes))
            }
        } else {
            const lossSourceAndType = getLossSourceAndType(values)
            currentLosssSources = lossSourceAndType.lossSources
            lossTypes = lossSourceAndType.lossTypes
            withholding = {
                ...CapEventCase.factory.newByType<CapFinancialAdjustmentWithholdingEntity>(
                    CapFinancialAdjustmentWithholdingEntity
                ),
                withholdingPayee: {
                    _uri: payeeLink
                },
                withholdingPct: values.withholdingPct,
                lossSources: currentLosssSources,
                lossTypes: Array.from(new Set(lossTypes))
            }
        }

        const financialAdjustment = eventCase.lossDetail?.financialAdjustment || {}
        const withholdings = eventCase.lossDetail?.financialAdjustment?.withholdings || []
        const currentWithholdingIndex = withholdings.findIndex(
            v =>
                v.withholdingPayee?._uri === currentWithholding?.withholdingPayee?._uri &&
                v._key.id === currentWithholding?._key.id
        )

        if (currentWithholdingIndex !== -1) {
            withholdings.splice(currentWithholdingIndex, 1, withholding)
        } else {
            withholdings.push(withholding)
        }

        const eventCaseParam = {
            ...eventCase,
            lossDetail: {
                ...eventCase.lossDetail,
                financialAdjustment: isEmpty(financialAdjustment)
                    ? {
                          ...CapEventCase.factory.newByType<CapFinancialAdjustmentEntity>(CapFinancialAdjustmentEntity),
                          withholdings
                      }
                    : {
                          ...eventCase.lossDetail?.financialAdjustment,
                          withholdings
                      }
            }
        }
        closeDrawer()
        onUpdateDeleteWithHolding(eventCaseParam as CapEventCaseEntity)
    }

    return (
        <ActionFormBase
            uiEngineProps={{
                formId: BALANCE_ACTION_REDUCE_PAYMENT_FORM_ID,
                config: reducePaymentConfig,
                initialValues: {
                    selectCustomPropsMap: new Map<string, SelectCustomProps>([
                        ['selectedClaim', {onChange: noop, getOptions: getAmountClaimOptions}],
                        ['selectedClaims', {onChange: noop, getOptions: getPCTClaimOptions, mode: 'multiple'}]
                    ]),
                    action: key,
                    reductionType: 'Overpayment Withholding',
                    claims: claims.map(v => {
                        return {code: v.rootId, displayValue: `${v.claimType} ${v.lossNumber}`}
                    }),
                    selectedClaim,
                    selectedClaims,
                    withholdingAmount,
                    numberOfEstimatedWeekly,
                    withholdingPct: currentWithholding.withholdingPct,
                    remainingBalanceType
                },
                slotComponents: {
                    WithholdingAmount: withBalance(WithholdingAmount, balanceStore),
                    WithholdingPercentage: withPercentage(WithholdingPercentage)
                },
                onNext: onFormConfirm
            }}
            drawerActionProps={{
                labels: {
                    createButtonLabel: getBtnText(currentWithholding)
                },
                drawerFormState: DrawerFormStateType.Create,
                handleFormCancel: closeDrawer,
                isLoading
            }}
        />
    )
})
