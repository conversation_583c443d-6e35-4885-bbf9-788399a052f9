import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 12,
            "name": "taxType",
            "label": "cap-core:taxes_form_tax_type",
            "lookupName": "TaxType",
            "valueType": "string",
            "mode": "default",
            "filterBy": [
              {
                "id": "ac0f04d8-02dd-44a8-bf46-751bfd1812e3",
                "fieldName": "manualWithholding",
                "filterLookupKey": "manualWithholding"
              }
            ],
            "condition": {},
            "placeholder": "cap-core:placeholder_select"
          },
          "id": "312e900c-1282-4e97-8cdd-fff96195d1db"
        }
      ],
      "id": "0422cb5d-e344-4d0e-ae05-fb0e63e620df"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "showMonthly"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": false
                  }
                },
                {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "FEDERAL"
                      }
                    }
                  ],
                  "operator": "$and"
                }
              ],
              "operator": "$and"
            }
          }
        },
        "itemCount": null
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxWeeklyAmount",
            "label": "cap-core:taxes_form_tax_amount",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxWeeklyAmount.amount"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage"
                    }
                  }
                }
              ]
            },
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage",
                "query": {
                  "$nor": [
                    null,
                    ""
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "390afe15-6ac2-45df-acd4-84c74f197daa"
        },
        {
          "type": "TEXT",
          "props": {
            "text": "Text",
            "md": 2,
            "pull": 0,
            "template": "OR",
            "offset": 0,
            "customCss": "margin-top: 2rem;\ntext-align: center;"
          },
          "id": "743ba308-ce96-45f3-bbcd-4b9674aa27d7"
        },
        {
          "type": "NUMBER_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxPercentage",
            "label": "cap-core:taxes_form_tax_percentage",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": true,
                  "type": "min",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": -1
                  }
                },
                {
                  "skipOnEmpty": true,
                  "type": "max",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 101
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxWeeklyAmount.amount"
                    }
                  }
                }
              ]
            },
            "allowDecimal": true,
            "alignRight": false,
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsFederal.federalTaxWeeklyAmount.amount",
                "query": {
                  "$nor": [
                    null,
                    0,
                    ""
                  ]
                }
              }
            },
            "placeholder": "%",
            "fractionSize": 4
          },
          "id": "d03b98eb-d044-4750-a224-9b8cea2ed017"
        },
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxExtraWithholding",
            "label": "cap-core:taxes_form_extra_withholding",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": []
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage"
                      },
                      "operator": "$notEmpty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "a7b6b30f-44d1-497e-8d1f-6b165f19ba07"
        }
      ],
      "id": "d2341492-4fd6-44ae-91c9-b46edb79fa8e"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "showMonthly"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": true
                  }
                },
                {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "FEDERAL"
                      }
                    }
                  ],
                  "operator": "$and"
                }
              ],
              "operator": "$and"
            }
          }
        },
        "itemCount": null
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxMonthlyAmount",
            "label": "cap-core:taxes_form_tax_amount",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxMonthlyAmount.amount"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage"
                    }
                  }
                }
              ]
            },
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage",
                "query": {
                  "$nor": [
                    null,
                    ""
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "e8214e8b-a242-42cc-bbaf-011f585901ae"
        },
        {
          "type": "TEXT",
          "props": {
            "text": "Text",
            "md": 2,
            "pull": 0,
            "template": "OR",
            "offset": 0,
            "customCss": "margin-top: 2rem;\ntext-align: center;"
          },
          "id": "a0940539-d861-4c56-ada1-bbad0e4f2cab"
        },
        {
          "type": "NUMBER_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxPercentage",
            "label": "cap-core:taxes_form_tax_percentage",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": true,
                  "type": "min",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": -1
                  }
                },
                {
                  "skipOnEmpty": true,
                  "type": "max",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 101
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsFederal.federalTaxMonthlyAmount.amount"
                    }
                  }
                }
              ]
            },
            "allowDecimal": true,
            "alignRight": false,
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsFederal.federalTaxMonthlyAmount.amount",
                "query": {
                  "$nor": [
                    null,
                    0,
                    ""
                  ]
                }
              }
            },
            "placeholder": "%",
            "fractionSize": 4
          },
          "id": "85d845ca-de8a-4e70-b7a0-40624339829e"
        },
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxExtraWithholding",
            "label": "cap-core:taxes_form_extra_withholding",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": []
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~financialAdjustmentTaxDetailsFederal.federalTaxPercentage"
                      },
                      "operator": "$notEmpty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "cb9e3c55-5879-4054-9aed-fa1ea6186f8e"
        }
      ],
      "id": "c02210a1-c3a8-40ec-9b88-70e80c75b33b"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "showMonthly"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": false
                  }
                },
                {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "STATE"
                      }
                    }
                  ],
                  "operator": "$and"
                }
              ],
              "operator": "$and"
            }
          }
        },
        "itemCount": null
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsState.stateTaxWeeklyAmount",
            "label": "cap-core:taxes_form_tax_amount",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxWeeklyAmount.amount"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxPercentage"
                    }
                  }
                }
              ]
            },
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsState.stateTaxPercentage",
                "query": {
                  "$nor": [
                    null,
                    ""
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "ee8d4ef5-b89c-4307-a038-479df87ffd34"
        },
        {
          "type": "TEXT",
          "props": {
            "text": "Text",
            "md": 2,
            "pull": 0,
            "template": "OR",
            "offset": 0,
            "customCss": "margin-top: 2rem;\ntext-align: center;"
          },
          "id": "bd189ac3-f8b2-4704-b809-05f6a5507ece"
        },
        {
          "type": "NUMBER_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsState.stateTaxPercentage",
            "label": "cap-core:taxes_form_tax_percentage",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": true,
                  "type": "min",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": -1
                  }
                },
                {
                  "skipOnEmpty": true,
                  "type": "max",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 101
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxPercentage"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxWeeklyAmount.amount"
                    }
                  }
                }
              ]
            },
            "allowDecimal": true,
            "alignRight": false,
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsState.stateTaxWeeklyAmount.amount",
                "query": {
                  "$nor": [
                    null,
                    0,
                    ""
                  ]
                }
              }
            },
            "placeholder": "%",
            "fractionSize": 4
          },
          "id": "4e5252c3-ee6f-4b59-8539-9d13387ab3c4"
        },
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsState.stateTaxExtraWithholding",
            "label": "cap-core:taxes_form_extra_withholding",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": []
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "financialAdjustmentTaxDetailsState.stateTaxPercentage"
                      },
                      "operator": "$empty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "286f12da-8032-4842-8c37-145ee7232d64"
        }
      ],
      "id": "eaf19115-9852-41d7-9ca1-7ae3c3bd4769"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "showMonthly"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": true
                  }
                },
                {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "STATE"
                      }
                    }
                  ],
                  "operator": "$and"
                }
              ],
              "operator": "$and"
            }
          }
        },
        "itemCount": null
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsState.stateTaxMonthlyAmount",
            "label": "cap-core:taxes_form_tax_amount",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxMonthlyAmount.amount"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxPercentage"
                    }
                  }
                }
              ]
            },
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsState.stateTaxPercentage",
                "query": {
                  "$nor": [
                    null,
                    ""
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "88bdd92c-6ed9-468c-8961-98881a03cd94"
        },
        {
          "type": "TEXT",
          "props": {
            "text": "Text",
            "md": 2,
            "pull": 0,
            "template": "OR",
            "offset": 0,
            "customCss": "margin-top: 2rem;\ntext-align: center;"
          },
          "id": "7164b641-453e-4d0d-a6ba-2f0eba1bb9f5"
        },
        {
          "type": "NUMBER_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsState.stateTaxPercentage",
            "label": "cap-core:taxes_form_tax_percentage",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": true,
                  "type": "min",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": -1
                  }
                },
                {
                  "skipOnEmpty": true,
                  "type": "max",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 101
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "or",
                  "fieldValues": {
                    "firstField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxPercentage"
                    },
                    "secondField": {
                      "type": "field",
                      "value": "~financialAdjustmentTaxDetailsState.stateTaxMonthlyAmount.amount"
                    }
                  }
                }
              ]
            },
            "allowDecimal": true,
            "alignRight": false,
            "condition": {
              "disabled": {
                "conditionInputType": "json",
                "type": "boolean",
                "field": "~financialAdjustmentTaxDetailsState.stateTaxMonthlyAmount.amount",
                "query": {
                  "$nor": [
                    null,
                    0,
                    ""
                  ]
                }
              }
            },
            "placeholder": "%",
            "fractionSize": 4
          },
          "id": "4eea1fb5-ba7d-4fcc-95f2-ced6acb9bfb9"
        },
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 6,
            "name": "financialAdjustmentTaxDetailsState.stateTaxExtraWithholding",
            "label": "cap-core:taxes_form_extra_withholding",
            "allowDecimal": true,
            "events": [
              {
                "id": "3d20f63f-e8f5-4d94-869f-4e82667486e7"
              }
            ],
            "field": {
              "validations": []
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "financialAdjustmentTaxDetailsState.stateTaxPercentage"
                      },
                      "operator": "$empty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            },
            "initialAmount": null
          },
          "id": "099e2451-d7ef-45f9-acb7-8f8e3a86f124"
        }
      ],
      "id": "2f0e9d19-e199-412c-b190-88f2a4fbfda3"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "itemCount": null,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "currentTaxType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "FEDERAL"
                  }
                }
              ]
            }
          }
        }
      },
      "components": [
        {
          "type": "DATEPICKER_INPUT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxTerm.effectiveDate",
            "label": "cap-core:taxes_form_from_date",
            "dateType": "date",
            "valueType": "DATETIME",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Federal Tax Term Effective Date"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "26fd4c48-cc1d-44c2-bb48-85604de18c0c"
        },
        {
          "type": "DATEPICKER_INPUT",
          "props": {
            "md": 12,
            "label": "cap-core:taxes_form_thru_date",
            "dateType": "date",
            "valueType": "DATETIME",
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxTerm.expirationDate",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Federal Tax Term Expiration Date"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "push": 0
          },
          "id": "33cb4a2b-fdd1-46d0-9e68-c2438626bf60"
        }
      ],
      "id": "5cee85d7-dd0a-486a-a4b0-0ae45b115deb"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "itemCount": null,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "currentTaxType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "STATE"
                  }
                }
              ]
            }
          }
        }
      },
      "components": [
        {
          "type": "DATEPICKER_INPUT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsState.stateTaxTerm.effectiveDate",
            "label": "cap-core:taxes_form_from_date",
            "dateType": "date",
            "valueType": "DATETIME",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "State Tax Term Effective Date"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "d81f0387-d307-4feb-ab5c-cf59dc97682d"
        },
        {
          "type": "DATEPICKER_INPUT",
          "props": {
            "md": 12,
            "label": "cap-core:taxes_form_thru_date",
            "dateType": "date",
            "valueType": "DATETIME",
            "name": "financialAdjustmentTaxDetailsState.stateTaxTerm.expirationDate",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "State Tax Term Expiration Date"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "push": 0
          },
          "id": "44e943bd-fa57-4ae8-9425-c82502d41d85"
        }
      ],
      "id": "58ed954a-1c2d-4870-9969-1ea9e70fe2f4"
    },
    {
      "type": "CUSTOM_VALIDATION",
      "id": "25160003-5a7a-4b65-97c5-65d583b652cf",
      "props": {
        "name": "financialAdjustmentTaxDetailsFederal.federalTaxTerm",
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "currentTaxType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "FEDERAL"
                  }
                }
              ]
            }
          }
        }
      }
    },
    {
      "type": "CUSTOM_VALIDATION",
      "id": "47d49dd9-0771-415c-a278-45467fb3ac67",
      "props": {
        "name": "financialAdjustmentTaxDetailsState.stateTaxTerm",
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "currentTaxType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "STATE"
                  }
                }
              ]
            }
          }
        }
      }
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsState.stateTaxState",
            "label": "cap-core:taxes_table_state",
            "lookupName": "TaxState",
            "valueType": "string",
            "placeholder": "cap-core:placeholder_select",
            "mode": "default",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "STATE"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "filterBy": [
              {
                "id": "e0c63b13-c5f4-4958-a20f-c3471b4a12b3",
                "fieldName": "incomeTax",
                "filterLookupKey": "incomeTax"
              }
            ]
          },
          "id": "9ec19ea6-79cb-4c69-ab53-b3ee8b713dd2"
        },
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsFederal.federalTaxMaritalStatus",
            "label": "cap-core:taxes_table_filing_status",
            "lookupName": "TaxMaritalStatus",
            "valueType": "string",
            "mode": "default",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "FEDERAL"
                      }
                    }
                  ]
                }
              }
            },
            "placeholder": "cap-core:placeholder_select"
          },
          "id": "dc76e173-9b15-49d6-ab3a-68fcb383e8b7"
        },
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsState.stateTaxMaritalStatus",
            "label": "cap-core:taxes_table_filing_status",
            "lookupName": "TaxMaritalStatus",
            "valueType": "string",
            "placeholder": "cap-core:placeholder_select",
            "mode": "default",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "currentTaxType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "STATE"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "2797b1eb-3639-4c48-a91c-6485ddc598ba"
        }
      ],
      "id": "96eb4b6b-f244-4578-880d-f0e19753b5c0"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "itemCount": null,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "currentTaxType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "FEDERAL"
                  }
                }
              ]
            }
          }
        }
      },
      "components": [
        {
          "type": "MULTI_SELECT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsFederal.lossSources",
            "label": "cap-core:taxes_form_claims",
            "pathToValue": "_uri",
            "options": [],
            "placeholder": "cap-core:placeholder_select",
            "events": [],
            "disabled": false,
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isPaymentLevelClaim"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ],
                  "operator": "$or"
                }
              },
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~financialAdjustmentTaxDetailsFederal.federalTaxExtraWithholding"
                      },
                      "operator": "$empty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "d5e8ee06-c82f-4aff-a2ba-d8b725dc7ae2"
        },
        {
          "type": "SELECT_INPUT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsFederal.lossSources[0]._uri",
            "label": "cap-core:taxes_form_claims",
            "options": [],
            "placeholder": "cap-core:placeholder_select",
            "mode": "default",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isPaymentLevelClaim"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              },
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~financialAdjustmentTaxDetailsFederal.federalTaxExtraWithholding"
                      },
                      "operator": "$notEmpty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "cdd01495-ecec-47f1-8b70-f0cc6d0b7562"
        }
      ],
      "id": "2bcbcfa8-5b35-418e-b3dd-034abc748755"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "itemCount": null,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "currentTaxType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "STATE"
                  }
                }
              ]
            }
          }
        }
      },
      "components": [
        {
          "type": "MULTI_SELECT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsState.lossSources",
            "label": "cap-core:taxes_form_claims",
            "pathToValue": "_uri",
            "options": [],
            "placeholder": "cap-core:placeholder_select",
            "events": [],
            "disabled": false,
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isPaymentLevelClaim"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              },
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~financialAdjustmentTaxDetailsState.stateTaxExtraWithholding"
                      },
                      "operator": "$empty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "53f37d76-a92b-4025-89ec-1c429a5d3341"
        },
        {
          "type": "SELECT_INPUT",
          "props": {
            "md": 12,
            "name": "financialAdjustmentTaxDetailsState.lossSources[0]._uri",
            "options": [],
            "placeholder": "cap-core:placeholder_select",
            "label": "cap-core:taxes_form_claims",
            "mode": "default",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~financialAdjustmentTaxDetailsState.stateTaxExtraWithholding"
                      },
                      "operator": "$notEmpty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ]
                }
              },
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isPaymentLevelClaim"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "82492bd4-85e9-4228-984f-7e916eadd8f8"
        }
      ],
      "id": "d44ed0cc-fdc1-4841-9802-1f32e3d62050"
    }
  ],
  "version": 538,
  "globalEvents": {},
  "actionChains": {}
}

export default config;
