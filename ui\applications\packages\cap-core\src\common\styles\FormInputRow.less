.@{PREFIX}-input-form-row-grid {
    .grid-mix-in {
        display: grid;
    }

    &-2x1 {
        .grid-mix-in();
        grid-template-columns: repeat(2, 1fr);
    }

    &-3x1 {
        .grid-mix-in();
        grid-template-columns: repeat(3, 1fr);
    }

    &-4x1 {
        .grid-mix-in();
        grid-template-columns: repeat(4, 1fr);
    }

    &-8x1 {
        .grid-mix-in();
        grid-template-columns: repeat(8, 1fr);
    }

    &-9x1 {
        .grid-mix-in();
        grid-template-columns: repeat(9, 1fr);
    }

    &-2-1-1 {
        .grid-mix-in();
        grid-template-columns: 2fr 1fr 1fr;
    }

    &-3-2-3 {
        .grid-mix-in();
        grid-template-columns: 3fr 2fr 3fr;
    }

    &-2-2-1 {
        .grid-mix-in();
        grid-template-columns: 2fr 2fr 1fr;
    }

    &-1-2-2 {
        .grid-mix-in();
        grid-template-columns: 1fr 2fr 2fr;
    }

    &-2-3-3 {
        .grid-mix-in();
        grid-template-columns: 2fr 3fr 3fr;
    }

    &-1-4 {
        .grid-mix-in();
        grid-template-columns: 1fr 4fr;
    }

    &-3-1 {
        .grid-mix-in();
        grid-template-columns: 3fr 1fr;
    }

    &-1-5-2 {
        .grid-mix-in();
        grid-template-columns: 1fr 5fr 2fr;
    }

    &-2-1 {
        .grid-mix-in();
        grid-template-columns: 2fr 1fr;
    }
}

.@{PREFIX}-input-from-row-margin-right-1 {
    .ant-form-item:last-child {
        margin-right: 1rem !important;
    }
}
