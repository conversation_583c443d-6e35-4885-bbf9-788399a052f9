import {CaseSystemLossFormStore} from '../CaseSystemLossFormStore'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {FormDrawerStore, FormDrawerStoreImpl} from '../../../components/form-drawer'

export class CaseSystemOverviewStoreImpl extends BaseRootStoreImpl {
    formDrawerStore: FormDrawerStore

    lossFormStores: Map<string, CaseSystemLossFormStore<any, any>>

    lossFormDrawerKeys: Map<string, string>

    constructor(lossFormDrawerKeys: Map<string, string>) {
        super()
        this.formDrawerStore = new FormDrawerStoreImpl()
        this.lossFormDrawerKeys = lossFormDrawerKeys
    }
}
