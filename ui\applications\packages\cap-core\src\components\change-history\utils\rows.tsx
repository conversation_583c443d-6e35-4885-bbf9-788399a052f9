/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {CapPaymentTemplate} from '@eisgroup/cap-financial-models'
import {ChangeHistoryRecordWithUser, CrmAddress, dateUtils} from '@eisgroup/cap-services'
import {PaymentMethod, PaymentMethodType} from '@eisgroup/common-business-components'
import {opt} from '@eisgroup/common-types'
import {LocalizationUtils, t} from '@eisgroup/i18n'
import {lookups} from '@eisgroup/lookups'
import {BusinessEntity} from '@eisgroup/models-api'
import {isEisDate, isEisDateTime} from '@eisgroup/ui-temporals'
import {isArray, isEmpty} from 'lodash'
import get from 'lodash/get'
import {Moment} from 'moment'
import {EntityLink, isEventCaseUri, NoDataVal, Settlements} from '../../..'
import {moneyByLocale, tryTranslateValue} from '../../../common/utils'

import {
    extractAttributeNameFromPath,
    extractParentPathFromAttributePath,
    flattenObjectRecord,
    getAttribute,
    getEntityType,
    getLabelAnnotationByAttributePath,
    getLookupNameByAttributePath,
    getModelRootEntityTypeName,
    isAttributePrimitive,
    isRootEntity,
    isRootEntityReference,
    removeIndexFromPathEnd
} from './model'
import {
    ChangeHistoryModelNames,
    ChangeHistoryRecordFlatten,
    ChangeHistoryRecordFlattenExtended,
    ChangeHistorySettlementModelNamesLifeClaims,
    ChangeHistoryValue,
    ChangeHistoryValueNumeric,
    ChangeHistoryValueString,
    ClaimModelToType,
    Entities,
    isCapRelationshipModel,
    isClaimModel,
    isEventCaseModel,
    isHistoryValueDecimal,
    isHistoryValueInteger,
    isHistoryValueMoney,
    isHistoryValueString,
    isHistoryValueTypeString,
    isHistoryValueUri,
    isPaymentTemplateModel,
    isSettlementModel,
    isSpecialHandlingModel,
    LossExtractData,
    LossIdentification
} from './types'
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity
import dateValue = LocalizationUtils.dateValue
import CodeValueLookup = lookups.CodeValueLookup
/**
 * Transforms the raw change history data records into table structured format.
 * Includes mapping and ordering.
 * @param changeHistoryRecords - change history records.
 * @param getUserFullNameFromUUID - function to extract full name from users
 * @param eventTypes - event types for attribute label substitution
 * @param entities - entities associated with event case: event case itself, claims, and settlements.
 * @returns @type HistoryTableRow[]
 */
export function extractHistoryTableRows(
    changeHistoryRecords: ChangeHistoryRecordWithUser[],
    {eventCase, claims, settlements, specialHandlings, paymentTemplates, parties, capRelationships}: Entities,
    getUserFullNameFromUUID: (uuid: string) => string,
    eventTypes?: CodeValueLookup[]
): {
    date?: Date | string
    currentDate?: Date | string
    reference: string
    new: string | string[]
    adjustedBy: string
    component: string
    metadata: {
        path: string
        refExtension?: string
        lookupName: string | undefined
        timestamp: Date
        lossIdentification: LossIdentification | undefined
    }
    original: string | string[]
    attribute: string
    entity: string
    eventCaseInformation: {
        link: string
        number: string
    }
}[] {
    const changeHistoryRecordsFlatten = flattenChangeHistoryRecords(changeHistoryRecords)
    const extendedHistoryRecords = flattenHistoryEntitiesIntoRecords(changeHistoryRecordsFlatten)
    return (
        extendedHistoryRecords
            .filter(record => extractValue(record.newValue) !== extractValue(record.oldValue))
            .filter(record => filterRelationshipData(record))
            .map(record => {
                const modelName = record.originalRecord.filter.values.modelName as ChangeHistoryModelNames

                let lossData: LossExtractData
                if (isEventCaseModel(modelName)) {
                    lossData = extractLossDataFromEventCase(record, eventCase)
                } else if (isClaimModel(modelName)) {
                    lossData = extractLossDataFromClaim(record, claims)
                } else if (isSettlementModel(modelName)) {
                    lossData = extractLossDataFromSettlement(record, settlements)
                } else if (isSpecialHandlingModel(modelName)) {
                    lossData = extractLossDataFromSpecialHandling(record, {eventCase, claims, specialHandlings})
                } else if (isPaymentTemplateModel(modelName)) {
                    lossData = extractLossDataFromPaymentSchedule(record, paymentTemplates, settlements, claims)
                } else if (isCapRelationshipModel(modelName)) {
                    lossData = extractLossDataFromCapRelationship(record, {eventCase, capRelationships})
                } else {
                    lossData = {component: modelName, reference: t('cap-core:not_available')}
                }

                const {component, reference, lossIdentification, refExtension} = lossData
                const allPaymentMethods =
                    parties.flatMap(party => party.customer?.paymentMethods) ?? ([] as PaymentMethod[])

                const allAddressIds: CrmAddress[] = parties.flatMap(
                    party => party.customer?.communicationInfo?.addresses ?? []
                )

                const replaceEventLabel = (attributePath: string): string => {
                    const eventTypeCd = get(eventCase, attributePath.slice(1).replace('eventDate', 'eventTypeCd'))
                    return eventTypes?.find(type => type.code === eventTypeCd)?.displayValue ?? 'eventDate'
                }

                return {
                    date: record.originalRecord._key.timestamp,
                    currentDate: record.originalRecord._key.timestamp,
                    component,
                    reference,
                    entity: tryTranslateValue(
                        getDisplayLabelForEntity(record.pathToParent, record.parentEntity, modelName)
                    ),
                    attribute: tryTranslateValue(
                        getDisplayLabelForAttribute(record.pathFull, record.parentEntity, modelName, replaceEventLabel)
                    ),
                    original: extractValue(
                        record.oldValue,
                        record.pathFull,
                        allPaymentMethods,
                        getUserFullNameFromUUID,
                        allAddressIds
                    ),
                    new: extractValue(
                        record.newValue,
                        record.pathFull,
                        allPaymentMethods,
                        getUserFullNameFromUUID,
                        allAddressIds
                    ),
                    adjustedBy: getUserFullName(record.originalRecord),
                    metadata: {
                        lookupName: record.lookupName,
                        path: record.pathFull,
                        timestamp: record.originalRecord._key.timestamp,
                        lossIdentification,
                        refExtension
                    },
                    eventCaseInformation: {
                        link: `CapEventCase/${eventCase._key.rootId}/${eventCase._key.revisionNo}`,
                        number: eventCase.lossNumber ?? ''
                    }
                }
            })
            // change history result is sorted in asc order by default,
            // reversing it here to get the proper desc order
            .reverse()
    )
}

/**
 * One change history entry can include multiple changes.
 * The method flattens these records into multiple ones with a single patch each
 */
export function flattenChangeHistoryRecords(
    changeHistoryRecords: ChangeHistoryRecordWithUser[]
): ChangeHistoryRecordFlatten[] {
    return changeHistoryRecords.reduce((acc, record) => {
        const allPaths = new Set([
            ...record.forwardPatch.patch.map(p => p.path.value),
            ...record.inversePatch.patch.map(p => p.path.value)
        ])

        const innerRecords = [...allPaths]
            .map(path => {
                const forwardPatchList = record.forwardPatch.patch.filter(p => p.path.value === path)
                const inversePatchList = record.inversePatch.patch.filter(p => p.path.value === path)
                if (inversePatchList.length > 1) {
                    const forwardPatch = record.forwardPatch.patch.find(p => p.path.value === path)
                    return inversePatchList.map(inversePatchTmp => {
                        return {
                            ...record,
                            forwardPatch: forwardPatch || {...inversePatchTmp, value: undefined},
                            inversePatch: inversePatchTmp || {...forwardPatch, value: undefined}
                        }
                    })
                }
                const inversePatch = record.inversePatch.patch.find(p => p.path.value === path)
                return forwardPatchList.map(forwardPatch => {
                    return {
                        ...record,
                        forwardPatch: forwardPatch || {...inversePatch, value: undefined},
                        inversePatch: inversePatch || {...forwardPatch, value: undefined}
                    }
                })
            })
            .flat()
        return [...acc, ...innerRecords]
    }, [] as ChangeHistoryRecordFlatten[])
}

/**
 * Flattens complex entity changes into multiple primitive ones
 */
function flattenHistoryEntitiesIntoRecords(
    changeHistoryRecords: ChangeHistoryRecordFlatten[]
): ChangeHistoryRecordFlattenExtended[] {
    return changeHistoryRecords.reduce((acc, record) => {
        const path = record.forwardPatch.path.value.replaceAll('/', '.')
        const modelName = record.filter.values.modelName
        const attribute = getAttribute(path, modelName)

        // No attribute found, assuming the record is invalid, skip
        if (!attribute) {
            return acc
        }

        const isPrimitiveType = isAttributePrimitive(attribute)

        const updatedRecord: ChangeHistoryRecordFlattenExtended = {
            originalRecord: record,
            oldValue: record.inversePatch?.value,
            newValue: record.forwardPatch?.value,
            lookupName: getLookupNameByAttributePath(path, modelName),
            parentEntity: getEntityType(attribute, path, modelName),
            pathFull: path,
            pathToAttribute: isPrimitiveType ? extractAttributeNameFromPath(path) : path,
            pathToParent: isPrimitiveType ? extractParentPathFromAttributePath(path) : path
        }

        if (isPrimitiveType) {
            return [...acc, updatedRecord]
        }
        const newValues = flattenObjectRecord(path, updatedRecord.originalRecord.forwardPatch.value as BusinessEntity)
        const oldValues = flattenObjectRecord(path, updatedRecord.originalRecord.inversePatch.value as BusinessEntity)

        const allValuePaths = new Set([...newValues.map(p => p.path), ...oldValues.map(p => p.path)])
        const groupedValues = [...allValuePaths]
            .map(valuePath => {
                const newValue = newValues.find(p => p.path === valuePath)
                const oldValue = oldValues.find(p => p.path === valuePath)
                return {
                    pathFull: valuePath,
                    newValue: newValue?.value,
                    oldValue: oldValue?.value
                }
            })
            .filter(entry => !isEmpty(entry.oldValue) || !isEmpty(entry.newValue))

        const flattenedEntityChangeRecord = groupedValues.map(value => ({
            ...updatedRecord,
            ...value,
            pathToAttribute: value.pathFull.replace(`${updatedRecord.pathToParent}.`, ''),
            lookupName: getLookupNameByAttributePath(value.pathFull, modelName)
        }))

        return [...acc, ...flattenedEntityChangeRecord]
    }, [] as ChangeHistoryRecordFlattenExtended[])
}

function getAmountNum(amount: ChangeHistoryValueNumeric): number {
    return isHistoryValueInteger(amount) ? amount.num : amount.bigDecimal
}

function getStringValue(
    obj: ChangeHistoryValueString,
    pathFull: string,
    paymentMethods: PaymentMethod[],
    getUserFullNameFromUUID?: (uuid: string) => string,
    addressIds?: CrmAddress[]
): string {
    /**
     * Formats a Date value using `dateValue`, preserving UTC semantics when the time is midnight (00:00:00.000Z).
     *
     * If the date's UTC time is exactly midnight, it is treated as a "date-only" value
     * and formatted date by locale without converting to local time.
     * Otherwise, the original Date object is formatted normally.
     */
    if (obj.value instanceof Date) {
        const inputDate = obj.value
        const iso = inputDate.toISOString()
        const isUtcMidnight = iso.endsWith('T00:00:00.000Z')
        const utcDate = isUtcMidnight ? iso.substring(0, 10) : inputDate
        return dateValue(utcDate).toString()
    }
    if (isEisDate(obj.value) || isEisDateTime(obj.value)) {
        return dateUtils(obj.value).format()
    }

    const paymentAttributes = [
        'memberPaymentMethodId',
        'payeePaymentMethodId',
        'beneficiaryPaymentMethodId',
        'paymentMethodId'
    ]
    if (paymentAttributes.some(paymentAttribute => pathFull?.includes(paymentAttribute))) {
        return getPaymentMethodInfo(paymentMethods, obj.value)
    }

    const addressAttributes = ['checkAddressId', 'memberCheckAddressId']
    if (addressAttributes.some(addressAttribute => pathFull?.includes(addressAttribute))) {
        return getAddressInfo(addressIds!, obj.value)
    }

    return getUserFullNameFromUUID ? getUserFullNameFromUUID(obj.value) : obj.value
}

function getPaymentMethodInfo(paymentMethods: PaymentMethod[], paymentMethodId: string): string {
    const resolvedPaymentMethod = paymentMethods.find(paymentMethod => paymentMethod?._key?.id === paymentMethodId)

    switch (resolvedPaymentMethod?._type) {
        case PaymentMethodType.CASH:
            return t('cap-core:preferred_payment_method_one_time_cash')
        case PaymentMethodType.CHECK:
            return t('cap-core:preferred_payment_method_one_time_check')
        case PaymentMethodType.CREDIT_CARD:
            return `${resolvedPaymentMethod.cardType}  ****${resolvedPaymentMethod.cardNumber}`
        case PaymentMethodType.EFT:
            return t('cap-core:preferred_payment_method_eft', {
                value: resolvedPaymentMethod.accountNumber.substring(resolvedPaymentMethod.accountNumber.length - 4)
            })
        default:
            return t('cap-core:not_available').toUpperCase()
    }
}

function getAddressInfo(addressInfos: CrmAddress[], checkAddressId: string): string {
    const resolvedAddressInfo = addressInfos.find(addressInfo => addressInfo?._key?.id === checkAddressId)
    return resolvedAddressInfo ? resolvedAddressInfo?.location?.addressLine1 : checkAddressId
}

function extractValue(
    obj: ChangeHistoryValue | undefined,
    pathFull?: string,
    paymentMethods?: PaymentMethod[],
    getUserFullNameFromUUID?: (uuid: string) => string,
    addressIds?: CrmAddress[]
): string | string[] {
    if (!obj) {
        return ''
    }

    if (isArray(obj)) {
        return obj.flatMap((value: ChangeHistoryValue) => extractValue(value))
    }

    if (isHistoryValueMoney(obj)) {
        const amount = getAmountNum(obj.amount)
        return moneyByLocale(amount)
    }

    if (isHistoryValueInteger(obj)) {
        return String(obj.num)
    }

    if (isHistoryValueDecimal(obj)) {
        return String(obj.bigDecimal)
    }

    if (isHistoryValueTypeString(obj)) {
        return obj.valueType === 'NULL' ? NoDataVal : obj.valueType
    }

    if (isHistoryValueUri(obj)) {
        return obj._uri.value
    }

    if (isHistoryValueString(obj)) {
        return getStringValue(obj, pathFull!, paymentMethods!, getUserFullNameFromUUID, addressIds)
    }

    return ''
}

function separateAttributeNames(str: string): string {
    return str
        .replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2')
        .replace(/([a-z\d])([A-Z])/g, '$1 $2')
        .replace(/\b\w/g, l => l.toUpperCase())
}

function getDisplayLabelForAttribute(
    attributePath: string,
    parentEntity: string,
    modelName: string,
    replaceEventLabelCallback: (attributePath: string) => string
): string {
    const attributeName = extractAttributeNameFromPath(attributePath)
    const attribute = getAttribute(attributePath, modelName)
    const entityType = (attribute && getEntityType(attribute, attributePath, modelName)) || parentEntity

    if (attributePath.includes('lossDetail.events') && attributeName === 'eventDate') {
        return replaceEventLabelCallback(attributePath)
    }
    const translateKeys = [
        `${modelName}:${entityType}_${attributeName}_label`,
        `cap-core:${entityType}_${attributeName}_label`
    ]
    const translations = translateKeys
        .map(key => ({key, translation: t(key)}))
        .filter(({translation, key}) => translation !== key)
    return translations.length ? translations[0].translation : separateAttributeNames(attributeName)
}

function getDisplayLabelForEntity(path: string, entity: string, modelName: string): string {
    // There's no labels for Ref attributes, so we add manual translation for each root reference, 1 for each model definition
    if (isRootEntityReference(entity, modelName) || isRootEntity(entity, modelName)) {
        return t(`cap-core:history_table_root_entity_${modelName}`)
    }
    const attributeLabel = getLabelAnnotationByAttributePath(path, modelName)

    const attributePath = removeIndexFromPathEnd(path)
    const attributeName = extractAttributeNameFromPath(attributePath)

    // Parent entity name is needed to resolve translation resource
    const parentAttributePath = extractParentPathFromAttributePath(attributePath)
    let parentEntity = getModelRootEntityTypeName(modelName)
    if (parentAttributePath) {
        const parentAttribute = getAttribute(parentAttributePath, modelName)
        if (parentAttribute) {
            parentEntity = getEntityType(parentAttribute, path, modelName)
        }
    }

    // Example: CapSmp:CapSMPEarningsInformationEntity_salaryMode_label, see generated resource files in model packages
    return attributeLabel
        ? t(`${modelName}:${parentEntity}_${attributeName}_label`)
        : `history_table_unlabeled_entity_${entity}_${attributeName}`
}

function getUserFullName(historyRecord: ChangeHistoryRecordFlatten): string {
    const {firstName, lastName} = historyRecord.userDomain?.personInfo || {}
    return firstName && lastName
        ? `${firstName} ${lastName}`
        : historyRecord.userDomain?.securityIdentity ||
              historyRecord.user.split('/').slice(-2, -1)?.[0] ||
              historyRecord.user
}

const getClaimType = claim => ClaimModelToType[claim?.claimType ?? (claim?._modelName as keyof typeof ClaimModelToType)]
const getCoverageFromSettlement = settlement => {
    const claimCoveragePrefix = settlement?.claimCoveragePrefix ? ` - ${settlement?.claimCoveragePrefix}` : ''
    return settlement?.claimCoverageName
        ? `${settlement?.claimCoverageName}${claimCoveragePrefix}`
        : settlement?.settlementLossInfo?.coverageType
}
const getClaimNumberFromSettlement = settlement =>
    settlement?.settlementLossInfo?.claimNumber ?? settlement?.settlementLossInfo?.lossNumber

function extractLossDataFromEventCase(
    record: ChangeHistoryRecordFlattenExtended,
    eventCase: Entities['eventCase']
): LossExtractData {
    const reference = opt(eventCase.lossNumber).orElse(t('cap-core:not_available'))
    const component = t('cap-core:history_table_component_event_case')
    const {modelName, rootId, revisionNo} = record.originalRecord.filter.values
    return {
        reference,
        component,
        lossIdentification: {
            modelName,
            rootId,
            revisionNo
        } as LossIdentification
    }
}

function extractLossDataFromClaim(
    record: ChangeHistoryRecordFlattenExtended,
    claims: Entities['claims']
): LossExtractData {
    const NA = t('cap-core:not_available')
    const {modelName, rootId, revisionNo} = record.originalRecord.filter.values
    const claim = claims.find(claimLoss => claimLoss._key.rootId === rootId)
    const reference = opt(claim?.lossNumber).orElse(NA)
    const component = t('cap-core:history_table_component_claim', {claimType: getClaimType(claim)})

    return {
        reference,
        component,
        lossIdentification: {
            modelName,
            rootId,
            revisionNo
        } as LossIdentification
    }
}

function extractLossDataFromSettlement(
    record: ChangeHistoryRecordFlattenExtended,
    settlements: Entities['settlements']
): LossExtractData {
    const NA = t('cap-core:not_available')
    const {rootId} = record.originalRecord.filter.values
    const settlement = settlements.find(settlementTmp => settlementTmp._key.rootId === rootId)
    const settlementLossInfo = settlement?.settlementLossInfo

    const coverage = getCoverageFromSettlement(settlement) ?? NA
    const componentType =
        ClaimModelToType[settlementLossInfo?.claimType as keyof typeof ClaimModelToType as string] ??
        ClaimModelToType[settlementLossInfo?.lossType as keyof typeof ClaimModelToType as string] ??
        NA

    const reference = getClaimNumberFromSettlement(settlement) ?? NA
    const component = t('cap-core:history_table_component_claim_and_coverage', {
        coverageType: coverage,
        claimType: componentType
    })

    const lossUri = settlement?.claimWrapperIdentification?._uri ?? settlementLossInfo?.lossSource?._uri

    return {
        reference,
        component,
        lossIdentification: EntityLink.isValid(lossUri) ? (EntityLink.from(lossUri) as LossIdentification) : undefined
    }
}

function extractLossDataFromSpecialHandling(
    record: ChangeHistoryRecordFlattenExtended,
    {eventCase, claims, specialHandlings}: Pick<Entities, 'eventCase' | 'claims' | 'specialHandlings'>
): LossExtractData {
    const NA = t('cap-core:not_available')
    const {modelName, rootId} = record.originalRecord.filter.values
    const specialHandling = specialHandlings.find(specialHndl => specialHndl._key.rootId === rootId)
    const lossUri = specialHandling?.claimLossIdentification?._uri

    if (!lossUri || !EntityLink.isValid(lossUri)) {
        return {component: modelName, reference: NA}
    }

    if (isEventCaseUri(lossUri)) {
        const caseReference = opt(eventCase?.lossNumber).orElse(NA)
        const caseComponent = t('cap-core:history_table_component_event_case')
        return {
            reference: caseReference,
            component: caseComponent,
            lossIdentification: EntityLink.isValid(lossUri)
                ? (EntityLink.from(lossUri) as LossIdentification)
                : undefined
        }
    }

    const claim = claims.find(claimLoss => claimLoss._key.rootId === EntityLink.from(lossUri).rootId)
    const reference = opt(claim?.lossNumber).orElse(NA)
    const component = t('cap-core:history_table_component_claim', {claimType: getClaimType(claim)})

    return {
        reference,
        component,
        lossIdentification: {
            modelName: claim?._modelName,
            rootId: claim?._key.rootId,
            revisionNo: claim?._key.revisionNo
        } as LossIdentification
    }
}

export const extractLossDataFromCapRelationship = (
    record: ChangeHistoryRecordFlattenExtended,
    {eventCase, capRelationships}: Pick<Entities, 'eventCase' | 'capRelationships'>
) => {
    const reference = opt(eventCase.lossNumber).orElse(t('cap-core:not_available'))
    const component = t('cap-core:history_table_component_event_case')

    return {
        reference,
        component,
        lossIdentification: {
            modelName: eventCase?._modelName,
            rootId: eventCase?._key.rootId,
            revisionNo: `${eventCase?._key.revisionNo ?? ''}`
        } as LossIdentification
    }
}

export const filterRelationshipData = (record: ChangeHistoryRecordFlattenExtended) => {
    if (record.parentEntity === 'CapRelationshipEntity') {
        return !['originSource', 'manualAdd'].includes(record.pathToAttribute)
    }
    return true
}

const getPaymentTemplateForARecord = (
    record: ChangeHistoryRecordFlattenExtended,
    paymentTemplates: Entities['paymentTemplates']
) => {
    const recordRevisionNo = parseInt(record.originalRecord.filter.values.revisionNo, 10) || 1
    const recordRootId = record.originalRecord.filter.values.rootId
    const paymentTemplateRevisionNo =
        record.originalRecord.forwardPatch.op.value !== 'add' && recordRevisionNo > 1
            ? recordRevisionNo - 1
            : recordRevisionNo
    return paymentTemplates
        ?.filter(pt => pt._key.rootId === recordRootId)
        ?.find(pt => pt._key.revisionNo === paymentTemplateRevisionNo)
}

const getSettlement = (
    paymentTemplateForARecord: any,
    settlements: Entities['settlements'],
    recordPathFull: string
): Settlements | undefined => {
    if (recordPathFull.includes('expenses')) {
        return undefined
    }
    const extractIndexFromPath = (path: string): number => {
        const regex = /\.(\d+)\./
        const match = regex.exec(path)
        if (match && match[1]) {
            return parseInt(match[1], 10)
        }
        return 0
    }
    const settlementLink =
        paymentTemplateForARecord?.buildPaymentScheduleInput?.settlements[extractIndexFromPath(recordPathFull)]?.uri ||
        ''
    return EntityLink.isValid(settlementLink)
        ? settlements.find(
              settlementDetails => settlementDetails._key.rootId === EntityLink.from(settlementLink).rootId
          )
        : undefined
}

const getIncidentDate = (paymentTemplate?: CapPaymentTemplateEntity, settlement?: Settlements) =>
    dateUtils(
        paymentTemplate?.paymentDetailsTemplate?.paymentAllocationTemplates.find(pat =>
            pat.allocationSource?._uri.includes(settlement?._key?.rootId || '')
        )?.allocationLossInfo?.dateOfLoss || ''
    ).toString()

function extractLossDataFromPaymentSchedule(
    record: ChangeHistoryRecordFlattenExtended,
    paymentTemplates: Entities['paymentTemplates'],
    settlements: Entities['settlements'],
    claims: Entities['claims']
): LossExtractData {
    const NA = t('cap-core:not_available')
    const paymentTemplateForARecord = getPaymentTemplateForARecord(record, paymentTemplates)
    const settlement = getSettlement(paymentTemplateForARecord, settlements, record.pathFull)
    const isLifeSettlement = Object.keys(ChangeHistorySettlementModelNamesLifeClaims).includes(
        settlement?._modelName || ''
    )
    const lossSource = get(paymentTemplateForARecord, record.pathToParent.slice(1))?.lossSource?._uri
    const getClaimFormExpenseOrExGratia = () => claims.find(claim => lossSource?.includes(claim._key.rootId))

    const claimNumber = getClaimNumberFromSettlement(settlement) ?? getClaimFormExpenseOrExGratia()?.lossNumber ?? NA
    const lossUri =
        settlement?.claimWrapperIdentification?._uri ?? settlement?.settlementLossInfo?.lossSource._uri ?? lossSource
    const coverageName = getCoverageFromSettlement(settlement) ?? getClaimFormExpenseOrExGratia()?.coverageType ?? NA
    const formatDate = (value: Moment | string): string => {
        const dateMoment = dateUtils(value).toMoment
        return dateMoment.isValid() ? dateMoment.format(t('cap-core:default_date_format')) : (value as string)
    }

    const constructRefExtension = (): string => {
        if (record.pathToParent.includes('expenses')) {
            return ''
        }
        if (record.pathToParent.includes('exGratias')) {
            return ''
        }
        return isLifeSettlement
            ? `, ${tryTranslateValue(coverageName)} ${formatDate(getIncidentDate(paymentTemplateForARecord, settlement))}`
            : `, ${coverageName}`
    }
    return {
        component: t('cap-core:history_table_component_payment'),
        reference: claimNumber,
        lossIdentification: EntityLink.isValid(lossUri) ? (EntityLink.from(lossUri) as LossIdentification) : undefined,
        refExtension: constructRefExtension()
    }
}
