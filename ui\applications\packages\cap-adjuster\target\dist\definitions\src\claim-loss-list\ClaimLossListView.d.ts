import * as React from 'react';
interface ClaimLossListViewProps {
    /**
     * Callback for 'Search' action button.
     */
    readonly onSearch: (searchCriteria: string) => void;
    /**
     * Claim loss data.
     */
    readonly data: any[];
}
export declare class ClaimLossListViewComponent extends React.Component<ClaimLossListViewProps> {
    render(): React.ReactNode;
}
export declare const ClaimLossListViewLoader: React.FC<Partial<import("react-router").RouterState> & Partial<ClaimLossListViewProps>>;
export {};
//# sourceMappingURL=ClaimLossListView.d.ts.map