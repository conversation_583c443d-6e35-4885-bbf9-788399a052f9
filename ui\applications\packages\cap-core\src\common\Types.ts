/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {authentication} from '@eisgroup/auth'
import {CapLeave, CapLtd, CapSmp, CapSmpSettlement, CapStd, CapStdSettlement} from '@eisgroup/cap-disability-models'
import {
    AcceleratedSettlement,
    AccidentalDismembermentSettlement,
    BusinessTypes,
    CapEventCase,
    CISettlement,
    DeathSettlement,
    HISettlement,
    PremiumWaiverSettlement
} from '@eisgroup/cap-event-case-models'
import {CapPaymentSchedule, CapPaymentTemplate, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {
    CapAdjusterClaimWrapperIdentifierRequest,
    CapAdjusterClaimWrapperLifeClaimClosureToOpenItemsOutputs,
    CapAdjusterEventCaseEventCaseClosureToOpenItemsOutputs,
    CapAdjusterEventCaseIdentifierRequest,
    CapAdjusterLeaveLossAbsenceClaimClosureToOpenItemsOutputs,
    CapAdjusterLeaveLossIdentifierRequest,
    CapAdjusterLtdLossAbsenceClaimClosureToOpenItemsOutputs,
    CapAdjusterLtdLossIdentifierRequest,
    CapAdjusterSmpLossAbsenceClaimClosureToOpenItemsOutputs,
    CapAdjusterSmpLossIdentifierRequest,
    CapAdjusterStdLossAbsenceClaimClosureToOpenItemsOutputs,
    CapAdjusterStdLossIdentifierRequest
} from '@eisgroup/cap-gateway-client'
import {
    CapClaimDefaultSettlementWithEditingStatus,
    CaseSystemService,
    ClaimParty,
    CSClaimWrapperService,
    CustomerType,
    EntityKeyParams,
    FormInputType,
    IndividualCustomer,
    LossParams,
    OrganizationCustomer,
    OrgPerson,
    PartyRole,
    WorkQueueDetails
} from '@eisgroup/cap-services'
import {PaymentMethod} from '@eisgroup/common-business-components'
import {RxResult} from '@eisgroup/common-types'
import {Money} from '@eisgroup/models-api'
import {StoreState} from '@eisgroup/state'
import {EisIconType} from '@eisgroup/ui-kit-icons'
import {EisDate, EisDateTime} from '@eisgroup/ui-temporals'
import {IObservableArray} from 'mobx'
import {Moment} from 'moment'
import * as React from 'react'
import {FormDrawerStore} from '../components/form-drawer'
import {ClaimTypesMap, FrequencyType} from './constants'
import {
    BalanceStore,
    CaseSystemPaymentStore,
    ClaimPartyStore,
    ClaimStore,
    EmploymentStore,
    EventCaseStore
} from './store'
import CapAcceleratedSettlementEntity = AcceleratedSettlement.CapAcceleratedSettlementEntity
import CapAccidentalDismembermentSettlementEntity = AccidentalDismembermentSettlement.CapAccidentalDismembermentSettlementEntity
import CapAbsenceDetailEntity = CapEventCase.CapAbsenceDetailEntity
import CapAcceleratedDeathDetailEntity = CapEventCase.CapAcceleratedDeathDetailEntity
import CapAccidentDetailEntity = CapEventCase.CapAccidentDetailEntity
import CapDeathDetailEntity = CapEventCase.CapDeathDetailEntity
import CapWellnessDetailEntity = CapEventCase.CapWellnessDetailEntity
import CapLeaveFinancialAdjustmentOffsetEntity = CapLeave.CapLeaveFinancialAdjustmentOffsetEntity
import CapLTDFinancialAdjustmentOffsetEntity = CapLtd.CapLTDFinancialAdjustmentOffsetEntity
import CapPaymentAdditionEntity = CapPaymentSchedule.CapPaymentAdditionEntity
import CapPaymentAllocationEntity = CapPaymentSchedule.CapPaymentAllocationEntity
import CapPaymentScheduleExGratiasInput = CapPaymentSchedule.CapPaymentScheduleExGratiasInput
import CapPaymentScheduleExpensesInput = CapPaymentSchedule.CapPaymentScheduleExpensesInput
import CapPaymentScheduleSettlementInterestsInput = CapPaymentSchedule.CapPaymentScheduleSettlementInterestsInput
import CapScheduledPaymentEntity = CapPaymentSchedule.CapScheduledPaymentEntity
import CapManualEOBRemarksEntity = CapPaymentTemplate.CapManualEOBRemarksEntity
import CapPaymentAdditionDetailsInterestEntity = CapPaymentTemplate.CapPaymentAdditionDetailsInterestEntity
import CapPaymentScheduleSettlementInfoInput = CapPaymentTemplate.CapPaymentScheduleSettlementInfoInput
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity
import CapSMPFinancialAdjustmentOffsetEntity = CapSmp.CapSMPFinancialAdjustmentOffsetEntity
import CapSmpSettlementEntity = CapSmpSettlement.CapSmpSettlementEntity
import CapSTDFinancialAdjustmentOffsetEntity = CapStd.CapSTDFinancialAdjustmentOffsetEntity
import CapStdSettlementEntity = CapStdSettlement.CapStdSettlementEntity
import CapCISettlementEntity = CISettlement.CapCISettlementEntity
import CapDeathSettlementEntity = DeathSettlement.CapDeathSettlementEntity
import CapHISettlementEntity = HISettlement.CapHISettlementEntity
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity
import CapPremiumWaiverSettlementEntity = PremiumWaiverSettlement.CapPremiumWaiverSettlementEntity

export type CapEventCaseEntity = CapEventCase.CapEventCaseEntity

export interface ClaimBannerDetailsColumn {
    /**
     * Claim banner detail title.
     */
    readonly title: string
    /**
     * Claim banner detail renderer.
     */
    readonly render: () => React.ReactNode
}

export interface StoreStateForDrawer<D extends object> extends StoreState<D> {
    prevState?: D
}

export interface FinancialEntity {
    /**
     * Cap payment entity holding payment details.
     */
    readonly payment: CapPaymentEntity
    /**
     * If payment is scheduled, this is its payment template.
     */
    readonly paymentTemplate?: CapPaymentTemplateEntity
    /**
     * Loss info payment associated to
     */
    readonly lossInfo?: LossParams
}

export type AbsenceReasonPartyInformation = {
    readonly parties: IndividualCustomer[]
    readonly partyRoles?: PartyRole[]
}

export interface ListIdxSearchParameters {
    /**
     * Lists path in eisModel state store.
     */
    readonly pathToList: string
    /**
     * Path to exact field inside lists object, which has been provided in pathToList parameter.
     */
    readonly pathToField: string
    /**
     * EIS store state model.
     */
    readonly eisModel: StoreState<any>
    /**
     * Value that is being searched.
     */
    readonly searchedValue: string | number | boolean
}

export interface LookupValue {
    code: string
    displayValue: string
    filters?: object
    orderNo?: number
}

export type OptionValue = {
    readonly code: string
    readonly displayValue: any
    readonly eisIcon?: EisIconType
}

export type OptionValueWithClassType = OptionValue & {
    readonly classType: number
}

export interface ErrorMessage {
    code: string
    message: string
}

export type OffsetEntity =
    | CapLTDFinancialAdjustmentOffsetEntity
    | CapSMPFinancialAdjustmentOffsetEntity
    | CapSTDFinancialAdjustmentOffsetEntity
    | CapLeaveFinancialAdjustmentOffsetEntity

export type RegistryTypeIdAware = {
    readonly registryTypeId?: string
}

export enum ClaimType {
    STD = 'STD',
    SMD = 'SMD',
    LTD = 'LTD'
}

// UI Builder Types
export type PropsCustomizerMap = Record<
    string,
    (props: Record<string, any>, meta: {prefix: string; prefixIdx: string}) => Record<string, any>
>

export type TooltipTrigger = 'hover' | 'focus' | 'click' | 'contextMenu'

export type EntityType = 'case' | 'claim'

export type DefaultQuery<T = string> = {
    [key: string]: T | T[] | null | undefined
}

/**
 * Labels for Actions control.
 */
export interface IActionsControlLabels {
    /**
     * The control label.
     */
    actions?: string
}

/**
 * Type of action item to show in the control
 */
export type ActionItem = {
    /**
     * Action name
     */
    label: string
    /**
     * Indicates whether action should be disabled
     */
    disabled?: boolean
    /**
     * Action tooltip
     */
    tooltip?: string
    /**
     * Children items
     */
    children?: any
    /**
     * Is action should be shown
     */
    isHidden?: boolean
}

export interface OpenPartyDrawerPayload {
    readonly customerType: CustomerType
    readonly formInputType?: FormInputType
    readonly showBackButton?: boolean
}

export interface AuthenticatedUserData extends authentication.AuthenticatedUser {
    readonly roles: {[roleName: string]: boolean}
    readonly authorityLevel: number
    readonly authorities: {[authorityName: string]: boolean}
}

export type RxResultReturnType<F extends RxResult<any>> = F extends RxResult<infer T> ? T : never
export type PromiseReturnType<F extends Promise<any>> = F extends Promise<infer T> ? T : never

export enum IntakeWizardStepKey {
    Member = 'member',
    CaseDetails = 'caseDetails',
    ClaimDetails = 'claimDetails',
    AdditionalParties = 'Additional Parties'
}

export type RangePickerType =
    | undefined[]
    | [moment.Moment]
    | [undefined, moment.Moment]
    | [moment.Moment, moment.Moment]
    | EisDateTime[]
    | EisDate[]

enum BaseSpecialHandlings {
    SIU_IND = 'siuInd',
    APPEAL_IND = 'appealInd',
    COMPLAINT_IND = 'complaintInd',
    ATTORNEY_IND = 'attorneyInd',
    LITIGATION_IND = 'litigationInd',
    REINSURANCE_IND = 'reinsuranceInd'
}

enum ClaimSpecialHandlings {
    POLICYEXCLUSION_IND = 'policyExclusionInd',
    ATP_IND = 'atpInd',
    ATPWITHCHECK_IND = 'atpWithCheckInd',
    SALVAGE_IND = 'salvageInd',
    SUBROGATION_IND = 'subrogationInd'
}

enum ClaimDisabilityExtraSpecialHandlings {
    RUNIN_IND = 'runInInd',
    TAKEOVER_IND = 'takeOverInd'
}

export interface SpecialHandlingTypes {
    readonly SIU_IND: BaseSpecialHandlings.SIU_IND
    readonly APPEAL_IND: BaseSpecialHandlings.APPEAL_IND
    readonly COMPLAINT_IND: BaseSpecialHandlings.COMPLAINT_IND
    readonly ATTORNEY_IND: BaseSpecialHandlings.ATTORNEY_IND
    readonly LITIGATION_IND: BaseSpecialHandlings.LITIGATION_IND
    readonly POLICYEXCLUSION_IND?: ClaimSpecialHandlings.POLICYEXCLUSION_IND
    readonly ATP_IND?: ClaimSpecialHandlings.ATP_IND
    readonly ATPWITHCHECK_IND?: ClaimSpecialHandlings.ATPWITHCHECK_IND
    readonly SALVAGE_IND?: ClaimSpecialHandlings.SALVAGE_IND
    readonly SUBROGATION_IND?: ClaimSpecialHandlings.SUBROGATION_IND
    readonly REINSURANCE_IND?: BaseSpecialHandlings.REINSURANCE_IND
    readonly RUNIN_IND?: ClaimDisabilityExtraSpecialHandlings.RUNIN_IND
    readonly TAKEOVER_IND?: ClaimDisabilityExtraSpecialHandlings.TAKEOVER_IND
}

export const CaseSpecialHandlings = {
    ...BaseSpecialHandlings
}
export const ClaimWrapperSpecialHandlings = {
    ...CaseSpecialHandlings,
    ...ClaimSpecialHandlings
}
export const AbsenceSpecialHandlings = {
    ...ClaimWrapperSpecialHandlings,
    ...ClaimDisabilityExtraSpecialHandlings
}

export enum CaseStates {
    INCOMPLETE = 'Incomplete',
    OPEN = 'Open',
    CLOSED = 'Closed'
}

export enum ClaimStates {
    OPEN = 'Open',
    PENDING = 'Pending',
    CLOSED = 'Closed',
    INCOMPLETE = 'Incomplete'
}

export enum HeaderStatusCode {
    INCOMPLETE = 'INCOMPLETE',
    OPEN = 'OPEN',
    CLOSED = 'CLOSED',
    ADJUDICATED = 'ADJUDICATED',
    PENDING = 'PENDING'
}

export enum LossActionMode {
    SUBMIT_LOSS = 'SubmitLoss',
    UPDATE_LOSS_INFORMATION = 'UpdateLossInformation',
    CLOSE_LOSS = 'CloseLoss',
    REOPEN_LOSS = 'ReopenLoss',
    SET_SUB_STATUS = 'SetSubStatus',
    CHANGE_SUB_STATUS = 'ChangeSubStatus',
    ADD_RELATED_CASE = 'AddRelatedCase',
    CREAT_DOCUMENT = 'CreatDocument',
    POLICY_REFRESH = 'PolicyRefresh',
    FOLLOW_UP_TASK = 'FollowUpTask',
    VIEW_CASE_DETAILS = 'ViewCaseDetails'
}

export function getClaimActionDrawerName(actionName: LossActionMode | string): string {
    return `${actionName}Drawer`
}

export const LossActionDrawerKey = {
    UPDATE_DRAWER_KEY: 'UpdateDrawer',
    VIEW_INFORMATION_DRAWER_KAY: 'ViewInformationDrawer',
    CLOSE_DRAWER_KEY: getClaimActionDrawerName(LossActionMode.CLOSE_LOSS),
    REOPEN_DRAWER_KEY: getClaimActionDrawerName(LossActionMode.REOPEN_LOSS),
    SET_SUBSTATUS_DRAWER_KEY: getClaimActionDrawerName(LossActionMode.SET_SUB_STATUS),
    CHANGE_SUBSTATUS_DRAWER_KEY: getClaimActionDrawerName(LossActionMode.CHANGE_SUB_STATUS),
    UPDATE_SPECIAL_HANDLING: 'UpdateSpecialHandling',
    ASSIGN_FORM_DRAWER_KEY: 'AssignDrawer',
    POLICY_REFRESH_DRAWER_KEY: getClaimActionDrawerName(LossActionMode.POLICY_REFRESH),
    FOLLOW_UP_TASK_DRAWER_KEY: getClaimActionDrawerName(LossActionMode.FOLLOW_UP_TASK),
    ADD_RELATED_CASE_KEY: getClaimActionDrawerName(LossActionMode.ADD_RELATED_CASE)
}

export interface ClaimActionDrawerInfo {
    drawerType?: LossActionMode
}

export interface DateMapping {
    months: string
    month: string
    monthShort: string
    days: string
    day: string
    dayShort: string
    years: string
    year: string
    yearShort: string
}

export interface ICaseSystem {
    state?: string
    memberRegistryTypeId?: string
    _key: EntityKeyParams
    _modelName: string
}

export type ICaseSystemPeriod = BusinessTypes.Period

export interface ICaseSystemLossSettlement extends BusinessTypes.CapSettlement {
    _timestamp: number
    _type: string
}

export interface ICaseSystemLoss extends BusinessTypes.CapLoss {
    policyId?: string
    policy?: any
    coverageType?: string
    claimType?: string
    lossType?: string
    _key: EntityKeyParams
    lossDetail?: {
        earnings?: any
        externalTime?: any
        _key: EntityKeyParams
    }
}

export enum customerTypes {
    INDIVIDUAL = 'INDV',
    NONINDIVIDUAL = 'NONINDV'
}

export type SelectInputOptions = {
    code: string
    displayValue: string
}

export type Settlements = (
    | CapSmpSettlementEntity
    | CapStdSettlementEntity
    | CapCISettlementEntity
    | CapHISettlementEntity
    | CapAcceleratedSettlementEntity
    | CapAccidentalDismembermentSettlementEntity
    | CapDeathSettlementEntity
    | CapPremiumWaiverSettlementEntity
) & {readonly associatedClaimRootId: string}

export type ScheduledAllocation = {
    paymentAllocation: CapPaymentAllocationEntity
    settlement?: Settlements
    expense?: CapPaymentScheduleExpensesInput
    exGratia?: CapPaymentScheduleExGratiasInput
    scheduledPayment: CapScheduledPaymentEntity
    paymentAdditions?: CapScheduledPaymentEntity
    reserveType?: string
    eobRemarks?: CapManualEOBRemarksEntity[]
}

export type ScheduledResult = {
    scheduleAllocations: ScheduledAllocation[]
    scheduleAdditions?: CapPaymentAdditionEntity[]
}

export type AllocationTableRecord = CapPaymentScheduleSettlementInfoInput & {
    allocationPeriodForRender?: Moment[]
    scheduledAllocations: ScheduledAllocation[]
    scheduledAdditions?: CapPaymentAdditionEntity[]
    interestDetails?: InterestDetails
    grossAmountMode?: FrequencyType
    reserveType?: string
}

export type InterestDetails = CapPaymentScheduleSettlementInterestsInput & CapPaymentAdditionDetailsInterestEntity

export enum PaymentScheduledState {
    Open = 'Open',
    Active = 'Active',
    Canceled = 'Canceled',
    Suspended = 'Suspended',
    Completed = 'Completed'
}

export interface Allocation {
    selectedClaim: string
    coverageType: string | undefined
    isInterestOnly?: boolean
    claimModelName?: string
    allocationPaymentAmount?: Money
    allocationSource?: string
    allocationPayeeSource?: string
    allocationPeriod?: Moment[] | EisDateTime[] | undefined[] | string
    allocationFrequency?: string
    allocationPayeeProvider?: string
    grossAmountMode?: FrequencyType
    expenseDescription?: string
    expenseNumber?: string
    exGratiaNumber?: string
    exGratiaDescription?: string
    exGratiaAmount?: Money
    eobRemarkCodes?: string[]
    manualEOBRemarks?: CapManualEOBRemarksEntity[]
    otherEOBMessage?: string
    representBeneficiary?: string
    isOtherEOBSelected?: boolean
    key?: string
    _key?: any
}

// Types merged from event-case
export type RelationshipKey = {
    rootId?: string
    revisionNo?: string
}

export type RelationshipParams = {
    fromUri: string
    toUri: string
    relationshipToInsuredCd: string
    _key?: RelationshipKey
    _timestamp?: Date
}

export enum EmployeeFormTypes {
    SearchEmployee = 'searchEmployee',
    AddEmployee = 'addEmployee'
}

export type LossDetailType =
    | CapDeathDetailEntity
    | CapAcceleratedDeathDetailEntity
    | CapAccidentDetailEntity
    | CapWellnessDetailEntity
    | CapAbsenceDetailEntity

export interface ManagerInfo {
    queueInfo?: WorkQueueDetails
    userInfo?: OrgPerson
}

export interface ManagersInfo {
    [key: string]: ManagerInfo
}

export type PaymentMethodWithFormPreferred = PaymentMethod & {
    formPreferred?: boolean
}

export enum CheckAddressTypes {
    MAILING = 'Mailing Check',
    PREFERRED = 'Preferred',
    TEMPORARY = 'Temporary'
}

export type ClaimTypesMapType = keyof typeof ClaimTypesMap

export type CapAdjusterLossIdentifierRequest =
    | CapAdjusterStdLossIdentifierRequest
    | CapAdjusterLtdLossIdentifierRequest
    | CapAdjusterSmpLossIdentifierRequest
    | CapAdjusterLeaveLossIdentifierRequest
    | CapAdjusterEventCaseIdentifierRequest
    | CapAdjusterClaimWrapperIdentifierRequest

export type ClosureToOpenItemsOutputsType =
    | CapAdjusterEventCaseEventCaseClosureToOpenItemsOutputs
    | CapAdjusterClaimWrapperLifeClaimClosureToOpenItemsOutputs
    | CapAdjusterStdLossAbsenceClaimClosureToOpenItemsOutputs
    | CapAdjusterSmpLossAbsenceClaimClosureToOpenItemsOutputs
    | CapAdjusterLtdLossAbsenceClaimClosureToOpenItemsOutputs
    | CapAdjusterLeaveLossAbsenceClaimClosureToOpenItemsOutputs

export type ClaimsForReducePayment = {
    claimType: string
    lossNumber: string
    rootId: string
    revisionNo: number
    _modelName: string
}

export enum RemainAmountType {
    YEAR = 'YEAR',
    GROUP = 'GROUP',
    AMOUNT = 'AMOUNT',
    POPUPLIMITAMOUNT = 'POPUPLIMITAMOUNT',
    POPUPUSEDAMOUNT = 'POPUPUSEDAMOUNT',
    POPUPPLANNEDAMOUNT = 'POPUPPLANNEDAMOUNT'
}

export enum RemainingLimitStatus {
    GROUP = 'Group',
    PEREVENT = 'PerEvent',
    PERCALENDARYEAR = 'PerCalendarYear',
    PERBENEFITYEAR = 'PerBenefitYear',
    PERPOLICY = 'PerPolicy',
    PERCONFINEMENT = 'PerConfinement',
    PERDEPENDENT = 'PerDependent'
}

export enum RemainPopUpBenefitCd {
    TERMLIFE = 'TermLife',
    TLCOVERAGE = 'TLCoverage',
    GTLACCLERATEDBENEFITOPTION = 'GTLAcceleratedBenefitOption',
    TLACCLERATEDBENEFITOPTION = 'TLAcceleratedBenefitOption',
    DEPENDENTTLCOVERAGE = 'DependentTLCoverage',
    SPOUSETLCOVERAGE = 'SpouseTLCoverage',
    CHILDTLCOVERAGE = 'ChildTLCoverage'
}

export enum RemainPopUpGroupCd {
    TLDEATHGROUP = 'TL_Death_Group',
    TLDEPENDENTDEATHGROUP = 'TL_Dependent_Death_Group',
    TLSPOUSEDEATHGROUP = 'TL_Spouse_Death_Group',
    TLCHILDDEATHGROUP = 'TL_Child_Death_Group'
}

export type LinkedCoverages = {
    code: string
    displayValue: string
    item: CapClaimDefaultSettlementWithEditingStatus
}

export enum ApprovalPeriodStatus {
    CANCELLED = 'Cancelled',
    APPROVED = 'Approved',
    TBD = 'TBD',
    COMPLETED = 'Completed'
}

export enum SettlementPeriodTypes {
    ELIMINATION = 'EliminationPeriod',
    BENEFIT = 'BenefitPeriod',
    APPROVAL = 'ApprovalPeriod'
}

export enum deductionAmountTypes {
    WEEKLY = 'WEEKLY',
    MONTHLY = 'MONTHLY',
    FIXED = 'FIXED',
    PERCENTAGE = 'PERCENTAGE'
}

export enum EventCaseIntakeWizardStepKey {
    Member = 'member',
    CaseDetails = 'caseDetails',
    ClaimDetails = 'claimDetails',
    AdditionalParties = 'Additional Parties'
}

export type StoreTypeForBalanceProps<
    CS extends ICaseSystem,
    CSService extends CaseSystemService | CSClaimWrapperService<CS>
> = {
    subjectOfClaim?: IndividualCustomer | OrganizationCustomer
    mainInsured?: IndividualCustomer | OrganizationCustomer
    employmentStore: EmploymentStore
    claimStore: ClaimStore
    claimPartyStore: ClaimPartyStore
    balanceStore: BalanceStore
    formDrawerStore: FormDrawerStore
    paymentsStore: CaseSystemPaymentStore<CS, CSService>
    eventCaseStore: EventCaseStore
    allClaimsBeneficiaries: IObservableArray<ClaimParty>
}
