/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {translateResources} from '@eisgroup/cap-core'
import {Localization} from '@eisgroup/i18n'
import {enUS} from './cap-adjuster-i18n.en'

export const enMT: Localization.ResourceBundle = {
    locale: {country: 'MT', language: 'en'},
    ns: 'cap-adjuster',
    resources: translateResources(enUS.resources)
}
