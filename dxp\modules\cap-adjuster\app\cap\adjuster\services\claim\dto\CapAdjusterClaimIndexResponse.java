/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.dto;

import java.util.List;

import core.services.ApiDTO;

public class CapAdjusterClaimIndexResponse implements ApiDTO {

    public List<CapAdjusterClaimIndex> result;

    public Long count;
}
