/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {SidebarAction} from '@eisgroup/ui-kit'

export const TIMELINE_CLASSNAME = 'bam-activity-list__timeline'
export const TIMELINE_SELECTOR = `.${TIMELINE_CLASSNAME}`

export enum TimelineTabs {
    Activities = '1',
    Interactions = '2'
}

export const clearWorkbenchData: SidebarAction = {
    type: 'SET_DATA',
    data: {
        bam: undefined,
        paymentsStore: undefined,
        lossNumber: undefined
    }
}
