import type {BusinessTypes} from '@eisgroup/cap-event-case-models'
import {
    backOfficeWorkflowService,
    backofficeWorkService,
    OrgPerson,
    WorkflowCase,
    WorkQueueDetails
} from '@eisgroup/cap-services'
import {action, observable, runInAction, toJS} from 'mobx'
import {ICaseSystem} from '../../Types'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {CaseSystemTaskStore} from '../CaseSystemTaskStore'

export const LOAD_WORK_CASES = 'loadWorkCases'
export const LOAD_WORK_USER_PROFILES = 'loadWorkUserProfiles'
export const LOAD_WORK_QUEUES = 'loadWorkQueues'

export class CaseSystemTaskStoreImpl<CS extends ICaseSystem>
    extends BaseRootStoreImpl
    implements CaseSystemTaskStore<CS>
{
    @observable
    associatedLosses?: BusinessTypes.CapLoss[]

    @observable
    userProfiles: Org<PERSON>erson[]

    @observable
    workCases: WorkflowCase[]

    @observable
    workQueues: WorkQueueDetails[]

    constructor(associatedLosses?: BusinessTypes.CapLoss[]) {
        super()
        this.associatedLosses = associatedLosses
    }

    @action
    loadWorkCases(loss?: BusinessTypes.CapLoss, caseSystem?: CS): void {
        let lossURI = ''
        if (loss) {
            lossURI = `gentity://CapLoss/${loss._modelName}//${loss._key.rootId}/${loss._key.revisionNo}`
        }
        if (caseSystem) {
            lossURI = `gentity://CapLoss/${caseSystem._modelName}//${caseSystem._key.rootId}/${caseSystem._key.revisionNo}`
        }
        const associatedLossesURIs =
            toJS(this.associatedLosses)?.map(
                v => `gentity://CapLoss/${v._modelName}//${v._key.rootId}/${v._key.revisionNo}`
            ) || []

        this.callService<WorkflowCase[]>(
            backOfficeWorkflowService.searchCaseByEntityURIs([lossURI, ...associatedLossesURIs]),
            response => {
                runInAction(() => {
                    this.workCases = response
                })
                this.loadWorkUserProfile()
                this.loadWorkQueues()
            },
            LOAD_WORK_CASES
        )
    }

    @action
    loadWorkUserProfile = () => {
        const userIds = this.workCases?.map(v => v.assignmentInfo?.userId).filter(Boolean) as string[]
        if (userIds?.length) {
            this.callService<OrgPerson[]>(
                backofficeWorkService.loadUserProfiles(userIds),
                response => {
                    runInAction(() => {
                        this.userProfiles = response
                    })
                },
                LOAD_WORK_USER_PROFILES
            )
        }
    }

    @action
    loadWorkQueues = () => {
        const queueCd = this.workCases.map(v => v.assignmentInfo?.queueCd).filter(Boolean) as string[]
        if (queueCd.length) {
            this.callService<WorkQueueDetails[]>(
                backOfficeWorkflowService.loadWorkQueues(queueCd),
                response => {
                    runInAction(() => {
                        this.workQueues = response
                    })
                },
                LOAD_WORK_QUEUES
            )
        }
    }
}
