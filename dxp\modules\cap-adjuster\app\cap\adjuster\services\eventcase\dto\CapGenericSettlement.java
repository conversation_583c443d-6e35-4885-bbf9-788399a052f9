/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.eventcase.dto;

import com.eisgroup.dxp.dataproviders.genesiscapeventcase.dto.EntityLinkDTO;

import cap.adjuster.services.common.dto.GenesisRootApiModel;
import dataproviders.dto.CapClaimSettlementResultDTO;

public class CapGenericSettlement extends GenesisRootApiModel {

    public String settlementType;
    public EntityLinkDTO claimLossIdentification;
    public EntityLinkDTO claimWrapperIdentification;
    public Object policy;
    public String policyId;
    public String settlementNumber;
    public Object settlementDetail;
    public String state;
    public Object applicabilityResult;
    public Object settlementLossInfo;
    public Object settlementAbsenceInfo;
    public Object coverageBasedConfiguration;
    public CapClaimSettlementResultDTO settlementResult;
    public Object settlementApprovalResult;
    public String claimCoverageName;
    public String claimCoveragePrefix;
    public Object cashAmountsADBInfo;
    public Object cashAmountsDBInfo;
}
