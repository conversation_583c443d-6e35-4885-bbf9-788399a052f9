/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {Org<PERSON>erson} from '@eisgroup/cap-services'
import {opt} from '@eisgroup/common-types'
import {LocalizationUtils} from '@eisgroup/i18n'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import * as React from 'react'
import {AutocompletableSearch, createNoDataSuggestion, isNotEmpty, ISuggestion, NO_DATA, UserStore} from '../../..'
import t = LocalizationUtils.translate

export interface UserState {
    searchInputValue: string
}

export interface UserSearchProps {
    readonly store: UserStore
}

@observer
export class UserSearch extends React.Component<UserSearchProps, UserState> {
    state = {
        searchInputValue: ''
    }

    render(): React.ReactNode {
        return (
            <div>
                <AutocompletableSearch
                    value={this.state.searchInputValue}
                    valueChangeHandler={value => this.inputChangeHandler(value)}
                    suggestions={this.retrieveUserSuggestions()}
                    onSearchInput={value => value.length > 2 && this.onSearchInputValueChange(value)}
                    onSuggestionSelected={(suggestionKey: any, suggestion?: ISuggestion) =>
                        this.onSuggestionSelected(suggestionKey, suggestion)
                    }
                    onShowAllResultsSelected={() => this.handleShowAllResultsSelected()}
                    placeholder={t('cap-core:search')}
                    defaultActiveFirstOption={false}
                    withDebounce
                    allowClear={false}
                />
            </div>
        )
    }

    private setSearchInputValueState(user: OrgPerson): void {
        this.setState({searchInputValue: this.checkUserSearchInputValue(user)})
    }

    private checkUserSearchInputValue(user: OrgPerson | undefined): string {
        return user && user?.personInfo
            ? [user?.personInfo.firstName, user?.personInfo.lastName].filter(Boolean).join(' ')
            : ''
    }

    private inputChangeHandler(value: string): void {
        if (value !== NO_DATA) {
            this.setState({searchInputValue: value})
        } else {
            this.setState({searchInputValue: ''})
        }
    }

    private retrieveUserSuggestions(): ISuggestion[] {
        const searchSuggestions: ISuggestion[] = []
        const userSearchResults = this.props?.store?.userSearchResults
        if (!isNotEmpty(this.state.searchInputValue)) {
            return []
        }
        if (!userSearchResults || !userSearchResults.length) {
            return this.state.searchInputValue.length > 2 ? createNoDataSuggestion() : []
        }

        userSearchResults.forEach(user => searchSuggestions.push(this.composeOrgPersonSuggestion(user)))
        return this.sortSuggestions(searchSuggestions)
    }

    private composeOrgPersonSuggestion(user: OrgPerson): ISuggestion {
        const firstName = opt(user.personInfo)
            .map(p => p.firstName)
            .orElse('')
        const lastName = opt(user.personInfo)
            .map(p => p.lastName)
            .orElse('')
        return {
            key: `${user._key!.rootId}${user._key!.revisionNo}`,
            value: `${firstName} ${lastName}`
        }
    }

    private sortSuggestions(searchSuggestions: ISuggestion[]): ISuggestion[] {
        return searchSuggestions.sort((a, b) => a.value.localeCompare(b.value))
    }

    private async onSuggestionSelected(suggestionKey: any, suggestion?: ISuggestion): Promise<void> {
        const userSearchResults = toJS(this.props?.store?.userSearchResults)
        const selectedUser =
            userSearchResults &&
            userSearchResults.find(user => `${user._key!.rootId}${user._key!.revisionNo}` === suggestionKey)
        if (selectedUser) {
            this.setSearchInputValueState(selectedUser)
            const inputValue = [selectedUser?.personInfo.firstName, selectedUser?.personInfo.lastName]
                .filter(Boolean)
                .join(' ')
            this.props.store.searchUsers(inputValue, false)
            this.props.store.setUserSearchTableResults([selectedUser])
            this.props.store.selectUser(selectedUser)
        }
    }

    private onSearchInputValueChange(value: string): void {
        this.props?.store?.searchUsers(value, false)
        this.clearSelectedUser()
    }

    private clearSelectedUser(): void {
        if (this.props?.store?.user) {
            this.props.store.selectUser(undefined)
        }
    }

    private handleShowAllResultsSelected(): void {
        this.clearSelectedUser()
        if (this.state.searchInputValue && this.state.searchInputValue.length > 2) {
            this.props?.store?.searchUsers(this.state.searchInputValue, true)
        } else {
            this.props.store.resetUserSearchTableResults()
            this.props.store.resetUserSearchResults()
        }
    }
}
