/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.converters;

import cap.adjuster.services.claim.dto.CapAdjusterClaimIndex;
import core.services.converters.CommonDTOConverter;
import dataproviders.dto.CapLossDTO;

public class CapAdjusterClaimSLSIndexEntityConverter<I extends CapLossDTO, A extends CapAdjusterClaimIndex>
        extends CommonDTOConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.caseSystemId = intDTO.eventCaseLink.uri;
        apiDTO.rootId = intDTO.key.rootId;
        apiDTO.state = intDTO.state;
        return apiDTO;
    }
}
