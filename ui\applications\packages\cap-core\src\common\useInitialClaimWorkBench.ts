/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {useEffect, useMemo} from 'react'

import {useSidebarDispatch, useSidebar} from '@eisgroup/ui-kit'

import {clearWorkbenchData} from '../components/activitiesList/types'

const defaultVisibleNames = ['customerDetails', 'file', 'note', 'task', 'bam', 'alert']

export const useInitialClaimWorkBench = (config?: {visibleActivityNames?: string[]}) => {
    const {visibleActivityNames = defaultVisibleNames} = config ?? {}
    const workbenchDispatch = useSidebarDispatch()
    const {activities} = useSidebar()
    const activityNames = activities.map(activity => activity.name)
    const visibilityActivities = useMemo(
        () =>
            activityNames.map(oneActivity => {
                return {name: oneActivity, isVisible: visibleActivityNames.includes(oneActivity)}
            }),
        [...activityNames]
    )
    useEffect(() => {
        workbenchDispatch({type: 'SET_ENTITIES', entities: undefined})
        workbenchDispatch({type: 'SET_VISIBILITY_ACTIVITIES', visibilityActivities})
        workbenchDispatch(clearWorkbenchData)
        return () => {
            workbenchDispatch({type: 'SET_ENTITIES', entities: undefined})
            workbenchDispatch({
                type: 'SET_VISIBILITY_ACTIVITIES',
                visibilityActivities: activityNames.map(name => ({name, isVisible: true}))
            })
            workbenchDispatch(clearWorkbenchData)
        }
    }, [...activityNames, visibilityActivities])
    return useMemo(
        () => ({
            workbenchDispatch
        }),
        [workbenchDispatch]
    )
}
