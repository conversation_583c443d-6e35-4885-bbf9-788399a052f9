/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {noop} from '@eisgroup/common'
import {RxResult} from '@eisgroup/common-types'
import {LocalizationUtils} from '@eisgroup/i18n'
import * as React from 'react'
import classNames from 'classnames'
import moment from 'moment'
import {Spin} from '@eisgroup/ui-kit'
import {Form, FormApi} from '@eisgroup/form'
import {observer} from 'mobx-react'
import {CapEventCase, ClaimWrapper, LifeIntakeLoss} from '@eisgroup/cap-event-case-models'
import {SettingEditMedium} from '@eisgroup/ui-kit-icons'
import {ORDERED_WEEK_DAYS, WorkDays} from '../../work-days/WorkDays'
import {LabelValueInfo} from './LabelValueInfo'
import {ListActions} from '../../list-actions/ListActions'
import {CAP_EVENT_CASE} from '../../../common/constants'
import {
    INPUT_FORM_ROW_1,
    WORK_DAYS_INLINE_ED,
    WORK_DAYS_INLINE_ED_ACTIONS,
    WORK_DAYS_INLINE_ED_BORDER
} from '../../../common/package-class-names'
import ReactNode = React.ReactNode
import CapLifeIntakeLossEntity = LifeIntakeLoss.CapLifeIntakeLossEntity
import t = LocalizationUtils.translate
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapClaimWrapperEntity = ClaimWrapper.CapClaimWrapperEntity
import CapAbsenceTypicalWorkWeekEntity = CapEventCase.CapAbsenceTypicalWorkWeekEntity

const WORK_SCHEDULE_PATH = 'loss.lossDetail.typicalWorkWeek'
const EVENT_CASE_WORK_SCHEDULE_PATH = 'loss.lossDetail.claimEvent.absenceDetail.typicalWorkWeek'

export interface WorkDaysInlineEditorProps {
    /**
     * Loss entity
     */
    loss?: CapLifeIntakeLossEntity | CapEventCaseEntity | CapClaimWrapperEntity
    /**
     * Update action indicator
     */
    isLoading?: () => boolean
    /**
     * Function to get latest version of loss on edit to avoid optimistic lock exception
     */
    refreshLoss?: () => RxResult<CapLifeIntakeLossEntity | CapEventCaseEntity>
    /**
     * Handler responcible for evenCase entity update
     * @param evenCase evenCase entity to update
     */
    saveEventCaseHandler?: (eventCase: CapEventCaseEntity) => RxResult<CapEventCaseEntity>
    /**
     * to disable edit btn
     */
    isEditDisabled?: boolean
    /**
     * work week value
     */
    workWeek: CapAbsenceTypicalWorkWeekEntity
}

interface WorkDaysInlineEditorState {
    /**
     * If editor in edit mode
     */
    edit: boolean
    /**
     * If loss refresh in progress
     */
    lossLoading: boolean
    /**
     * If update request is in progress
     */
    submit: boolean
    /**
     * If values differ from initial
     */
    dirty: boolean
}

/**
 * Inline editor for absence loss case work days.
 * In view mode displays label and string with working hours,
 * in edit mode displays {@link WorkDays} component UI
 */

@observer
export class WorkDaysInlineEditor extends React.Component<WorkDaysInlineEditorProps, WorkDaysInlineEditorState> {
    state: WorkDaysInlineEditorState = {
        edit: false,
        lossLoading: false,
        submit: false,
        dirty: false
    }

    render(): React.ReactNode {
        return (
            <div
                className={classNames(
                    WORK_DAYS_INLINE_ED,
                    INPUT_FORM_ROW_1,
                    this.state.edit ? WORK_DAYS_INLINE_ED_BORDER : ''
                )}
            >
                <LabelValueInfo label={t('cap-core:claim_subject_participant_work_days')}>
                    {this.renderDays()}
                </LabelValueInfo>
            </div>
        )
    }

    private getFieldNames = (loss: CapLifeIntakeLossEntity | CapEventCaseEntity | CapClaimWrapperEntity) => {
        if (loss?._modelName !== CAP_EVENT_CASE && loss?.lossDetail?.typicalWorkWeek) {
            return ORDERED_WEEK_DAYS.map((v, idx) => `loss.lossDetail.typicalWorkWeek.${v}`)
        }
        if (loss?.lossDetail?.claimEvent?.absenceDetail?.typicalWorkWeek) {
            return ORDERED_WEEK_DAYS.map((v, idx) => `loss.lossDetail.claimEvent.absenceDetail.typicalWorkWeek.${v}`)
        }
        return []
    }

    fieldNames: string[] = this.props.loss ? this.getFieldNames(this.props.loss) : []

    // View mode
    private workDaysInfo(): ReactNode {
        const {isEditDisabled, workWeek} = this.props
        return (
            <>
                <span>
                    {ORDERED_WEEK_DAYS.map((v, idx) => ({weekDay: idx + 1, hoursPerDay: workWeek[v]}))
                        .filter(v => v.hoursPerDay > 0)
                        .map((v, idx) => `${moment.weekdaysShort(false, v.weekDay || 0)} ${v.hoursPerDay}`)
                        .join(', ')}
                </span>
                {!isEditDisabled && <SettingEditMedium onClick={this.handleEdit} />}
            </>
        )
    }

    // Edit mode
    private workDaysEdit(): ReactNode {
        const {workWeek} = this.props
        return (
            <div className={WORK_DAYS_INLINE_ED}>
                <Form<CapEventCaseEntity> onSubmit={noop} initialValues={{loss: this.props.loss}}>
                    {({form}) => (
                        <>
                            <WorkDays
                                data={workWeek}
                                onChange={workWeekEntity => this.handleChange(form as FormApi, workWeekEntity)}
                            />

                            {this.actionButtons()}
                        </>
                    )}
                </Form>
            </div>
        )
    }

    // Save/Cancel buttons
    private actionButtons = (): ReactNode => {
        return (
            <ListActions
                formActions
                className={WORK_DAYS_INLINE_ED_ACTIONS}
                isLoading={this.state.submit || (this.props.isLoading && this.props.isLoading())}
                isSubmitDisabled={!this.state.dirty}
                handleFormConfirm={this.handleSave}
                handleFormCancel={this.handleCancel}
            />
        )
    }

    private handleEdit = (): void => {
        if (this.props.refreshLoss) {
            this.setState((state, props) => ({
                lossLoading: true
            }))
            this.props.refreshLoss().subscribe(e =>
                this.setState((state, props) => ({
                    edit: !state.edit,
                    lossLoading: false
                }))
            )
        }
    }

    private handleCancel = (): void => {
        this.close()
    }

    private close = (): void => {
        this.setState({
            edit: false,
            submit: false
        })
    }

    private handleSave = (form: FormApi<CapEventCaseEntity>): void => {
        this.setState({
            submit: true
        })
        if (this.props.loss?._modelName === CAP_EVENT_CASE && this.props.saveEventCaseHandler) {
            this.props.saveEventCaseHandler(form.getState().values.loss).subscribe(this.close)
        }
    }

    private handleChange = (form: FormApi<CapEventCaseEntity>, value: CapAbsenceTypicalWorkWeekEntity): void => {
        const {workWeek} = this.props
        const formWorkWeek = (): CapAbsenceTypicalWorkWeekEntity => {
            return this.props.loss?._modelName !== CAP_EVENT_CASE
                ? form.getState().values.loss?.lossDetail?.typicalWorkWeek
                : form.getState().values.loss?.lossDetail?.claimEvent?.absenceDetail?.typicalWorkWeek
        }
        form.change(
            this.props.loss?._modelName !== CAP_EVENT_CASE ? WORK_SCHEDULE_PATH : EVENT_CASE_WORK_SCHEDULE_PATH,
            value
        )
        this.setState({
            // dirty property for some reason doesn't track checkboxes changes, so checking it manually
            dirty:
                form.getState().dirty ||
                (this.isDaysChanged(workWeek, formWorkWeek()) &&
                    (this.props.loss?._modelName !== CAP_EVENT_CASE
                        ? true
                        : ORDERED_WEEK_DAYS.findIndex(p => formWorkWeek()[p] !== 0) !== -1))
        })
    }

    private isDaysChanged = (
        initial: CapAbsenceTypicalWorkWeekEntity | undefined,
        current: CapAbsenceTypicalWorkWeekEntity | undefined
    ): boolean => {
        if (!initial || !current) {
            return false
        }

        for (const workDay of ORDERED_WEEK_DAYS) {
            if (initial[workDay] !== current[workDay]) {
                return true
            }
        }

        return false
    }

    private renderDays = () => {
        if (this.state.lossLoading) {
            return <Spin />
        }
        return this.state.edit ? this.workDaysEdit() : this.workDaysInfo()
    }
}
