/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.specialHandling.converters;


import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.specialHandling.dto.CapGenericSpecialHandling;
import dataproviders.dto.CapSpecialHandlingDTO;


public class CapAdjusterGenericSpecialHandlingConverter<I extends CapSpecialHandlingDTO, A extends CapGenericSpecialHandling>
        extends GenesisRootApiModelConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO = super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.claimLossIdentification = intDTO.claimLossIdentification;
        apiDTO.complaintInd = intDTO.complaintInd;
        apiDTO.appealInd = intDTO.appealInd;
        apiDTO.policyExclusionInd = intDTO.policyExclusionInd;
        apiDTO.atpWithCheckInd = intDTO.atpWithCheckInd;
        apiDTO.atpInd = intDTO.atpInd;
        apiDTO.siuInd = intDTO.siuInd;
        apiDTO.attorneyInd = intDTO.attorneyInd;
        apiDTO.litigationInd = intDTO.litigationInd;
        apiDTO.reinsuranceInd = intDTO.reinsuranceInd;
        apiDTO.runInInd = intDTO.runInInd;
        apiDTO.takeOverInd = intDTO.takeOverInd;
        apiDTO.salvageInd = intDTO.salvageInd;
        apiDTO.subrogationInd = intDTO.subrogationInd;
        return apiDTO;
    }

}
