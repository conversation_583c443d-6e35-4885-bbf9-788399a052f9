/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {
    CapAdjusterClaimSearchCapHeaderModelCapHeaderEntity,
    CapAdjusterClaimWrapperCapClaimWrapperUpdateInput,
    CapLifeClaimSettlement
} from '@eisgroup/cap-gateway-client'
import {BusinessTypes} from '@eisgroup/cap-event-case-models'
import {
    backofficeCustomerCommonService,
    backOfficeWorkflowService,
    backofficeWorkService,
    CapGenericLoss,
    ClaimParty,
    claimService,
    claimWrapperService,
    eventCaseService,
    FollowUpTask,
    IndividualCustomer,
    LossParams,
    OrganizationCustomer,
    OrgPerson,
    PartyRole,
    WorkflowCase,
    workflowPublisherService,
    WorkQueueDetails
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {Right} from '@eisgroup/data.either'
import {action, IObservableArray, observable, ObservableMap, runInAction, toJS} from 'mobx'
import {Observable} from 'rxjs'
import {CAP_LOSS, ClaimTypesMap, LOAD_CLAIMS_ACTION} from '../../constants'
import {CapEventCaseEntity, ManagersInfo} from '../../Types'
import {checkValidity, isValidValue} from '../../utils'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {ClaimStore, CoveragesInfo, SubjectOfClaimsInfo} from '../ClaimStore'
import {CustomerStore} from '../CustomerStore'
import {QueueStore} from '../QueueStore'
import {LOAD_WORK_QUEUES} from './CaseSystemTaskStoreImpl'
import CapLoss = BusinessTypes.CapLoss

export const UPDATE_CLAIM_ACTIONS = 'updateClaimActions'
export const SEARCH_CLAIMS_ACTION = 'searchClaimsAction'
export const PUBLISH_FOLLOW_UP_TASK = 'publishFollowUpTask'

export class ClaimStoreImpl extends BaseRootStoreImpl implements ClaimStore {
    queueStore: QueueStore

    customerStore: CustomerStore | undefined

    @observable claims: CapGenericLoss[] = []

    @observable subjectOfClaimsInfo?: SubjectOfClaimsInfo

    @observable managersInfo: ManagersInfo

    @observable coveragesInfo?: CoveragesInfo

    @observable claimsManagersInfo: ObservableMap<string, string | undefined> = observable<string, string | undefined>(
        new Map()
    )

    @observable allClaimsBeneficiaries: IObservableArray<ClaimParty> = observable<ClaimParty>([])

    constructor(parentStore?) {
        super()
        if (parentStore) {
            this.customerStore = parentStore.customerStore
        }
    }

    @action
    setClaims = (claims: CapGenericLoss[]) => {
        this.claims = claims
    }

    @action
    loadClaims = ({rootId, revisionNo}: LossParams, claimAmount?: number) => {
        if (claimAmount) {
            return this.call<CapGenericLoss[]>(() =>
                eventCaseService.pollClaims({rootId, revisionNo}, claimAmount)
            ).flatMap(either => {
                return either.fold(errorToRxResult, data => {
                    return either.fold(errorToRxResult, payload => {
                        runInAction(() => {
                            this.claims = either.get()
                        })

                        this.callService<WorkflowCase[]>(
                            backOfficeWorkflowService.pollCaseByEntityURIs(
                                this.claims?.map(
                                    v => `gentity://CapLoss/ClaimWrapper//${v?._key?.rootId}/${v?._key?.revisionNo}`
                                ),
                                this.claims?.length
                            ),
                            response => {
                                this.call<CapGenericLoss[]>(() =>
                                    eventCaseService.loadClaims({rootId, revisionNo})
                                ).flatMap(eitherRes => {
                                    return eitherRes.fold(errorToRxResult, responseData => {
                                        runInAction(() => {
                                            this.claims = responseData
                                        })
                                        return Observable.of(Right(responseData))
                                    })
                                })
                            }
                        )
                        return Observable.of(Right(payload))
                    })
                })
            })
        }
        return this.call<CapGenericLoss[]>(() => eventCaseService.loadClaims({rootId, revisionNo})).flatMap(either => {
            return either.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.claims = payload
                })
                return Observable.of(Right(payload))
            })
        })
    }

    @action
    actionsAfterLoadClaims = (claims: CapGenericLoss[]) => {
        // get claim manager
        this.getClaimManagersInfo(claims)
        // get Subject of Claim
        this.getSubjectOfClaimInfo(claims)
        let partyRoles = [] as PartyRole[]
        claims.forEach(claim => {
            const claimRootId = claim._key!.rootId!
            if (
                claim.claimType &&
                [ClaimTypesMap.TL, ClaimTypesMap.PL, ClaimTypesMap.ACC, ClaimTypesMap.CI, ClaimTypesMap.HI].includes(
                    claim.claimType
                )
            ) {
                // get benefit of claim
                const {rootId = '', revisionNo = 0} = claim._key!
                this.loadCoverages({rootId, revisionNo}, claimRootId)
                // get beneficiaries for payment drawer
                partyRoles = [...partyRoles, ...(claim.lossDetail.beneficiaryRole ?? [])]
            }
        })
        const beneficiaryRegistryIds = [...new Set(partyRoles.map(v => v.registryId))].filter(Boolean) as string[]
        const guardianRegistryIds = [...new Set(partyRoles.map(v => v.representativeRegistryId))].filter(
            Boolean
        ) as string[]
        this.getBeneficiaries(beneficiaryRegistryIds, partyRoles)
        if (guardianRegistryIds?.length) {
            this.getGuardians(guardianRegistryIds, partyRoles)
        }
    }

    @action
    loadClaimsWithAction = (eventCase: CapEventCaseEntity, claimAmount?: number) => {
        this.actionsStore.startAction(LOAD_CLAIMS_ACTION)
        this.loadClaims(eventCase._key, claimAmount).subscribe(either => {
            either.map(r => {
                runInAction(() => {
                    this.actionsAfterLoadClaims(r)
                    this.actionsStore.completeAction(LOAD_CLAIMS_ACTION)
                })
            })
        })
    }

    @action
    getBeneficiaries = (registryIds: string[], partyRoles: PartyRole[]) => {
        this.promiseCall(() =>
            this.customerStore
                ? this.customerStore.getOrLoadCustomer(registryIds)
                : backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(registryIds)
        ).then(customers => {
            runInAction(() => {
                partyRoles.forEach(partyRole => {
                    const filteredCustomer = customers.find(
                        customer =>
                            [
                                (customer as IndividualCustomer).details.person?.registryTypeId,
                                (customer as OrganizationCustomer).details.legalEntity?.registryTypeId
                            ]
                                .filter(Boolean)
                                .indexOf(partyRole.registryId) > -1
                    )
                    if (filteredCustomer) {
                        this.allClaimsBeneficiaries.push({
                            customer: filteredCustomer,
                            partyRole
                        })
                    }
                })
            })
        })
    }

    @action
    getGuardians = (registryIds: string[], partyRoles: PartyRole[]) => {
        this.promiseCall(() => backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(registryIds)).then(
            customers => {
                runInAction(() => {
                    partyRoles.forEach(partyRole => {
                        const filteredCustomer = customers.find(
                            customer =>
                                [(customer as IndividualCustomer).details.person?.registryTypeId]
                                    .filter(Boolean)
                                    .indexOf(partyRole.representativeRegistryId) > -1
                        )
                        if (filteredCustomer) {
                            this.allClaimsBeneficiaries.push({
                                customer: filteredCustomer,
                                partyRole
                            })
                        }
                    })
                })
            }
        )
    }

    getClaimManagerUserInformation = (userId: string) => {
        return this.call(() => backofficeWorkService.searchOrganizationalUserByUserID(userId)).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                return Observable.of(Right(payload))
            })
        )
    }

    getClaimManagerQueueInformation = (queueCd: string[]) => {
        return backOfficeWorkflowService.loadWorkQueues(queueCd)
    }

    @action
    getClaimManagersInfo = (claims: CapGenericLoss[]) => {
        this.managersInfo = {}
        const entityURIs = [] as string[]
        if (!claims.length) {
            return
        }
        claims.forEach(claim => {
            if (claim._key?.rootId) {
                entityURIs.push(`gentity://CapLoss/ClaimWrapper//${claim._key.rootId}/1`)
                this.managersInfo = {...this.managersInfo, [claim._key.rootId]: {}}
            }
        })

        this.callService<WorkflowCase[]>(backOfficeWorkflowService.searchCaseByEntityURIs(entityURIs), response => {
            const caseInfos = toJS(response)
            caseInfos.forEach(async caseInfo => {
                const claimRootId = caseInfo?.entityKey?.rootId as string
                if (caseInfo?.assignmentInfo?.userId) {
                    const either = await this.getClaimManagerUserInformation(caseInfo.assignmentInfo.userId).toPromise()
                    if (either.isRight) {
                        const data = either.get()
                        // TODO When userId is not empty but the result of the search API is undefined, userId is temporarily assembled into UserInfo and returned.
                        const userInfo = (
                            data[0]
                                ? toJS(data[0])
                                : {
                                      securityIdentity: caseInfo?.assignmentInfo?.userId
                                  }
                        ) as OrgPerson
                        runInAction(() => {
                            this.managersInfo = {
                                ...this.managersInfo,
                                [claimRootId]: {
                                    ...this.managersInfo[claimRootId],
                                    userInfo
                                }
                            }
                        })
                    }
                } else if (caseInfo?.assignmentInfo?.queueCd) {
                    const either = await this.getClaimManagerQueueInformation([
                        caseInfo.assignmentInfo.queueCd
                    ]).toPromise()
                    if (either.isRight) {
                        const payload = either.get()
                        const queueInfo = (payload[0] ? toJS(payload[0]) : {}) as WorkQueueDetails
                        runInAction(() => {
                            this.managersInfo = {
                                ...this.managersInfo,
                                [claimRootId]: {
                                    ...this.managersInfo[claimRootId],
                                    queueInfo
                                }
                            }
                        })
                    }
                }
            })
        })
    }

    assignClaimManager = (instanceCase: WorkflowCase) => {
        const setClaimManagerInfo = (userInfo: OrgPerson, userId?: string) => {
            const organizationUri = userInfo?.organizationAssignments?.[0]?.organization?._uri
            if (!checkValidity(organizationUri)) {
                this.claimsManagersInfo?.set(
                    instanceCase.entityURI || '',
                    `${userInfo.personInfo.firstName} ${userInfo.personInfo.lastName}`
                )
                return
            }
            this.claimsManagersInfo?.set(entityURI, userId)
        }
        const assignmentInfo = instanceCase.assignmentInfo
        const {entityURI = ''} = instanceCase
        if (assignmentInfo?.queueCd) {
            const queueCd = assignmentInfo?.queueCd || ''
            const queue = this.queueStore?.queueAllResults?.find(v => v.queueCd === queueCd)
            const setQueue = (queueDetails: WorkQueueDetails) =>
                runInAction(() => this.claimsManagersInfo?.set(entityURI, queueDetails.name))

            if (!queue) {
                this.call(() => backOfficeWorkflowService.loadWorkQueues([queueCd]), LOAD_WORK_QUEUES).subscribe(
                    either => {
                        const responseQueue = either.get()
                        setQueue(responseQueue[0])
                    }
                )
            } else {
                setQueue(queue)
            }
        }
        if (assignmentInfo?.userId) {
            const userId = assignmentInfo?.userId || ''
            this.call<OrgPerson[]>(() => backofficeWorkService.searchOrganizationalUserByUserID(userId)).subscribe(
                either => {
                    const responseUser = either.get()
                    runInAction(() => {
                        const userInfo = responseUser[0]
                        setClaimManagerInfo(userInfo, userId)
                    })
                }
            )
        }
    }

    @action
    getClaimManagerInfo = (entityURIs: string[]) => {
        if (!entityURIs.length) {
            return
        }
        this.call<WorkflowCase[]>(() => backOfficeWorkflowService.searchCaseByEntityURIs(entityURIs)).subscribe(
            either => {
                const responses = either.get()
                runInAction(() => {
                    if (!responses.length || responses.some(value => !isValidValue(value))) {
                        return
                    }
                    responses.forEach(this.assignClaimManager)
                })
            }
        )
    }

    @action
    getSubjectOfClaimInfo = (claims: CapGenericLoss[]) => {
        this.subjectOfClaimsInfo = {}
        const subjectOfClaimLinks = [] as string[]
        const claimRootIdMap = {}
        claims.forEach(claim => {
            const subjectOfClaimLink = claim.subjectOfClaim?.registryId || ''
            if (subjectOfClaimLinks.indexOf(subjectOfClaimLink) === -1) {
                subjectOfClaimLinks.push(subjectOfClaimLink)
            }
            if (claim._key?.rootId) {
                Object.assign(claimRootIdMap, {[claim._key.rootId]: subjectOfClaimLink})
            }
        })

        this.promiseCall(() =>
            (this.customerStore
                ? this.customerStore.getOrLoadCustomer(subjectOfClaimLinks)
                : backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(subjectOfClaimLinks)
            ).then(response => {
                runInAction(() => {
                    ;((response as IndividualCustomer[]) ?? []).forEach(v => {
                        const registryTypeId = v.details?.person?.registryTypeId
                        Object.keys(claimRootIdMap).forEach(claimRootId => {
                            if (claimRootIdMap[claimRootId] === registryTypeId) {
                                this.subjectOfClaimsInfo = {
                                    ...this.subjectOfClaimsInfo,
                                    [claimRootId]: v
                                }
                            }
                        })
                    })
                })
            })
        )
    }

    @action
    searchClaimHeaders = (caseLossRootId: string) => {
        const params = {
            limit: 100,
            query: {
                caseSystemId: {
                    matches: [caseLossRootId]
                }
            }
        }
        return this.call(() => claimService.searchClaims(params)).flatMap(either => {
            return either.fold(errorToRxResult, payload => {
                const claimList = (payload.result as CapAdjusterClaimSearchCapHeaderModelCapHeaderEntity[]) ?? []
                const associatedClaims = claimList.map(v => {
                    return {
                        _key: v._key,
                        _modelName: v.claimModelName,
                        _modelType: CAP_LOSS,
                        claimType: v.claimType,
                        lossNumber: v.claimNumber,
                        state: v.state
                    }
                }) as CapLoss[] & {claimType?: string}
                return Observable.of(Right(associatedClaims))
            })
        })
    }

    @action
    updateClaimWrapper = (requestBody: CapAdjusterClaimWrapperCapClaimWrapperUpdateInput) => {
        return this.call(() => claimWrapperService.updateClaimWrapper(requestBody)).flatMap(either => {
            return either.fold(errorToRxResult, payload => {
                return Observable.of(Right(payload))
            })
        })
    }

    @action
    loadCoverages = ({rootId, revisionNo}: LossParams, claimRootId: string) => {
        this.coveragesInfo = {}
        this.callService<CapLifeClaimSettlement[]>(
            claimWrapperService.loadCoverages({rootId, revisionNo}),
            response => {
                runInAction(() => {
                    this.coveragesInfo = {
                        ...this.coveragesInfo,
                        [claimRootId]: response
                    }
                })
            }
        )
    }

    @action
    publishFollowUpTask = (task: FollowUpTask) => {
        return this.call(() => workflowPublisherService.publishFollowUpTaskEvent(task), PUBLISH_FOLLOW_UP_TASK)
    }
}
