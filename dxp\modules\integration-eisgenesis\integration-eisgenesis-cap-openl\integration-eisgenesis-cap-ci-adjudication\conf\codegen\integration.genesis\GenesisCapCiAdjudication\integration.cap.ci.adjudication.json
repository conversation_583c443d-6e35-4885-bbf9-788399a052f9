{"openapi": "3.0.1", "info": {"description": "Auto-generated OpenAPI schema from the OpenL rules", "title": "claim-life-ci-adjudication", "version": "1.0.0"}, "servers": [{"url": "/claim-life-ci-adjudication", "variables": {}}], "security": [{"BasicAuth": [], "SSOToken": []}], "paths": {"/_api_ci_adjudication": {"post": {"description": "Rules method: org.openl.generated.beans.CapCISettlementResultEntity _api_ci_adjudication(org.openl.generated.beans.CapCISettlementRulesInput request)", "operationId": "_api_ci_adjudication", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapCISettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapCISettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapCISettlementResultEntity _api_ci_adjudication(CapCISettlementRulesInput)"}}, "/_api_ci_applicability": {"post": {"description": "Rules method: org.openl.generated.beans.CapCILossApplicabilityResult _api_ci_applicability(org.openl.generated.beans.CapCILossApplicabilityInput request)", "operationId": "_api_ci_applicability", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapCILossApplicabilityInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapCILossApplicabilityResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapCILossApplicabilityResult _api_ci_applicability(CapCILossApplicabilityInput)"}}, "/_api_ci_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapCISettlementResultEntity _api_ci_calculation(org.openl.generated.beans.CapCISettlementRulesInput request)", "operationId": "_api_ci_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapCISettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapCISettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapCISettlementResultEntity _api_ci_calculation(CapCISettlementRulesInput)"}}, "/_api_ci_face_value_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimFaceValueCalculationResultEntity _api_ci_face_value_calculation(org.openl.generated.beans.CapClaimWrapperFaceValueCalculationInput request)", "operationId": "_api_ci_face_value_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperFaceValueCalculationInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimFaceValueCalculationResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimFaceValueCalculationResultEntity _api_ci_face_value_calculation(CapClaimWrapperFaceValueCalculationInput)"}}}, "components": {"schemas": {"AccumulatorRemainingsEntity": {"type": "object", "properties": {"accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorType": {"type": "string"}, "amountUnit": {"type": "string"}, "limitAmount": {"type": "number"}, "policyTerm": {"$ref": "#/components/schemas/Term"}, "remainingAmount": {"type": "number"}, "usedAmount": {"type": "number"}}}, "BaseLifeGrossBenefitAmount": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "totalApprovedAmount": {"$ref": "#/components/schemas/Money"}}}, "BaseLifePolicyCoverageLimitLevel": {"type": "object", "properties": {"limitLevelType": {"type": "string"}, "timePeriodCd": {"type": "string"}}}, "BaseLifeRelatedSettlmentInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "incidentDate": {"type": "string", "format": "date-time"}}}, "BaseLifeSettlementAccumulatorDetails": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorAmount": {"type": "number"}, "accumulatorAmountUnit": {"type": "string"}, "accumulatorCustomerUri": {"type": "string"}, "accumulatorExtension": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorPolicyUri": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time"}, "accumulatorType": {"type": "string"}, "autoAdjudicatedAmount": {"type": "number"}}}, "BaseLifeSettlementAccumulatorDetailsExtension": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorUnitCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapAgeReductionDetailsEntity": {"type": "object", "properties": {"ageCd": {"type": "integer", "format": "int32"}, "reducedToCd": {"type": "number"}}}, "CapCICertInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapCISettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapCISettlementCoverageInfoEntity"}}}}, "CapCILossApplicabilityInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "eventDate": {"type": "string", "format": "date-time"}, "lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/CapLifeLossPolicyInfoEntity"}}}}, "CapCILossApplicabilityResult": {"type": "object", "properties": {"applicability": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}}}, "CapCIMasterInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapCISettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapCISettlementCoverageInfoEntity"}}}}, "CapCISettlementAttrOptionsEntity": {"type": "object", "properties": {"attrName": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}}}, "CapCISettlementBenefitInfoEntity": {"type": "object", "properties": {"benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitDuration": {"type": "integer", "format": "int32"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "lifetimeMaxBenefitAmountPct": {"type": "number"}, "lodgingDistanceToHospitalMoreThanCd": {"type": "integer", "format": "int32"}, "maxBenefitNumber": {"type": "integer", "format": "int32"}, "recurrenceBenefitPct": {"type": "number"}, "remainingAmount": {"$ref": "#/components/schemas/Money"}, "timePeriodCd": {"type": "string"}, "transportationTravelDistanceMoreThan": {"type": "integer", "format": "int32"}, "waitingPeriod": {"type": "integer", "format": "int32"}, "waiverTypeCd": {"type": "string"}}}, "CapCISettlementCoverageConfigEntity": {"type": "object", "properties": {"accidentInd": {"type": "boolean"}, "accumulationCategoryGroup": {"type": "string"}, "applicableLossTypes": {"type": "array", "items": {"type": "string"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/components/schemas/CapCISettlementAttrOptionsEntity"}}, "calculationFormulaId": {"type": "string"}, "groupUnit": {"type": "string"}, "limitLevels": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string"}}}, "CapCISettlementCoverageInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "coverageCd": {"type": "string"}, "coverageWaitingPeriod": {"type": "integer", "format": "int32"}, "isRecurrenceApplied": {"type": "boolean"}}}, "CapCISettlementDetailEntity": {"type": "object", "properties": {"_archived": {"type": "boolean"}, "_key": {"$ref": "#/components/schemas/EntityKey"}, "_modelName": {"type": "string"}, "_modelType": {"type": "string"}, "_modelVersion": {"type": "string"}, "_timestamp": {"type": "string"}, "_type": {"type": "string"}, "_version": {"type": "string"}, "approvedAmountOverride": {"$ref": "#/components/schemas/Money"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "grossAmount": {"$ref": "#/components/schemas/Money"}, "incidentDate": {"type": "string", "format": "date-time"}, "isRecurring": {"type": "boolean"}, "numberOfUnits": {"type": "integer", "format": "int32"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}}, "CapCISettlementLossInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "absence": {"$ref": "#/components/schemas/EntityLink"}, "icdCode": {"type": "string", "description": "ICD Code"}, "certificateReceivedDate": {"type": "string", "format": "date-time"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "coverageType": {"type": "string"}, "lastWorkDate": {"type": "string", "format": "date-time"}, "lossDateTime": {"type": "string", "format": "date-time"}, "lossItems": {"type": "array", "items": {"type": "string"}}, "lossType": {"type": "string"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}}}, "CapCISettlementPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "ciCertInfo": {"$ref": "#/components/schemas/CapCICertInfoEntity"}, "ciMasterInfo": {"$ref": "#/components/schemas/CapCIMasterInfoEntity"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string"}}}, "CapCISettlementResultEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/components/schemas/Money"}, "autoAdjudicatedDuration": {"type": "number"}, "benefitAmountPerUnit": {"$ref": "#/components/schemas/Money"}, "benefitCd": {"type": "string"}, "claimCoverageName": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityEvaluationCd": {"type": "string"}, "grossBenefitAmount": {"$ref": "#/components/schemas/BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time"}, "isAutoAdjudicated": {"type": "boolean"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}, "numberOfUnits": {"type": "integer", "format": "int32"}, "paymentDetailInfo": {"$ref": "#/components/schemas/PaymentDetailInfoEntity"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}, "reserve": {"type": "number", "format": "double"}}}, "CapCISettlementRulesInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/components/schemas/AccumulatorRemainingsEntity"}}, "claimCoverageName": {"type": "string"}, "coverageConfiguration": {"$ref": "#/components/schemas/CapCISettlementCoverageConfigEntity"}, "details": {"$ref": "#/components/schemas/CapCISettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "lifetimeRemainingAmount": {"type": "number"}, "loss": {"$ref": "#/components/schemas/CapCISettlementLossInfoEntity"}, "policy": {"$ref": "#/components/schemas/CapCISettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeRelatedSettlmentInfo"}}, "remainingAmount": {"type": "number"}, "wrapperInfo": {"$ref": "#/components/schemas/CapSettlementWrapperInfoEntity"}}}, "CapClaimFaceValueCalculationResultEntity": {"type": "object", "properties": {"calculatedFaceAmount": {"$ref": "#/components/schemas/Money"}, "isAgeReductionUsed": {"type": "boolean"}, "originalFaceAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimWrapperCoverageInfoEntity": {"type": "object", "properties": {"ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "benefitStructures": {"type": "array", "items": {"$ref": "#/components/schemas/CapCoverageBenefitStructuresEntity"}}}}, "CapClaimWrapperFaceValueCalculationInput": {"type": "object", "properties": {"capCoverageInfo": {"$ref": "#/components/schemas/CapClaimWrapperCoverageInfoEntity"}, "faceValueAmount": {"$ref": "#/components/schemas/Money"}, "isMasterPolicy": {"type": "boolean"}, "memberAge": {"type": "integer", "format": "int32"}, "relationshipToInsuredCd": {"type": "string"}}}, "CapCoverageBenefitStructuresEntity": {"type": "object", "properties": {"approvedAmount": {"$ref": "#/components/schemas/Money"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitTypeCd": {"type": "string"}, "incrementAmount": {"$ref": "#/components/schemas/Money"}, "individualAmtPct": {"type": "number"}, "maxBenefitAmount": {"$ref": "#/components/schemas/Money"}, "minBenefitAmount": {"$ref": "#/components/schemas/Money"}, "typeOfBenefitStructure": {"type": "string"}}}, "CapInsuredInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "isMain": {"type": "boolean"}, "registryTypeId": {"type": "string"}}}, "CapLifeLossPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapSettlementWrapperInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "claimSubjectId": {"type": "string"}, "claimWrapperIdentification": {"$ref": "#/components/schemas/EntityLink"}, "memberRegistryTypeId": {"type": "string"}}}, "EntityKey": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string"}}}, "EntityLink": {"type": "object", "properties": {"_uri": {"type": "string"}}}, "JAXRSErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["USER_ERROR", "RULES_RUNTIME", "COMPILATION", "SYSTEM", "BAD_REQUEST", "VALIDATION"]}, "message": {"type": "string"}}}, "JAXRSUserErrorResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "MessageType": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "source": {"type": "string"}}}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}}}, "PaymentDetailInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "allocationPeriod": {"$ref": "#/components/schemas/Period"}, "initiatePayment": {"type": "boolean"}, "payee": {"$ref": "#/components/schemas/EntityLink"}}}, "Period": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}}, "Term": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"BasicAuth": {"description": "Genesis Basic credentials", "scheme": "basic", "type": "http"}, "SSOToken": {"description": "Genesis Authorization header. Value format: `[Token {token}]`", "in": "header", "name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}