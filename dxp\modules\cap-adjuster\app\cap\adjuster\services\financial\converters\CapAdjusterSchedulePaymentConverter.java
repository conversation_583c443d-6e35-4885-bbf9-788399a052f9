/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial.converters;

import cap.adjuster.services.financial.dto.CapPaymentMessage;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapPaymentSchedule_CapScheduledPaymentEntityDTO;

import cap.adjuster.services.common.dto.GenesisApiModelKey;
import cap.adjuster.services.financial.dto.CapFinancialPayment;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapPaymentSchedule_CapScheduledPaymentMessageEntityDTO;
import core.services.converters.CommonDTOConverter;

import javax.inject.Inject;

public class CapAdjusterSchedulePaymentConverter<I extends CapPaymentSchedule_CapScheduledPaymentEntityDTO, A extends CapFinancialPayment>
    extends CommonDTOConverter<I, A> {

    private CapScheduledPaymentMessageConverter<CapPaymentSchedule_CapScheduledPaymentMessageEntityDTO, CapPaymentMessage> scheduledPaymentMessageConverter;

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);

        apiDTO.key = new GenesisApiModelKey();
        apiDTO.key.rootId = intDTO._key.rootId.toString();
        apiDTO.key.revisionNo = Math.toIntExact(intDTO._key.revisionNo);
        apiDTO.key.id = intDTO._key.id.toString();
        apiDTO.key.parentId = intDTO._key.parentId.toString();
        apiDTO.gentityType = intDTO._type;

        apiDTO.paymentNetAmount = intDTO.paymentNetAmount;
        apiDTO.paymentDate = intDTO.paymentDetails.paymentDate;
        apiDTO.payee = intDTO.paymentDetails.payeeDetails != null ? intDTO.paymentDetails.payeeDetails.payee._uri : intDTO.paymentDetails.payeeRoleDetails.registryId;
        apiDTO.paymentDetails = intDTO.paymentDetails;
        apiDTO.withholdingDetails = intDTO.withholdingDetails;
        apiDTO.paymentNumber = intDTO.paymentNumber;
        apiDTO.messages = scheduledPaymentMessageConverter.convertToApiDTOs(intDTO.messages);
        return apiDTO;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setScheduledPaymentMessageConverter(CapScheduledPaymentMessageConverter scheduledPaymentMessageConverter) {
        this.scheduledPaymentMessageConverter = scheduledPaymentMessageConverter;
    }
}
