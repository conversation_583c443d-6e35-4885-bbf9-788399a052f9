{"swagger": "2.0", "x-dxp-spec": {"imports": {"ltd.settlement": {"schema": "integration.cap.ltd.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: LTD Settlement API", "version": "1", "title": "CAP Adjuster: LTD Settlement API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-ltd", "description": "CAP Adjuster: LTD Loss API"}], "paths": {"/losses-ltd/settlements/readjudicate": {"patch": {"summary": "Readjudicate LTD settlement", "x-dxp-path": "/api/capsettlement/CapLtdSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/settlements/rules/bundle": {"post": {"summary": "Bundle LTD settlement", "x-dxp-path": "/api/capsettlement/CapLtdSettlement/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-ltd"]}}, "/losses-ltd/settlements/create-additional-benefit-settlement": {"post": {"summary": "Create Additional LTD Benefit settlement", "x-dxp-path": "/api/capsettlement/CapLtdSettlement/v1/command/createAdditionalBenefitSettlement", "tags": ["/cap-adjuster/v1/losses-ltd"]}}}}