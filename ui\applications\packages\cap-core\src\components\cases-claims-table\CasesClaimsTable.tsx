/**
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {Table} from '@eisgroup/ui-kit'
import {t} from '@eisgroup/i18n'
import {CasesClaimsFilters} from '@eisgroup/common-claim-shares'
import {Pagination} from './components/Pagination'
import {PREFIX} from '../../common/package-class-names'
import {useCasesClaimsTableData} from './hooks/useCasesClaimsTableData'
import {CasesClaimsSearchService} from './services/CasesClaimsSearchService'

const CASES_CLAIMS_TABLE = `${PREFIX}-cases-claims-table`
const CASES_CLAIMS_TABLE_FOOTER = `${PREFIX}-cases-claims-table-footer`

export interface CasesClaimsTableProps {
    dataFilter?: CasesClaimsFilters
    onChange?: ({pageNum, pageSize, count}: {pageNum: number; pageSize: number; count: number}) => void
    setReloadingItems?: (value: boolean) => void
    isLoading?: boolean
    casesClaimsSearchService: CasesClaimsSearchService
    emptyText?: React.ReactNode
    onFiltersChange?: (filters: CasesClaimsFilters) => void
}

export const CasesClaimsTable = (props: CasesClaimsTableProps) => {
    const {columns, dataSource, onTableChange, locale, loading, pagination, onPaginationChange} =
        useCasesClaimsTableData(props)
    return (
        <div className={CASES_CLAIMS_TABLE}>
            <Table
                columns={columns}
                dataSource={dataSource}
                onChange={onTableChange}
                pagination={false}
                loading={loading}
                locale={locale}
            />
            <div className={CASES_CLAIMS_TABLE_FOOTER}>
                <Pagination
                    current={pagination.current}
                    total={pagination.total}
                    onChange={onPaginationChange}
                    size='small'
                    showSizeChanger
                    showQuickJumper
                    defaultPageSize={pagination.pageSize}
                    pageSizeOptions={['5', '10', '15', '20', '25']}
                    onShowSizeChange={onPaginationChange}
                    hideOnSinglePage
                    jumpToText={t('cap-core:table_pagination_jump_to_label')}
                    locale={{page: ''}}
                />
            </div>
        </div>
    )
}
