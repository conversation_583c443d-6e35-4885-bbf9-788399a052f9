/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.policy;

import cap.adjuster.services.policy.impl.CapAdjusterPoliciesServiceImpl;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchCapPolicySearchRequestBody;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse;
import com.google.inject.ImplementedBy;
import core.services.pagination.PageData;

import java.util.concurrent.CompletionStage;

/**
 * Service that provides methods for policy
 */
@ImplementedBy(CapAdjusterPoliciesServiceImpl.class)
public interface CapAdjusterPoliciesService {

    /**
     * Search policies with additional data
     *
     * @param requestBody
     * @param fields
     * @param pageData
     * @return
     */
    CompletionStage<CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse> searchPolicyHeaders(CapAdjusterPolicySearchCapPolicySearchRequestBody requestBody, String fields, PageData pageData);
}
