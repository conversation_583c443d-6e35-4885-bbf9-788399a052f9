/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {injectable} from 'inversify'
import {Right} from '@eisgroup/data.either'
import {Observable} from 'rxjs'

import {lookups} from '@eisgroup/lookups'
import {RxResult} from '@eisgroup/common'

@injectable()
export class DXPLookupProxy implements lookups.LookupProxy {
    getLookup(props: lookups.ValuesResolutionProps): RxResult<lookups.CodeValueLookup[]> {
        return Observable.of(Right([{code: 'code', value: 'value'}]))
    }
}
