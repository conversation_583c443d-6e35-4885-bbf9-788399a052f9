import React, {FC} from 'react'
import {observer} from 'mobx-react'
import {t} from '@eisgroup/i18n'
import {CaseSystemService, CSClaimWrapperService} from '@eisgroup/cap-services'
import {FormApi} from '@eisgroup/form'
import {ActionFormBase} from './ActionFormBase'
import cancelWaiveOverPaymentConfig from '../../../../builder/BalanceCancelWaiveOverpayment.builder'
import {BALANCE_ACTION_CANCEL_WAIVE_OVERPAYMENT_FORM_ID, BALANCE_ACTIONS_MAP} from '../../../../common/constants'

import {DrawerFormStateType, ICaseSystem} from '../../../..'

import {BalanceActionDrawerProps} from '../BalanceActionDrawer'

type CancelWaiveOverpaymentActionFormProps = BalanceActionDrawerProps<
    ICaseSystem,
    CaseSystemService | CSClaimWrapperService<ICaseSystem>
>

const OVERPAYMENT_SELECT_INPUT_ID = 'b1beed92-7c35-4405-94d2-1fa3a7b657a6'

export const CancelWaiveOverpaymentActionForm: FC<CancelWaiveOverpaymentActionFormProps> = observer(props => {
    const {actionKey: key, balanceStore, paymentsStore, getBalanceChangeLog, closeDrawer, getLoadPayments} = props

    const isLoading = balanceStore.actionsStore.isRunning(BALANCE_ACTIONS_MAP.CANCEL_WAIVE_OVERPAYMENT)

    const onOverpaymentWaiveChange = (
        _values: unknown,
        _props: unknown,
        _eventValue: string,
        _prefix: string,
        formApi: FormApi
    ) => {
        const overpaymentWaive = formApi.getState().values.overpaymentWaive
        const selectedPayment = paymentsStore.paymentList.find(p => p._key.rootId === overpaymentWaive)
        formApi.change('overpaymentWaiveAmount', selectedPayment?.paymentNetAmount)
    }

    const getWaivePaymentsOptions = () => {
        const overpaymentWaive = paymentsStore.paymentList.filter(
            e => e._variation === 'overpaymentWaive' && e.state === 'Issued'
        )
        return overpaymentWaive.map(ex => {
            return {
                displayValue: ex.paymentNumber,
                code: ex._key.rootId
            }
        })
    }

    const onFormConfirm = values => {
        const nextBalanceCount = balanceStore.balanceChangeLogCount + 1

        const overpaymentWaive = paymentsStore.paymentList.find(p => p._key.rootId === values.overpaymentWaive)
        const overpaymentWaiveKey = overpaymentWaive._key
        const saveParams = {
            _key: overpaymentWaiveKey,
            cancellationReason: values.comment
        }
        balanceStore.cancelOverpaymentWaive(saveParams).subscribe(() => {
            getLoadPayments()
            getBalanceChangeLog(nextBalanceCount)
            closeDrawer()
        })
    }

    const propsCustomizerMap = {
        [OVERPAYMENT_SELECT_INPUT_ID]: (properties, {prefix, prefixIdx}) => ({
            ...properties,
            options: getWaivePaymentsOptions()
        })
    }

    return (
        <ActionFormBase
            uiEngineProps={{
                formId: BALANCE_ACTION_CANCEL_WAIVE_OVERPAYMENT_FORM_ID,
                config: cancelWaiveOverPaymentConfig,
                initialValues: {
                    action: key
                },
                propsCustomizerMap,
                apiServices: {
                    onOverpaymentChanged: onOverpaymentWaiveChange
                },
                onNext: onFormConfirm
            }}
            drawerActionProps={{
                drawerFormState: DrawerFormStateType.Create,
                handleFormCancel: closeDrawer,
                isLoading,
                labels: {
                    createButtonLabel: t('cap-core:balance_actions_drawer_confirm_button')
                }
            }}
        />
    )
})
