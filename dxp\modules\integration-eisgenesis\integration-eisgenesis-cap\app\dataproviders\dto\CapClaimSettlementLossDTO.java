/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package dataproviders.dto;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

import com.eisgroup.dxp.dataproviders.genesiscapacceleratedsettlement.dto.EntityKeyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapacceleratedsettlement.dto.EntityLinkDTO;
import com.eisgroup.dxp.dataproviders.genesiscapacceleratedsettlement.dto.MoneyDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

public class CapClaimSettlementLossDTO extends core.dataproviders.dto.PartialUpdateInternalDTO implements core.dataproviders.dto.InternalDTO {

    @JsonProperty(value = "claimEligibilityEvaluationCd")
    public String claimEligibilityEvaluationCd;

    @JsonProperty(value = "_type")
    public String _type;

    @JsonProperty(value = "_key")
    public EntityKeyDTO _key;

    @JsonProperty(value = "claimType")
    public String claimType;

    @JsonProperty(value = "lossType")
    public String lossType;

    @JsonProperty(value = "lossDateTime")
    public ZonedDateTime lossDateTime;

    @JsonProperty(value = "lastWorkDate")
    public ZonedDateTime lastWorkDate;

    @JsonProperty(value = "lossNumber")
    public String lossNumber;

    @JsonProperty(value = "caseNumber")
    public String caseNumber;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = core.utils.JsonUtils.DATE_ONLY_FORMAT)
    @JsonProperty(value = "dateOfLifeExpectancyPrescribed")
    public ZonedDateTime dateOfLifeExpectancyPrescribed;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = core.utils.JsonUtils.DATE_ONLY_FORMAT)
    @JsonProperty(value = "dateOfDiagnosis")
    public ZonedDateTime dateOfDiagnosis;

    @JsonProperty(value = "overrideFaceValueAmount")
    public MoneyDTO overrideFaceValueAmount;

    @JsonProperty(value = "claimNumber")
    public String claimNumber;

    @JsonProperty(value = "eventCaseLink")
    public EntityLinkDTO eventCaseLink;
}
