/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    CapAdjusterEventCaseCapEventCaseSubStatusInput,
    CapAdjusterEventCaseClaimLossCloseInput,
    CapAdjusterEventCaseClaimLossReopenInput
} from '@eisgroup/cap-gateway-client'
import {BusinessTypes, CapEventCase} from '@eisgroup/cap-event-case-models'
import {CapAccumulatorContainer, CapSpecialHandling} from '@eisgroup/cap-models'
import {CheckBoxCardsCodeValue, CustomerRelationship, LossParams, OrgPerson, Relationship} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {IObservableArray} from 'mobx'
import {ICaseSystemLoss, RelationshipParams} from '../Types'
import {BaseRootStore} from './BaseRootStore'
import {EmploymentStore} from './EmploymentStore'
import CapLoss = BusinessTypes.CapLoss
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapSpecialHandlingEntity = CapSpecialHandling.CapSpecialHandlingEntity
import CapAccumulatorContainerEntity = CapAccumulatorContainer.CapAccumulatorContainerEntity
import {IcdCodeStore} from './IcdCodeStore'

export interface EventCaseStore extends BaseRootStore {
    employmentStore: EmploymentStore
    eventCase: CapEventCaseEntity
    icdCodeStore: IcdCodeStore

    /**
     * Save Loss Type Value Backend
     */
    caseLoading: boolean
    lossTypeValue: CheckBoxCardsCodeValue[]
    lossTypes?: CheckBoxCardsCodeValue[]
    updateEventCase: (eventCase?: CapEventCaseEntity) => void
    setCaseLoading: (caseLoading: boolean) => void
    /**
     * get Loss Types Backend
     */
    refreshEventCase: (skipPolling?: boolean) => RxResult<CapEventCaseEntity>
    updateEventCaseTypeValue: () => void
    updateEventCaseByEntity: (request: CapEventCaseEntity) => RxResult<CapEventCaseEntity>
    updateEventCaseManually: (request: CapEventCaseEntity) => RxResult<CapEventCaseEntity>
    loadLossTypes: () => void
    loadEventCase: (params: LossParams, actionName?: string) => void
    pollLoadEventCase: (params: LossParams, actionName?: string) => void
    getEventCase: (params: LossParams, fromIntake?: boolean) => RxResult<CapEventCaseEntity>
    loadLossesRelativeToEventCase: () => void
    lossesRelativeToCase: IObservableArray
    absenceLossesRelativeToEventCase: IObservableArray<CapLoss>
    associatedNotClosedLosses: CapLoss[]
    periodAccumulators: CapAccumulatorContainerEntity[]
    /**
     * Action to call update event case api
     */
    updateEventCaseDraftByApi: (eventCase: CapEventCaseEntity) => RxResult<CapEventCaseEntity>
    updateEventCasesByApi: (eventCase: CapEventCaseEntity) => RxResult<CapEventCaseEntity>
    setCaseDetailClaimEvents: (claimEvents: string[]) => void
    updateClaimEvent: (claimEvents: string[] | undefined) => void
    closeEventCase: (request: CapAdjusterEventCaseClaimLossCloseInput) => RxResult<CapEventCaseEntity>
    reOpenEventCase: (request: CapAdjusterEventCaseClaimLossReopenInput) => RxResult<CapEventCaseEntity>
    changeCaseSubStatus: (request: CapAdjusterEventCaseCapEventCaseSubStatusInput) => RxResult<CapEventCaseEntity>
    getLatestTimestamp: (params: LossParams, callback?: any) => void
    createCustomerRelationship: (params: RelationshipParams) => Promise<CustomerRelationship>
    updateCustomerRelationship: (params: RelationshipParams) => Promise<CustomerRelationship>
    getCustomerRelationships: (customerId: string) => Promise<CustomerRelationship[]>
    relationships: Relationship[]
    allSpecialHandlingsUnderEventCase: CapSpecialHandlingEntity[]
    loadAllSpecialHandlingsUnderEventCase: ({rootId, revisionNo}: LossParams) => void
    approverPersons: OrgPerson[]
    getApproverPersonsBySecurityIdentity: (securityIds?: string[]) => void
    getApproverPersonFullName: (securityIdentity: string) => string
    loadAccumulators: (losses: ICaseSystemLoss[]) => void
}
