/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.converters;

import cap.adjuster.services.common.dto.GenesisLink;
import core.services.converters.CommonDTOConverter;
import dataproviders.common.dto.GenesisLinkDTO;

public class GenesisLinkConverter<I extends GenesisLinkDTO, A extends GenesisLink> extends CommonDTOConverter<I, A> {

    @Override
    public I convertToInternalDTO(A apiDTO, I intDTO) {
        intDTO.uri = apiDTO.uri;

        return intDTO;
    }

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO.uri = intDTO.uri;

        return apiDTO;
    }
}
