/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import core.services.ApiDTO;
import io.swagger.annotations.ApiModelProperty;

public class GenesisApiModel implements ApiDTO {

    @JsonProperty(value = "_key")
    public GenesisApiModelKey key;

    @JsonProperty(value = "_type")
    @ApiModelProperty(required = true)
    public String gentityType;
}
