/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    backofficeCustomerCommonService,
    backofficeCustomerRelationshipService,
    backofficeIndCustomerService,
    ClaimParty,
    EmployeeIndividual,
    IndividualCustomer,
    IOCustomer,
    OrganizationCustomer,
    PartyRole,
    Relationship
} from '@eisgroup/cap-services'
import {action, IObservableArray, observable, runInAction, toJS} from 'mobx'
import {
    EntityLink,
    LOAD_RELATIONSHIPS,
    ORGANIZATION_CUSTOMER,
    relatedCustomerState,
    relationshipState,
    relationshipTypeCd
} from '../..'
import {useRelationship} from '../constants'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'

export interface CustomerStore extends BaseRootStore {
    allAssociatedCustomers: IOCustomer[]
    mainInsured?: IndividualCustomer
    disabilityMainInsured?: EmployeeIndividual
    subjectOfClaim?: IndividualCustomer | OrganizationCustomer
    relatedSubjects: Relationship[]
    disabilityRelatedSubjects: IObservableArray<IndividualCustomer>
    subjectOfCaseRelationShip: string
    eventCaseRelatedIndividualParties: IndividualCustomer[]
    customer?: IndividualCustomer
    allClaimsBeneficiaries: IObservableArray<ClaimParty>
    searchCustomerByRootId: (rootId: string) => void
    getCustomerByRootId: (rootId: string) => IOCustomer | undefined
    getRelatedSubjects: (registryTypeId: string) => void
    getMainInsured: (registryTypeId: string) => Promise<IndividualCustomer>
    getDisabilityMainInsured: (registryTypeId: string) => void
    updateMainInsured: (customer: IndividualCustomer) => Promise<IndividualCustomer>
    getSubjectOfClaim: (registryTypeId: string) => Promise<IndividualCustomer>
    getClaimSubjectRelationShip: (memberRootId: string, subjectOfCaseLink: string) => void
    loadEventCaseIndividualParties: (registryIds: string[]) => void
    searchCustomersByRegistryIds: (registryIds) => Promise<IOCustomer[]>
    getBeneficiaries: (registryIds: string[], partyRoles: PartyRole[]) => void
    getGuardians: (registryIds: string[], partyRoles: PartyRole[]) => void
    searchAllCustomersByRegistryIds: (registryIds: string[]) => Promise<IOCustomer[]>
    /**
     * Updates a customer in the associated list.
     * @param customer The customer to update.
     */
    updateCustomerInAssociatedList: (customer: IOCustomer) => void

    /**
     * Retrieves a customer by either a registry ID or a root ID. If the customer is not already loaded,
     * it will be fetched from the backoffice service and added to the store.
     *
     * @param id  or ids - The registry ID or root ID to search for.
     * @returns A promise that resolves to the found customer.
     */
    getOrLoadCustomer<T extends IOCustomer>(id: string): Promise<T>

    getOrLoadCustomer<T extends IOCustomer>(ids: string[]): Promise<T[]>
}

export const LOAD_CUSTOMER = 'loadCustomer'

export class CustomerStoreImpl extends BaseRootStoreImpl implements CustomerStore {
    @observable allAssociatedCustomers: IObservableArray<IOCustomer> = observable<IOCustomer>([])

    @observable mainInsured?: IndividualCustomer

    @observable disabilityMainInsured?: EmployeeIndividual

    @observable subjectOfClaim?: IndividualCustomer | OrganizationCustomer

    @observable relatedSubjects: Relationship[] = []

    @observable disabilityRelatedSubjects: IObservableArray<IndividualCustomer> = observable([])

    @observable subjectOfCaseRelationShip: string

    @observable eventCaseRelatedIndividualParties: IndividualCustomer[] = []

    @observable customer?: IndividualCustomer

    @observable allClaimsBeneficiaries: IObservableArray<ClaimParty> = observable<ClaimParty>([])

    constructor() {
        super()
    }

    getOrLoadCustomer<T extends IOCustomer>(id: string): Promise<T>

    getOrLoadCustomer<T extends IOCustomer>(ids: string[]): Promise<T[]>

    @action
    getOrLoadCustomer<T extends IOCustomer>(idOrIds: string | string[]): Promise<T | T[]> {
        if (Array.isArray(idOrIds)) {
            // Handle an array of IDs
            return Promise.all(idOrIds.map(i => this.getOrLoadCustomer(i) as unknown as T))
        }
        const id = idOrIds
        // Check if the ID is a registry ID or a root ID
        const rootId = EntityLink.isValid(id) ? EntityLink.from(id).rootId : id
        const existingCustomer = this.getCustomerByRootId(rootId)
        return existingCustomer
            ? Promise.resolve(existingCustomer as T)
            : this.promiseCall(() => backofficeCustomerCommonService().searchCustomerByRootId(rootId)).then(result => {
                  runInAction(() => {
                      this.allAssociatedCustomers.push(result)
                  })
                  return result as T
              })
    }

    @action
    searchAllCustomersByRegistryIds = (registryIds: string[]): Promise<IOCustomer[]> => {
        return this.searchCustomersByRegistryIds(registryIds).then(customers =>
            runInAction(() => {
                this.allAssociatedCustomers.replace(customers)
                return customers
            })
        )
    }

    @action
    getCustomerByRootId = (rootId: string): IOCustomer | undefined => {
        return this.allAssociatedCustomers.find(customer => customer?._key?.rootId === rootId)
    }

    @action
    updateCustomerInAssociatedList = (customer: IOCustomer) => {
        const index = this.allAssociatedCustomers.findIndex(c => c?._key?.rootId === customer?._key?.rootId)
        if (index !== -1) {
            this.allAssociatedCustomers[index] = customer
        }
    }

    @action
    searchCustomerByRootId = (rootId: string) => {
        this.promiseCall(() => backofficeCustomerCommonService().searchCustomerByRootId(rootId)).then(r => {
            runInAction(() => {
                this.customer = r as IndividualCustomer
            })
        })
    }

    @action
    getRelatedSubjects = (registryTypeId: string) => {
        this.relatedSubjects = [] as Relationship[]
        this.promiseCall<any>(
            () => backofficeCustomerRelationshipService().getCustomerRelationships(registryTypeId),
            LOAD_RELATIONSHIPS
        ).then(r =>
            runInAction(() => {
                r.filter(v => {
                    const fromRootId = EntityLink.from(v.relationship?.from?._uri || '').rootId
                    const toRootId = EntityLink.from(v.relationship?.to?._uri || '').rootId
                    let relationshipRoleCd = ''
                    if (registryTypeId === fromRootId) {
                        relationshipRoleCd = v.relationship.relationshipRoleToCd
                    }
                    if (registryTypeId === toRootId) {
                        relationshipRoleCd = v.relationship.relationshipRoleFromCd
                    }
                    return (
                        useRelationship.findIndex(p => p === relationshipRoleCd) !== -1 &&
                        v.relatedCustomer.state === relatedCustomerState.CUSTOMER &&
                        v.relationship.relationshipTypeCd === relationshipTypeCd.PERSONAL &&
                        v.relationship.state === relationshipState.ACTIVE
                    )
                }).map(v => this.relatedSubjects.push(v))
            })
        )
    }

    @action
    getMainInsured = (registryTypeId: string): Promise<IndividualCustomer> => {
        return this.promiseCall(() =>
            this.getOrLoadCustomer(registryTypeId).then(payload => {
                runInAction(() => {
                    this.mainInsured = payload as IndividualCustomer
                })
                return payload as IndividualCustomer
            })
        )
    }

    @action
    getDisabilityMainInsured = (registryTypeId: string): void => {
        this.promiseCall(() => backofficeCustomerCommonService().searchCustomerByRegistryTypeId(registryTypeId))
            .then(r => {
                runInAction(() => {
                    this.disabilityMainInsured = r as EmployeeIndividual
                })
                const personalRelationships = toJS(this.disabilityMainInsured?.personalRelationships) ?? []
                return personalRelationships?.length > 0
                    ? backofficeCustomerCommonService().searchCustomersByRootId(
                          personalRelationships.map(v => EntityLink.from(v.relatedCustomer._uri).rootId)
                      )
                    : Promise.resolve([] as any)
            })
            .then(r => runInAction(() => this.disabilityRelatedSubjects.replace(r)))
    }

    @action
    updateMainInsured = (customer: IndividualCustomer): Promise<IndividualCustomer> => {
        return this.promiseCall(() =>
            backofficeIndCustomerService()
                .updateIndividualCustomer(customer)
                .then(payload => {
                    runInAction(() => {
                        this.mainInsured = payload
                    })
                    return payload
                })
        )
    }

    @action
    getSubjectOfClaim = (registryTypeId: string): Promise<IndividualCustomer> => {
        return this.promiseCall(
            () =>
                this.getOrLoadCustomer(registryTypeId).then(payload => {
                    runInAction(() => {
                        this.subjectOfClaim = payload as IndividualCustomer
                    })
                    return payload as IndividualCustomer
                }),
            LOAD_CUSTOMER
        )
    }

    @action
    getClaimSubjectRelationShip = (memberRootId: string, subjectOfCaseLink: string) => {
        this.promiseCall<any>(() =>
            backofficeCustomerRelationshipService().getCustomerRelationships(memberRootId)
        ).then(r =>
            runInAction(() => {
                r = r.filter(v => {
                    return (
                        ((v?.relationship?.from?._uri?.includes(this.mainInsured?._key?.rootId) &&
                            useRelationship.indexOf(v.relationship.relationshipRoleToCd) > -1) ||
                            (v?.relationship?.to?._uri?.includes(this.mainInsured?._key?.rootId) &&
                                useRelationship.indexOf(v.relationship.relationshipRoleFromCd) > -1)) &&
                        [relatedCustomerState.CUSTOMER, relatedCustomerState.QUALIFIED].includes(
                            v.relatedCustomer.state
                        ) &&
                        v.relationship.state === relationshipState.ACTIVE
                    )
                })
                const subjectOfCase = r.filter(
                    v => v.relatedCustomer.details.person.registryTypeId === subjectOfCaseLink
                )[0]
                if (subjectOfCase?.relationship?.from?._uri?.includes(this.mainInsured?._key?.rootId)) {
                    this.subjectOfCaseRelationShip = subjectOfCase.relationship?.relationshipRoleToCd
                }
                if (subjectOfCase?.relationship?.to?._uri?.includes(this.mainInsured?._key?.rootId)) {
                    this.subjectOfCaseRelationShip = subjectOfCase.relationship?.relationshipRoleFromCd
                }
            })
        )
    }

    @action
    loadEventCaseIndividualParties = (registryIds: string[]) => {
        const individualRegistryIds = registryIds.filter(v => EntityLink.from(v).modelName !== ORGANIZATION_CUSTOMER)
        this.promiseCall(() => this.getOrLoadCustomer(individualRegistryIds)).then(customer => {
            runInAction(() => {
                this.eventCaseRelatedIndividualParties = customer as IndividualCustomer[]
            })
        })
    }

    @action
    searchCustomersByRegistryIds = registryIds => {
        return this.promiseCall(
            () => backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(registryIds),
            LOAD_CUSTOMER
        )
    }

    @action
    getBeneficiaries = (registryIds: string[], partyRoles: PartyRole[]) => {
        this.promiseCall(() => this.getOrLoadCustomer(registryIds)).then(customers => {
            runInAction(() => {
                partyRoles.forEach(partyRole => {
                    const filteredCustomer = customers.find(
                        customer =>
                            [
                                (customer as IndividualCustomer).details.person?.registryTypeId,
                                (customer as OrganizationCustomer).details.legalEntity?.registryTypeId
                            ]
                                .filter(Boolean)
                                .indexOf(partyRole.registryId) > -1
                    )
                    if (filteredCustomer) {
                        this.allClaimsBeneficiaries.push({
                            customer: filteredCustomer,
                            partyRole
                        })
                    }
                })
            })
        })
    }

    /**
     * Get guardians from registryIds and partyRoles
     */
    @action
    getGuardians = (registryIds: string[], partyRoles: PartyRole[]) => {
        this.promiseCall(() => backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(registryIds)).then(
            customers => {
                runInAction(() => {
                    partyRoles.forEach(partyRole => {
                        const filteredCustomer = customers.find(
                            customer =>
                                [(customer as IndividualCustomer).details.person?.registryTypeId]
                                    .filter(Boolean)
                                    .indexOf(partyRole.representativeRegistryId) > -1
                        )
                        if (filteredCustomer) {
                            this.allClaimsBeneficiaries.push({
                                customer: filteredCustomer,
                                partyRole
                            })
                        }
                    })
                })
            }
        )
    }
}
