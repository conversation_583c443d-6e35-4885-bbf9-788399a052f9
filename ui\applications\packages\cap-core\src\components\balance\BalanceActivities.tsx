/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import React, {useEffect} from 'react'
import {observer} from 'mobx-react'
import {opt} from '@eisgroup/common-types'
import {useTranslate} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery} from '@eisgroup/cap-gateway-client'
import {ColumnProps, Table, Collapse, Tooltip} from '@eisgroup/ui-kit'
import {CapBalanceChangeLog} from '@eisgroup/cap-financial-models'
import {
    FINANCIAL_INFORMATION_BALANCE_SUB_TABLE,
    MoneyFormat,
    BLANCE_ACTIVITIES_TABLE,
    BLANCE_ACTIVITIES_COLLAPSE,
    BALANCE_DESCRIPTION_ELLIPSIS
} from '../..'
import {BalanceStore} from '../../common/store/BalanceStore'
import {LOAD_BALANCE_CHANGE_LOG} from '../../common/store/impl/BalanceStoreImpl'
import CapBalanceChangeLogEntity = CapBalanceChangeLog.CapBalanceChangeLogEntity

const {Panel} = Collapse

export interface BalanceActivitiesProps {
    balanceStore: BalanceStore
    payeeLink: string
}

export const BalanceActivities: React.FC<BalanceActivitiesProps> = observer(props => {
    const {balanceStore, payeeLink = ''} = props
    const {t} = useTranslate()

    useEffect(() => {
        loadBalanceChangeLog()
    }, [payeeLink])

    const loadBalanceChangeLog = () => {
        if (payeeLink) {
            const requestBody = {
                payee: {
                    matches: [payeeLink]
                },
                originSource: {
                    matches: [balanceStore.balanceSource]
                }
            } as CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery
            balanceStore.getBalanceChangeLog(requestBody)
        }
    }

    const getColumns = (): ColumnProps<any>[] => {
        return [
            {
                key: 'date',
                title: t('cap-core:balance_activities_table_date_label'),
                width: '10%',
                render: (text: string, record: CapBalanceChangeLogEntity) => dateUtils(record.creationDate).render
            },
            {
                key: 'id',
                title: t('cap-core:balance_activities_table_transaction_id_label'),
                width: '10%',
                render: (text: string, record: CapBalanceChangeLogEntity) => {
                    return (
                        <span>
                            {opt(record.balanceChangeLogDetails?.transactionNumber).orElse(t('cap-core:not_available'))}
                        </span>
                    )
                }
            },
            {
                key: 'type',
                title: t('cap-core:balance_activities_table_type_label'),
                width: '10%',
                render: (text: string, record: CapBalanceChangeLogEntity) => {
                    return (
                        <span>
                            <LookupLabel
                                lookup='CapTransactionType'
                                code={record.balanceChangeLogDetails?.transactionTypeCd}
                                emptyLabel={t('cap-core:not_available')}
                            />
                        </span>
                    )
                }
            },
            {
                key: 'amount',
                title: t('cap-core:balance_activities_table_amount_label'),
                align: 'right',
                width: '20%',
                render: (text: string, record: CapBalanceChangeLogEntity) => {
                    const currentBalanceAmount = record.balanceChangeLogDetails?.totalBalanceAmount?.amount || 0
                    const currentIndex = balanceStore.balanceChangeLog.findIndex(
                        v => v._key.rootId === record._key.rootId
                    )
                    if (currentIndex === -1) {
                        return t('cap-core:not_available')
                    }
                    const previousIndex = currentIndex + 1
                    const previousBalance = balanceStore.balanceChangeLog[previousIndex] || {}
                    const previousBalanceAmount =
                        previousBalance?.balanceChangeLogDetails?.totalBalanceAmount?.amount || 0
                    const transactionAmount = currentBalanceAmount - previousBalanceAmount
                    return transactionAmount === 0 ? (
                        0
                    ) : (
                        <MoneyFormat value={currentBalanceAmount - previousBalanceAmount} />
                    )
                }
            },
            {
                key: 'description',
                title: t('cap-core:balance_activities_table_transaction_description_label'),
                width: '10%',
                render: (text: string, record: CapBalanceChangeLogEntity) => {
                    return (
                        <Tooltip title={record.balanceChangeLogDetails?.description}>
                            <div className={BALANCE_DESCRIPTION_ELLIPSIS}>
                                {record.balanceChangeLogDetails?.description}
                            </div>
                        </Tooltip>
                    )
                }
            },
            {
                key: 'totalBalance',
                title: t('cap-core:balance_activities_table_total_balance_label'),
                align: 'right',
                width: '20%',
                render: (text: string, record: CapBalanceChangeLogEntity) => {
                    const currentBalanceAmount = record.balanceChangeLogDetails?.totalBalanceAmount?.amount || 0
                    return currentBalanceAmount === 0 ? 0 : <MoneyFormat value={currentBalanceAmount} />
                }
            }
        ]
    }
    const filterDataSource = balanceStore?.balanceChangeLog.filter((item, index, arr) => {
        return index !== arr.length - 1
            ? arr[index].balanceChangeLogDetails?.totalBalanceAmount?.amount !==
                  arr[index + 1].balanceChangeLogDetails?.totalBalanceAmount?.amount
            : item.balanceChangeLogDetails?.totalBalanceAmount?.amount !== 0
    })
    return (
        <div className={FINANCIAL_INFORMATION_BALANCE_SUB_TABLE}>
            <Collapse
                className={BLANCE_ACTIVITIES_COLLAPSE}
                defaultActiveKey='balanceActicities'
                isWhite
                accordion
                bordered
            >
                <Panel key='balanceActicities' header={<>{t('cap-core:balance_activities_table_label')}</>}>
                    <Table
                        className={BLANCE_ACTIVITIES_TABLE}
                        dataSource={filterDataSource}
                        columns={getColumns()}
                        loading={balanceStore.actionsStore.isRunning(LOAD_BALANCE_CHANGE_LOG)}
                        pagination={{
                            size: 'small',
                            defaultPageSize: 5,
                            pageSizeOptions: ['5', '10', '15', '20', '25'],
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: filterDataSource.length
                        }}
                    />
                </Panel>
            </Collapse>
        </div>
    )
})
