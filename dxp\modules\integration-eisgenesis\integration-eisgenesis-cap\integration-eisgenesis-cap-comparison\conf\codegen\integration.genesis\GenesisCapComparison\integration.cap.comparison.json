{"swagger": "2.0", "info": {"description": "API for comparison", "version": "1", "title": "comparison model API facade"}, "basePath": "/", "schemes": ["http", "https"], "paths": {"/api/common/comparison/v1/changeHistory": {"post": {"description": "Returns change history results", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapChangeHistoryRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapChangeHistoryEntityListSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapChangeHistoryEntityListSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapChangeHistoryEntityListSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapChangeHistoryEntityListSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "CapChangeHistoryRequest": {"properties": {"filter": {"type": "object"}, "uri": {"type": "string"}}, "title": "CapChangeHistoryRequest"}, "CapChangeHistoryEntity": {"properties": {"filter": {"$ref": "#/definitions/CapChangeHistoryFilter"}, "commandName": {"type": "string"}, "forwardPatch": {"type": "object"}, "inversePatch": {"type": "object"}, "_key": {"$ref": "#/definitions/CapChangeHistoryKey"}, "user": {"type": "string"}}, "title": "CapChangeHistoryEntity"}, "CapChangeHistoryRequestBody": {"properties": {"body": {"$ref": "#/definitions/CapChangeHistoryRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapChangeHistoryRequestBody"}, "CapChangeHistoryEntityListSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "array", "items": {"$ref": "#/definitions/CapChangeHistoryEntity"}}}, "title": "CapChangeHistoryEntityListSuccess"}, "CapChangeHistoryFilter": {"properties": {"values": {"type": "object"}}, "title": "CapChange<PERSON>istoryFilter"}, "CapChangeHistoryKey": {"properties": {"URI": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}, "title": "CapChangeHistoryKey"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}}}