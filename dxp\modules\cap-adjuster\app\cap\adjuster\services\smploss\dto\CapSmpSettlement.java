/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.smploss.dto;

import cap.adjuster.services.common.dto.GenesisRootApiModel;
import java.util.Map;

public class CapSmpSettlement extends GenesisRootApiModel {
    public Map<String, Object> policy;
    public String policyId;
    public Map<String, Object> settlementAbsenceInfo;
    public Map<String, Object> settlementDetail;
    public Map<String, Object> settlementLossInfo;
    public String settlementNumber;
    public Map<String, Object> settlementResult;
    public String settlementType;
    public String state;
}
