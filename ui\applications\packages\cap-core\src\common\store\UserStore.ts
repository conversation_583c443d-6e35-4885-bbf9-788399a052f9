/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    securityService,
    backofficeWorkService,
    Organization,
    OrgPerson,
    SecurityUserDomainRoleProfile
} from '@eisgroup/cap-services'
import {observable, action, runInAction, IObservableArray, toJS} from 'mobx'
import {opt, ErrorMessage} from '@eisgroup/common-types'
import {Either} from '@eisgroup/data.either'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'
import {EntityLink} from '../../utils/EntityLink'

const getEitherResult = (result: Either<ErrorMessage, any>) => {
    if (result.isRight) {
        return result.get()
    }
    return undefined
}

export interface UserStore extends BaseRootStore {
    isLoading?: boolean
    setLoadingStatus: (isLoading: boolean) => void
    /**
     * User that was selected from organizational person search results.
     */
    user?: OrgPerson

    userSearchResults: IObservableArray<OrgPerson>
    userSearchTableResults?: IObservableArray<UserEntity>
    resetUserSearchResults: () => void
    setUserSearchTableResults: (users: OrgPerson[]) => void
    resetUserSearchTableResults: () => void
    /**
     * Action to select an user.
     */
    selectUser: (selectedUser?: OrgPerson) => void
    /**
     * Action to search for users.
     */
    searchUsers: (userName: string, setTableResults?: boolean) => void
    clearSelectedUser: () => void
}

export interface UserEntity {
    userInfo: OrgPerson
    organizationInfo?: Organization
    roleInfo?: SecurityUserDomainRoleProfile
}

export class UserStoreImpl extends BaseRootStoreImpl implements UserStore {
    @observable user

    @observable userSearchResults

    @observable userSearchTableResults

    @observable isLoading?: boolean = false

    @action
    setLoadingStatus = (isLoading: boolean) => {
        runInAction(() => {
            this.isLoading = isLoading
        })
    }

    @action
    selectUser = (selectedUser?: OrgPerson) => {
        this.user = selectedUser
    }

    @action
    searchUsers = (userName: string, setTableResults?: boolean) => {
        this.callService<OrgPerson[]>(backofficeWorkService.searchOrganizationPersons(userName), response =>
            runInAction(() => {
                this.userSearchResults = response
                if (response.length < 1) {
                    this.resetUserSearchResults()
                    this.resetUserSearchTableResults()
                } else if (setTableResults) {
                    this.setUserSearchTableResults(response)
                }
            })
        )
    }

    @action
    resetUserSearchResults = (): void => {
        this.userSearchResults = []
    }

    @action
    resetUserSearchTableResults = (): void => {
        this.userSearchTableResults = []
    }

    @action
    setUserSearchTableResults = (users: OrgPerson[]) => {
        const result: UserEntity[] = []
        if (users && users.length > 0) {
            this.setLoadingStatus(true)
            const uuids = users
                .map(user => {
                    return opt(user?.securityIdentity).orElse('')
                })
                .filter(Boolean)
            this.searchDomainUsers(uuids, usersRes => {
                if (usersRes?.length === 0) {
                    this.setLoadingStatus(false)
                    this.resetUserSearchTableResults()
                    this.clearSelectedUser()
                }
                usersRes.forEach(userRes => {
                    const userRootId = opt(userRes.userProfiles?.[0]?._key.rootId).orElse('')
                    const userRevisionNo = opt(userRes.userProfiles?.[0]?._key.revisionNo).orElse('')
                    const user = users.filter(usr => usr.securityIdentity === userRes.uuid)[0]
                    const organizationRootId = EntityLink.from(
                        user.organizationAssignments?.[0]?.organization?._uri || ''
                    ).rootId
                    runInAction(() => {
                        this.getOrganizationRoleInfo(
                            userRootId,
                            userRevisionNo,
                            organizationRootId,
                            (organizationInfo, roleInfo) => {
                                const userSearchItem = {
                                    userInfo: {
                                        ...toJS(user),
                                        personInfo: {...toJS(user).personInfo}
                                    },
                                    organizationInfo: {...organizationInfo},
                                    roleInfo: {
                                        ...roleInfo
                                    }
                                } as UserEntity
                                result.push(userSearchItem)
                                if (result.length === usersRes.length) {
                                    runInAction(() => {
                                        this.userSearchTableResults = result
                                    })
                                    this.selectUser(result[0]?.userInfo)
                                    this.setLoadingStatus(false)
                                }
                            }
                        )
                    })
                })
            })
        } else {
            this.setLoadingStatus(false)
        }
    }

    @action
    clearSelectedUser = (): void => {
        this.user = undefined
    }

    async getOrganizationRoleInfo(
        userRootId: string,
        userRevisionNo: number,
        organizationRootId: string,
        callback?: any
    ): Promise<void> {
        if (userRootId && userRevisionNo && organizationRootId) {
            await Promise.all([
                backofficeWorkService.searchOrganizationalByUser(organizationRootId).map(getEitherResult).toPromise(),
                securityService.getUserDomainDetail(userRootId, userRevisionNo).map(getEitherResult).toPromise()
            ]).then(response => {
                const userProfiles = opt(response[1]?.userProfiles[0]).orElse({})
                const operationAssignment = opt(userProfiles.operationalAssignments[0]).orElse({})
                const roleInfo =
                    operationAssignment.roleAccessProfile && operationAssignment.roleAccessProfile?.length > 0
                        ? operationAssignment.roleAccessProfile[0]
                        : {}
                callback(response[0], roleInfo)
            })
        } else if (userRootId && userRevisionNo && !organizationRootId) {
            securityService
                .getUserDomainDetail(userRootId, userRevisionNo)
                .map(getEitherResult)
                .toPromise()
                .then(response => {
                    callback(response, {})
                })
        } else {
            backofficeWorkService
                .searchOrganizationalByUser(organizationRootId)
                .map(getEitherResult)
                .toPromise()
                .then(response => {
                    callback({}, response)
                })
        }
    }

    @action
    async getUserDomainDetail(rootId: string, revisionNo: number): Promise<void> {
        return securityService.getUserDomainDetail(rootId, revisionNo).map(getEitherResult).toPromise()
    }

    searchDomainUsers(uuids: string[], callback?: any): void {
        securityService
            .searchUserDomainUsers(uuids)
            .map(getEitherResult)
            .toPromise()
            .then(response => {
                callback(response)
            })
    }
}
