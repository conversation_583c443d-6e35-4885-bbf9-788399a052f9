"use strict";
/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.resources = void 0;
const cap_adjuster_i18n_en_MT_1 = require("./cap-adjuster-i18n.en_MT");
const cap_adjuster_i18n_en_1 = require("./cap-adjuster-i18n.en");
exports.resources = [cap_adjuster_i18n_en_MT_1.enMT, cap_adjuster_i18n_en_1.enUS];
//# sourceMappingURL=index.js.map