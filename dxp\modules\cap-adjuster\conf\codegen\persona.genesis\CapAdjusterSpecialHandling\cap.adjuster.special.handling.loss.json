{"swagger": "2.0", "x-dxp-spec": {"imports": {"death": {"schema": "integration.cap.special.handling.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Special Handling API", "version": "1", "title": "CAP Adjuster: Special Handling API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/special-handling", "description": "CAP Adjuster: Special Handling API"}], "paths": {"/special-handling": {"post": {"summary": "Initialize and create the specialHandling", "x-dxp-method": "post", "x-dxp-path": "/api/capspecialhandling/CapSpecialHandling/v1/command/createSpecialHandling", "tags": ["/cap-adjuster/v1/special-handling"]}, "put": {"summary": "Update specialHandling", "x-dxp-method": "post", "x-dxp-path": "/api/capspecialhandling/CapSpecialHandling/v1/command/updateSpecialHandling", "tags": ["/cap-adjuster/v1/special-handling"]}}}}