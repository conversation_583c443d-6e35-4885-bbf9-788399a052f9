/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {observable, action} from 'mobx'
import {RouterState} from 'react-router'

export interface RoutingStore {
    routingData: Partial<RouterState>
    setRoutingData: (routingData: Partial<RouterState>) => void
}

export class RoutingStoreImpl implements RoutingStore {
    @observable routingData: Partial<RouterState> = {}

    @action
    setRoutingData = (routingData: Partial<RouterState>) => {
        this.routingData = routingData
    }
}
