/// <reference types="react" />
import { BaseRootStoreImpl } from '@eisgroup/cap-core';
export declare const LOAD_INITIAL_DATA = "loadInitialData";
export interface ClaimLossListStore {
    /**
     * Claim loss list
     */
    claimLossList: any[];
    /**
     * Initiates store with needed data
     */
    initLossListStore: () => void;
    /**
     * Searches claim losses with provided search criteria
     */
    searchClaimLoss: (lossNumber: string) => void;
}
export declare class ClaimLossListStore extends BaseRootStoreImpl implements ClaimLossListStore {
    claimLossList: any[];
    private filterLifeLoss;
    initLossListStore: () => void;
    searchClaimLoss: (lossNumber: string) => void;
}
export declare const createViewLoader: <P = {}>(config: import("@eisgroup/cap-core").ViewLoaderConfig<ClaimLossListStore, P>) => import("react").FC<Partial<import("react-router").RouterState> & P>, connectToStore: <P, EP = Partial<P>>(config: import("@eisgroup/cap-core").ConnectToStoreConfig<P, EP, ClaimLossListStore>) => import("react").FC<EP>;
//# sourceMappingURL=ClaimLossListStore.d.ts.map