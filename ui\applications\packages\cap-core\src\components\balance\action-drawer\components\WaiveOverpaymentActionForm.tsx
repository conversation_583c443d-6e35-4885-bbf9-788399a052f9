import React, {FC, useState} from 'react'
import {t} from '@eisgroup/i18n'
import {Money} from '@eisgroup/models-api'
import {observer} from 'mobx-react'
import {PaymentDefinition} from '@eisgroup/cap-financial-models'
import {CaseSystemService, CSClaimWrapperService} from '@eisgroup/cap-services'
import {ActionFormBase} from './ActionFormBase'
import {BALANCE_ACTION_FORM_ID} from '../../../../common/constants'
import config from '../../../../builder/BalanceWaiveOverPayment.builder'
import {DrawerFormStateType} from '../../../form-drawer'
import {WAIVE_OVERPAYMENT} from '../../../../common/store'

import PaymentApprovalResult = PaymentDefinition.PaymentApprovalResult
import OverpaymentWaiveApprovalInput = PaymentDefinition.OverpaymentWaiveApprovalInput
import {extractValuesBetweenAngleBrackets} from '../../../../common/utils'
import {BalanceActionDrawerProps} from '../BalanceActionDrawer'
import {ICaseSystem} from '../../../../common/Types'

type WaiveOverpaymentActionFormProps = BalanceActionDrawerProps<
    ICaseSystem,
    CaseSystemService | CSClaimWrapperService<ICaseSystem>
> & {
    totalBalance: number
    payUnderpaymentAmount: Money | undefined
}

export const WaiveOverpaymentActionForm: FC<WaiveOverpaymentActionFormProps> = observer(props => {
    const {
        actionKey: key,
        totalBalance,
        payUnderpaymentAmount,
        getBalanceChangeLog,
        payeeLink,
        closeDrawer,
        balanceStore,
        getLoadPayments
    } = props
    const isWaiveOverpaymentLoading = balanceStore.actionsStore.isRunning(WAIVE_OVERPAYMENT)

    const [overpaymentAuthority, setOverpaymentAuthority] = useState<PaymentApprovalResult>()

    const handleVisibleChange = (visible: boolean, values: any): void => {
        if (!visible) {
            return
        }

        setOverpaymentAuthority({} as PaymentApprovalResult)
        const amount = values && values?.amount?.amount
        balanceStore
            .validateOverpaymentWaiveAuthority({
                paymentNetAmount: amount,
                _type: 'OverpaymentWaiveApprovalInput'
            } as OverpaymentWaiveApprovalInput)
            .subscribe(value => {
                setOverpaymentAuthority(value.get())
            })
    }

    const constructOverpaymentAuthorityMessage = (authority?: PaymentApprovalResult): string => {
        if (authority?.approvalStatus === 'Approved') {
            return t('cap-core:balance_actions_drawer_overpayment_authorized_label')
        }
        const messageValues = extractValuesBetweenAngleBrackets(authority?.message)
        if (!messageValues || !messageValues.length) {
            return t('cap-core:form_not_available')
        }
        return t('cap-core:balance_actions_drawer_overpayment_unauthorized_label', messageValues)
    }

    const constructOverpaymentPopupCancel = (authority?: PaymentApprovalResult): string => {
        return authority?.approvalStatus === 'Approved'
            ? t('cap-core:cancel')
            : t('cap-core:balance_actions_drawer_overpayment_close_btn')
    }

    const onFormConfirm = (values): void => {
        const params = {
            payee: {_uri: payeeLink},
            originSource: {_uri: balanceStore.balanceSource}
        }
        const saveParams = {...params, waiveAmount: values.amount, waiveReason: values.comments}
        const nextBalanceCount = balanceStore.balanceChangeLogCount + 1

        balanceStore.waiveOverpayment(saveParams).subscribe(() => {
            getBalanceChangeLog(nextBalanceCount)
            getLoadPayments()
            closeDrawer()
        })
    }

    return (
        <ActionFormBase
            uiEngineProps={{
                formId: BALANCE_ACTION_FORM_ID,
                config,
                initialValues: {
                    totalBalance,
                    amount: payUnderpaymentAmount,
                    action: key
                },
                onNext: onFormConfirm
            }}
            drawerActionProps={{
                isSaveConfirmationEnabled: true,
                handleSaveConfirmVisibleChange: handleVisibleChange,
                isSaveConfirmationButtonEnabled: overpaymentAuthority?.approvalStatus === 'Approved',
                drawerFormState: DrawerFormStateType.Create,
                handleFormCancel: closeDrawer,
                isLoading: isWaiveOverpaymentLoading,
                handleFormConfirm: onFormConfirm,
                labels: {
                    saveConfirmMessage: constructOverpaymentAuthorityMessage(overpaymentAuthority),
                    saveConfirmAcceptLabel: t('cap-core:payments_action_payments_popconfirm_authorize'),
                    saveConfirmCancelLabel: constructOverpaymentPopupCancel(overpaymentAuthority)
                }
            }}
        />
    )
})
