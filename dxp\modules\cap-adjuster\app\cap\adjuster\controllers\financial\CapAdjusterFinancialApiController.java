/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.financial;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import cap.adjuster.services.financial.CapAdjusterFinancialService;
import cap.adjuster.services.financial.dto.CapFinancialPayment;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterFinancialApiController.TAG_API_CAP_ADJUSTER_FINANCIAL,
                description = "CAP Adjuster: Financial API")})
@Api(value = CapAdjusterFinancialApiController.TAG_API_CAP_ADJUSTER_FINANCIAL,
        tags = CapAdjusterFinancialApiController.TAG_API_CAP_ADJUSTER_FINANCIAL)
public class CapAdjusterFinancialApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_FINANCIAL = "/cap-adjuster/v1/financial";

    private CapAdjusterFinancialService capAdjusterFinancialService;

    /**
     * Get scheduled and actual payments by indicated loss
     *
     * @param rootId intake loss identifier
     * @param revisionNo intake loss revision
     * @param modelName intake loss model name
     *
     * @return list of scheduled and actual payments related to indicated loss
     */
    @ApiOperation(value = "Get scheduled and actual payments",
        response = CapFinancialPayment.class,
        responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
            message = HttpStatusDescription.BAD_REQUEST,
            response = GenesisCommonExceptionDTO.class),
        @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
            message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
            response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getPaymentsWithScheduled(
        @ApiParam(value = "Intake Loss Identifier", required = true) String rootId,
        @ApiParam(value = "Intake Loss Revision number", required = true) Integer revisionNo,
        @ApiParam(value = "Intake Loss model name", required = true) String modelName) {
        return completeOk(capAdjusterFinancialService.getPaymentsWithScheduled(rootId, revisionNo, modelName));
    }

    @Inject
    public void setCapAdjusterFinancialService(CapAdjusterFinancialService capAdjusterFinancialService) {
        this.capAdjusterFinancialService = capAdjusterFinancialService;
    }
}
