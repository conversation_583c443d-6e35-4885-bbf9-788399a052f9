/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package dataproviders;

import java.util.concurrent.CompletionStage;

import com.google.inject.ImplementedBy;

import core.services.pagination.PageData;
import dataproviders.common.dto.GenesisRootDTO;
import dataproviders.common.dto.GenesisSearchResponseDTO;
import dataproviders.impl.CapClaimSLSDataProviderImpl;

@ImplementedBy(CapClaimSLSDataProviderImpl.class)
public interface CapClaimSLSDataProvider<T extends GenesisRootDTO> {

    /**
     * Single line search claim
     *
     * @param text
     * @param fields
     * @param pageData
     * @param responseType
     * @return
     */
    CompletionStage<GenesisSearchResponseDTO> singleLineSearchClaim(String text, String fields, PageData pageData, Class<T> responseType);
}

