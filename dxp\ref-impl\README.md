# DXP API Reference Implementation Project

This document describes how to work with DXP API Project for reference implementation

**Table of Content**

- [Requirements](#Requirements)
- [Update Environments/Nexus Configuration](#Update Environments/Nexus Configuration)
- [Configure Project Dependencies](#Configure Project Dependencies)
- [Configure Coding Conventions](#Configure Coding Conventions)
- [Use Source Code/Binary Dependencies in Development](#Use Source Code/Binary Dependencies in Development)
- [Compile/Build and Run Project](#Compile/Build and Run Project)
- [Known Issues](#Known Issues)
- [Additional Documentation](#Additional Documentation)

## Requirements

* IDE with GIT and SBT support (IntelliJ or Eclipse)
* Java 21
* SBT (is used as build tool to compile Scala and Java code in the project)
`<root_project_directory>/project/build.properties`
* Scala plugin/extension (add to the IDE plugins)

## Update Environments/Nexus Configuration

By default, DXP API Reference Implementation project configuration is provided to use Base DXP API environments and Nexus Repository.

1. To update environments configuration, change environments configuration files "**envs/application.<env_type>.conf**"
   with specific back-end MS URLs:
   ```
   eiscore.baseurl = "<EIS_Suite_V12_URL>"
   genesis.<domain>.baseurl = "<EIS_Suite_V20_Domain_MS_URL>"
   ```

2. To update Nexus Repository configuration, change all resolvers URLs in following files:

   **project/plugins.sbt**:
   ```
   resolvers += "SBT Group Sonatype Nexus Repository Manager" at "<Nexus_NPM_Repo_URL>"
   resolvers += "DXP Group Sonatype Nexus Repository Manager" at "<Nexus_MVN_Repo_URL>"
   ```
   **project/Common.scala**:
   ```
   resolvers += "DXP Group Sonatype Nexus Repository Manager" at "<Nexus_MVN_Repo_URL>"
   ```
   **project/Release.scala**:
   ```
   publishTo := {
      if (version.value.trim.matches("\\d{1,}(.\\d{1,})+"))
        Some("dxp-release" at "<Nexus_Release_Repo_URL>")
      else
        Some("dxp-snapshot" at "<Nexus_Staging_Repo_URL>")
   }
   ```

## Configure Project Dependencies

Add file to User home .ivy2 folder (with name ***.credentials.genesis***) contains user domain credentials with access to Nexus repository:
```
realm=Sonatype Nexus Repository Manager
host=<Nexus_Base_Path_URL> // example - sfoeisgennexus01.exigengroup.com
user=<username>
password=<password>
```
**NOTE**: Nexus Base Path URL should be the same in the project configuration files (from **Update Environments Configuration** section) and credentials file

## Configure Coding Conventions

Configure code style template to follow specified formatting and standards
Code style settings can be set by using Checkstyle rules config file (IDE Integration) that is available using the following path in project:
`<root_project_directory>/project/codequality`

Add Code Style Settings:
1. Open following settings:
`File → Settings → Editor → Code Style → Java → ⚙ ️→ Import Scheme → IntelliJ IDEA code style XML`
2. Choose Checkstyle file from following directory (as a config file):
`<root_project_directory>/project/codequality/Genesis_Intellij.xml`
3. Save settings

## Use Source Code/Binary Dependencies in Development

By default, DXP API Reference Implementation project use binary dependencies of Base DXP API project.
Each module contains following binary dependency of Base module in **build.sbt** file:

`libraryDependencies += "com.eisgroup.dxp" % "<module_name>" % "<version>"`

All module configuration files/folders/packages names are started using specific vendor prefix.

Vendor prefix is configurable and can be updated using one of the scripts file depends on OS (<root_project_directory>/scripts/...):
```
# Enter new vendor prefix to be applied in the project
new_vendor_prefix="eis"
```

Provide necessary **new_vendor_prefix** value and run the script from the project root directory (**NOT** from "/scripts" directory).
```
<full_path_to_the_script_file_on_local_machine>.sh
```
Examples:
* MacOS - /Users/<USER>/dxp-api/scripts/ref_impl_update_vendor_prefix_macos.sh
* Windows - C:/Users/<USER>/dxp-api/scripts/ref_impl_update_vendor_prefix_windows.sh

It will automatically rename all necessary configurations, files and folders.

If there is a need to use source code instead of binary dependency, please use following steps:

**<vendor_prefix>** - value of the specified prefix for the project (e.g. "eis")
1. Update **conf/common.conf** file with specific module configuration file:
    ```
    # Reference configuration for included modules
    ...
    include "<vendor_prefix>.backoffice.module.conf"
    ...
    ```
2. Update **conf/gateway.routes** file with specific module route file:
    ```
    ...
    ->  /  <vendor_prefix>.backoffice.module.Routes
    ...
    ```
3. Update specific module ***.module.conf** and ***.routes** files (the same way as in step 1-2):
    * Use configuration files *with* vendor prefix - specific project configuration (source code)
    * Use configuration files *without* vendor prefix - base project configuration (binary dependency)

## Compile/Build and Run Project

Use SBT shell to run/debug application using following steps:
1. Open root folder of the project
2. Compile project: use following command - `;clean ;compile`
3. Debug project: click `Attach debugger to sbt shell` button on the left sidebar of sbt shell
4. Run application: use following command - `run <port>` (e.g. 9000)
5. Verify that application is running using DXP Swagger UI in browser:
   http://localhost:9000/core/swagger/index.html

## Known Issues

While first compilation/build process you can face with issues for existing packages, e.g.:
```
[error] ..\modules\integration-eisgenesis\integration-eisgenesis-common\app\dataproviders\common\utils\GenesisPathUtils.java:
        package com.google.common.collect does not exist
[error] com.google.common.collect.ImmutableMap
```
***Solution:*** Remove all local artifacts from .m2 and .ivy2 folders in your <user_home> folder, check that you are using Nexus from configuration and recompile project

## Additional Documentation

[DXP API Development - CookBook](https://wiki.eisgroup.com/display/GRC/DXP+API+Reference+Implementation)
