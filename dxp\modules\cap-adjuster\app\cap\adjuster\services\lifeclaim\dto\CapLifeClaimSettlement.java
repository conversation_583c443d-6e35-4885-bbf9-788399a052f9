/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.lifeclaim.dto;

import cap.adjuster.services.common.dto.GenesisRootApiModel;
import dataproviders.dto.CapClaimSettlementResultDTO;

public class CapLifeClaimSettlement extends GenesisRootApiModel {
    public String settlementType;
    public Object claimLossIdentification;
    public Object claimWrapperIdentification;
    public Object policy;
    public String policyId;
    public String settlementNumber;
    public Object settlementDetail;
    public String state;
    public Object applicabilityResult;
    public Object settlementLossInfo;
    public Object coverageBasedConfiguration;
    public CapClaimSettlementResultDTO settlementResult;
    public Object settlementApprovalResult;
    public Object settlementLifeIntakeInfo;
    public String claimCoverageName;
    public String claimCoveragePrefix;
}
