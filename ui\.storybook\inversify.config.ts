/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {IoC} from '@eisgroup/ioc'
import {Localization, LocalizerProvider, LocalizationUtils} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {authentication} from '@eisgroup/auth'
import {lookups} from '@eisgroup/lookups'
import {claims} from '@eisgroup/claims-core'
import {resources} from '@eisgroup/claims-core/src/i18n'
import {AuthenticationProviderImpl, AuthTypes, Session, SessionImpl} from '@eisgroup/common-security-provider'

import {LinkResolverServiceImpl} from './LinkResolverServiceImpl'
import {DXPLookupProxy} from './DXPLookupProxy'

const supportedLocales: Localization.Locale[] = [
    {country: 'US', language: 'en'},
    {country: 'GB', language: 'en'},
    {country: 'MT', language: 'en'}
]
const currentLocale = supportedLocales[0]

IoC.rebind(Localization.TYPES.LocalizerProvider).to(LocalizerProvider)

IoC.get<LocalizerProvider>(Localization.TYPES.LocalizerProvider).initLocalizer({
    supportedLocales,
    currentLocale
})

IoC.bind<Session>(AuthTypes.SessionService).to(SessionImpl)

IoC.bind<authentication.AuthenticationProvider>(authentication.TYPES.AuthenticationProvider).to(
    AuthenticationProviderImpl
)

IoC.get<authentication.AuthenticationFacade>(authentication.TYPES.AuthenticationFacade).init({
    ...IoC.get<authentication.AuthenticationProvider>(
        authentication.TYPES.AuthenticationProvider
    ).getAuthenticationService(),
    getAuthenticatedUser: () => opt({userName: 'qa'} as any)
})

IoC.bind(claims.TYPES.LinkResolverService).to(LinkResolverServiceImpl)

IoC.bind(lookups.TYPES.LookupProxy).to(DXPLookupProxy)

LocalizationUtils.addResourceBundles(resources)
