/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {useState, useEffect} from 'react'
import {Either} from '@eisgroup/data.either'
import {Observable} from 'rxjs'

import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import {noop} from 'lodash'

const cache: Record<string, any> = {}
const instances: Record<string, number> = {}
const observables: Record<string, Observable<Either<ErrorMessage, any>>> = {}

export function useLoadLinkEntities<T, R>(
    uri: string[],
    obs?: RxResult<T>,
    transform?: (data: T) => R
): {data?: R; loaded: boolean; error: boolean} {
    const [data, setData] = useState<R>()
    const [loaded, setLoaded] = useState(!obs)
    const [error, setError] = useState(!obs)

    useEffect(() => {
        if (!obs) {
            return noop
        }

        const key = uri.join()
        const cachedValue = cache[key]

        if (cachedValue) {
            setData(cachedValue)
            setLoaded(true)
            return noop
        }

        instances[key] = (instances[key] || 0) + 1

        if (!observables[key]) {
            observables[key] = obs.shareReplay()
        }

        const sub = (observables[key] as RxResult<T>).subscribe({
            next: either => {
                if (either.isLeft) {
                    setError(true)
                    return
                }

                const result = either.get()
                const transformedData = transform ? transform(result) : result
                cache[key] = transformedData
                setData(transformedData as R)
            },
            complete: () => setLoaded(true)
        })

        return () => {
            instances[key] -= 1

            if (instances[key] === 0) {
                delete instances[key]
                delete observables[key]
            }

            sub.unsubscribe()
        }
    }, [obs, uri])

    return {data, loaded, error}
}
