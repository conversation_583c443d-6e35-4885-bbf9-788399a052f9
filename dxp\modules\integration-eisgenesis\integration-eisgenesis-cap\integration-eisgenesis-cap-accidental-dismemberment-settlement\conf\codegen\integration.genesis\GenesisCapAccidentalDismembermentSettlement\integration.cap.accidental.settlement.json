{"swagger": "2.0", "info": {"description": "API for AccidentalDismembermentSettlement", "version": "1", "title": "AccidentalDismembermentSettlement model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capsettlement/AccidentalDismembermentSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/approveSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/closeSettlement": {"post": {"description": "The command that closes settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/initManualSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeInitManualSettlementInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/initSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeSettlementInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/AccidentalDismembermentSettlementKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/CapAccidentalDismembermentSettlementAdjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapAccidentalDismembermentSettlementAdjudicationInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/adjudicateSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/approveSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/disapproveSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/initSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/readjudicateSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/settlementToChildSettlementRefreshInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AccidentalDismembermentSettlementKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "AccidentalDismembermentSettlementKrakenDeprecatedBundleRequest"}, "AccidentalDismembermentSettlementKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentSettlementKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AccidentalDismembermentSettlementKrakenDeprecatedBundleRequestBody"}, "AccidentalDismembermentSettlementLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntity"}}}, "title": "AccidentalDismembermentSettlementLoadHistoryResult"}, "AccidentalDismembermentSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AccidentalDismembermentSettlementLoadHistoryResult"}}, "title": "AccidentalDismembermentSettlementLoadHistoryResultSuccess"}, "AccidentalDismembermentSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentSettlementLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AccidentalDismembermentSettlementLoadHistoryResultSuccessBody"}, "AccidentalDismembermentSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "AccidentalDismembermentSettlement AccessTrackInfo"}, "AccidentalDismembermentSettlement_BaseLifeAccumulatorRemaining": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeAccumulatorRemaining"}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorType": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "amountUnit": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Claim Coverage Prefix"}, "limitAmount": {"type": "number", "description": "Accumulator limit amount."}, "policyTerm": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "usedAmount": {"type": "number", "description": "Accumulator used amount."}}, "title": "AccidentalDismembermentSettlement BaseLifeAccumulatorRemaining"}, "AccidentalDismembermentSettlement_BaseLifeFormulaCalculationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeFormulaCalculationDetails"}, "calculatedResult": {"$ref": "#/definitions/Money"}, "calculatedTerm": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Term"}, "formulaContent": {"type": "string", "description": "Formula Content."}, "limitLevelType": {"type": "string", "description": "Limit level type."}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeFormulaParameter"}}}, "title": "AccidentalDismembermentSettlement BaseLifeFormulaCalculationDetails"}, "AccidentalDismembermentSettlement_BaseLifeFormulaParameter": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeFormulaParameter"}, "paramExtension": {"type": "object", "description": "Extension of parameter. E.g currency"}, "paramName": {"type": "string", "description": "Parameter name."}, "paramValue": {"type": "number", "description": "Value of parameter."}}, "title": "AccidentalDismembermentSettlement BaseLifeFormulaParameter"}, "AccidentalDismembermentSettlement_BaseLifeGrossBenefitAmount": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeGrossBenefitAmount"}, "totalApprovedAmount": {"$ref": "#/definitions/Money"}}, "title": "AccidentalDismembermentSettlement BaseLifeGrossBenefitAmount"}, "AccidentalDismembermentSettlement_BaseLifePolicyCoverageLimitLevel": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifePolicyCoverageLimitLevel"}, "limitLevelType": {"type": "string", "description": "Type of limit level"}, "timePeriodCd": {"type": "string", "description": "Time period Code"}}, "title": "AccidentalDismembermentSettlement BaseLifePolicyCoverageLimitLevel"}, "AccidentalDismembermentSettlement_BaseLifeSettlementAccumulatorDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementAccumulatorDetails"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCoverage": {"type": "string", "description": "Accumulator Coverage"}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}, "autoAdjudicatedAmount": {"type": "number", "description": "Auto adjudicated amount for each accumulator details."}}, "title": "AccidentalDismembermentSettlement BaseLifeSettlementAccumulatorDetails"}, "AccidentalDismembermentSettlement_BaseLifeSettlementAccumulatorDetailsExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "AccidentalDismembermentSettlement BaseLifeSettlementAccumulatorDetailsExtension"}, "AccidentalDismembermentSettlement_BaseLifeSettlementApprovalResultDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementApprovalResultDetails"}, "authorityLimit": {"$ref": "#/definitions/Money"}, "calculatedAmount": {"$ref": "#/definitions/Money"}, "entityRefNo": {"type": "string"}, "messageCd": {"type": "string"}, "modelScope": {"type": "string"}, "settlementApprovalCd": {"type": "string"}}, "title": "AccidentalDismembermentSettlement BaseLifeSettlementApprovalResultDetails"}, "AccidentalDismembermentSettlement_BaseLifeSettlementWrapperInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementWrapperInfo"}, "claimSubjectId": {"type": "string", "description": "Unique identifier for subject of claim"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID."}}, "title": "AccidentalDismembermentSettlement BaseLifeSettlementWrapperInfo"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentBenefitInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentBenefitInfoEntity"}, "benefitAmount": {"$ref": "#/definitions/Money"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "burnInfo": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapBurnInfoEntity"}}, "childBenefitPct": {"type": "number"}, "incurralPeriod": {"type": "integer", "format": "int64"}, "maxBenefitAmount": {"$ref": "#/definitions/Money"}, "maxBenefitNumber": {"type": "integer", "format": "int64"}, "multiplePct": {"type": "number"}, "partialOrChipPct": {"type": "number"}, "reductionInfo": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapReductionAmountInfoEntity"}}, "repairedThroughSurgeryPeriod": {"type": "integer", "format": "int64"}, "spouseBenefitPct": {"type": "number"}, "timePeriodCd": {"type": "string"}, "waitingPeriod": {"type": "integer", "format": "int64"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentBenefitInfoEntity", "description": "An entity for benefit information."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentCertInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentCertInfoEntity"}, "capBenefitInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentCoverageInfoEntity"}}, "eligibilityInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapMainCoverageEligibilityInfoEntity"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentCertInfoEntity"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentCoverageInfoEntity"}, "childOrganizedSportBenefitPct": {"type": "number", "description": "Child Organized Sport Benefit Pct"}, "coverageCd": {"type": "string", "description": "The value presents the value of policy coverage code"}, "isChildOrganizedSportApplied": {"type": "boolean"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentCoverageInfoEntity", "description": "An entity for coverage information."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentMasterInfoEntity"}, "capBenefitInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentCoverageInfoEntity"}}, "eligibilityInfos": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapMainCoverageEligibilityInfoEntity"}}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentRelatedSettlmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentRelatedSettlmentEntity"}, "benefitCategory": {"type": "string", "description": "Benefit Category"}, "benefitCd": {"type": "string"}, "burnDegree": {"type": "string", "description": "Burn degree: Second/Third"}, "burnLink": {"$ref": "#/definitions/EntityLink"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Period"}, "dependentRegistryId": {"type": "string", "description": "Individual dependent identifier which is associated with the case"}, "incidentDate": {"type": "string", "format": "date-time"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentRelatedSettlmentEntity"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementApprovalResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementApprovalResultEntity"}, "approvalStatus": {"type": "string"}, "resultDetails": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeSettlementApprovalResultDetails"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementApprovalResultEntity"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementAttrOptionsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementAttrOptionsEntity"}, "attrName": {"type": "string", "description": "Attribute Name"}, "options": {"type": "array", "items": {"type": "string", "description": "Options of attribute, e.g. Mandatory"}}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementAttrOptionsEntity"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementCoverageConfigEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementCoverageConfigEntity"}, "accidentInd": {"type": "boolean", "description": "Indicator to mark if the death is caused by a accident and could be covered by ADD, cause of loss = accident."}, "accumulationCategoryGroup": {"type": "string", "description": "Category group to indicate the accumulating group a benefit is belonged to in settlement amount calculation."}, "applicableBurnDegrees": {"type": "array", "items": {"type": "string", "description": "Applicable burn degrees lookup configuration for UI"}}, "applicableReductionAmountTypes": {"type": "array", "items": {"type": "string", "description": "Applicable reduction amount types lookup configuration for UI"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementAttrOptionsEntity"}}, "benefitCategory": {"type": "string", "description": "Benefit Category"}, "benefitLevelMapping": {"type": "array", "items": {"type": "string", "description": "Claim fields mapping to policy fields configuration."}}, "calculationFormulaId": {"type": "string", "description": "Formula Id to define the formulas that are used in settlement calculation for different benefits."}, "grossAmountMode": {"type": "string", "description": "Mode of gross amount used to define if the gross amount calculated is based on the defined time frequency. This will determines the payment frequency for the recurring payment."}, "groupUnit": {"type": "string", "description": "Indicate the amount type for group accumulator limit level amount"}, "incurralPeriodUnit": {"type": "string", "description": "Incurral Period Unit."}, "limitLevels": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string", "description": "Indicate the amount type for accumulator limit level amount"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementCoverageConfigEntity"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "AccidentalDismembermentSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "benefitCd": {"type": "string", "description": "Policy Benefit Code"}, "burnDegree": {"type": "string", "description": "Burn degree: Second/Third"}, "burnLink": {"$ref": "#/definitions/EntityLink"}, "cancelReason": {"type": "string", "description": "The settlement cancelled reason"}, "chipFractureInd": {"type": "boolean", "description": "Chip fracture indicator"}, "coverageCd": {"type": "string", "description": "Policy Coverage Code"}, "dateRange": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Period"}, "dependentRegistryId": {"type": "string", "description": "Individual dependent identifier which is associated with the case"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string", "description": "The attribute represents the eligibility rules decision override reason."}, "grossAmount": {"$ref": "#/definitions/Money"}, "icdOrCptId": {"type": "string", "description": "Id of icd/cpt entity which used to bind settlement with icd/cpt"}, "incidentDate": {"type": "string", "format": "date-time", "description": "To record the date treatment is received or the offical diagosis date, override this date on UI"}, "isCancelled": {"type": "boolean", "description": "Whether the settlement is cancelled"}, "isGrossAmountOverrided": {"type": "boolean", "description": "This attribute defines if GrossAmount is overrided"}, "multipleDislocationsInd": {"type": "boolean", "description": "Multiple dislocations indicator"}, "multipleFracturesInd": {"type": "boolean", "description": "Multiple fractures indicator"}, "numberOfUnits": {"type": "integer", "format": "int64"}, "numberOfUnitsOverrideInd": {"type": "boolean", "description": "numberOfUnits Override Indicator"}, "paidToDate": {"type": "string", "format": "date", "description": "Paid to date"}, "partialDislocationInd": {"type": "boolean", "description": "Partial dislocation indicator"}, "planCd": {"type": "string", "description": "Policy Plan Code"}, "planId": {"type": "string", "description": "Policy Plan Id"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of Loss received date, override this date on UI"}, "reductionAmountType": {"type": "string", "description": "Reduction amount type: Open/Closed"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementDetailEntity", "description": "Entity that encompasses state-mandated product settlement details."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "AccidentalDismembermentSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_AccessTrackInfo"}, "claimCoverageName": {"type": "string", "description": "Claim Coverage Name Display on select coverage popup"}, "claimCoveragePrefix": {"type": "string", "description": "Used to distinguish the type of coverage as prefix"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "coverageBasedConfiguration": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementCoverageConfigEntity"}, "policy": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "settlementAbsenceInfo": {"type": "object", "description": "The object that includes information taken from Absence case."}, "settlementApprovalResult": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementApprovalResultEntity"}, "settlementDetail": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementLossInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementEntity", "description": "The object that encompasses attributes set for Accidental Dismemberment Settlement."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntity"}}, "title": "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccess"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementEntitySuccessBody"}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementLossInfoEntity"}, "age": {"type": "integer", "format": "int64", "description": "Age of this subject"}, "caseNumber": {"type": "string"}, "claimEligibilityEvaluationCd": {"type": "string", "description": "Claim eligibility evaluation code."}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "claimNumber": {"type": "string"}, "claimType": {"type": "string", "description": "Type of claim."}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "firstTreatmentDate": {"type": "string", "format": "date", "description": "First day of treatment"}, "isChildOrganizedSport": {"type": "boolean", "description": "Is Child Organized Sport"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossItems": {"type": "array", "items": {"type": "string", "description": "Loss Items"}}, "lossNumber": {"type": "string"}, "lossType": {"type": "string", "description": "Type of loss. Inherited from claim level."}, "overrideFaceValueAmount": {"$ref": "#/definitions/Money"}, "overrideFaceValueAmountTL": {"$ref": "#/definitions/Money"}, "relationshipToInsuredCd": {"type": "string", "description": "Relationship to insured"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementLossInfoEntity", "description": "Business entity that houses the information from loss to use in settlement."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementPolicyInfoEntity"}, "accidentalDismembermentCertInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentCertInfoEntity"}, "accidentalDismembermentMasterInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentMasterInfoEntity"}, "ageReductionDetails": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAgeReductionDetailsEntity"}}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementPolicyInfoEntity", "description": "Entity for Accidental Dismemberment Settlement Policy Information."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementResultEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/definitions/Money"}, "autoAdjudicatedDuration": {"type": "number", "description": "Indicates the duration of Auto Adjudication Number of Units (Guideline value in Days, Times, Visits, Round Trips and whatever)"}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "benefitCd": {"type": "string", "description": "Benefit Code"}, "claimCoverageName": {"type": "string", "description": "Claim coverage name"}, "dateRange": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Period"}, "dependentRegistryId": {"type": "string", "description": "Individual dependent identifier which is associated with the case"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "formulaCalculationDetails": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeFormulaCalculationDetails"}}, "grossAmountMode": {"type": "string", "description": "Mode of gross amount used to define if the gross amount calculated is based on the defined time frequency. This will determines the payment frequency for the recurring payment."}, "grossBenefitAmount": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if this Claim is the subject to Auto Adjudication."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_MessageType"}}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "paymentDetailInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_PaymentDetailInfoEntity"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of Loss received date"}, "reserve": {"type": "number"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementResultEntity", "description": "Business entity defines Accidental Dismemberment settlement result."}, "AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentSettlementRulesInput"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeAccumulatorRemaining"}}, "claimCoverageName": {"type": "string", "description": "Claim Coverage Name"}, "claimCoveragePrefix": {"type": "string"}, "coverageConfiguration": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementCoverageConfigEntity"}, "details": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementLossInfoEntity"}, "policy": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentRelatedSettlmentEntity"}}, "settlement": {"$ref": "#/definitions/EntityLink"}, "wrapperInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_BaseLifeSettlementWrapperInfo"}}, "title": "AccidentalDismembermentSettlement CapAccidentalDismembermentSettlementRulesInput"}, "AccidentalDismembermentSettlement_CapAgeReductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAgeReductionDetailsEntity"}, "ageCd": {"type": "integer", "format": "int64", "description": "Age"}, "reducedToCd": {"type": "number", "description": "Reduced To Percentage"}}, "title": "AccidentalDismembermentSettlement CapAgeReductionDetailsEntity"}, "AccidentalDismembermentSettlement_CapBurnInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBurnInfoEntity"}, "burnAmount": {"$ref": "#/definitions/Money"}, "burnDegreeTypeCd": {"type": "string"}}, "title": "AccidentalDismembermentSettlement CapBurnInfoEntity", "description": "Contains the details info of Burn benefit."}, "AccidentalDismembermentSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "AccidentalDismembermentSettlement CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "AccidentalDismembermentSettlement_CapMainCoverageEligibilityInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMainCoverageEligibilityInfoEntity"}, "waitingPeriodDefCd": {"type": "string"}, "waitingPeriodModeCd": {"type": "string"}}, "title": "AccidentalDismembermentSettlement CapMainCoverageEligibilityInfoEntity"}, "AccidentalDismembermentSettlement_CapReductionAmountInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapReductionAmountInfoEntity"}, "reductionAmount": {"$ref": "#/definitions/Money"}, "reductionAmountTypeCd": {"type": "string"}}, "title": "AccidentalDismembermentSettlement CapReductionAmountInfoEntity", "description": "Contains the details info of Reduction amount."}, "AccidentalDismembermentSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "AccidentalDismembermentSettlement MessageType", "description": "Holds information of message type."}, "AccidentalDismembermentSettlement_PaymentDetailInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PaymentDetailInfoEntity"}, "allocationPeriod": {"$ref": "#/definitions/AccidentalDismembermentSettlement_Period"}, "initiatePayment": {"type": "boolean", "description": "Defines if settlement is payable."}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "AccidentalDismembermentSettlement PaymentDetailInfoEntity", "description": "Business entity defines payment detail info."}, "AccidentalDismembermentSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "AccidentalDismembermentSettlement Period"}, "AccidentalDismembermentSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "AccidentalDismembermentSettlement Term"}, "AdjudicateSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateSettlementToAccumulatorTxOutputs"}, "AdjudicateSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateSettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateSettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveSettlementToAccumulatorTxOutputs"}, "ApproveSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveSettlementToAccumulatorTxOutputs"}}, "title": "ApproveSettlementToAccumulatorTxOutputsSuccess"}, "ApproveSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveSettlementToAccumulatorTxOutputsSuccessBody"}, "CapAccidentalDismembermentSettlementAdjudicationInputOutputs": {"properties": {"output": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementRulesInput"}}, "title": "CapAccidentalDismembermentSettlementAdjudicationInputOutputs"}, "CapAccidentalDismembermentSettlementAdjudicationInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapAccidentalDismembermentSettlementAdjudicationInputOutputs"}}, "title": "CapAccidentalDismembermentSettlementAdjudicationInputOutputsSuccess"}, "CapAccidentalDismembermentSettlementAdjudicationInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapAccidentalDismembermentSettlementAdjudicationInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccidentalDismembermentSettlementAdjudicationInputOutputsSuccessBody"}, "CapLifeInitManualSettlementInput": {"properties": {"claimCoverageName": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "policy": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "settlementDetail": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementLossInfoEntity"}, "settlementResult": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementResultEntity"}, "settlementType": {"type": "string"}, "state": {"type": "string"}}, "title": "CapLifeInitManualSettlementInput"}, "CapLifeInitManualSettlementInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeInitManualSettlementInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLifeInitManualSettlementInputBody"}, "CapLifeSettlementInitInput": {"required": ["entity"], "properties": {"claimCoverageName": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementDetailEntity"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapLifeSettlementInitInput"}, "CapLifeSettlementInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeSettlementInitInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLifeSettlementInitInputBody"}, "CapSettlementReadjudicateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/AccidentalDismembermentSettlement_CapAccidentalDismembermentSettlementDetailEntity"}}, "title": "CapSettlementReadjudicateInput"}, "CapSettlementReadjudicateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementReadjudicateInputBody"}, "DisapproveSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveSettlementToAccumulatorTxOutputs"}, "DisapproveSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveSettlementToAccumulatorTxOutputs"}}, "title": "DisapproveSettlementToAccumulatorTxOutputsSuccess"}, "DisapproveSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveSettlementToAccumulatorTxOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitSettlementToAccumulatorTxOutputs"}, "InitSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitSettlementToAccumulatorTxOutputs"}}, "title": "InitSettlementToAccumulatorTxOutputsSuccess"}, "InitSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitSettlementToAccumulatorTxOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ReadjudicateSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateSettlementToAccumulatorTxOutputs"}, "ReadjudicateSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateSettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateSettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SettlementToChildSettlementRefreshInputOutputs": {"properties": {"output": {"type": "object"}}, "title": "SettlementToChildSettlementRefreshInputOutputs"}, "SettlementToChildSettlementRefreshInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputs"}}, "title": "SettlementToChildSettlementRefreshInputOutputsSuccess"}, "SettlementToChildSettlementRefreshInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SettlementToChildSettlementRefreshInputOutputsSuccessBody"}}}