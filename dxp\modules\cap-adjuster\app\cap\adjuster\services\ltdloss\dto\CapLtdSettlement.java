/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.ltdloss.dto;

import cap.adjuster.services.common.dto.GenesisRootApiModel;

public class CapLtdSettlement extends GenesisRootApiModel {
    public String settlementType;
    public Object settlementResult;
    public String settlementNumber;
    public Object settlementDetail;
    public String policyId;
    public String state;
    public Object policy;
    public Object settlementAbsenceInfo;
    public Object settlementLossInfo;
}
