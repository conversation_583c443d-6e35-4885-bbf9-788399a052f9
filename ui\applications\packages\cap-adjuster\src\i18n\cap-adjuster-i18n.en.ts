/*
 * Copyright © 2019 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {Localization} from '@eisgroup/i18n'

export const enUS: Localization.ResourceBundle = {
    locale: {country: 'US', language: 'en'},
    ns: 'cap-adjuster',
    resources: {}
}
