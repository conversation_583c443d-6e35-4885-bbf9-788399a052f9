/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import * as React from 'react'
import {ControlsRenderingComponentProps, renderControl} from '@eisgroup/react-components'
import {t} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {Field} from '@eisgroup/form'
import {required} from '../../utils/ValidationUtils'
import {INPUT_FORM_ROW_2X1, INPUT_FORM_ROW_2_1_1} from '../../common/package-class-names'

const {Input, LookupSelect} = Field

/**
 * Defines set of controls that are rendered by this component by default.
 */
export interface AddressInfoControls {
    /**
     * Postal code control.
     */
    readonly postalCode: React.ReactNode
    /**
     * Country control.
     */
    readonly countryCd: React.ReactNode
    /**
     * City control.
     */
    readonly city: React.ReactNode
    /**
     * State/Province control.
     */
    readonly stateProvinceCd: React.ReactNode
    /**
     * First address line control.
     */
    readonly addressLine1: React.ReactNode
    /**
     * Second address line control.
     */
    readonly addressLine2: React.ReactNode
    /**
     * Third address line control.
     */
    readonly addressLine3: React.ReactNode
}

export interface AddressInfoProps extends ControlsRenderingComponentProps<AddressInfoControls, AddressInfoProps> {
    readonly disabled?: boolean
}

/**
 * Renders a container of controls for address parameters
 * @Deprecated since 23.14 use AddressInfo ui-builder block
 */
export class AddressInfo extends React.Component<AddressInfoProps> {
    private getControls = (): AddressInfoControls => {
        const {disabled} = this.props
        const controlsProps = opt(this.props.controls).orElse({})
        return {
            postalCode: renderControl(
                controlsProps.postalCode,
                this.props,
                <Input
                    label={t('cap-core:form_address_info_postal_code')}
                    name='postalCode'
                    validate={required('cap-core:customer_address_postalCode_is_required')}
                    required
                    maxLength={10}
                    disabled={disabled}
                />
            ),
            countryCd: renderControl(
                controlsProps.countryCd,
                this.props,
                <LookupSelect
                    label={t('cap-core:form_address_info_country')}
                    name='countryCd'
                    lookupName='Country'
                    validate={required('cap-core:customer_address_countryCd_is_required')}
                    required
                    placeholder={t('cap-core:select_placeholder')}
                    disabled={disabled}
                />
            ),
            city: renderControl(
                controlsProps.city,
                this.props,
                <Input
                    label={t('cap-core:form_address_info_city')}
                    name='city'
                    validate={required('cap-core:customer_address_city_is_required')}
                    required
                    maxLength={30}
                    disabled={disabled}
                />
            ),
            stateProvinceCd: renderControl(
                controlsProps.stateProvinceCd,
                this.props,
                <LookupSelect
                    label={t('cap-core:form_address_info_province')}
                    name='stateProvinceCd'
                    lookupName='StateProv'
                    placeholder={t('cap-core:select_placeholder')}
                    disabled={disabled}
                />
            ),
            addressLine1: renderControl(
                controlsProps.addressLine1,
                this.props,
                <Input
                    label={t('cap-core:form_address_info_address_line_1')}
                    name='addressLine1'
                    validate={required('cap-core:customer_addressLine1_is_required')}
                    required
                    maxLength={40}
                    disabled={disabled}
                />
            ),
            addressLine2: renderControl(
                controlsProps.addressLine2,
                this.props,
                <Input
                    label={t('cap-core:form_address_info_address_line_2')}
                    name='addressLine2'
                    maxLength={40}
                    disabled={disabled}
                />
            ),
            addressLine3: renderControl(
                controlsProps.addressLine3,
                this.props,
                <Input
                    label={t('cap-core:form_address_info_address_line_3')}
                    name='addressLine3'
                    maxLength={40}
                    disabled={disabled}
                />
            )
        }
    }

    render(): React.ReactNode {
        const controls = this.getControls()
        return this.props.controls && this.props.controls.render ? (
            this.props.controls.render(controls)
        ) : (
            <section>
                {controls.addressLine1}
                <div className={INPUT_FORM_ROW_2X1}>
                    {controls.addressLine2}
                    {controls.addressLine3}
                    {controls.countryCd}
                </div>
                <div className={INPUT_FORM_ROW_2_1_1}>
                    {controls.city}
                    {controls.stateProvinceCd}
                    {controls.postalCode}
                </div>
            </section>
        )
    }
}
