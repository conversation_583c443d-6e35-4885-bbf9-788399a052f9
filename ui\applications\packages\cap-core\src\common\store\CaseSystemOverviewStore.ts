/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {CaseSystemService, ClaimParty, IndividualCustomer, OrganizationCustomer} from '@eisgroup/cap-services'
import {IObservableArray} from 'mobx'
import {FormDrawerStore} from '../../components/form-drawer'
import {ICaseSystem} from '../Types'
import {BaseRootStore} from './BaseRootStore'
import {CaseSystemLossFormStore} from './CaseSystemLossFormStore'
import {CaseSystemPaymentStore} from './CaseSystemPaymentStore'
import {ClaimPartyStore} from './ClaimPartyStore'
import {ClaimStore} from './ClaimStore'

export interface CaseSystemOverviewStore<CS extends ICaseSystem, CSService extends CaseSystemService>
    extends BaseRootStore {
    paymentsStore?: CaseSystemPaymentStore<CS, CSService>
    formDrawerStore: FormDrawerStore
    lossFormStores: Map<string, CaseSystemLossFormStore<any, any>>
    lossFormDrawerKeys: Map<string, string>
    subjectOfClaim?: IndividualCustomer | OrganizationCustomer
    claimPartyStore: ClaimPartyStore
    claimStore: ClaimStore
    allClaimsBeneficiaries: IObservableArray<ClaimParty>
}
