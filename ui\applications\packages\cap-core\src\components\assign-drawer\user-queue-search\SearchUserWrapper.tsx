/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {Org<PERSON><PERSON>} from '@eisgroup/cap-services'
import {LocalizationUtils} from '@eisgroup/i18n'
import {ColumnProps, Radio, Spin, Table} from '@eisgroup/ui-kit'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import * as React from 'react'
import {INPUT_FORM_ROW_2X1, UserEntity} from '../../..'
import {NoDataVal} from '../../../common/constants'
import {
    ASSIGN_USER_INFO_POPOVER,
    ASSIGN_USER_SEARCH_INPUT_WRAPPER,
    ASSIGN_USER_SEARCH_RESULT_TABLE,
    ASSIGN_USER_SEARCH_RESULT_TABLE_COL_DEPARTMENT,
    ASSIGN_USER_SEARCH_RESULT_TABLE_COL_NAME,
    ASSIGN_USER_SEARCH_RESULT_TABLE_COL_ROLE,
    ASSIGN_USER_SEARCH_RESULT_TABLE_TITLE,
    ASSIGN_USER_SEARCH_WRAPPER
} from '../../../common/package-class-names'
import {Manager} from '../../assign-manager/Manager'
import {UserSearch} from './UserSearch'
import t = LocalizationUtils.translate

export interface SearchUserWrapperProps {
    isLoading?: boolean
    userSearchAllResults?: OrgPerson[]
    placeholder?: string
    store: any
}

export interface SearchUserWrapperState {
    selectedResult: UserEntity
}

@observer
export class SearchUserWrapper<
    P extends SearchUserWrapperProps,
    S extends SearchUserWrapperState
> extends React.Component<P, SearchUserWrapperState> {
    constructor(props: P) {
        super(props)
        this.state = {
            selectedResult: {} as UserEntity
        }
    }

    private createColumns: () => ColumnProps<any>[] = () => {
        return [
            {
                key: 'radio',
                title: '',
                render: (text: string, record: any) => {
                    return <Radio value={record.userInfo._key.rootId} />
                }
            },
            {
                key: 'customerName',
                title: t('cap-core:assign_form_search_result_table_user_name'),
                render: (text: string, record: any) => {
                    return (
                        <div className={ASSIGN_USER_SEARCH_RESULT_TABLE_COL_NAME}>
                            <Manager
                                userInfo={record.userInfo}
                                organizationsInfo={record.organizationInfo}
                                isButtonHidden
                                trigger='hover'
                                overlayClassName={ASSIGN_USER_INFO_POPOVER}
                            />
                        </div>
                    )
                }
            },
            {
                title: t('cap-core:assign_form_search_result_table_department'),
                key: 'department',
                render: (text: string, record: any, index: number) => {
                    return (
                        <div className={ASSIGN_USER_SEARCH_RESULT_TABLE_COL_DEPARTMENT}>
                            {record.organizationInfo?.name ? record.organizationInfo.name : NoDataVal}
                        </div>
                    )
                }
            },
            {
                title: t('cap-core:assign_form_search_result_table_role'),
                key: 'role',
                render: (text: string, record: any, index: number) => {
                    return (
                        <div className={ASSIGN_USER_SEARCH_RESULT_TABLE_COL_ROLE}>
                            {record.roleInfo?.name ? record.roleInfo.name : NoDataVal}
                        </div>
                    )
                }
            }
        ]
    }

    private selectTableResult = e => {
        const userSearchTableResults = toJS(this.props?.store?.userSearchTableResults) || []
        const userSelected = userSearchTableResults.find(p => e.target.value === p.userInfo._key.rootId)
        if (userSelected) {
            this.props.store.selectUser(userSelected.userInfo)
            this.setState({
                selectedResult: userSelected
            })
        }
    }

    render(): React.ReactNode {
        let newLocal = [] as any[]
        newLocal = toJS(this.props.store?.userSearchTableResults || ([] as any))
        return (
            <div className={ASSIGN_USER_SEARCH_WRAPPER}>
                <div className={ASSIGN_USER_SEARCH_INPUT_WRAPPER}>
                    <div className={INPUT_FORM_ROW_2X1}>
                        <UserSearch store={this.props.store} />
                    </div>
                </div>
                {newLocal && newLocal.length ? (
                    <div className={ASSIGN_USER_SEARCH_RESULT_TABLE}>
                        <div className={ASSIGN_USER_SEARCH_RESULT_TABLE_TITLE}>
                            <label>{t('cap-core:assign_form_search_result_table_title')}</label>
                        </div>

                        <Spin spinning={this.props.store?.isLoading}>
                            <Radio.Group
                                className='customer-search-result-table-radio-group'
                                onChange={this.selectTableResult}
                                value={
                                    this.state.selectedResult?.userInfo?._key?.rootId
                                        ? this.state.selectedResult?.userInfo?._key?.rootId
                                        : newLocal[0].userInfo?._key?.rootId
                                }
                            >
                                <Table
                                    rowKey={record => record.userInfo._key.id || record.userInfo._key.rootId}
                                    pagination={false}
                                    columns={this.createColumns()}
                                    dataSource={newLocal}
                                    loading={this.props.store.isLoading}
                                />
                            </Radio.Group>
                        </Spin>
                    </div>
                ) : null}
            </div>
        )
    }
}
