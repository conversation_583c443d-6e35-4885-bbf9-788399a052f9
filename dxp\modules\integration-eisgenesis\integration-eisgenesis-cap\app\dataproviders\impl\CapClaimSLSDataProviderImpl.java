/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package dataproviders.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletionStage;

import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.ClaimSLSRequestDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;

import core.dataproviders.dto.InternalDTO;
import core.services.pagination.PageData;
import dataproviders.CapClaimSLSDataProvider;
import dataproviders.common.GenesisDataProvider;
import dataproviders.common.dto.GenesisRootDTO;
import dataproviders.common.dto.GenesisSearchResponseDTO;

public class CapClaimSLSDataProviderImpl<T extends GenesisRootDTO> extends GenesisDataProvider implements
    CapClaimSLSDataProvider {

    private static final String COMMON_CLAIM_SLS_URL = "/api/common/search/v1/claim_sls";

    @Override
    public CompletionStage<GenesisSearchResponseDTO> singleLineSearchClaim(String text, String fields, PageData pageData, Class responseType) {
        ClaimSLSRequestDTO slsRequestDTO = new ClaimSLSRequestDTO();
        slsRequestDTO.query = text;
        slsRequestDTO.fields = Lists.newArrayList(fields);
        slsRequestDTO.offset= Long.valueOf(pageData.getOffset());
        slsRequestDTO.limit = Long.valueOf(pageData.getLimit());
        return super.postSearchRequest(COMMON_CLAIM_SLS_URL, slsRequestDTO)
            .thenApply(successNode -> parseSearchSuccessResponse(successNode, responseType));
    }

    private <T extends GenesisRootDTO> GenesisSearchResponseDTO<T> parseSearchSuccessResponse(JsonNode successNode,
                                                                                              Class<T> resultType) {
        GenesisSearchResponseDTO<T> searchResponse = new GenesisSearchResponseDTO<>();
        searchResponse.count = successNode.get("count").intValue();
        searchResponse.result = parseSearchResult(successNode, resultType);
        return searchResponse;
    }

    private <T extends InternalDTO> List<T> parseSearchResult(JsonNode successNode, Class<T> resultType) {
        return parseArrayNode(successNode.get("result"), resultType);
    }

    private <T extends InternalDTO> List<T> parseArrayNode(JsonNode jsonNode, Class<T> resultType) {
        List<T> result = new ArrayList<>();
        if (jsonNode != null) {
            jsonNode.forEach(item -> result.add(deserializeFromNode(item, resultType)));
        }

        return result;
    }

    @Override
    protected String getBaseUrl() {
        return configuration.getString("genesis.cap.generic.baseurl");
    }
}
