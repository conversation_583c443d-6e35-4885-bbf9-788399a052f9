import {PaginationConfig} from '@eisgroup/ui-kit'
import {action, observable} from 'mobx'
import {searchCaseTableService, SearchCaseTableServiceType} from '../services/SearchCaseService'
import {SorterResult} from '../types/SorterResultType'
import {CaseSearchResultType} from '../types/CaseSearchResultType'
import {defaultPaginationConfig} from '../constants'

export type CaseTableStoreInitType = {
    searchingCaseTableService?: SearchCaseTableServiceType
}

export class CaseTableStore {
    @observable showCaseTable = false

    @observable caseTableLoading = false

    @observable caseTablePagination: PaginationConfig = defaultPaginationConfig

    @observable caseTableDataSource: CaseSearchResultType[] = []

    @observable caseTableResultCount = 0

    @observable selectedRow: CaseSearchResultType | undefined

    private searchingCaseTableService: SearchCaseTableServiceType = searchCaseTableService

    @action
    changeShowCaseTable = (visible: boolean) => {
        this.showCaseTable = visible
    }

    @action
    changeCaseTableDataSource = (ds: CaseSearchResultType[]) => {
        this.caseTableDataSource = ds
    }

    @action
    changeCaseTableResultCount = (count: number) => {
        this.caseTableResultCount = count
    }

    @action
    changeCaseTablePagination = (pagination?: PaginationConfig) => {
        this.caseTablePagination = pagination ?? defaultPaginationConfig
    }

    @action
    changeCaseTableLoading = (loading: boolean) => {
        this.caseTableLoading = loading
    }

    @action
    changeSelectedRow = (selectedRow?: CaseSearchResultType) => {
        this.selectedRow = selectedRow
    }

    @action
    searchTableCases =
        (originFilterParams: object) =>
        async (
            pagination: PaginationConfig,
            filters?: Record<string, string[]>,
            sorter?: SorterResult<CaseSearchResultType>
        ) => {
            this.changeCaseTableLoading(true)

            const {items, count} = await this.searchingCaseTableService({
                filters: {...originFilterParams, ...filters},
                sorter,
                pageNum: pagination?.current,
                pageSize: pagination.pageSize ?? pagination?.defaultPageSize
            })

            this.changeCaseTableDataSource(items)
            this.changeCaseTableResultCount(count)
            this.changeCaseTablePagination({...pagination, total: count})
            this.changeCaseTableLoading(false)
        }

    @action
    init = (params?: CaseTableStoreInitType) => {
        const {searchingCaseTableService} = params ?? {}
        if (searchingCaseTableService) {
            this.searchingCaseTableService = searchingCaseTableService
        }
    }

    @action
    resetAll = () => {
        this.showCaseTable = false
        this.caseTableDataSource = []
        this.caseTableResultCount = 0
        this.caseTablePagination = defaultPaginationConfig
        this.caseTableLoading = false
        this.selectedRow = undefined
    }
}
