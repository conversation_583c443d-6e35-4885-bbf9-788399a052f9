GET        /event-cases/:rootId/:revisionNo/life/claims                    cap.adjuster.controllers.eventcase.CapAdjusterEventCasesApiController.getLifeClaims(rootId, revisionNo: Integer)
GET        /event-cases/:rootId/:revisionNo/life/settlements               cap.adjuster.controllers.eventcase.CapAdjusterEventCasesApiController.getLifeSettlements(rootId, revisionNo: Integer)
GET        /event-cases/:rootId/:revisionNo/life/losses                    cap.adjuster.controllers.eventcase.CapAdjusterEventCasesApiController.getLifeLosses(rootId, revisionNo: Integer)
GET        /event-cases/:rootId/:revisionNo/absence/losses                 cap.adjuster.controllers.eventcase.CapAdjusterEventCasesApiController.getAbsenceLosses(rootId, revisionNo: Integer)
GET        /event-cases/:rootId/:revisionNo/settlements               cap.adjuster.controllers.eventcase.CapAdjusterEventCasesApiController.getSettlements(rootId, revisionNo: Integer)
