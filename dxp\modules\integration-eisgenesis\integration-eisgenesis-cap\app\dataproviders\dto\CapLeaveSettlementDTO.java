/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.dto;

import com.eisgroup.dxp.dataproviders.genesiscapleavesettlement.dto.CapLeaveSettlement_CapLeaveSettlementPolicyInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapleavesettlement.dto.CapLeaveSettlement_CapLeaveSettlementAbsenceInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapleavesettlement.dto.CapLeaveSettlement_CapLeaveSettlementDetailEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapleavesettlement.dto.CapLeaveSettlement_CapLeaveSettlementLossInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapleavesettlement.dto.CapLeaveSettlement_CapLeaveSettlementResultEntityDTO;
import dataproviders.common.dto.GenesisRootDTO;

public class CapLeaveSettlementDTO extends GenesisRootDTO {

    public String settlementType;
    public CapLeaveSettlement_CapLeaveSettlementResultEntityDTO settlementResult;
    public String settlementNumber;
    public CapLeaveSettlement_CapLeaveSettlementDetailEntityDTO settlementDetail;
    public String policyId;
    public String state;
    public CapLeaveSettlement_CapLeaveSettlementAbsenceInfoEntityDTO settlementAbsenceInfo;
    public CapLeaveSettlement_CapLeaveSettlementPolicyInfoEntityDTO policy;
    public CapLeaveSettlement_CapLeaveSettlementLossInfoEntityDTO settlementLossInfo;
}
