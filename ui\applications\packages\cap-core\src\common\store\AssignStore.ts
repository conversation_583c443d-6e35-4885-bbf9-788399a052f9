/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {
    backOfficeWorkflowService,
    backofficeWorkService,
    Organization,
    OrgPerson,
    WorkQueueDetails
} from '@eisgroup/cap-services'
import {action, observable, runInAction} from 'mobx'
import {isEmpty} from 'lodash'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'

export interface AssignStore extends BaseRootStore {
    queueInfo?: WorkQueueDetails
    userInfo?: OrgPerson
    organizationsInfo?: Organization
    handleReassignCase: (caseId: string, userId: string, queueCd: string, callback?: Function) => void
    getCaseManagerUserInformation: (arg0: string) => void
    getCaseManagerQueueInformation: (arg0: string[]) => void
}

export class AssignStoreImpl extends BaseRootStoreImpl implements AssignStore {
    @observable queueInfo?: WorkQueueDetails

    @observable userInfo?: OrgPerson

    @observable organizationsInfo?: Organization

    @action
    getOrganizationalPersonAddress = (rootId: string) => {
        this.callService<Organization>(backofficeWorkService.searchOrganizationalByUser(rootId), response => {
            runInAction(() => {
                this.organizationsInfo = response
            })
        })
    }

    @action
    getCaseManagerUserInformation = (userId: string) => {
        this.callService<OrgPerson[]>(backofficeWorkService.searchOrganizationalUserByUserID(userId), response => {
            runInAction(() => {
                this.queueInfo = undefined
                this.userInfo = response[0]
                if (
                    this.userInfo?.organizationAssignments &&
                    this.userInfo?.organizationAssignments[0]?.organization?._uri
                ) {
                    const rootId = this.userInfo?.organizationAssignments[0]?.organization?._uri.split('//')
                    this.getOrganizationalPersonAddress(rootId[rootId.length - 1])
                } else if (isEmpty(this.userInfo)) {
                    // TODO When userId is not empty but the result of the search API is undefined, userId is temporarily assembled into UserInfo and returned.
                    this.userInfo = {
                        securityIdentity: userId
                    } as OrgPerson
                }
            })
        })
    }

    @action
    getCaseManagerQueueInformation = (queueCd: string[]) => {
        this.callService(backOfficeWorkflowService.loadWorkQueues(queueCd), response => {
            runInAction(() => {
                this.queueInfo = response[0]
                this.userInfo = undefined
            })
        })
    }

    @action
    handleReassignCase = (caseId: string, userId: string, queueCd: string, callback?: Function) => {
        this.call<any>(() => backOfficeWorkflowService.setCaseAssignment(caseId, userId, queueCd)).subscribe(r => {
            if (r.isRight && callback) {
                callback()
            }
        })
    }
}
