/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {Organization, OrgPerson, WorkQueueDetails} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {Button, Popover} from '@eisgroup/ui-kit'
import {isEmpty} from 'lodash'
import React from 'react'
import {
    CLAIM_BANNER_CONTACT_INFO_INSURED_TITLE,
    CLAIM_BANNER_HYPERLINK,
    CLAIM_HEADER_FLEX,
    CLAIM_MANAGER_INFO_POPOVER
} from '../../common/package-class-names'
import {TooltipTrigger} from '../../common/Types'
import {ManagerInfoDetails} from './ManagerInfoDetails'

export interface BannerDetailsMainInsuredProps {
    queueInfo?: WorkQueueDetails
    userInfo?: OrgPerson
    organizationsInfo?: Organization
    fullName?: string
    reassign?: () => void
    overlayClassName?: string
    isButtonHidden?: boolean
    trigger?: TooltipTrigger
    reassignButtonLabel?: string
    hasAssignPrivilege?: boolean
    isStateClosed?: boolean
}

export const Manager = (props: BannerDetailsMainInsuredProps) => {
    const {queueInfo, userInfo, reassign, organizationsInfo, isStateClosed} = props
    let fullName: string | undefined
    if (!isEmpty(userInfo)) {
        fullName =
            !userInfo?.personInfo?.firstName && !userInfo?.personInfo?.lastName
                ? userInfo?.securityIdentity
                : [userInfo?.personInfo.firstName, userInfo?.personInfo.lastName].join(' ')
    } else if (queueInfo) {
        fullName = queueInfo.name ? queueInfo.name : queueInfo.queueCd
    }
    const reassignButtonLabel = opt(props.reassignButtonLabel).orElse(t('cap-core:assign_button_text'))
    const renderTitle = (insuredFullName: string): React.ReactNode => {
        return (
            <div className={CLAIM_HEADER_FLEX}>
                <div className={CLAIM_BANNER_CONTACT_INFO_INSURED_TITLE}>{insuredFullName}</div>
                {!props.isButtonHidden && props.hasAssignPrivilege && (
                    <Button type='secondary' size='small' onClick={reassign} hidden={isStateClosed}>
                        {reassignButtonLabel}
                    </Button>
                )}
            </div>
        )
    }

    const unassigned = t('cap-core:assign_unassigned')
    return (
        <Popover
            title={renderTitle(fullName || unassigned)}
            overlayClassName={CLAIM_MANAGER_INFO_POPOVER}
            content={
                <ManagerInfoDetails queueInfo={queueInfo} userInfo={userInfo} organizationsInfo={organizationsInfo} />
            }
            size='medium'
            destroyTooltipOnHide
            placement='bottomLeft'
            trigger={props.trigger ? props.trigger : 'click'}
        >
            <a className={CLAIM_BANNER_HYPERLINK}>{fullName || unassigned}</a>
        </Popover>
    )
}
