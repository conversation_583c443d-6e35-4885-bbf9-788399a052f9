/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.smploss.impl;

import static java.lang.String.format;

import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import javax.inject.Inject;

import cap.adjuster.services.smploss.converters.CapAdjusterSmpSettlementConverter;
import cap.adjuster.services.smploss.dto.CapSmpSettlement;
import org.apache.commons.lang3.StringUtils;

import cap.adjuster.services.smploss.CapAdjusterSmpLossService;
import cap.adjuster.services.financial.converters.CapAdjusterPaymentConverter;
import cap.adjuster.services.financial.dto.CapPayment;
import core.utils.AsyncUtils;
import dataproviders.dto.CapPaymentDTO;
import dataproviders.dto.CapSmpSettlementDTO;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;

public class CapAdjusterSmpLossServiceImpl implements CapAdjusterSmpLossService {

    private static final String SETTLEMENT_BASED_ON_LOSS = "based_on";
    private static final String PAYMENT_USES_RESERVES_SETTLEMENT = "uses/reserves";
    private static final String CAP_SETTLEMENT = "CapSettlement";
    private static final String CAP_PAYMENT = "CapPayment";
    private static final String CAP_PAYMENT_ENTITY = "CapPaymentEntity";
    private static final String SMP_LOSS_URI = "gentity://CapLoss/CapSmp//%s/%s";
    private static final String SETTLEMENT_URI = "gentity://CapSettlement/CapSmpSettlement//%s/%s";

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private CapAdjusterPaymentConverter<CapPaymentDTO, CapPayment> paymentConverter;
    private CapAdjusterSmpSettlementConverter<CapSmpSettlementDTO, CapSmpSettlement> settlementConverter;

    @Override
    public CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo)
                .thenCompose(capSmpSettlementDTOs -> AsyncUtils.sequence(
                        capSmpSettlementDTOs.stream()
                                .map(this::getPaymentsAssociatedWithSettlement)
                                .collect(Collectors.toList())
                ))
                .thenApply(capSmpLossDTOs -> capSmpLossDTOs.stream()
                        .flatMap(List::stream)
                        .filter(this::isPaymentTypeAppropriate)
                        .map(paymentConverter::convertToApiDTO)
                        .collect(Collectors.toList())
                );
    }

    @Override
    public CompletionStage<List<CapSmpSettlement>> getSettlements(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo)
                .thenApply(capSmpSettlementDTOs -> capSmpSettlementDTOs.stream()
                        .map(settlementConverter::convertToApiDTO)
                        .collect(Collectors.toList()));
    }

    private boolean isPaymentTypeAppropriate(CapPaymentDTO paymentDTO) {
        return StringUtils.equals(paymentDTO.gentityType, CAP_PAYMENT_ENTITY);
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapSmpSettlementDTO>> getSettlementsAssociatedWithLoss(String rootId, Integer revisionNo) {
        String lossUri = format(SMP_LOSS_URI, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(lossUri, SETTLEMENT_BASED_ON_LOSS, CAP_SETTLEMENT);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapSmpSettlementDTO.class);
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapPaymentDTO>> getPaymentsAssociatedWithSettlement(CapSmpSettlementDTO smpSettlementDTO) {
        String settlementUri = format(SETTLEMENT_URI, smpSettlementDTO.key.rootId, smpSettlementDTO.key.revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(CAP_PAYMENT, PAYMENT_USES_RESERVES_SETTLEMENT, settlementUri);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapPaymentDTO.class);
    }

    @Inject
    public void setSearchRelationshipsDataProvider(
            GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setPaymentConverter(CapAdjusterPaymentConverter paymentConverter) {
        this.paymentConverter = paymentConverter;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setSettlementConverter(CapAdjusterSmpSettlementConverter settlementConverter) {
        this.settlementConverter = settlementConverter;
    }
}
