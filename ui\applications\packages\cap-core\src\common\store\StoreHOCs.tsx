/**
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import React, {useEffect, useMemo} from 'react'
import {RouterState} from 'react-router'
import {Spin} from '@eisgroup/ui-kit'
import {observer} from 'mobx-react'

/**
 * Properties of View Loader HOC.
 * <S> - type of store
 */
export interface ViewLoaderConfig<S, P = {}> {
    /**
     * Function initiates side-effects(loading data)
     * @param store - target store
     * @param routerState - Router state to get route params
     */
    startLoading: (store: S, routerState: Partial<RouterState> & P) => void
    /**
     * Function determines the loading conditions for view component.
     * @param store - target store
     */
    isLoaded: (store: S) => boolean
    /**
     * View component
     */
    component: React.ComponentType<P>
}

/**
 * Properties of HOC for connecting component to store.
 * <P> - type of a wrapped component props
 * <S> - type of store
 */
export interface ConnectToStoreConfig<P, EP, S> {
    /**
     * Function converts store data to component props
     * <P> - type of a wrapped component props
     * @returns Component props
     */
    mapStoreToProps: (store: S, props: EP) => P
    /**
     * Wrapped component
     * <P> - type of a wrapped component props
     */
    component: React.ComponentType<P>
}

/**
 * Function creates react context for specific store
 * <P> - type of a wrapped component props
 * <S> - type of store
 * @returns Context provider, hook to get store
 */
export function createViewStoreContext<S>(): {StoreProvider: React.Provider<S | null>; useViewStore: () => S} {
    const StoreContext = React.createContext<S | null>(null)

    const useViewStore = () => {
        const store = React.useContext(StoreContext)

        if (!store) {
            throw new Error('useViewStore must be used within a StoreContext.Provider')
        }

        return store
    }

    return {
        StoreProvider: StoreContext.Provider,
        useViewStore
    }
}

/**
 * Function creates following HOCs for specific store:
 * HOC to create view loader and HOC for connecting component to store
 * @param createStore - function returns instance of store
 * @returns HOC to create view loader, HOC for connecting component to store
 */
export function storeBindingFactory<S>(createStore: () => S): {
    createViewLoader: <P = {}>(config: ViewLoaderConfig<S, P>) => React.FC<Partial<RouterState> & P>
    connectToStore: <P, EP = Partial<P>>(config: ConnectToStoreConfig<P, EP, S>) => React.FC<EP>
    useStore: () => S
} {
    const {StoreProvider, useViewStore} = createViewStoreContext<S>()

    function createViewLoader<P = {}>(config: ViewLoaderConfig<S, P>): React.FC<Partial<RouterState> & P> {
        const {startLoading, isLoaded, component: Component} = config

        const ViewLoader: React.FC<Partial<RouterState> & P> = observer(props => {
            const store = useViewStore()

            useEffect(() => {
                if (!isLoaded(store)) {
                    startLoading(store, props)
                }
            }, [])

            return isLoaded(store) ? <Component {...props} /> : <Spin style={{margin: '16px'}} />
        })

        return (props: Partial<RouterState> & P) => {
            const store = useMemo(() => createStore(), [])

            return (
                <StoreProvider value={store}>
                    <ViewLoader {...props} />
                </StoreProvider>
            )
        }
    }

    function connectToStore<P, EP = Partial<P>>(config: ConnectToStoreConfig<P, EP, S>): React.FC<EP> {
        return observer(componentProps => {
            const {mapStoreToProps, component: Component} = config
            const store = useViewStore()
            const props = mapStoreToProps(store, componentProps)

            return <Component {...props} />
        })
    }

    return {
        createViewLoader,
        connectToStore,
        useStore: useViewStore
    }
}
