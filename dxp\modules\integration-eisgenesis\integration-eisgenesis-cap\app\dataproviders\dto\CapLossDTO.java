/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import dataproviders.common.dto.GenesisLinkDTO;
import dataproviders.common.dto.GenesisRootDTO;
import io.swagger.annotations.ApiModelProperty;

import java.time.ZonedDateTime;
import java.util.List;

public class CapLossDTO extends GenesisRootDTO {

    public String lossType;
    public String coverageType;
    public String lossNumber;
    public GenesisLinkDTO absence;
    public GenesisLinkDTO eventCaseLink;
    public String state;
    public String claimType;
    public String memberRegistryTypeId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @ApiModelProperty(example = "2020-04-20", dataType = "date")
    public ZonedDateTime dateOfLoss;
    public String reasonCd;
    public String reasonDescription;
    public String lossSubStatusCd;
    public String policyId;
    public Object lossDetail;
    public Object eventCaseInfo;
    public Object policy;
    public Object eligibilityResult;
    public Object subjectOfClaim;
    public List<Object> damageLosses;
    public Boolean isGenerated;
    public List<Object> policies;
    public Object applicabilityResult;
    public String claimWrapperUpdateUIAction;
}
