{"swagger": "2.0", "x-dxp-spec": {"imports": {"event.case": {"schema": "integration.cap.event.case.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Event Case API", "version": "1", "title": "CAP Adjuster: Event Case API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/event-cases", "description": "CAP Adjuster: Event Case API"}], "paths": {"/event-cases/{rootId}/{revisionNo}": {"get": {"summary": "Get event case by rootId and revisionNo", "x-dxp-path": "/api/caploss/CapEventCase/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/timeline/{rootId}/{revisionNo}": {"get": {"summary": "Get event case timeline data by rootId and revisionNo", "x-dxp-path": "/api/caploss/CapEventCase/v1/timeline/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for event case", "x-dxp-path": "/api/caploss/CapEventCase/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/rules/bundle": {"post": {"summary": "Rules bundle for Event Case", "x-dxp-path": "/api/caploss/CapEventCase/v1/rules/bundle", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases": {"post": {"summary": "Create event case", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/createCase", "tags": ["/cap-adjuster/v1/event-cases"]}, "put": {"summary": "Update event case", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/updateCase", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/draft": {"post": {"summary": "Init event case", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/initCase", "tags": ["/cap-adjuster/v1/event-cases"]}, "put": {"summary": "Update event case draft", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/updateCaseDraft", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/sub-statuses": {"post": {"summary": "Set sub status", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/setCaseSubStatus", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/close": {"post": {"summary": "Close event case", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/closeCase", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/reopen": {"post": {"summary": "Reopen event case", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/reopenCase", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/submit": {"post": {"summary": "Submit event case", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/submitCase", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/retrieve-closure-open-items": {"post": {"x-dxp-path": "/api/caploss/CapEventCase/v1/transformation/eventCaseClosureToOpenItems", "summary": "Get all open items related to the event case", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/request-close-case": {"post": {"summary": "Manual Close event case and automtic close claim", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/requestCloseCase", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/duplication-check": {"post": {"summary": "Check event case duplication", "x-dxp-path": "/api/caploss/CapEventCase/v1/transformation/caseDuplicationValidate", "tags": ["/cap-adjuster/v1/event-cases"]}}, "/event-cases/duplication-override": {"post": {"summary": "Check event case duplication", "x-dxp-path": "/api/caploss/CapEventCase/v1/command/overrideDuplication", "tags": ["/cap-adjuster/v1/event-cases"]}}}}