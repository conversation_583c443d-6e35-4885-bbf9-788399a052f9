/*
 * Copyright © 2019 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {definition} from '../src/index'

describe('root index', () => {
    describe('module', () => {
        it('should export definition', () => {
            expect(definition).toBeDefined()
        })
    })
})
