/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.specialHandling.dto;

import cap.adjuster.services.common.dto.GenesisRootApiModel;
import com.eisgroup.dxp.dataproviders.genesiscaplifeintakesettlement.dto.EntityLinkDTO;

public class CapGenericSpecialHandling extends GenesisRootApiModel {

    public EntityLinkDTO claimLossIdentification;
    public Boolean complaintInd;
    public Boolean appealInd;
    public Boolean policyExclusionInd;
    public Boolean atpWithCheckInd;
    public Boolean atpInd;
    public Boolean siuInd;
    public Boolean attorneyInd;
    public Boolean litigationInd;
    public Boolean reinsuranceInd;
    public Boolean runInInd;
    public Boolean takeOverInd;
    public Boolean salvageInd;
    public Boolean subrogationInd;
}
