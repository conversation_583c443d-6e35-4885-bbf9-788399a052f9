import React, {createRef, FC, useMemo} from 'react'
import {Field} from '@eisgroup/form'
import classNames from 'classnames'
import {observer} from 'mobx-react'
import {isEmpty} from 'lodash'
import {toJS} from 'mobx'
import {useTranslate} from '@eisgroup/i18n'
import {AutocompletableSearch, AutocompletableSearchProps} from '../search/AutocompletableSearch'
import {CaseSearchRootStore} from './stores/CaseSearchRootStore'
import {CaseSearchFilters, CaseSearchFiltersProps} from './components/CaseSearchFilters'
import {getAdvanceSearchFilters, GetAdvanceSearchFilterType} from './CaseSearchConfigs'
import {
    CASE_SEARCH_AUTOCOMPLETE,
    CASE_SEARCH_AUTOCOMPLETE_WITH_FILTER,
    CASE_SEARCH_COMPONENT,
    CASE_SEARCH_COMPONENT_DROPDOWN,
    CASE_SEARCH_FILTER_POPOVER,
    CASE_SEARCH_FILTER_POPOVER_WRAPPER,
    CASE_SEARCH_RESULT
} from '../../common/package-class-names'
import {CaseSearchResultTable, CaseSearchResultTableProps, CasseSearchResultFilters} from './components'
import {DEFAULT_SHOW_VIEW_ALL_THRESHOLD} from './constants'
import {SearchCaseSuggestionServiceType, SearchCaseTableServiceType} from './services'
import {useRelatedToAutoCompleteErrorBlock} from './hooks'

export type CaseSearchProps = {
    showErrorRelatedToAutocomplete?: boolean

    value?: string

    onFocus?: () => void
    onBlur?: () => void
    /**
     * Callback when selected one case in suggestion list or search result table.
     */
    onChange?: (value?: string) => void

    className?: string

    caseSuggestionProps?: {
        /**
         * Start searching when user input length >= searchingThreshold
         */
        searchingThreshold?: number
        /**
         * Show View All in suggestions when suggestions length > showViewAllThreshold
         */
        showViewAllThreshold?: number

        searchingCaseSuggestionService?: SearchCaseSuggestionServiceType

        autoCompleteConfig?: Partial<AutocompletableSearchProps>
    }

    caseTableProps?: {
        searchingCaseTableService?: SearchCaseTableServiceType

        resultTableConfig?: Partial<CaseSearchResultTableProps>
    }

    caseAdvanceSearchProps?: {
        getAdvanceFilters?: GetAdvanceSearchFilterType
        caseFilterConfig?: Partial<CaseSearchFiltersProps>
    }
}
const ADVANCED_SEARCH_CONTROL_CLASS = `gen-advanced-search-control`

export const CaseSearch: FC<CaseSearchProps> = observer(props => {
    const {t} = useTranslate()
    const {
        className,
        showErrorRelatedToAutocomplete = false,
        onChange: onSelectCaseCallback,
        onFocus: onFocusCallback,
        onBlur: onBlurCallback,
        caseSuggestionProps,
        caseTableProps,
        caseAdvanceSearchProps
    } = props

    const {
        showViewAllThreshold = DEFAULT_SHOW_VIEW_ALL_THRESHOLD,
        autoCompleteConfig = {},
        ...suggestionInitProps
    } = caseSuggestionProps ?? {}

    const {resultTableConfig = {}, ...caseTableInitProps} = caseTableProps ?? {}

    const {caseFilterConfig = {}, ...advanceFilterInitProps} = caseAdvanceSearchProps ?? {}
    const store = useMemo(() => {
        const storeTmp = new CaseSearchRootStore()
        storeTmp.init({
            selectCaseCallback: onSelectCaseCallback,
            onFocusCallback,
            onBlurCallback,
            suggestionInitProps,
            caseTableInitProps
        })
        return storeTmp
    }, [])

    useRelatedToAutoCompleteErrorBlock(showErrorRelatedToAutocomplete)

    const {searchInputStore, searchFilterStore, searchResultTableStore} = store

    const ref = createRef<HTMLDivElement>()

    const filters = useMemo(
        () => advanceFilterInitProps?.getAdvanceFilters?.() ?? getAdvanceSearchFilters(),
        [advanceFilterInitProps?.getAdvanceFilters]
    )

    const resultFilters = Object.keys(searchFilterStore.advanceFilters ?? {}).map(filterKey => {
        const filterValue = searchFilterStore.advanceFilters?.[filterKey]
        const labelKey = filters?.find(filter => filter.name === filterKey)?.labelKey
        return {
            key: filterKey,
            labelKey,
            value: filterValue
        }
    })

    const advancedSearchForm = useMemo(() => {
        return filters.map(filter => (
            <div key={filter.name} className={ADVANCED_SEARCH_CONTROL_CLASS}>
                <Field.Input key={filter.name} label={t(filter.labelKey)} {...filter} />
            </div>
        ))
    }, [filters])

    return (
        <div className={classNames(CASE_SEARCH_COMPONENT, className)}>
            <div className={CASE_SEARCH_AUTOCOMPLETE_WITH_FILTER}>
                <div ref={ref} className={CASE_SEARCH_AUTOCOMPLETE}>
                    <AutocompletableSearch
                        withDebounce
                        useHints
                        dropdownClassName={CASE_SEARCH_COMPONENT_DROPDOWN}
                        maxLength={50}
                        showViewAll={() => {
                            return searchInputStore.suggestionsCount > showViewAllThreshold
                        }}
                        allowClear={false}
                        value={searchInputStore.searchValue}
                        valueChangeHandler={value => store.changeAutocompleteValue(value, true)}
                        // onSearchInputBefore={store.changeAutocompleteValue}
                        isSuggestionSelected={suggestion => {
                            return store.searchInputStore.selectedSuggestionId === suggestion.key
                        }}
                        onSearchInput={store.searchAutocompleteSuggestions}
                        suggestions={store.suggestions}
                        onSuggestionSelected={store.onAutocompleteSuggestionSelected}
                        onShowAllResultsSelected={store.onViewAllOrPressEnter}
                        onViewAllClick={store.onViewAllOrPressEnter}
                        onFocus={onFocusCallback}
                        onBlur={onBlurCallback}
                        {...autoCompleteConfig}
                    />
                </div>
                <div className={CASE_SEARCH_FILTER_POPOVER_WRAPPER}>
                    <CaseSearchFilters
                        popoverRef={ref}
                        className={classNames(CASE_SEARCH_FILTER_POPOVER, caseFilterConfig?.className)}
                        visible={searchFilterStore.advanceSearchVisible}
                        setVisible={searchFilterStore.changeAdvanceSearchVisible}
                        advancedSearchForm={advancedSearchForm}
                        filterData={searchFilterStore.advanceFilters}
                        onAdvanceSubmit={store.onAdvanceSearchSubmit}
                        onAdvanceReset={store.onAdvanceSearchReset}
                        {...caseFilterConfig}
                    />
                </div>
            </div>
            {searchResultTableStore.showCaseTable ? (
                <div className={CASE_SEARCH_RESULT}>
                    <CasseSearchResultFilters
                        searchContent={searchInputStore.searchValue}
                        pageNum={searchResultTableStore.caseTablePagination?.current ?? 0}
                        pageSize={searchResultTableStore.caseTablePagination?.pageSize ?? 0}
                        total={searchResultTableStore.caseTableResultCount}
                        searchMode={isEmpty(toJS(searchFilterStore.advanceFilters)) ? 'singleSearch' : 'advanceSearch'}
                        filters={resultFilters}
                        onRemoveFilter={store.onRemoveSingleFilter}
                        onRemoveAllFilters={store.onRemoveAllFilters}
                    />
                    <CaseSearchResultTable
                        tableConfig={{
                            rowKey: record => record?._key?.rootId,
                            dataSource: searchResultTableStore.caseTableDataSource,
                            pagination: searchResultTableStore.caseTablePagination,
                            loading: searchResultTableStore.caseTableLoading,
                            onChange: store.onCaseTableChange,
                            rowSelection: {
                                type: 'radio',
                                selectedRowKeys: searchResultTableStore.selectedRow
                                    ? [searchResultTableStore.selectedRow?._key?.rootId]
                                    : undefined,
                                onChange: store.onRowSelectionChange
                            }
                        }}
                        {...resultTableConfig}
                    />
                </div>
            ) : null}
        </div>
    )
})
