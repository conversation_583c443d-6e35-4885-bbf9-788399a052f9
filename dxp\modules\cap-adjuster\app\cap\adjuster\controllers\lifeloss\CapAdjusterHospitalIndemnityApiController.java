/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.lifeloss;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import cap.adjuster.services.lifeloss.CapAdjusterLifeLossService;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterHospitalIndemnityApiController.TAG_API_CAP_ADJUSTER_DEATH,
                description = "CAP Adjuster: Hospital Indemnity Loss API")})
@Api(value = CapAdjusterHospitalIndemnityApiController.TAG_API_CAP_ADJUSTER_DEATH,
        tags = CapAdjusterHospitalIndemnityApiController.TAG_API_CAP_ADJUSTER_DEATH)
public class CapAdjusterHospitalIndemnityApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_DEATH = "/cap-adjuster/v1/losses-hi";
    protected static final String MODEL_NAME = "HILoss";

    private CapAdjusterLifeLossService lifeLossService;

    /**
     * Get Hospital Indemnity Loss' settlements
     *
     * @param rootId Hospital Indemnity Loss identifier
     * @param revisionNo Hospital Indemnity Loss revision number
     * @return list of settlements related to Hospital Indemnity Loss
     */
    @ApiOperation(value = "Get settlements associated with hospital indemnity loss",
            response = CapGenericSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Hospital Indemnity Loss Identifier", required = true) String rootId,
                                                  @ApiParam(value = "Hospital Indemnity Loss Revision Number", required = true) Integer revisionNo) {
        return completeOk(lifeLossService.getLossSettlements(rootId, revisionNo, MODEL_NAME));
    }

    @Inject
    public void setLifeLossService(CapAdjusterLifeLossService lifeLossService) {
        this.lifeLossService = lifeLossService;
    }
}