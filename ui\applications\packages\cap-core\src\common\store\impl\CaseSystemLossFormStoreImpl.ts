import {action, observable} from 'mobx'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {CaseSystemLossFormStore} from '../CaseSystemLossFormStore'
import {ICaseSystem, ICaseSystemLoss} from '../../Types'

export class CaseSystemLossFormStoreImpl<CS extends ICaseSystem, CSLoss extends ICaseSystemLoss>
    extends BaseRootStoreImpl
    implements CaseSystemLossFormStore<CS, CSLoss>
{
    @observable caseSystem: CS

    @observable lossForm: CSLoss

    @action initLoss: (lossForm: CSLoss) => void

    @action updateLoss: (lossForm: CSLoss) => void

    createLossFormEarningsEntity: () => any

    createExternalTimeEntity: () => any

    constructor({
        caseSystem,
        lossForm,
        createLossFormEarningsEntity,
        createExternalTimeEntity,
        initLoss,
        updateLoss
    }: {
        caseSystem: CS
        lossForm: CSLoss
        createLossFormEarningsEntity: () => any
        createExternalTimeEntity: () => any
        initLoss: (loss: any) => void
        updateLoss: (loss: CSLoss) => void
    }) {
        super()
        this.caseSystem = caseSystem
        this.lossForm = lossForm
        this.createLossFormEarningsEntity = createLossFormEarningsEntity
        this.createExternalTimeEntity = createExternalTimeEntity
        this.initLoss = initLoss
        this.updateLoss = updateLoss
    }

    @action
    setLossForm(lossForm: CSLoss): void {
        this.lossForm = this.createClaimLoss(lossForm)
    }

    @action
    protected createClaimLoss = (loss: CSLoss): CSLoss => ({
        ...loss,
        lossDetail: {
            ...loss.lossDetail!,
            earnings: loss.lossDetail?.earnings ? loss.lossDetail.earnings : this.createLossFormEarningsEntity(),
            externalTime: loss.lossDetail?.externalTime ? loss.lossDetail.externalTime : this.createExternalTimeEntity()
        }
    })
}
