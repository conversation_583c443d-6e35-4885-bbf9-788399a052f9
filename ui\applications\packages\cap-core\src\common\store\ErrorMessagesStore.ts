/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {observable, action} from 'mobx'
import {ErrorMessage} from '@eisgroup/common-types'
import {MessageActions} from '@eisgroup/dispatch'

export interface ErrorMessagesStore {
    errorMessages: ErrorMessage[]
    handleError: (message: ErrorMessage) => void
}

export class ErrorMessagesStoreImpl implements ErrorMessagesStore {
    @observable errorMessages: ErrorMessage[] = []

    @action
    handleError = (message: ErrorMessage) => {
        MessageActions.errorMessage.next(message)
        this.errorMessages.push(message)
    }
}
