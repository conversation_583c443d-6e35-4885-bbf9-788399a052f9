/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {action, observable, runInAction} from 'mobx'
import {PaymentMethod, PaymentMethodCustomer, PaymentMethodService} from '@eisgroup/common-business-components'
import {errorToRxResult} from '@eisgroup/common'
import {RxResult} from '@eisgroup/common-types'
import {Observable} from 'rxjs'
import {Right} from '@eisgroup/data.either'
import {BaseRootStoreImpl} from './BaseRootStore'

export interface PaymentMethodStore {
    paymentMethods: PaymentMethod[]
    loadPaymentMethods: (customer: PaymentMethodCustomer) => RxResult<PaymentMethod[]>
    onPreferredPaymentMethodChange: (id?: string) => void
    updatePaymentMethods: (
        paymentMethods: PaymentMethod[],
        customer: PaymentMethodCustomer
    ) => RxResult<PaymentMethodCustomer>
}

export class PaymentMethodStoreImpl extends BaseRootStoreImpl implements PaymentMethodStore {
    @observable paymentMethods: PaymentMethod[] = []

    paymentMethodService: PaymentMethodService

    constructor(paymentMethodService: PaymentMethodService) {
        super()
        this.paymentMethodService = paymentMethodService
    }

    @action
    loadPaymentMethods = (customer: PaymentMethodCustomer): RxResult<PaymentMethod[]> => {
        return this.call(() => this.paymentMethodService.loadPaymentMethods(customer)).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentMethods = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    onPreferredPaymentMethodChange = (id?: string) => {
        this.paymentMethods = this.paymentMethods.map(v =>
            v._key?.id === id ? {...v, preferred: true} : {...v, preferred: false}
        )
    }

    @action
    updatePaymentMethods = (
        paymentMethods: PaymentMethod[],
        customer: PaymentMethodCustomer
    ): RxResult<PaymentMethodCustomer> => {
        return this.paymentMethodService.updatePaymentMethods(paymentMethods, customer)
    }
}
