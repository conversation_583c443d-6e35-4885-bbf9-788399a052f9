/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.controllers.ltdloss;

import cap.adjuster.services.financial.dto.CapPayment;
import cap.adjuster.services.ltdloss.CapAdjusterLtdLossService;
import cap.adjuster.services.ltdloss.dto.CapLtdSettlement;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterLtdLossApiController.TAG_API_CAP_ADJUSTER_LTD_LOSS,
                description = "CAP Adjuster: LTD Loss API")})
@Api(value = CapAdjusterLtdLossApiController.TAG_API_CAP_ADJUSTER_LTD_LOSS,
        tags = CapAdjusterLtdLossApiController.TAG_API_CAP_ADJUSTER_LTD_LOSS)
public class CapAdjusterLtdLossApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_LTD_LOSS = "/cap-adjuster/v1/losses-ltd";

    private CapAdjusterLtdLossService ltdLossService;

    /**
     * Get payments for ltd loss
     *
     * @param rootId ltd loss identifier
     * @param revisionNo ltd loss revision number
     * @return list of payments related to ltd loss
     */
    @ApiOperation(value = "Get payments for ltd loss",
            response = CapPayment.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getPayments(@ApiParam(value = "Ltd loss identifier", required = true) String rootId,
                                              @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(ltdLossService.getPayments(rootId, revisionNo));
    }

    /**
     * Get settlements for ltd loss
     *
     * @param rootId ltd loss identifier
     * @param revisionNo ltd loss revision number
     * @return list of settlements related to ltd loss
     */
    @ApiOperation(value = "Get settlements for ltd loss",
            response = CapLtdSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Ltd loss identifier", required = true) String rootId,
                                               @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(ltdLossService.getSettlements(rootId, revisionNo));
    }

    @Inject
    public void setLtdLossService(CapAdjusterLtdLossService ltdLossService) {
        this.ltdLossService = ltdLossService;
    }
}
