/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import moment from 'moment'
import React from 'react'
import {LocalizationUtils} from '@eisgroup/i18n'
import {PaymentMethodCustomer} from '@eisgroup/common-business-components'
import {Col, Row} from '@eisgroup/ui-kit'
import {
    Field,
    OnChange,
    PrefixProvider,
    StateMapperParams,
    UpdateMapperParams,
    ValuesSpy,
    withFormState,
    FormSpy,
    defaultLookupValueExtractor,
    LookupContext,
    LookupContextType
} from '@eisgroup/form'
import {lookups} from '@eisgroup/lookups'
import {composeValidators, isNumberPositive, MaxNumber, required, isDatePast} from '../../utils/ValidationUtils'
import {AddressInfo} from '../address-info/AddressInfo'
import {
    ADDRESS_DRAWER_CONTENT,
    INPUT_FORM_ROW_2_1_1,
    INPUT_FORM_ROW_2X1,
    INPUT_FORM_ROW_3X1,
    INPUT_FORM_ROW_MARGIN_RIGHT_1
} from '../../common/package-class-names'

import t = LocalizationUtils.translate
import {customerModelTypes} from '../../common/constants'

const {LookupSelect, Checkbox, Datepicker, InputNumber} = Field

export interface AddressDrawerContentProps {
    customer: PaymentMethodCustomer
    idx: number
    updateFormState: (name: string, value: unknown) => void
}

export interface AddressDrawerMappingState {
    customer: PaymentMethodCustomer
    idx: number
}

export interface AddressDrawerContentState {
    isTemporaryAddress: boolean
    effectiveFrom?: Date
    effectiveTo?: Date
    duration?: number
}
export class AddressDrawerContent extends React.Component<AddressDrawerContentProps, AddressDrawerContentState> {
    state = {
        isTemporaryAddress: false,
        effectiveFrom: undefined,
        effectiveTo: undefined,
        duration: undefined
    }

    componentDidMount() {
        const {customer, idx} = this.props
        const schedulingContactInfo = customer.communicationInfo.addresses?.[idx]?.schedulingContactInfo
        if (schedulingContactInfo?.temporary) {
            this.setState({
                isTemporaryAddress: true,
                effectiveFrom: schedulingContactInfo.effectiveFrom,
                effectiveTo: schedulingContactInfo.effectiveTo
            })
            this.durationOnchange(schedulingContactInfo.effectiveFrom, schedulingContactInfo.effectiveTo)
        }
    }

    private calculateDuration = (startDate?: Date, endDate?: Date) =>
        startDate && endDate && moment(endDate).diff(moment(startDate), 'days')

    private durationOnchange = (startDate?: Date, endDate?: Date) => {
        const {idx, updateFormState} = this.props
        const schedulingContactInfoName = `customer.communicationInfo.addresses.${idx}.schedulingContactInfo.`
        updateFormState(schedulingContactInfoName.concat('duration'), this.calculateDuration(startDate, endDate))
    }

    handleChange = (name, value, form) => {
        const {effectiveFrom, effectiveTo} = this.state
        const {idx, updateFormState} = this.props
        const schedulingContactInfoName = `customer.communicationInfo.addresses.${idx}.schedulingContactInfo.`
        const triggerRule = (prefix: string, formValue: any) => {
            if (!formValue.getFieldState(prefix).valid) {
                formValue.getFieldState(prefix).blur()
            }
        }
        if (name === schedulingContactInfoName.concat('temporary')) {
            this.setState({isTemporaryAddress: value})
            if (!value) {
                updateFormState(schedulingContactInfoName.concat('effectiveFrom'), undefined)
                updateFormState(schedulingContactInfoName.concat('effectiveTo'), undefined)
                updateFormState(schedulingContactInfoName.concat('duration'), undefined)
            }
        }
        if (name === schedulingContactInfoName.concat('effectiveFrom')) {
            this.setState({
                effectiveFrom: value,
                duration: this.calculateDuration(value, effectiveTo)
            })
            this.durationOnchange(value, effectiveTo)
            updateFormState(schedulingContactInfoName.concat('effectiveFrom'), value)
            triggerRule(schedulingContactInfoName.concat('duration'), form)
        }
        if (name === schedulingContactInfoName.concat('effectiveTo')) {
            this.setState({
                effectiveTo: value,
                duration: this.calculateDuration(effectiveFrom, value)
            })
            this.durationOnchange(effectiveFrom, value)
            updateFormState(schedulingContactInfoName.concat('effectiveTo'), value)
            triggerRule(schedulingContactInfoName.concat('duration'), form)
        }
        if (name === schedulingContactInfoName.concat('duration')) {
            this.setState({
                duration: value
            })
            if (!effectiveFrom && effectiveTo) {
                const calculatedEffectiveFrom = moment(effectiveTo).subtract(value, 'days').toDate()
                this.setState({effectiveFrom: calculatedEffectiveFrom})
                updateFormState(schedulingContactInfoName.concat('effectiveFrom'), calculatedEffectiveFrom)
            }
            if (effectiveFrom && !effectiveTo) {
                const calculatedEffectiveTo = moment(effectiveFrom).add(value, 'days').toDate()
                this.setState({
                    effectiveTo: calculatedEffectiveTo
                })
                updateFormState(schedulingContactInfoName.concat('effectiveTo'), calculatedEffectiveTo)
            }
            updateFormState(schedulingContactInfoName.concat('duration'), value)
            triggerRule(schedulingContactInfoName.concat('duration'), form)
        }
    }

    initLookupSelect: LookupContextType = async (
        lookupName: string,
        lookupType?: string,
        filter?: lookups.LookupFilter,
        body?: any,
        loadType?: lookups.AdditionalLookupLoadTypes
    ) => {
        if (lookupName === 'AddressType') {
            return defaultLookupValueExtractor(lookupName).then(lookup => {
                return lookup.filter(lookupValue => {
                    const entityType =
                        this.props.customer._type === customerModelTypes.IndividualCustomer ? 'Person' : 'NonPerson'
                    // for not have entityType, not filter
                    return lookupValue.filters?.entityType?.includes(entityType) || !lookupValue.filters?.entityType
                })
            })
        }
        return defaultLookupValueExtractor(lookupName, lookupType, filter, body, loadType)
    }

    render(): React.ReactNode {
        const idx = this.props.idx
        const schedulingContactInfoName = `customer.communicationInfo.addresses.${idx}.schedulingContactInfo`
        return (
            <div className={ADDRESS_DRAWER_CONTENT}>
                <LookupContext.Provider value={this.initLookupSelect}>
                    <PrefixProvider prefixInputName={`customer.communicationInfo.addresses.${idx}.location`}>
                        <LookupSelect
                            label={t('cap-core:address_drawer_type')}
                            name='addressType'
                            lookupName='AddressType'
                            placeholder={t('cap-core:select_placeholder')}
                            required
                            validate={required('cap-core:address_drawer_type_is_required')}
                        />
                        <section>
                            <AddressInfo
                                controls={{
                                    stateProvinceCd: () => (
                                        <ValuesSpy relatedFields={['~countryCd']}>
                                            {({relatedValues}) => {
                                                const isRequired = relatedValues.countryCd === 'US'
                                                return (
                                                    <LookupSelect
                                                        label={t('cap-core:form_address_info_province')}
                                                        name='stateProvinceCd'
                                                        lookupName='StateProv'
                                                        placeholder={t('cap-core:select_placeholder')}
                                                        filterBy={['~countryCd']}
                                                        required={isRequired}
                                                        validate={
                                                            isRequired
                                                                ? required(
                                                                      'cap-core:party_detail_form_state_prov_is_required'
                                                                  )
                                                                : undefined
                                                        }
                                                    />
                                                )
                                            }}
                                        </ValuesSpy>
                                    ),
                                    render: controls => (
                                        <div>
                                            {controls.addressLine1}
                                            <div className={`${INPUT_FORM_ROW_2X1} ${INPUT_FORM_ROW_MARGIN_RIGHT_1}`}>
                                                {controls.addressLine2}
                                                {controls.addressLine3}
                                                {controls.countryCd}
                                            </div>
                                            <div className={INPUT_FORM_ROW_2_1_1}>
                                                {controls.city}
                                                {controls.stateProvinceCd}
                                            </div>
                                            <div className={INPUT_FORM_ROW_3X1}>{controls.postalCode}</div>
                                        </div>
                                    )
                                }}
                            />
                        </section>
                    </PrefixProvider>
                </LookupContext.Provider>
                <PrefixProvider prefixInputName={schedulingContactInfoName}>
                    <Checkbox label={t('cap-core:address_drawer_temporary')} name='temporary' />
                    {this.state.isTemporaryAddress && (
                        <>
                            <Row gutter={24}>
                                <Col span={12}>
                                    <Datepicker
                                        label={t('cap-core:address_drawer_effective_from')}
                                        name='effectiveFrom'
                                        required
                                        dateType='date-string'
                                        valueType='DATE'
                                        validate={required('cap-core:address_drawer_effective_from_is_required')}
                                    />
                                </Col>
                                <Col span={12}>
                                    <Datepicker
                                        label={t('cap-core:address_drawer_effective_to')}
                                        name='effectiveTo'
                                        required
                                        dateType='date-string'
                                        valueType='DATE'
                                        validate={composeValidators(
                                            required('cap-core:address_drawer_effective_to_is_required'),
                                            isDatePast(
                                                'cap-core:address_drawer_effective_to_can_not_be_earlier_than_today'
                                            )
                                        )}
                                    />
                                </Col>
                            </Row>
                            <Row gutter={24}>
                                <Col span={12}>
                                    <InputNumber
                                        label={t('cap-core:address_drawer_duration')}
                                        name='duration'
                                        required
                                        validate={composeValidators(
                                            required('cap-core:address_drawer_duration_is_required'),
                                            isNumberPositive,
                                            MaxNumber(50000, 'cap-core:address_drawer_exceed_max_value')
                                        )}
                                    />
                                </Col>
                            </Row>
                        </>
                    )}
                    <FormSpy>
                        {({form}) => {
                            return (
                                <OnChange>
                                    {(name: string, value: any) => this.handleChange(name, value, form)}
                                </OnChange>
                            )
                        }}
                    </FormSpy>
                </PrefixProvider>
            </div>
        )
    }
}

export const AddressDrawerContentWithFormState = withFormState<AddressDrawerContentProps>(
    AddressDrawerContent,
    ({state}: StateMapperParams<AddressDrawerMappingState>) => ({
        customer: state.customer,
        idx: state.idx
    }),
    (params: UpdateMapperParams) => ({
        updateFormState: (name: string, value: unknown) => {
            params.update({
                name,
                value
            })
        }
    })
)
