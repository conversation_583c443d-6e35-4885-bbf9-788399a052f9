/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.controllers.policy;

import cap.adjuster.services.policy.CapAdjusterPoliciesService;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchCapPolicySearchRequest;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchCapPolicySearchRequestBody;
import com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import core.services.pagination.PageData;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ResponseHeader;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static core.swagger.SwaggerConstants.PARAM_DATATYPE_INTEGER;
import static core.swagger.SwaggerConstants.PARAM_DATATYPE_STRING;
import static core.swagger.SwaggerConstants.PARAM_TYPE_BODY;
import static core.swagger.SwaggerConstants.PARAM_TYPE_QUERY;
import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterPoliciesApiController.TAG_API_CAP_ADJUSTER_POLICIES,
                description = "CAP Adjuster: Policies API")})
@Api(value = CapAdjusterPoliciesApiController.TAG_API_CAP_ADJUSTER_POLICIES,
        tags = CapAdjusterPoliciesApiController.TAG_API_CAP_ADJUSTER_POLICIES)
public class CapAdjusterPoliciesApiController extends ApiController {
    protected static final String TAG_API_CAP_ADJUSTER_POLICIES = "/cap-adjuster/v1/policies";

    private CapAdjusterPoliciesService capAdjusterPoliciesService;

    @ApiOperation(value = "Search latest policy headers by criteria",
            notes = "Policy headers search with intersection",
            response = CapAdjusterPolicySearchclaim_policy_header_CapPolicySearchProjectionResponse.class,
            responseContainer = RESPONSE_TYPE_LIST,
            responseHeaders = {
                    @ResponseHeader(name = "X-Total-Count",
                            response = Long.class,
                            description = "Total number of entries") },
            tags = { TAG_API_CAP_ADJUSTER_POLICIES })
    @ApiImplicitParams({
            @ApiImplicitParam(
                    dataType = PARAM_DATATYPE_INTEGER,
                    paramType = PARAM_TYPE_QUERY,
                    name = PARAM_OFFSET),
            @ApiImplicitParam(
                    dataType = PARAM_DATATYPE_INTEGER,
                    paramType = PARAM_TYPE_QUERY,
                    name = PARAM_LIMIT),
            @ApiImplicitParam(
                    dataType = PARAM_DATATYPE_STRING,
                    paramType = PARAM_TYPE_QUERY,
                    name = PARAM_FIELDS),
            @ApiImplicitParam(
                    value = "Search criteria",
                    dataType = "com.eisgroup.dxp.services.capadjusterpolicysearch.dto.CapAdjusterPolicySearchCapPolicySearchRequest",
                    paramType = PARAM_TYPE_BODY,
                    name = PARAM_TYPE_BODY) })
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> searchLatestPolicyHeaders() {
        CapAdjusterPolicySearchCapPolicySearchRequestBody body = new CapAdjusterPolicySearchCapPolicySearchRequestBody();
        body.addFieldInRequest(PARAM_TYPE_BODY);
        body.body = parseRequestBody(CapAdjusterPolicySearchCapPolicySearchRequest.class, false);
        PageData pageData = getPageData();

        return completeOk(capAdjusterPoliciesService.searchPolicyHeaders(body, getQueryParam(PARAM_FIELDS), pageData));
    }

    @Inject
    public void setCapAdjusterPoliciesService(CapAdjusterPoliciesService capAdjusterPoliciesService) {
        this.capAdjusterPoliciesService = capAdjusterPoliciesService;
    }
}
