/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {useTranslate} from '@eisgroup/i18n'
import {Collapse, Table} from '@eisgroup/ui-kit'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import React, {useEffect, useMemo} from 'react'
import {CHANGE_HISTORY_TABLE, CHANGE_HISTORY_TABLE_WRAPPER} from '../../common/package-class-names'
import {
    ChangeHistoryStore,
    LOAD_CHANGE_HISTORY_ACTION,
    LOAD_SETTLEMENT,
    POLL_SETTLEMENT,
    READJUDICATE_SETTLEMENT
} from '../../common/store'
import {getHistoryTableColumns} from './utils/columns'
import {extractHistoryTableRows} from './utils/rows'
import {useHistoryTableFilters} from './utils/useHistoryTableFilters'
import {FilterTag} from './utils/filterTag'

const CHANGE_HISTORY_COLLAPSE_KEY = 'change_history_panel'

export interface ChangeHistoryTableProps {
    store: ChangeHistoryStore
    isLoading?: boolean
    isClaimOverview?: boolean
}

export const ChangeHistoryTable: React.FC<ChangeHistoryTableProps> = observer(({store, ...props}) => {
    const {t} = useTranslate()

    useEffect(() => {
        store.loadChangeHistory(props.isClaimOverview)
    }, [
        store.eventCase,
        store.loss,
        store.settlements,
        store.specialHandlingStore.specialHandling,
        store.paymentsStore.paymentTemplates,
        store.caseRelationshipStore?.getRelationshipDependecies()
    ])

    const data = useMemo(
        () =>
            extractHistoryTableRows(
                toJS(store.changeHistoryRecords),
                {
                    eventCase: toJS(store.eventCase),
                    claims: toJS(store.paymentsStore.allAssociatedClaims),
                    settlements: [...toJS(store.settlements)].filter(Boolean),
                    specialHandlings: [
                        toJS(store.specialHandlingStore.specialHandling),
                        ...toJS(store.eventCaseStore.allSpecialHandlingsUnderEventCase)
                    ],
                    paymentTemplates: toJS(store.templates),
                    parties:
                        store.claimPartyStore && store.claimStore
                            ? [
                                  ...toJS(store.claimPartyStore.parties),
                                  ...toJS(store.claimPartyStore.beneficiaries),
                                  ...toJS(store.claimStore.allClaimsBeneficiaries)
                              ]
                            : [],
                    capRelationships: toJS(store?.caseRelationshipStore?.caseRelationshipsWithTargetCases ?? [])
                },
                store.getUserFullNameFromUUID,
                store.eventTypes
            ),
        [
            store.changeHistoryRecords,
            store.eventCase,
            store.paymentsStore.allAssociatedClaims,
            store.getUserFullNameFromUUID,
            store.settlements,
            store.specialHandlingStore.specialHandling,
            store.eventCaseStore.allSpecialHandlingsUnderEventCase,
            store.templates,
            store.claimPartyStore
        ]
    )

    const {optionsList, tableData, handleReset, handleSearch, tableFilters} = useHistoryTableFilters({data})

    const isLoading =
        store.actionsStore.isRunning(LOAD_CHANGE_HISTORY_ACTION) ||
        store.actionsStore.isRunning(READJUDICATE_SETTLEMENT) ||
        store.actionsStore.isRunning(POLL_SETTLEMENT) ||
        store.actionsStore.isRunning(LOAD_SETTLEMENT)

    return (
        <div className={CHANGE_HISTORY_TABLE_WRAPPER}>
            <Collapse defaultActiveKey={CHANGE_HISTORY_COLLAPSE_KEY} isWhite accordion bordered={false}>
                <Collapse.Panel key={CHANGE_HISTORY_COLLAPSE_KEY} header={t('cap-core:history_table_label')}>
                    <FilterTag tableFilters={tableFilters} handleReset={handleReset} />
                    <Table
                        rowKey={(value, index) =>
                            value.metadata.timestamp.toString() + value.metadata.path + index.toString()
                        }
                        columns={getHistoryTableColumns({
                            optionsList,
                            handleReset,
                            handleSearch,
                            tableFilters
                        })}
                        loading={isLoading || props.isLoading}
                        dataSource={tableData}
                        pagination={{
                            size: 'small',
                            defaultPageSize: 10,
                            pageSizeOptions: ['10', '15', '20', '25'],
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: tableData.length
                        }}
                        className={CHANGE_HISTORY_TABLE}
                    />
                </Collapse.Panel>
            </Collapse>
        </div>
    )
})
