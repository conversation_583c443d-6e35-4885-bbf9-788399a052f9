/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {injectable} from 'inversify'
import {uniq} from 'lodash'

import {claims} from '@eisgroup/claims-core'
import {promiseToRxResult, RxResult} from '@eisgroup/common'

@injectable()
export class LinkResolverServiceImpl implements claims.LinkResolverService {
    resolveLinks<T>(links: string[]): RxResult<Record<string, T>> {
        return promiseToRxResult(options =>
            Promise.resolve({
                result: uniq(links).reduce((accum, link) => {
                    let result: any = link
                    if (link.includes('INDIVIDUALCUSTOMER')) {
                        result = {
                            details: {
                                person: {
                                    firstName: 'John',
                                    lastName: 'Doe'
                                }
                            },
                            customerNumber: 'IC0000000052'
                        }
                    } else if (link.includes('ORGANIZATIONCUSTOMER')) {
                        result = {
                            details: {
                                legalEntity: {
                                    legalName: 'EIS Group'
                                }
                            },
                            customerNumber: 'OC0000000033'
                        }
                    }

                    return {...accum, [link]: result}
                }, {})
            })
        ).map(response => response.map(result => (result as {result: Record<string, T>}).result))
    }
}
