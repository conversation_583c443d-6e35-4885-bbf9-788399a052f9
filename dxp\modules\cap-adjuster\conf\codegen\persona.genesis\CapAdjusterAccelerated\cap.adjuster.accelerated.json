{"swagger": "2.0", "x-dxp-spec": {"imports": {"accelerated": {"schema": "integration.cap.accelerated.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Accelerated Loss API", "version": "1", "title": "CAP Adjuster: Accelerated Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-accelerated", "description": "CAP Adjuster: Accelerated Loss API"}], "paths": {"/losses-accelerated/{rootId}/{revisionNo}": {"get": {"summary": "Get accelerated loss by rootId and revisionNo", "x-dxp-path": "/api/caploss/AcceleratedLoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-accelerated"]}}, "/losses-accelerated/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for accelerated loss", "x-dxp-path": "/api/caploss/AcceleratedLoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-accelerated"]}}, "/losses-accelerated": {"post": {"summary": "Create accelerated loss", "x-dxp-path": "/api/caploss/AcceleratedLoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-accelerated"]}, "put": {"summary": "Update accelerated loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/AcceleratedLoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-accelerated"]}}, "/losses-accelerated/draft": {"post": {"summary": "Init accelerated loss", "x-dxp-path": "/api/caploss/AcceleratedLoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-accelerated"]}}}}