{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap.claim.wrapper": {"schema": "integration.cap.claim.wrapper.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Claim Wrappers API", "version": "1", "title": "CAP Adjuster: Claim Wrappers API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/claim-wrappers", "description": "CAP Adjuster: Claim Wrappers API"}], "paths": {"/claim-wrappers/{rootId}/{revisionNo}": {"get": {"summary": "<PERSON> <PERSON><PERSON><PERSON>", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/rules/bundle": {"post": {"summary": "Rules bundle for <PERSON><PERSON>m Wrapper", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/rules/bundle", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/draft": {"post": {"summary": "Initiate New Loss Creation", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/initLoss", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers": {"post": {"x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/createLoss", "summary": "Create the already initiated ClaimWrapper", "tags": ["/cap-adjuster/v1/claim-wrappers"]}, "put": {"x-dxp-method": "post", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/updateLossImmediate", "summary": "Update ClaimWrapper immediately without workflow trigger", "tags": ["/cap-adjuster/v1/claim-wrappers"]}, "patch": {"x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/updateLossImmediate", "summary": "Partial Update ClaimWrapper immediately without workflow trigger", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/submit": {"post": {"summary": "Submit <PERSON><PERSON><PERSON>", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/submitLoss", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/close": {"post": {"summary": "Close Claim Wrapper", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/closeLoss", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/reopen": {"post": {"summary": "Reopen <PERSON><PERSON><PERSON>", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/reopenLoss", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/sub-status": {"post": {"summary": "Create <PERSON><PERSON><PERSON>per Sub-Status", "x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/setLossSubStatus", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/retrieve-coverages": {"post": {"x-dxp-path": "/api/caploss/ClaimWrapper/v1/transformation/claimWrapperCoverages", "summary": "Get all of available claim coverages from coverage-based, and filter out by actual policy owned benefits", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/start-claim-updating-flow": {"post": {"x-dxp-path": "/api/caploss/ClaimWrapper/v1/command/updateLoss", "summary": "Start claim updating flow to refresh claim or generate coverages", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}, "/claim-wrappers/retrieve-closure-open-items": {"post": {"x-dxp-path": "/api/caploss/ClaimWrapper/v1/transformation/lifeClaimClosureToOpenItems", "summary": "Get all open items related to the claim", "tags": ["/cap-adjuster/v1/claim-wrappers"]}}}}