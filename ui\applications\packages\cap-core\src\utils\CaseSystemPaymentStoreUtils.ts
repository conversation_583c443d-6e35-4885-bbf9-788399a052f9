/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {Either} from '@eisgroup/data.either'
import {Observable} from 'rxjs/Observable'
import {errorToRxResult} from '@eisgroup/common'
import {t} from '@eisgroup/i18n'
import {ErrorMessage, opt, RxResult} from '@eisgroup/common-types'
import {
    backofficeCustomerRelationshipService,
    backOfficeWorkflowService,
    ClaimLoss,
    createCommonPollingObservable,
    dateUtils,
    financialService,
    LossParams,
    LossParamsWithModelName,
    Relationship,
    WorkflowProcess
} from '@eisgroup/cap-services'
import {ExternalLink} from '@eisgroup/models-api'
import {BusinessTypes, CapPaymentSchedule, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {
    CHANGE_PAYMENT_STATUS,
    LifeClaimTypesMap,
    LOAD_PAYMENTS,
    PAYMENT_ACTIONS_MAP,
    POLL_COUNT,
    POLL_DELAY,
    RELATIONSHIP,
    RELATIONSHIP_STATE,
    ReserveType,
    SettlementModelName
} from '../common/constants'
import {Allocation, AllocationTableRecord, ScheduledAllocation, ScheduledResult, Settlements} from '../common/Types'
import {EntityLink} from './EntityLink'
import {periodToRange, rangeToPeriod} from '../components/range-picker/RangePicker'
import {BaseRootStoreImpl} from '../common/store'
import {getTranslatedCoverageName} from './CoverageUtils'
import Period = BusinessTypes.Period
import CapPaymentScheduleExpensesInput = CapPaymentSchedule.CapPaymentScheduleExpensesInput
import CapPaymentScheduleExGratiasInput = CapPaymentSchedule.CapPaymentScheduleExGratiasInput
import CapPaymentScheduleSettlementInfoInput = CapPaymentSchedule.CapPaymentScheduleSettlementInfoInput
import CapScheduledPaymentEntity = CapPaymentSchedule.CapScheduledPaymentEntity
import CapPaymentAdditionEntity = CapPaymentSchedule.CapPaymentAdditionEntity
import CapBuildPaymentScheduleInput = CapPaymentSchedule.CapBuildPaymentScheduleInput
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity
import {getEOBRemarks} from './eobUtils'

const rootStore = new BaseRootStoreImpl()

export const isLifeSettlement = (settlementModelName: string): boolean => {
    return [
        SettlementModelName.CI_SETTLEMENT,
        SettlementModelName.HI_SETTLEMENT,
        SettlementModelName.DEATH_SETTLEMENT,
        SettlementModelName.ACCELERATED_SETTLEMENT,
        SettlementModelName.ACCIDENTALDISMEMBERMENT_SETTLEMENT,
        SettlementModelName.PREMIUMWAIVER_SETTLEMENT
    ].includes(settlementModelName)
}

export const isAbsenceNotLeaveSettlement = (settlementModelName: string): boolean => {
    return [
        SettlementModelName.STD_SETTLEMENT,
        SettlementModelName.SMP_SETTLEMENT,
        SettlementModelName.LTD_SETTLEMENT
    ].includes(settlementModelName)
}

export const getEitherResult = (result: Either<ErrorMessage, any>) => {
    if (result.isRight) {
        return result.get()?.result || []
    }
    return undefined
}

// Because the error message returned by the back-end is inconsistent with the requirements, the front-end needs special processing
export const getErrorMessage = e => {
    let errorMsg = ''
    if (e.errors && e.errors.length) {
        errorMsg =
            e.errors?.[0]?.code &&
            ['DisabilityAlloc-007', 'LifeAlloc-003', 'ExpenseAlloc-004'].includes(e.errors?.[0]?.code)
                ? `cap-core:${t(e.errors?.[0]?.code)}`
                : e.errors?.[0]?.message
    }
    return errorToRxResult({
        code: String(e.errors ? e.errors?.[0]?.code : e.code),
        message: String(e.errors ? errorMsg : e.message)
    } as ErrorMessage)
}

export const findSettlement = (uri: string | undefined, associatedSettlements: Settlements[]): Settlements | null => {
    if (!uri) {
        return null
    }
    const settlementRootId = EntityLink.from(uri).rootId
    return associatedSettlements.find(v => v._key.rootId === settlementRootId) || null
}

export const getLossTypeFromAllocationUri = (
    associatedClaims: ClaimLoss[],
    associatedSettlements: Settlements[],
    uri?: string
): string => {
    const settlement = findSettlement(uri, associatedSettlements)
    if (!settlement) {
        return t('cap-core:not_available')
    }
    const loss = opt(associatedClaims)
        .orElse([])
        .find(v => v._key.rootId === settlement.associatedClaimRootId)
    return `${loss?.lossNumber}`
}

export const getCoverageForSettlement = (associatedSettlements: Settlements[], uri?: string): string => {
    const settlement = findSettlement(uri, associatedSettlements)
    if (!settlement) {
        return t('cap-core:not_available')
    }
    const name: string = settlement?.claimCoverageName || settlement.settlementResult?.coverageCd
    const result = getTranslatedCoverageName(name, settlement?.claimCoveragePrefix)
    return result ?? ''
}

export const getLossTypeFromLossSource = (associatedClaims: ClaimLoss[], uri?: string): string => {
    const loss = opt(associatedClaims)
        .orElse([])
        .find(v => v._key.rootId === uri?.split('/')[5])
    return loss ? `${loss.lossNumber}` : ''
}

export const filterAllocationCondition = (allocationPeriod: Period, benefitPeriod?: Period) => {
    const startDate = dateUtils(allocationPeriod?.startDate).toDate
    const endDate = dateUtils(allocationPeriod?.endDate).toDate
    const benefitStartDate = dateUtils(benefitPeriod?.startDate).toDate
    const benefitEndDate = dateUtils(benefitPeriod?.endDate).toDate
    return (
        !allocationPeriod ||
        (startDate &&
            endDate &&
            benefitStartDate &&
            benefitEndDate &&
            ((benefitStartDate >= startDate && benefitStartDate <= endDate) ||
                (benefitEndDate >= startDate && benefitEndDate <= endDate)))
    )
}

export const getSettlementIndex = (
    allocationSource: ExternalLink | undefined,
    associatedSettlements: Settlements[]
): number => {
    if (!allocationSource) {
        return -1
    }
    const settlementRootId = EntityLink.from(allocationSource._uri).rootId
    return associatedSettlements.findIndex(v => v._key.rootId === settlementRootId)
}

export const setExpenseFlag = (
    v,
    scheduleInput:
        | CapPaymentScheduleSettlementInfoInput
        | CapPaymentScheduleExpensesInput
        | CapPaymentScheduleExGratiasInput
) => {
    const lossSourceUri = scheduleInput?.lossSource?._uri ?? ''
    const expenseAmount = scheduleInput?.expenseAmount
    const expenseDescription = scheduleInput?.expenseDescription

    return (
        v.allocationLossInfo?.lossSource?._uri === lossSourceUri &&
        v.allocationGrossAmount?.amount === expenseAmount?.amount &&
        v.expenseDescription === expenseDescription
    )
}

export const setExGratiaFlag = (
    v,
    scheduleInput:
        | CapPaymentScheduleSettlementInfoInput
        | CapPaymentScheduleExpensesInput
        | CapPaymentScheduleExGratiasInput
) => {
    const lossSourceUri = scheduleInput?.lossSource?._uri ?? ''
    const exGratiaAmount = scheduleInput?.exGratiaAmount
    const exGratiaDescription = scheduleInput?.exGratiaDescription

    return (
        v.allocationLossInfo?.lossSource?._uri === lossSourceUri &&
        v.allocationGrossAmount?.amount === exGratiaAmount?.amount &&
        v.exGratiaDescription === exGratiaDescription
    )
}

export const constructScheduledAllocationsAndAdditions = (
    scheduleInput:
        | CapPaymentScheduleSettlementInfoInput
        | CapPaymentScheduleExpensesInput
        | CapPaymentScheduleExGratiasInput,
    settlement?: Settlements,
    payments?: CapScheduledPaymentEntity[]
): ScheduledResult => {
    const allocationSourceUri: string = scheduleInput.uri ?? ''
    const lossSourceUri = scheduleInput?.lossSource?._uri ?? ''
    const expenseAmount = scheduleInput?.expenseAmount
    const exGratiaAmount = scheduleInput?.exGratiaAmount
    const expenseDescription = scheduleInput?.expenseDescription
    const exGratiaDescription = scheduleInput?.exGratiaDescription
    const allocationPeriod = scheduleInput.allocationPeriod
    const isInterestOnly = scheduleInput.isInterestOnly
    const result: ScheduledAllocation[] = []
    let scheduleAdditions: CapPaymentAdditionEntity[] = []
    payments?.forEach(scheduledPayment => {
        const messages = scheduledPayment.messages || []
        const allocations = scheduledPayment.paymentDetails?.paymentAllocations
        if (allocations && allocations.length > 0) {
            allocations.forEach(v => {
                if (v.reserveType === ReserveType.COVERAGE) {
                    const benefitPeriod = v.allocationPayableItem?.benefitPeriod
                    if (
                        v.allocationSource?._uri === allocationSourceUri &&
                        v.isInterestOnly === isInterestOnly &&
                        filterAllocationCondition(allocationPeriod, benefitPeriod)
                    ) {
                        result.push({
                            paymentAllocation: v,
                            eobRemarks: getEOBRemarks(messages, v),
                            settlement,
                            scheduledPayment,
                            reserveType: v.reserveType
                        })
                        scheduleAdditions =
                            scheduledPayment.paymentDetails?.paymentAdditions || ([] as CapPaymentAdditionEntity[])
                    }
                } else if (
                    setExpenseFlag(v, scheduleInput) &&
                    !result.some(
                        r =>
                            r.expense?.expenseAmount?.amount === expenseAmount?.amount &&
                            r.expense?.expenseDescription === expenseDescription &&
                            r.paymentAllocation.allocationLossInfo?.lossSource?._uri === lossSourceUri
                    )
                ) {
                    result.push({
                        paymentAllocation: v,
                        eobRemarks: getEOBRemarks(messages, v),
                        expense: scheduleInput,
                        scheduledPayment,
                        reserveType: v.reserveType
                    })
                } else if (
                    setExGratiaFlag(v, scheduleInput) &&
                    !result.some(
                        r =>
                            r.exGratia?.exGratiaAmount?.amount === exGratiaAmount?.amount &&
                            r.exGratia?.exGratiaDescription === exGratiaDescription &&
                            r.paymentAllocation.allocationLossInfo?.lossSource?._uri === lossSourceUri
                    )
                ) {
                    result.push({
                        paymentAllocation: v,
                        eobRemarks: getEOBRemarks(messages, v),
                        exGratia: scheduleInput,
                        scheduledPayment,
                        reserveType: v.reserveType
                    })
                }
            })
        }
    })
    return {scheduleAllocations: result, scheduleAdditions}
}

export const formatToAllocations = (
    associatedClaims: ClaimLoss[],
    associatedSettlements: Settlements[],
    settlements?: CapPaymentScheduleSettlementInfoInput[],
    expenses?: CapPaymentScheduleExpensesInput[],
    exGratias?: CapPaymentScheduleExGratiasInput[]
) => {
    const allocations = [] as Allocation[]
    settlements?.forEach(v => {
        const settlementRootId = v.uri?.split('//')[2]?.split('/')[0]
        const selectedSettlement = associatedSettlements.filter(k => k._key.rootId === settlementRootId)[0]
        const selectedClaim = selectedSettlement?.associatedClaimRootId
        const selectedClaimType = associatedClaims.filter(k => k._key.rootId === selectedClaim)[0]?.claimType
        const selectedClaimModelName = associatedClaims.filter(k => k._key.rootId === selectedClaim)[0]?._modelName
        const claimModelName = Object.keys(LifeClaimTypesMap).includes(selectedClaimType)
            ? selectedClaimType
            : selectedClaimModelName
        const grossAmountMode = selectedSettlement?.coverageBasedConfiguration?.grossAmountMode
        allocations.push({
            selectedClaim,
            isInterestOnly: v.isInterestOnly,
            coverageType: ReserveType.COVERAGE,
            allocationSource: v.uri,
            allocationPayeeSource: v.payeeDetails?.payee?._uri || '',
            allocationPaymentAmount: v.allocationPaymentAmount,
            allocationPeriod: v?.allocationPeriod ? periodToRange(v?.allocationPeriod) : undefined,
            grossAmountMode: grossAmountMode ?? undefined,
            claimModelName,
            manualEOBRemarks: v.manualEOBRemarks,
            representBeneficiary: v.payeeDetails?.representBeneficiary?._uri || '',
            _key: v._key
        })
    })
    expenses?.forEach(v => {
        const selectedClaim = v.lossSource!._uri.split('/')[5]
        const curClaim = associatedClaims.filter(claim => claim._key.rootId === selectedClaim)[0]
        const curClaimModelName = curClaim?._modelName
        allocations.push({
            selectedClaim,
            isInterestOnly: false,
            coverageType: ReserveType.EXPENSE,
            allocationPayeeSource: v.payee?._uri || '',
            allocationPaymentAmount: v.expenseAmount,
            expenseNumber: v.expenseNumber,
            expenseDescription: v.expenseDescription,
            claimModelName: curClaimModelName,
            manualEOBRemarks: v.manualEOBRemarks,
            representBeneficiary: v.representBeneficiary?._uri || '',
            _key: v._key
        })
    })
    exGratias?.forEach(v => {
        const selectedClaim = v.lossSource!._uri.split('/')[5]
        const curClaimModelName = associatedClaims.filter(claim => claim._key.rootId === selectedClaim)[0]?._modelName
        allocations.push({
            selectedClaim,
            isInterestOnly: false,
            coverageType: ReserveType.EXGRATIA,
            allocationPayeeSource: v.payee?._uri || '',
            exGratiaAmount: v.exGratiaAmount,
            exGratiaNumber: v.exGratiaNumber,
            exGratiaDescription: v.exGratiaDescription,
            claimModelName: curClaimModelName,
            manualEOBRemarks: v.manualEOBRemarks,
            representBeneficiary: v.representBeneficiary?._uri || '',
            _key: v._key
        })
    })
    return allocations
}

export const formatRecalculateParams = (
    allocationTableData: AllocationTableRecord[],
    buildPaymentScheduleInput?: CapBuildPaymentScheduleInput
) => {
    const settlements: CapPaymentScheduleSettlementInfoInput[] = allocationTableData
        .filter(item => item._type === 'CapPaymentScheduleSettlementInfoInput')
        .map(x => {
            const {
                uri,
                interestDetails,
                allocationPeriod,
                allocationPeriodForRender,
                payeeDetails,
                isInterestOnly,
                frequencyConfiguration,
                partialDisability,
                _key,
                _type
            } = x
            const tmpAllocationPeriod = {
                ...allocationPeriod,
                ...(allocationPeriodForRender ? rangeToPeriod(allocationPeriodForRender, {}) : {})
            }
            return {
                uri,
                allocationPeriod: allocationPeriod ? tmpAllocationPeriod : undefined,
                interestDetails,
                payeeDetails,
                isInterestOnly,
                frequencyConfiguration,
                partialDisability: {...partialDisability, _type: 'PartialDisabilityInput'},
                _key,
                _type
            } as CapPaymentScheduleSettlementInfoInput
        })
    return {
        ...buildPaymentScheduleInput,
        settlements
    } as CapBuildPaymentScheduleInput
}

export const searchCustomerRelationshipWithTypeCd = (chosenValue: string) => {
    const {rootId} = EntityLink.from(chosenValue)
    const relationshipRequest = {
        relationshipRoleToCd: {
            matches: [RELATIONSHIP.GUARDIAN]
        },
        state: {
            notEqual: RELATIONSHIP_STATE.DELETED
        }
    }
    return backofficeCustomerRelationshipService().searchCustomerRelationshipWithTypeCd(rootId, relationshipRequest)
}

export const filterRelationships = (relationships: Relationship[], choosedValue: string) => {
    const seen = new Set()
    return relationships
        ?.filter(item => item.relatedCustomer?.details?.person.registryTypeId !== choosedValue)
        ?.map(oneInd => {
            const {registryTypeId, firstName, lastName} = oneInd?.relatedCustomer?.details?.person ?? {}
            if (seen.has(registryTypeId)) {
                return null
            }
            seen.add(registryTypeId)
            return {
                code: registryTypeId,
                displayValue: `${firstName} ${lastName}`
            }
        })
        ?.filter(Boolean)
}

export const pollPaymentsAfterClaimUpdate = (
    caseParams: LossParamsWithModelName,
    claimParams: LossParamsWithModelName
) => {
    return rootStore.call(
        () =>
            createCommonPollingObservable<WorkflowProcess[]>(
                result => result.length !== 0,
                () =>
                    backOfficeWorkflowService.searchWorkflowProcessByEntityUris(
                        [
                            `gentity://CapLoss/${caseParams.modelName}//${caseParams.rootId}/${caseParams.revisionNo}`,
                            `gentity://CapLoss/${claimParams.modelName}//${claimParams.rootId}/${claimParams.revisionNo}`
                        ],
                        {
                            processDefinitionKeys: [
                                'cap_claim_update',
                                'getFinancialDataInitTemplate',
                                'case_payments_rescheduling',
                                'cap_settlement_cascade_update'
                            ]
                        }
                    )
            ),
        LOAD_PAYMENTS
    )
}

export const pollDataAfterPaymentScheduleCreateOrUpdate = (capPaymentTemplateUri: string) => {
    return rootStore.call(() =>
        createCommonPollingObservable<WorkflowProcess[]>(
            workflowResult => workflowResult.length === 0 || !workflowResult.every(v => v.statusCd === 'COMPLETED'),
            () =>
                backOfficeWorkflowService.searchWorkflowProcessByEntityUri(capPaymentTemplateUri, {
                    statusCd: undefined,
                    processDefinitionKeys: ['case_payments_scheduling']
                }),
            POLL_COUNT,
            POLL_DELAY
        )
    )
}

export const getPreviewPaymentScheduleErrorMsg = (e: ErrorMessage) => {
    let errorMsg = ''
    if (e.errors && e.errors.length) {
        errorMsg =
            e.errors?.[0]?.code === 'DisabilityAlloc-007'
                ? `cap-core:${t(e.errors?.[0]?.code)}`
                : e.errors?.[0]?.message
    }
    return errorMsg
}

export const paymentAction = (params: LossParams, action: string): RxResult<CapPaymentEntity> => {
    switch (action) {
        case PAYMENT_ACTIONS_MAP.REQUEST_ISSUE:
            return rootStore.call<any>(() => financialService.requestIssuePayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.ISSUE:
            return rootStore.call<any>(() => financialService.issuePayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.CANCEL:
            return rootStore.call<any>(() => financialService.cancelPayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.FAILOUNTBOUND:
            return rootStore.call<any>(() => financialService.failOutBoundPayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.REQUEST_STOP:
            return rootStore.call<any>(() => financialService.requestStopPayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.CANCELRECOVERY: {
            const requestBody = {
                _key: params
            }
            return rootStore.call<any>(() => financialService.cancelRecovery(requestBody), CHANGE_PAYMENT_STATUS)
        }
        default:
            return Observable.empty()
    }
}

export const underpaymentAction = (
    params: LossParams,
    action: string,
    message?: string
): RxResult<CapPaymentEntity> => {
    switch (action) {
        case PAYMENT_ACTIONS_MAP.APPROVE:
            return rootStore.call(() => financialService.approveUnderpayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.DISAPPROVE:
            return rootStore.call(() => financialService.cancelUnderpayment(params, message), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.REQUEST_ISSUE:
            return rootStore.call(() => financialService.requestIssueUnderpayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.ISSUE:
            return rootStore.call(() => financialService.issueUnderpayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.FAILOUNTBOUND:
            return rootStore.call(() => financialService.failOutBoundUnderpayment(params), CHANGE_PAYMENT_STATUS)
        case PAYMENT_ACTIONS_MAP.REQUEST_STOP:
            return rootStore.call(() => financialService.requestStopUnderpayment(params), CHANGE_PAYMENT_STATUS)
        default:
            return Observable.empty()
    }
}
