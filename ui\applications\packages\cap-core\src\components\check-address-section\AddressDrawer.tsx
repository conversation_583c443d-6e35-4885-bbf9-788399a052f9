/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import React from 'react'
import {LocalizationUtils} from '@eisgroup/i18n'
import {FormSpy} from '@eisgroup/form'
import {UIEngine} from '@eisgroup/builder'
import {PaymentMethod, PaymentMethodCustomer} from '@eisgroup/common-business-components'
import {DrawerActions, DrawerFormStateType, FormDrawer} from '../form-drawer'
import {PAYMENT_METHOD_DRAWER} from '../../common/package-class-names'
import editorConfig from '../../builder/editor-config'
import config from './builder/AddressDrawer.builder'
import {AddressDrawerContentWithFormState} from './AddressDrawerContent'
import t = LocalizationUtils.translate

export const ADDRESS_DRAWER_KEY = 'addressDrawer'

export interface AddressDrawerProps {
    customer: PaymentMethodCustomer
    idx: number
    addressDrawerVisible: boolean
    onClose: () => void
    onSuccess: (customer?) => void
    formTitle: string
    updateCustomerCEM: (customer: PaymentMethodCustomer) => Promise<PaymentMethodCustomer>
    paymentMethods?: PaymentMethod[]
}

export interface AddressDrawerState {
    isLoading: boolean
}

export class AddressDrawer extends React.Component<AddressDrawerProps, AddressDrawerState> {
    state = {
        isLoading: false
    }

    private renderDrawerContent = () => {
        const {customer, updateCustomerCEM, onClose, onSuccess, idx} = this.props
        return (
            <UIEngine
                {...editorConfig}
                formId={ADDRESS_DRAWER_KEY}
                config={config}
                initialValues={{customer, idx}}
                slotComponents={{
                    ADDRESS_INFO: AddressDrawerContentWithFormState
                }}
            >
                <FormSpy>
                    {params => {
                        return (
                            <DrawerActions
                                handleFormCancel={onClose}
                                labels={{
                                    editButtonLabel: t('cap-core:save'),
                                    createButtonLabel: t('cap-core:save')
                                }}
                                drawerFormState={DrawerFormStateType.Create}
                                isLoading={this.state.isLoading}
                                handleFormConfirm={() => {
                                    if (params.form.getState().hasValidationErrors) {
                                        return
                                    }
                                    const formCustomer = params.form.getState().values.customer
                                    const {paymentMethods} = this.props
                                    let resultPaymentMethods = formCustomer.paymentMethods
                                    if (paymentMethods && paymentMethods.length) {
                                        resultPaymentMethods = paymentMethods
                                    }
                                    const savedCustomer = {
                                        ...formCustomer,
                                        paymentMethods: resultPaymentMethods ?? []
                                    }
                                    this.setState({isLoading: true})
                                    updateCustomerCEM(savedCustomer).then(backCustomer => {
                                        this.setState({
                                            isLoading: false
                                        })
                                        onSuccess(backCustomer)
                                    })
                                }}
                            />
                        )
                    }}
                </FormSpy>
            </UIEngine>
        )
    }

    render(): React.ReactNode {
        const {addressDrawerVisible, formTitle, onClose} = this.props
        return (
            <>
                {addressDrawerVisible && (
                    <FormDrawer
                        className={PAYMENT_METHOD_DRAWER}
                        formTitle={formTitle}
                        formToRender={this.renderDrawerContent()}
                        onFormCancel={onClose}
                    />
                )}
            </>
        )
    }
}
