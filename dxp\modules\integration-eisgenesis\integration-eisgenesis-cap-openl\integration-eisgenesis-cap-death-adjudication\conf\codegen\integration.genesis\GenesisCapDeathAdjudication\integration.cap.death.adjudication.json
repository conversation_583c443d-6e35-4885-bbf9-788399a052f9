{"openapi": "3.0.1", "info": {"description": "Auto-generated OpenAPI schema from the OpenL rules", "title": "claim-life-death-adjudication", "version": "1.0.0"}, "servers": [{"url": "/claim-life-death-adjudication", "variables": {}}], "security": [{"BasicAuth": [], "SSOToken": []}], "paths": {"/_api_death_adjudication": {"post": {"description": "Rules method: org.openl.generated.beans.CapDeathSettlementResultEntity _api_death_adjudication(org.openl.generated.beans.CapDeathSettlementRulesInput request)", "operationId": "_api_death_adjudication", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapDeathSettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapDeathSettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapDeathSettlementResultEntity _api_death_adjudication(CapDeathSettlementRulesInput)"}}, "/_api_death_applicability": {"post": {"description": "Rules method: org.openl.generated.beans.CapDeathLossApplicabilityResult _api_death_applicability(org.openl.generated.beans.CapDeathLossApplicabilityInput request)", "operationId": "_api_death_applicability", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapDeathLossApplicabilityInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapDeathLossApplicabilityResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapDeathLossApplicabilityResult _api_death_applicability(CapDeathLossApplicabilityInput)"}}, "/_api_term_life_face_value_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimFaceValueCalculationResultEntity _api_term_life_face_value_calculation(org.openl.generated.beans.CapClaimWrapperFaceValueCalculationInput request)", "operationId": "_api_term_life_face_value_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperFaceValueCalculationInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimFaceValueCalculationResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimFaceValueCalculationResultEntity _api_term_life_face_value_calculation(CapClaimWrapperFaceValueCalculationInput)"}}}, "components": {"schemas": {"AccumulatorRemainingsEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorType": {"type": "string"}, "amountUnit": {"type": "string"}, "coverage": {"type": "string"}, "limitAmount": {"type": "number"}, "policyTerm": {"$ref": "#/components/schemas/Term"}, "remainingAmount": {"type": "number"}, "usedAmount": {"type": "number"}}}, "BaseLifeGrossBenefitAmount": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "totalApprovedAmount": {"$ref": "#/components/schemas/Money"}}}, "BaseLifePolicyCoverageLimitLevel": {"type": "object", "properties": {"limitLevelType": {"type": "string"}, "timePeriodCd": {"type": "string"}}}, "BaseLifeRelatedSettlmentInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "incidentDate": {"type": "string", "format": "date-time"}}}, "BaseLifeSettlementAccumulatorDetails": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorAmount": {"type": "number"}, "accumulatorAmountUnit": {"type": "string"}, "accumulatorCoverage": {"type": "string"}, "accumulatorCustomerUri": {"type": "string"}, "accumulatorExtension": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorPolicyUri": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time"}, "accumulatorType": {"type": "string"}}}, "BaseLifeSettlementAccumulatorDetailsExtension": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapAgeReductionDetailsEntity": {"type": "object", "properties": {"ageCd": {"type": "integer", "format": "int32"}, "reducedToCd": {"type": "number"}}}, "CapClaimFaceValueCalculationResultEntity": {"type": "object", "properties": {"result": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimFaceValueResultEntity"}}}}, "CapClaimFaceValueResultEntity": {"type": "object", "properties": {"calculatedFaceAmount": {"$ref": "#/components/schemas/Money"}, "claimCoveragePrefix": {"type": "string"}, "isAgeReductionUsed": {"type": "boolean"}, "originalFaceAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimWrapperCoverageInfoEntity": {"type": "object", "properties": {"ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "benefitStructures": {"type": "array", "items": {"$ref": "#/components/schemas/CapCoverageBenefitStructuresEntity"}}, "coverageCd": {"type": "string"}}}, "CapClaimWrapperFaceValueCalculationInput": {"type": "object", "properties": {"basicCoverageEarning": {"$ref": "#/components/schemas/Money"}, "basicTermLifeFaceValueAmount": {"$ref": "#/components/schemas/Money"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimWrapperCoverageInfoEntity"}}, "isMasterPolicy": {"type": "boolean"}, "memberAge": {"type": "integer", "format": "int32"}, "relationshipToInsuredCd": {"type": "string"}, "subjectOfClaimAge": {"type": "integer", "format": "int32"}, "voluntaryCoverageEarning": {"$ref": "#/components/schemas/Money"}, "voluntaryTermLifeFaceValueAmount": {"$ref": "#/components/schemas/Money"}}}, "CapCoverageBenefitStructuresEntity": {"type": "object", "properties": {"annualEarningsAmount": {"$ref": "#/components/schemas/Money"}, "approvedAmount": {"$ref": "#/components/schemas/Money"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitTypeCd": {"type": "string"}, "employeeAmtpct": {"type": "number"}, "roundingMethod": {"type": "string"}, "salaryMultiple": {"type": "number"}, "typeOfBenefitStructure": {"type": "string"}}}, "CapDeathCertInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapDeathSettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapDeathSettlementCoverageInfoEntity"}}}}, "CapDeathLossApplicabilityInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "eventDate": {"type": "string", "format": "date-time"}, "lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/CapLifeLossPolicyInfoEntity"}}}}, "CapDeathLossApplicabilityResult": {"type": "object", "properties": {"applicability": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}}}, "CapDeathMasterInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapDeathSettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapDeathSettlementCoverageInfoEntity"}}}}, "CapDeathSettlementAttrOptionsEntity": {"type": "object", "properties": {"attrName": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}}}, "CapDeathSettlementBenefitInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "approvedAmount": {"$ref": "#/components/schemas/Money"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "childBenefitAmount": {"$ref": "#/components/schemas/Money"}, "flatBenefitAmount": {"$ref": "#/components/schemas/Money"}, "incurralPeriod": {"type": "integer", "format": "int32"}, "maxBenefitAmount": {"$ref": "#/components/schemas/Money"}, "pctofEmployeeAmount": {"$ref": "#/components/schemas/Money"}, "reamingAmount": {"$ref": "#/components/schemas/Money"}, "spouseBenefitAmount": {"$ref": "#/components/schemas/Money"}, "timePeriodCd": {"type": "string"}}}, "CapDeathSettlementCoverageConfigEntity": {"type": "object", "properties": {"accidentInd": {"type": "boolean"}, "accumulationCategoryGroup": {"type": "string"}, "applicableLossTypes": {"type": "array", "items": {"type": "string"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/components/schemas/CapDeathSettlementAttrOptionsEntity"}}, "benefitCategory": {"type": "string"}, "calculationFormulaId": {"type": "string"}, "incurralPeriodUnit": {"type": "string"}, "limitLevels": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string"}}}, "CapDeathSettlementCoverageInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "applyBenefit": {"type": "string"}, "associatedItems": {"type": "string"}, "childOrganizedSportBenefitPct": {"type": "number"}, "coverageCd": {"type": "string"}, "coverageLimit": {"$ref": "#/components/schemas/Money"}, "coveredLoss": {"type": "string"}, "isChildOrganizedSportApplied": {"type": "boolean"}}}, "CapDeathSettlementDetailEntity": {"type": "object", "properties": {"_archived": {"type": "boolean"}, "_key": {"$ref": "#/components/schemas/EntityKey"}, "_modelName": {"type": "string"}, "_modelType": {"type": "string"}, "_modelVersion": {"type": "string"}, "_timestamp": {"type": "string"}, "_type": {"type": "string"}, "_version": {"type": "string"}, "approvedAmountOverride": {"$ref": "#/components/schemas/Money"}, "associatedInsurableRiskOid": {"type": "string"}, "benefitCd": {"type": "string"}, "claimant": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "eliminationPeriodOverrideEndDate": {"type": "string", "format": "date-time"}, "eliminationPeriodOverrideReason": {"type": "string"}, "expenseOldAmount": {"$ref": "#/components/schemas/Money"}, "expenseReserveAmount": {"$ref": "#/components/schemas/Money"}, "expenseReserveComments": {"type": "string"}, "expenseReserveReason": {"type": "string"}, "grossAmount": {"$ref": "#/components/schemas/Money"}, "incidentDate": {"type": "string", "format": "date-time"}, "indemnityReserveAmount": {"$ref": "#/components/schemas/Money"}, "indemnityReserveComments": {"type": "string"}, "indemnityReserveOldAmount": {"$ref": "#/components/schemas/Money"}, "indemnityReserveReason": {"type": "string"}, "isGrossAmountOverrided": {"type": "boolean"}, "maxBenefitOverridePeriod": {"$ref": "#/components/schemas/Period"}, "numberOfUnits": {"type": "integer", "format": "int32"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}, "recoveryOldAmount": {"$ref": "#/components/schemas/Money"}, "recoveryReserveAmount": {"$ref": "#/components/schemas/Money"}, "recoveryReserveComments": {"type": "string"}, "recoveryReserveReason": {"type": "string"}, "isCancelled": {"type": "boolean", "description": "Whether the settlement is cancelled"}, "cancelReason": {"type": "string", "description": "The settlement cancelled reason"}}}, "CapDeathSettlementLossInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "absence": {"$ref": "#/components/schemas/EntityLink"}, "accidentDate": {"type": "string", "format": "date-time"}, "age": {"type": "integer", "format": "int32"}, "claimType": {"type": "string"}, "coverageType": {"type": "string"}, "deathCertificateReceivedDate": {"type": "string", "format": "date-time"}, "isChildOrganizedSport": {"type": "boolean"}, "lastWorkDate": {"type": "string", "format": "date-time"}, "lossDateTime": {"type": "string", "format": "date-time"}, "lossItems": {"type": "array", "items": {"type": "string"}}, "lossType": {"type": "string"}, "officialDeathDate": {"type": "string", "format": "date-time"}, "offsets": {"$ref": "#/components/schemas/CapDeathSettlementOffsetEntity"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}, "relationshipToInsuredCd": {"type": "string"}}}, "CapDeathSettlementOffsetEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "isPrePostTax": {"type": "boolean"}, "offsetTerm": {"$ref": "#/components/schemas/Term"}, "offsetType": {"type": "string"}, "proratingRate": {"type": "string"}}}, "CapDeathSettlementPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "deathCertInfo": {"$ref": "#/components/schemas/CapDeathCertInfoEntity"}, "deathMasterInfo": {"$ref": "#/components/schemas/CapDeathMasterInfoEntity"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "terminationAge": {"type": "integer", "format": "int64"}, "txEffectiveDate": {"type": "string"}}}, "CapDeathSettlementResultEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/components/schemas/Money"}, "autoAdjudicatedDuration": {"type": "number"}, "benefitCd": {"type": "string"}, "claimCoverageName": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityEvaluationCd": {"type": "string"}, "grossBenefitAmount": {"$ref": "#/components/schemas/BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time"}, "isAutoAdjudicated": {"type": "boolean"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}}, "CapDeathSettlementRulesInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/components/schemas/AccumulatorRemainingsEntity"}}, "claimCoverageName": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "coverageConfiguration": {"$ref": "#/components/schemas/CapDeathSettlementCoverageConfigEntity"}, "currentDateTime": {"type": "string", "format": "date-time"}, "dateOfDiagnosis": {"type": "string", "format": "date-time"}, "details": {"$ref": "#/components/schemas/CapDeathSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/components/schemas/CapDeathSettlementLossInfoEntity"}, "policy": {"$ref": "#/components/schemas/CapDeathSettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeRelatedSettlmentInfo"}}, "settlement": {"$ref": "#/components/schemas/EntityLink"}, "wrapperInfo": {"$ref": "#/components/schemas/CapDeathSettlementWrapperInfoEntity"}}}, "CapDeathSettlementWrapperInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "claimSubjectId": {"type": "string"}, "claimWrapperIdentification": {"$ref": "#/components/schemas/EntityLink"}, "memberRegistryTypeId": {"type": "string"}}}, "CapInsuredInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "isMain": {"type": "boolean"}, "registryTypeId": {"type": "string"}}}, "CapLifeLossPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string", "format": "date-time"}}}, "EntityKey": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string"}}}, "EntityLink": {"type": "object", "properties": {"_uri": {"type": "string"}}}, "JAXRSErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["USER_ERROR", "RULES_RUNTIME", "COMPILATION", "SYSTEM", "BAD_REQUEST", "VALIDATION"]}, "message": {"type": "string"}}}, "JAXRSUserErrorResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "MessageType": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "source": {"type": "string"}}}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}}}, "Period": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}}, "Term": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"BasicAuth": {"description": "Genesis Basic credentials", "scheme": "basic", "type": "http"}, "SSOToken": {"description": "Genesis Authorization header. Value format: `[Token {token}]`", "in": "header", "name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}