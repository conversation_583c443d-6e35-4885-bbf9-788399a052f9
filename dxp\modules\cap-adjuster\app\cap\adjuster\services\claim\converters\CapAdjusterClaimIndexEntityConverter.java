/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.converters;

import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.ClaimClaimIndexEntityDTO;

import cap.adjuster.services.claim.dto.CapAdjusterClaimIndex;
import core.services.converters.CommonDTOConverter;

public class CapAdjusterClaimIndexEntityConverter<I extends ClaimClaimIndexEntityDTO, A extends CapAdjusterClaimIndex>
        extends CommonDTOConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.caseSystemId = intDTO.caseSystemId;
        apiDTO.lastName = intDTO.lastName;
        apiDTO.workQueueCd = intDTO.workQueueCd;
        apiDTO.rootId = intDTO.rootId;
        apiDTO.city = intDTO.city;
        apiDTO.postalCode = intDTO.postalCode;
        apiDTO.policyNumber = intDTO.policyNumber;
        apiDTO.legalName = intDTO.legalName;
        apiDTO.workUserId = intDTO.workUserId;
        apiDTO.state = intDTO.state;
        apiDTO.email = intDTO.email;
        apiDTO.caseLossNumber = intDTO.caseLossNumber;
        apiDTO.workStatusCd = intDTO.workStatusCd;
        apiDTO.stateProvinceCd = intDTO.stateProvinceCd;
        apiDTO.legalId = intDTO.legalId;
        apiDTO.claimSystemId = intDTO.claimSystemId;
        apiDTO.customerNumber = intDTO.customerNumber;
        apiDTO.workCaseId = intDTO.workCaseId;
        apiDTO.lossModelName = intDTO.lossModelName;
        apiDTO.claimLossNumber = intDTO.claimLossNumber;
        apiDTO.firstName = intDTO.firstName;
        apiDTO.phoneNumber = intDTO.phoneNumber;
        apiDTO.lossDate = intDTO.lossDate;
        apiDTO.streetAddress = intDTO.streetAddress;
        apiDTO.capPolicyId = intDTO.capPolicyId;
        apiDTO.taxId = intDTO.taxId;
        return apiDTO;
    }
}
