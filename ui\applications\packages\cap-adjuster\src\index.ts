/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {routing} from '@eisgroup/react-routing'
import {LocalizationUtils} from '@eisgroup/i18n'
import {resources} from './i18n'
import {ClaimLossListViewLoader} from './claim-loss-list/ClaimLossListView'

LocalizationUtils.addResourceBundles(resources)

export const definition: routing.ViewModuleDefinition = {
    entryPointComponent: ClaimLossListViewLoader,
    views: [
        {
            url: 'claim-loss',
            component: ClaimLossListViewLoader
        },
        {
            url: 'dashboard',
            component: ClaimLossListViewLoader
        }
    ]
}
