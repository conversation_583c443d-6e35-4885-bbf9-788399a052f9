/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.disability;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import cap.adjuster.services.disability.CapAdjusterDisabilityService;
import cap.adjuster.services.disability.dto.CapStdSettlement;
import cap.adjuster.services.financial.dto.CapPayment;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterDisabilityApiController.TAG_API_CAP_ADJUSTER_DISABILITY,
                description = "CAP Adjuster: STD Loss API")})
@Api(value = CapAdjusterDisabilityApiController.TAG_API_CAP_ADJUSTER_DISABILITY,
        tags = CapAdjusterDisabilityApiController.TAG_API_CAP_ADJUSTER_DISABILITY)
public class CapAdjusterDisabilityApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_DISABILITY = "/cap-adjuster/v1/losses-std";

    private CapAdjusterDisabilityService disabilityService;

    /**
     * Get payments for std loss
     *
     * @param rootId disability loss identifier
     * @param revisionNo disability loss revision number
     * @return list of payments related to disability loss
     */
    @ApiOperation(value = "Get payments for disability loss",
            response = CapPayment.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getPayments(@ApiParam(value = "Std loss identifier", required = true) String rootId,
                                              @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(disabilityService.getPayments(rootId, revisionNo));
    }

    /**
     * Get settlements for std loss
     *
     * @param rootId disability loss identifier
     * @param revisionNo disability loss revision number
     * @return list of settlements  related to disability loss
     */
    @ApiOperation(value = "Get settlements for disability loss",
            response = CapStdSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Std loss identifier", required = true) String rootId,
                                               @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(disabilityService.getSettlements(rootId, revisionNo));
    }

    @Inject
    public void setDisabilityService(CapAdjusterDisabilityService disabilityService) {
        this.disabilityService = disabilityService;
    }
}