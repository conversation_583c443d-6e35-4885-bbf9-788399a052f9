Common.projectSettings

lazy val integrationEisGenesisCapLifeCoverages = project.in(file("integration-eisgenesis-cap-life-coverages"))
lazy val integrationEisGenesisCapHiAdjudication = project.in(file("integration-eisgenesis-cap-hi-adjudication"))
lazy val integrationEisGenesisCapCiAdjudication = project.in(file("integration-eisgenesis-cap-ci-adjudication"))
lazy val integrationEisGenesisCapDeathAdjudication = project.in(file("integration-eisgenesis-cap-death-adjudication"))
lazy val integrationEisGenesisCapAccAdjudication = project.in(file("integration-eisgenesis-cap-acc-adjudication"))
lazy val integrationEisGenesisCapAcceleratedAdjudication = project.in(file("integration-eisgenesis-cap-accelerated-adjudication"))

lazy val integrationEisGenesisCapOpenl = project.in(file("."))
  .enablePlugins(PlayMinimalJava)
  .aggregate(
    integrationEisGenesisCapLifeCoverages,
    integrationEisGenesisCapHiAdjudication,
    integrationEisGenesisCapCiAdjudication,
    integrationEisGenesisCapDeathAdjudication,
    integrationEisGenesisCapAccAdjudication,
    integrationEisGenesisCapAcceleratedAdjudication
  )
  .dependsOn(
    integrationEisGenesisCapLifeCoverages,
    integrationEisGenesisCapHiAdjudication,
    integrationEisGenesisCapCiAdjudication,
    integrationEisGenesisCapDeathAdjudication,
    integrationEisGenesisCapAccAdjudication,
    integrationEisGenesisCapAcceleratedAdjudication
  )
