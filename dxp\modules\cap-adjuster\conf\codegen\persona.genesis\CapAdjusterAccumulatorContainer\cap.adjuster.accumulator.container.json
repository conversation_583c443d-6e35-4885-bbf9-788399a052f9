{"swagger": "2.0", "x-dxp-spec": {"imports": {"accumulator.container": {"schema": "integration.cap.accumulator.container.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Accumulator Container API", "version": "1", "title": "CAP Adjuster: Accumulator Container API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/accumulator-containers", "description": "CAP Adjuster: Accumulator Container API"}], "paths": {"/accumulator-containers/retrieve": {"post": {"summary": "Load accumulators by policyURI and customerURI", "x-dxp-path": "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/accumulator/loadAccumulators", "tags": ["/cap-adjuster/v1/accumulator-containers"]}}, "/accumulator-containers/search-by-link": {"post": {"summary": "Return all cap accumulator records for given links", "x-dxp-path": "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/link/", "tags": ["/cap-adjuster/v1/accumulator-containers"]}}}}