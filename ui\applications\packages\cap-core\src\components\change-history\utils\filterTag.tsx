/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {isEqual, noop} from 'lodash'
import {Tag} from '@eisgroup/ui-kit'
import {t} from '@eisgroup/i18n'
import {dateUtils} from '@eisgroup/cap-services'
import {
    CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_CONTAINER,
    CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_NAME,
    CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_VALUE_TAG
} from '../../../common/package-class-names'
import {tableFiltersProps, tableFiltersTitleReference} from './types'

interface filterTagProps {
    tableFilters: tableFiltersProps
    handleReset: (clearFilters: () => void, dataIndex: string) => void
}
export const FilterTag: React.FunctionComponent<filterTagProps> = ({tableFilters, handleReset}) => {
    const clearFiltersData = (dataIndex: string) => {
        handleReset(noop, dataIndex)
    }
    return (
        <div>
            {tableFilters &&
                Object.keys(tableFilters).map((v: string) => {
                    const filtersValue = tableFilters[v]
                    if (!(filtersValue && !isEqual([], filtersValue))) {
                        return null
                    }
                    if (v === 'date') {
                        return (
                            <Tag
                                key={v}
                                className={CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_CONTAINER}
                                closable
                                onClose={() => clearFiltersData(v)}
                            >
                                <span className={CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_NAME}>
                                    {t(tableFiltersTitleReference[v])}
                                </span>
                                <Tag className={CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_VALUE_TAG}>
                                    {dateUtils(filtersValue[0]).render}-{dateUtils(filtersValue[1]).render}
                                </Tag>
                            </Tag>
                        )
                    }
                    return (
                        <Tag
                            key={v}
                            className={CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_CONTAINER}
                            closable
                            onClose={() => clearFiltersData(v)}
                        >
                            <span className={CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_NAME}>
                                {t(tableFiltersTitleReference[v])}
                            </span>
                            {filtersValue.map((m: string) => (
                                <Tag key={m} className={CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_VALUE_TAG}>
                                    {m}
                                </Tag>
                            ))}
                        </Tag>
                    )
                })}
        </div>
    )
}
