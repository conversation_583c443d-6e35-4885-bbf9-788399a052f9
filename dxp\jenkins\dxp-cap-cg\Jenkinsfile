@Library("ci-config@master")
@Library("ci-library@master")
import PodTemplateGenerator

pipeline {   
    parameters {
        string(name: 'TARGET_BRANCH', defaultValue: '', description: 'GitLab MR target branch')
        string(name: 'SOURCE_BRANCH', defaultValue: '', description: 'GitLab MR source branch')
    }

    options {
        skipStagesAfterUnstable()
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        timeout(time: 2, unit: 'HOURS')
    }

    agent {
        kubernetes {
            defaultContainer 'sbt'
            yaml new PodTemplateGenerator(this)
                .kind(templateKind: 'sbt-maven21')
                .generate()
        }
    }

    stages {
        stage('Merge branches'){
            steps {
                script {
                    sh "git checkout $TARGET_BRANCH && git merge origin/$SOURCE_BRANCH"
                }
            }
        }
        stage('Build') {
            steps {
                container('sbt') {
                    sh "cd dxp && sbt -Djava.io.tmpdir=\$HOME -Dsbt.log.noformat=true clean compile dist"
                }
            }
        }
        /*stage('Publish docker image') {
            steps {
                script {
                    env.GIT_COMMIT_SHORT = sh(script: "printf \$(git rev-parse --short ${GIT_COMMIT})", returnStdout: true)
                    image.build(dockerfile: './dxp', name: 'dxp-cap', tag: "$GIT_COMMIT_SHORT", registry: 'staging', push: true)
                }
            }
        }
        stage('Autotests') {
            steps {
                container('maven') {
                    script {
                        // Sets job description in a form of link to test analytics site with ETCS results
                        setTaJobDescription(true)
                        // Defines a ReleaseAssembly class object that refers to ReleaseAssembly repo
                        def releases = new ReleaseAssembly(this)

                        templates.create(input: 'dxp/jenkins/dxp-cap/lib/pom.template.xml', output: 'cg.pom.xml', bindings: [
                            releases.getNewestVersion(dxpProjectVersion(project: 'dxp')), 
                            [        
                                platform_version: "24.0.0",   // frunner version, can be updated
                                dxp_member_version: "$GIT_COMMIT_SHORT",
                                etcs_dir: "dxp/ref-impl/automation"
                            ]
                        ])
                    }
                    sh "mvn test -B -f cg.pom.xml -e"
                }
            }
        }*/

    }
}
