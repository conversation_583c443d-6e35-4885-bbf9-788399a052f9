import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "SELECT_INPUT",
      "props": {
        "md": 12,
        "name": "overpaymentWaive",
        "label": "cap-core:balance_actions_drawer_waive_overpayment_label",
          "field": {
              "validations": [
                  {
                      "skipOnEmpty": false,
                      "type": "required-named",
                      "fieldValue": {
                          "type": "value",
                          "valueType": "string",
                          "value": "cap-core:balance_actions_drawer_waive_overpayment_label"
                      }
                  },
                  {
                      "skipOnEmpty": false,
                      "type": "required"
                  }
              ]
          },
        "options": [],
        "events": [
          {
            "id": "dde1119c-240e-4529-8823-ad6d9e4639b4",
            "dispatchEventProperty": "onChange",
            "eventName": "OVERPAYMENT_CHANGED"
          }
        ]
      },
      "id": "b1beed92-7c35-4405-94d2-1fa3a7b657a6"
    },
    {
      "type": "MONEY_INPUT",
      "props": {
        "md": 12,
        "name": "overpaymentWaiveAmount",
        "label": "cap-core:balance_actions_drawer_waive_overpayment_amount_label",
        "allowDecimal": true,
        "currencyDisplay": "symbol",
        "initialAmount": null,
        "field": {
          "validations": [
            {
              "skipOnEmpty": false,
              "type": "required-named",
              "fieldValue": {
                "type": "value",
                "valueType": "string",
                "value": "Waive Overpayment Amount"
              }
            },
            {
              "skipOnEmpty": false,
              "type": "required"
            }
          ]
        },
        "disabled": true
      },
      "id": "d4e9109f-7955-4101-aeb8-3fc6df240853"
    },
    {
      "type": "TEXT_AREA",
      "props": {
        "md": 24,
        "name": "comment",
        "label": "cap-core:balance_actions_drawer_reason_label",
        "autosizeItem": true,
        "autosize": {
          "minRows": 6,
          "maxRows": 6
        },
        "field": {
          "validations": [
            {
              "skipOnEmpty": false,
              "type": "required-named",
              "fieldValue": {
                "type": "value",
                "valueType": "string",
                "value": "cap-core:balance_actions_drawer_reason_label"
              }
            },
            {
              "skipOnEmpty": false,
              "type": "required"
            }
          ]
        }
      },
      "id": "ea282f35-d1d8-4821-85bb-439340185db8"
    }
  ],
  "version": 28,
  "actionChains": {
    "OVERPAYMENT_CHANGED": {
      "type": "API",
      "apiSettings": {
        "method": "onOverpaymentChanged"
      },
      "nestedActions": {}
    }
  }
}

export default config;
