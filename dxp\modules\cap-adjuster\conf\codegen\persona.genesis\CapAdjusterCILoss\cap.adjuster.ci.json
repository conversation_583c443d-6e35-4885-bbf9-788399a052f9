{"swagger": "2.0", "x-dxp-spec": {"imports": {"accelerated": {"schema": "integration.cap.ci.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Critical Illness Loss API", "version": "1", "title": "CAP Adjuster: Critical Illness Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-ci", "description": "CAP Adjuster: Critical Illness Loss API"}], "paths": {"/losses-ci/{rootId}/{revisionNo}": {"get": {"summary": "Get critical illness loss by rootId and revisionNo", "x-dxp-path": "/api/caploss/CILoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-ci"]}}, "/losses-ci/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for critical illness loss", "x-dxp-path": "/api/caploss/CILoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-ci"]}}, "/losses-ci": {"post": {"summary": "Create critical illness loss", "x-dxp-path": "/api/caploss/CILoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-ci"]}, "put": {"summary": "Update critical illness loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CILoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-ci"]}}, "/losses-ci/draft": {"post": {"summary": "Init critical illness loss", "x-dxp-path": "/api/caploss/CILoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-ci"]}}}}