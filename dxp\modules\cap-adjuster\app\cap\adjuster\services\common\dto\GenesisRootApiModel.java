/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

public class GenesisRootApiModel extends GenesisApiModel {

    @JsonProperty("_modelName")
    @ApiModelProperty(required = true)
    public String modelName;

    @JsonProperty("_modelVersion")
    public String modelVersion;

    @JsonProperty("_timestamp")
    public String timestamp;
}
