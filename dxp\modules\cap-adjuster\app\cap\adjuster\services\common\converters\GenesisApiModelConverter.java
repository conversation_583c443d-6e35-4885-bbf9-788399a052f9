/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.converters;

import cap.adjuster.services.common.dto.GenesisApiModel;
import cap.adjuster.services.common.dto.GenesisApiModelKey;
import core.services.converters.CommonDTOConverter;
import dataproviders.common.dto.GenesisDTO;
import dataproviders.common.dto.GenesisEntityKeyDTO;

import javax.inject.Inject;

public class GenesisApiModelConverter<I extends GenesisDTO, A extends GenesisApiModel> extends CommonDTOConverter<I, A> {

    private GenesisApiModelKeyConverter<GenesisEntityKeyDTO, GenesisApiModelKey> apiModelKeyConverter;

    @Override
    public I convertToInternalDTO(A apiDTO, I intDTO) {
        intDTO.key = apiModelKeyConverter.convertToInternalDTO(apiDTO.key);
        intDTO.gentityType = apiDTO.gentityType;

        return intDTO;
    }

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO.key = apiModelKeyConverter.convertToApiDTO(intDTO.key);
        apiDTO.gentityType = intDTO.gentityType;

        return apiDTO;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setApiModelKeyConverter(GenesisApiModelKeyConverter apiModelKeyConverter) {
        this.apiModelKeyConverter = apiModelKeyConverter;
    }
}
