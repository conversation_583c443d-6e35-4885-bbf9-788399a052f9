/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.specialHandling;


import cap.adjuster.services.specialHandling.CapAdjusterSpecialHandlingService;
import cap.adjuster.services.specialHandling.dto.CapGenericSpecialHandling;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterSpecialHandlingApiController.TAG_API_CAP_ADJUSTER_SPECIAL_HANDLING,
                description = "CAP Adjuster: Special handling API")})
@Api(value = CapAdjusterSpecialHandlingApiController.TAG_API_CAP_ADJUSTER_SPECIAL_HANDLING,
        tags = CapAdjusterSpecialHandlingApiController.TAG_API_CAP_ADJUSTER_SPECIAL_HANDLING)
public class CapAdjusterSpecialHandlingApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_SPECIAL_HANDLING = "/cap-adjuster/v1/special-handling";

    private CapAdjusterSpecialHandlingService specialHandlingService;

    /**
     * Get special handling associated with claim
     *
     * @param rootId     claim identifier
     * @param revisionNo claim revision number
     * @param modelName  model name
     * @return list of special handling related to claim
     */
    @ApiOperation(value = "Get special handling associated with claim ",
            response = CapGenericSpecialHandling.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSpecialHandling(@ApiParam(value = "Claim Identifier", required = true) String rootId,
                                                      @ApiParam(value = "Claim Revision Number", required = true) Integer revisionNo,
                                                      @ApiParam(value = "Model Name", required = true) String modelName) {
        return completeOk(specialHandlingService.getSpecialHandling(rootId, revisionNo, modelName));
    }

    /**
     * Get all special handling associated with event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     * @return list of special handling related to claim and absence
     */
    @ApiOperation(value = "Get all special handling associated with event case",
            response = CapGenericSpecialHandling.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getAllSpecialHandlingInCase(@ApiParam(value = "Event Case Identifier", required = true) String rootId,
                                                      @ApiParam(value = "Event Case Revision Number", required = true) Integer revisionNo) {
        return completeOk(specialHandlingService.getAllSpecialHandlingInCase(rootId, revisionNo));
    }


    @Inject
    public void setSpecialHandlingService(CapAdjusterSpecialHandlingService specialHandlingService) {
        this.specialHandlingService = specialHandlingService;
    }

}
