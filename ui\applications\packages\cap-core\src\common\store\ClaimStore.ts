/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {
    CapAdjusterClaimWrapperCapClaimWrapperUpdateInput,
    CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity,
    CapLifeClaimSettlement
} from '@eisgroup/cap-gateway-client'
import {BusinessTypes} from '@eisgroup/cap-event-case-models'
import {IObservableArray, ObservableMap} from 'mobx'
import {
    FollowUpTask,
    FollowUpTaskPublishResponse,
    IndividualCustomer,
    LossParams,
    CapGenericLoss,
    ClaimParty
} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {BaseRootStore} from './BaseRootStore'
import {CapEventCaseEntity, ManagersInfo} from '../Types'
import CapLoss = BusinessTypes.CapLoss
import {QueueStore} from './QueueStore'

export interface SubjectOfClaimsInfo {
    [key: string]: IndividualCustomer
}
export interface CoveragesInfo {
    [key: string]: CapLifeClaimSettlement[]
}

export interface ClaimStore extends BaseRootStore {
    queueStore: QueueStore
    claims: CapGenericLoss[]
    claimsManagersInfo: ObservableMap<string, string | undefined>
    subjectOfClaimsInfo?: SubjectOfClaimsInfo
    coveragesInfo?: CoveragesInfo
    managersInfo: ManagersInfo
    loadClaims: (params: LossParams, claimAmount?: number) => RxResult<CapGenericLoss[]>
    setClaims: (claims: CapGenericLoss[]) => void
    getClaimManagerInfo: (entityURIs: string[]) => void
    getClaimManagersInfo: (claims: CapGenericLoss[]) => void
    getClaimManagerUserInformation: (userId: string) => void
    getClaimManagerQueueInformation: (queueCd: string[]) => void
    getSubjectOfClaimInfo: (claims: CapGenericLoss[]) => void
    searchClaimHeaders: (caseLossRootId: string) => RxResult<CapLoss[]>
    updateClaimWrapper: (
        requestBody: CapAdjusterClaimWrapperCapClaimWrapperUpdateInput
    ) => RxResult<CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity>
    loadCoverages: ({rootId, revisionNo}: LossParams, claimRootId: string) => void
    publishFollowUpTask: (task: FollowUpTask) => RxResult<FollowUpTaskPublishResponse>
    loadClaimsWithAction: (eventCase: CapEventCaseEntity, claimAmount?: number) => void
    allClaimsBeneficiaries: IObservableArray<ClaimParty>
}
