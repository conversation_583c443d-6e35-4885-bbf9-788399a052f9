/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {
    constructEmployeeRequestBody,
    constructEventCaseBody,
    eventCaseService,
    EventCaseWithEmployeeRequest,
    EventCaseWithEmployeeResponse,
    IndividualCustomer,
    LossParams
} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {StepStatus} from '@eisgroup/react-components'
import {RoutingUtils} from '@eisgroup/react-routing'
import {action, computed, observable, runInAction, toJS} from 'mobx'
import {FormDrawerStore, FormDrawerStoreImpl} from '../../../components/form-drawer'
import {LOAD_EVENT_CASE} from '../../constants'
import {DefaultQuery, EmployeeFormTypes, EventCaseIntakeWizardStepKey} from '../../Types'
import {createInitialEventCase, generateLossToSaveRequest, groupAddOrUpdateRelationship} from '../../utils'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {ClaimPartyStore, ClaimPartyStoreImpl} from '../ClaimPartyStore'
import {EmploymentStore} from '../EmploymentStore'
import {EventCaseStore} from '../EventCaseStore'
import {EmploymentStoreImpl} from './EmploymentStoreImpl'
import {EventCaseStoreImpl} from './EventCaseStoreImpl'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity

export const CREATE_NEW_CASE = 'createNewCase'

export class IntakeStoreImpl extends BaseRootStoreImpl {
    claimPartyStore: ClaimPartyStore

    formDrawerStore: FormDrawerStore

    employmentStore: EmploymentStore

    eventCaseStore: EventCaseStore

    @observable activeStep: EventCaseIntakeWizardStepKey = EventCaseIntakeWizardStepKey.Member

    @observable stepsStatuses: Record<string, StepStatus> = {}

    @observable isQuarterlyPriorEarnings = false

    @observable urlParams?: DefaultQuery

    @observable eventCaseAfterInvokeDraft?: CapEventCaseEntity = createInitialEventCase()

    constructor() {
        super()
        this.claimPartyStore = new ClaimPartyStoreImpl()
        this.formDrawerStore = new FormDrawerStoreImpl()
        this.eventCaseStore = new EventCaseStoreImpl(this)
        this.employmentStore = new EmploymentStoreImpl(this.eventCaseStore)
        this.eventCaseStore.updateEventCase(createInitialEventCase())
    }

    @computed
    public get eventCase(): any {
        return this.eventCaseStore.eventCase || ({} as CapEventCaseEntity)
    }

    @action
    setUrlParams = (urlParams?: DefaultQuery) => {
        this.urlParams = urlParams
    }

    @action
    setCaseDetailClaimEvents = (claimEvents: string[]) => {
        this.eventCaseStore.updateEventCase({
            ...this.eventCase,
            lossDetail: {
                ...this.eventCase.lossDetail,
                claimEvent: {
                    ...this.eventCase.lossDetail?.claimEvent,
                    claimEvents
                }
            }
        })
    }

    @action
    loadEventCaseForEnterIntakePage = (lossParams: LossParams) => {
        return this.call<CapEventCaseEntity>(() => eventCaseService.loadEventCase(lossParams, true), LOAD_EVENT_CASE)
    }

    @action
    setEmployeeStepData = (values: any) => {
        const store = this.employmentStore
        store.employee = values.employee
        this.eventCaseStore.updateEventCase(values.eventCase)
        if (store.employeeFormType === EmployeeFormTypes.AddEmployee) {
            store.selectEmployee(values.employee)
        }
    }

    @action
    updateActiveStepStatus = (stepStatus: StepStatus) => {
        this.stepsStatuses = {
            ...this.stepsStatuses,
            [this.activeStep]: stepStatus
        }
    }

    saveEmployee = (): Promise<IndividualCustomer> => {
        return this.employmentStore.employeeFormType === EmployeeFormTypes.AddEmployee
            ? this.employmentStore.saveUpdateEmployee()
            : Promise.resolve(this.employmentStore.employee!)
    }

    initEventCase = (employee: IndividualCustomer): RxResult<CapEventCaseEntity> => {
        const lossToSave = generateLossToSaveRequest(this.eventCase, employee)
        return eventCaseService.initEventCase(toJS(lossToSave))
    }

    /**
     * Subsequently saves parties from loss's absence reasons to cem,
     * updates employee with new related parties, updates eventCase entity,
     * saves parties to cap as party roles with type PARTICIPANT.
     * Each step adds results to context object, which is passed to next step.
     * @param data eventCase and employee data to be saved
     * @return response with saved data
     */
    @action
    updateEventCaseWithEmployee = async (
        data: EventCaseWithEmployeeRequest
    ): Promise<EventCaseWithEmployeeResponse> => {
        const absenceReasonParties = await eventCaseService.saveAbsenceReasonParties(
            data.absenceReasonParties!,
            data.employee
        )
        const contextAfterEmployeeUpdate = await this.updateEmployee(data, {absenceReasonParties})
        const contextAfterEventCaseUpdate = await this.updateEventCase(data, contextAfterEmployeeUpdate)
            .map(e => e.get())
            .toPromise()
        return this.promiseCall(() => this.createOrUpdateRelationshipToCem(data, contextAfterEventCaseUpdate))
    }

    createOrUpdateRelationshipToCem = async (
        data: EventCaseWithEmployeeRequest,
        context: EventCaseWithEmployeeResponse
    ): Promise<EventCaseWithEmployeeResponse> => {
        const {eventCase} = context
        const relationships = await this.eventCaseStore.getCustomerRelationships(
            this.employmentStore.employee?._key?.rootId!
        )
        const {needAddRelationshipParams, needUpdateRelationshipParams} = groupAddOrUpdateRelationship(
            this.eventCaseAfterInvokeDraft,
            context.eventCase,
            relationships
        )
        if (needAddRelationshipParams?.length > 0) {
            needAddRelationshipParams.forEach(addParams => {
                this.eventCaseStore.createCustomerRelationship(addParams)
            })
        }
        if (needUpdateRelationshipParams?.length > 0) {
            needUpdateRelationshipParams.forEach(updateParams => {
                this.eventCaseStore.updateCustomerRelationship(updateParams)
            })
        }
        return {
            ...context,
            eventCase
        }
    }

    updateEventCase = (
        data: EventCaseWithEmployeeRequest,
        context: EventCaseWithEmployeeResponse
    ): RxResult<EventCaseWithEmployeeResponse> => {
        return this.eventCaseStore
            .updateEventCaseDraftByApi(
                constructEventCaseBody(data, context.absenceReasonParties, this.eventCaseAfterInvokeDraft)
            )
            .map(either =>
                either.map(eventCase => {
                    runInAction(() => {
                        this.eventCaseAfterInvokeDraft = eventCase
                    })
                    this.eventCaseStore.updateEventCase(eventCase)
                    return {
                        ...context,
                        eventCase
                    }
                })
            )
    }

    @action
    updateEmployee = async (
        data: EventCaseWithEmployeeRequest,
        context: EventCaseWithEmployeeResponse
    ): Promise<EventCaseWithEmployeeResponse> => {
        this.employmentStore.employee = constructEmployeeRequestBody(data, context.absenceReasonParties)
        const employee = await this.employmentStore.saveUpdateEmployee()
        return {
            ...context,
            employee
        }
    }

    @action
    changeStep = (key: EventCaseIntakeWizardStepKey) => {
        this.activeStep = key
    }

    @action
    openNewCase = () => {
        const request = {
            _key: this.eventCase._key
        }

        this.callService<CapEventCaseEntity>(
            eventCaseService.createEventCase(request),
            response => {
                const {
                    _modelName,
                    _key: {rootId, revisionNo}
                } = response
                const urlParamsString = Object.keys(this.urlParams || {})
                    .map(v => {
                        return `${v}=${(this.urlParams || {})[v]}`
                    })
                    .join('&')
                const paramString = urlParamsString ? `&${urlParamsString}` : ''
                RoutingUtils.goTo(`${_modelName}/${rootId}/${revisionNo}?${paramString}`)
            },
            CREATE_NEW_CASE
        )
    }
}
