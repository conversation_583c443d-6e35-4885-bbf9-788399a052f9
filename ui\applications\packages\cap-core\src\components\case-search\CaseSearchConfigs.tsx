import {ColumnProps} from '@eisgroup/ui-kit'
import {Link} from 'react-router'
import {t} from '@eisgroup/i18n'
import React from 'react'
import {LookupLabel} from '@eisgroup/react-components'
import {DisabilityClaimTypesMap} from '../../common/constants'
import {CaseStates} from '../../common/Types'
import {SubjectOfClaimInfo} from '../cases-claims-table'
import {AdvanceSearchFilterType, CaseSearchResultType} from './types'

export type GetAdvanceSearchFilterType = () => AdvanceSearchFilterType[]

export const getAdvanceSearchFilters: GetAdvanceSearchFilterType = () => {
    return [
        {
            name: 'caseNumber',
            labelKey: 'cap-core:case_relationship_drawer_case_search_caseNo',
            placeholder: 'cap-core:case_relationship_drawer_case_search_caseNo'
        },
        {
            name: 'firstName',
            labelKey: 'cap-core:case_relationship_drawer_case_search_firstName',
            placeholder: 'cap-core:case_relationship_drawer_case_search_firstName'
        },
        {
            name: 'lastName',
            labelKey: 'cap-core:case_relationship_drawer_case_search_lastName',
            placeholder: 'cap-core:case_relationship_drawer_case_search_lastName'
        },
        {
            name: 'customerNumberClaim',
            labelKey: 'cap-core:case_relationship_drawer_case_search_customerNo',
            placeholder: 'cap-core:case_relationship_drawer_case_search_customerNo'
        },
        {
            name: 'stateProvinceCd',
            labelKey: 'cap-core:case_relationship_drawer_case_search_state',
            placeholder: 'cap-core:case_relationship_drawer_case_search_state'
        },
        {
            name: 'city',
            labelKey: 'cap-core:case_relationship_drawer_case_search_city',
            placeholder: 'cap-core:case_relationship_drawer_case_search_city'
        },
        {
            name: 'postalCode',
            labelKey: 'cap-core:case_relationship_drawer_case_search_zipCode',
            placeholder: 'cap-core:case_relationship_drawer_case_search_zipCode'
        },
        {
            name: 'policyNumberClaim',
            labelKey: 'cap-core:case_relationship_drawer_case_search_policyNo',
            placeholder: 'cap-core:case_relationship_drawer_case_search_policyNo'
        },
        {
            name: 'taxId',
            labelKey: 'cap-core:case_relationship_drawer_case_search_taxId',
            placeholder: 'cap-core:case_relationship_drawer_case_search_taxId'
        },
        {
            name: 'legalName',
            labelKey: 'cap-core:case_relationship_drawer_case_search_businessName',
            placeholder: 'cap-core:case_relationship_drawer_case_search_businessName'
        }
    ]
}

export const getSearchResultColumns = <T extends CaseSearchResultType>(): ColumnProps<T>[] => {
    const splitArray = (arr, subGroupLength: number) => {
        const result = [] as string[][]
        for (let i = 0; i < arr.length; i += subGroupLength) {
            result.push(arr.slice(i, i + subGroupLength))
        }
        return result
    }
    return [
        {
            title: t('cap-core:cases-claims-table-columns-case-claim-number'),
            key: 'caseClaimNumber',
            dataIndex: 'caseClaimNumber',
            sorter: true,
            render: (text, record, index) => {
                let url = ''
                const {rootId, revisionNo} = record._key
                if (record.claimNumber) {
                    if (Object.keys(DisabilityClaimTypesMap).includes(record.claimType)) {
                        url = `/${record.claimModelName}/${rootId}/${revisionNo}`
                    } else {
                        url = `/claim/claim-overview/${rootId}/${revisionNo}`
                    }
                } else if (record.state === CaseStates.INCOMPLETE) {
                    url = `/event-case/intake?rootId=${rootId}&revisionNo=${revisionNo}`
                } else {
                    url = `/CapEventCase/${rootId}/${revisionNo}`
                }
                return <Link to={url}>{record.claimNumber || record.caseNumber}</Link>
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-policy-number'),
            key: 'policyNumber',
            dataIndex: 'policyNumber',
            render: (text, record, index) => {
                const policyNumbers = record.policies?.map(policy => policy.policyNumber)
                if (Array.isArray(policyNumbers)) {
                    const splitPolicyNumbers = splitArray(policyNumbers, 4)
                    return (
                        <div style={{whiteSpace: 'pre-line'}}>
                            {splitPolicyNumbers.map(itemArr => itemArr.join(', ')).join(`, \n `)}
                        </div>
                    )
                }
                return '-'
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-subject-of-claim'),
            key: 'subjectOfClaim',
            dataIndex: 'subjectOfClaim',
            render: (text, record, index) => {
                return <SubjectOfClaimInfo customerInfo={record.subjectOfClaimInfo} />
            }
        },
        {
            title: t('cap-core:cases-claims-table-columns-status'),
            key: 'state',
            dataIndex: 'state',
            render: (_, record) => {
                return <LookupLabel code={record.state} lookup='DisabilityClaimStatus' />
            }
        }
    ]
}
