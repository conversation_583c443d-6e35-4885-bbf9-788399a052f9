"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.definition = void 0;
const i18n_1 = require("@eisgroup/i18n");
const i18n_2 = require("./i18n");
const ClaimLossListView_1 = require("./claim-loss-list/ClaimLossListView");
i18n_1.LocalizationUtils.addResourceBundles(i18n_2.resources);
exports.definition = {
    entryPointComponent: ClaimLossListView_1.ClaimLossListViewLoader,
    views: [
        {
            url: 'claim-loss',
            component: ClaimLossListView_1.ClaimLossListViewLoader
        },
        {
            url: 'dashboard',
            component: ClaimLossListView_1.ClaimLossListViewLoader
        }
    ]
};
//# sourceMappingURL=index.js.map