import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "PREFIX_PROVIDER",
      "id": "1cd6adc3-09be-4a10-87c2-d2871f0cd9e0",
      "props": {
        "prefixInputName": "settlement.settlementDetail.approvalPeriods[0]"
      },
      "components": [
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "TEXT_INPUT",
              "props": {
                "md": 12,
                "name": "absencePeriodRange",
                "label": "cap-core:absence_period",
                "maxLength": null,
                "disablePrefixName": true,
                "disabled": true,
                "className": "gen-date-range-summary"
              },
              "id": "099f8319-a104-4cd6-aff5-05be72255263"
            },
            {
              "type": "TEXT_INPUT",
              "props": {
                "md": 12,
                "name": "eliminationPeriodThroughDate",
                "label": "cap-core:elimination_period_through_date",
                "maxLength": null,
                "disablePrefixName": true,
                "disabled": true,
                "className": "gen-date-range-summary"
              },
              "id": "099f8319-a104-4cd6-aff5-05ne72255263"
            }
          ],
          "id": "a622ea24-40b3-4e5a-9cf1-50145366c919"
        },
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "DATEPICKER_INPUT",
              "props": {
                "md": 12,
                "name": "approvalPeriod.startDate",
                "label": "cap-core:approval_period_start_date",
                "dateType": "date",
                "valueType": "DATETIME",
                "field": {
                  "validations": []
                }
              },
              "id": "15b62e75-62ce-4827-b8bd-8a8242d923a3"
            },
            {
              "type": "DATEPICKER_INPUT",
              "props": {
                "md": 12,
                "name": "approvalPeriod.endDate",
                "label": "cap-core:approval_period_end_date",
                "dateType": "date",
                "valueType": "DATETIME",
                "field": {
                  "validations": []
                }
              },
              "id": "68e3a6ec-b6dc-4244-86b8-65524d0c7015"
            }
          ],
          "id": "59211f1e-6b56-478a-bdf4-8102522717a3"
        },
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "CUSTOM_VALIDATION",
              "id": "b4590b63-b035-417d-9667-022f3eeea8f1",
              "props": {
                "disablePrefixName": true,
                "name": "settlement.settlementDetail.approvalPeriods"
              }
            }
          ],
          "id": "f02ef970-7987-4117-9d82-bebd5e72f3e3"
        },
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "LOOKUP_SELECT",
              "props": {
                "md": 12,
                "name": "approvalStatus",
                "label": "cap-core:approval_period_status",
                "placeholder": "cap-core:placeholder_select",
                "lookupName": "ApprovalStatus",
                "valueType": "string",
                "loadType": "loadEffective",
                "field": {
                  "validations": []
                },
                "filterBy": [
                  {
                    "id": "e3a82991-9a05-4b5f-bb00-75c64a80027a",
                    "filterLookupKey": "isPWOnly",
                    "fieldName": "isPWOnly"
                  }
                ],
                "disablePrefixName": false,
                "condition": {
                  "disabled": {
                    "type": "boolean"
                  }
                }
              },
              "id": "8b0fc32d-ab55-4359-9f6c-29cd40981c7f"
            },
            {
              "type": "SELECT_INPUT",
              "props": {
                "md": 12,
                "label": "cap-core:payment_detail_payee_label",
                "options": [],
                "placeholder": "cap-core:placeholder_select",
                "name": "payee",
                "mode": "default",
                "field": {
                  "validations": [
                    {
                      "skipOnEmpty": false,
                      "type": "mandatory_by_value",
                      "fieldValues": {
                        "fieldName": {
                          "type": "value",
                          "valueType": "string",
                          "value": "Payee"
                        },
                        "fieldValue": {
                          "type": "value",
                          "valueType": "string",
                          "value": "Payee"
                        }
                      }
                    },
                    {
                      "skipOnEmpty": false,
                      "type": "required"
                    }
                  ]
                },
                "condition": {
                  "undefined": {}
                }
              },
              "id": "approval-periods-drawer-payee-input-id"
            }
          ],
          "id": "a1dc99fb-d95a-46f3-9300-a50506672e4d"
        },
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "LOOKUP_SELECT",
              "props": {
                "md": 12,
                "name": "frequencyType",
                "label": "cap-core:approval_period_payment_frequency",
                "valueType": "string",
                "loadType": "loadEffective",
                "lookupName": "FrequencyType",
                "withBodyParamsConfig": false,
                "filterBy": [],
                "field": {
                  "validations": []
                },
                "body": "{\n  \"code\": [\n    \"SINGLEOCCURRENCE\",\n    \"WEEKLY\"\n  ]\n}",
                "condition": {
                  "disabled": {
                    "conditionInputType": "form",
                    "type": "boolean",
                    "rulesTree": {
                      "rules": [
                        {
                          "inputSource": {
                            "type": "FIELD",
                            "value": "~partialDisability.isPartialDisability"
                          },
                          "operator": "$eq",
                          "outputSource": {
                            "type": "PRIMITIVE",
                            "value": true
                          }
                        }
                      ]
                    }
                  }
                }
              },
              "id": "approval-periods-drawer-frequency-type-input-id"
            }
          ],
          "id": "17b4b2fe-e67c-4897-ba27-ef640d38add2"
        },
        {
          "type": "ROW",
          "props": {
            "gutter": 16,
            "md": 24,
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "lossType"
                      },
                      "operator": "$ne",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "SMP"
                      }
                    }
                  ]
                }
              }
            },
            "itemCount": null
          },
          "components": [
            {
              "type": "RADIO_INPUT",
              "props": {
                "md": 12,
                "name": "partialDisability.isPartialDisability",
                "label": "cap-core:approval_period_partial",
                "options": [
                  {
                    "label": "cap-core:yes",
                    "value": "true"
                  },
                  {
                    "label": "cap-core:no",
                    "value": "false"
                  }
                ],
                "defaultValue": "false",
                "field": {
                  "validations": []
                }
              },
              "id": "8f2e11ff-872a-4797-aa9e-b4733372e1e3"
            },
            {
              "type": "MONEY_INPUT",
              "props": {
                "md": 12,
                "name": "partialDisability.currentEarningsAmount",
                "label": "cap-core:approval_period_current_earnings",
                "allowDecimal": true,
                "condition": {
                  "display": {
                    "conditionInputType": "form",
                    "type": "display",
                    "rulesTree": {
                      "rules": [
                        {
                          "inputSource": {
                            "type": "FIELD",
                            "value": "~partialDisability.isPartialDisability"
                          },
                          "operator": "$eq",
                          "outputSource": {
                            "type": "PRIMITIVE",
                            "value": true
                          }
                        }
                      ]
                    }
                  }
                },
                "field": {
                  "validations": []
                },
                "prohibitNegative": true
              },
              "id": "cf178e37-c618-4668-a68b-470d67ad0470"
            }
          ],
          "id": "6056a704-08e9-45c4-a13f-1af5cedac494"
        },
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "DATEPICKER_INPUT",
              "props": {
                "md": 12,
                "name": "proofOfLossReceivedDate",
                "dateType": "date",
                "valueType": "DATETIME",
                "label": "cap-core:approval_period_pol_received_date"
              },
              "id": "45ab8f77-1b65-4c83-89eb-5eabf6178679"
            }
          ],
          "id": "e80ea623-9d76-4fbd-a2a7-fe380a144ca4"
        },
        {
          "type": "ROW",
          "props": {
            "md": 24
          },
          "components": [
            {
              "type": "TEXT_AREA",
              "props": {
                "md": 24,
                "name": "cancelReason",
                "label": "cap-core:approval_period_note",
                "autosize": {
                  "minRows": 3
                },
                "condition": {
                  "display": {
                    "conditionInputType": "form",
                    "type": "display",
                    "rulesTree": {
                      "rules": [
                        {
                          "inputSource": {
                            "type": "FIELD",
                            "value": "~approvalStatus"
                          },
                          "operator": "$eq",
                          "outputSource": {
                            "type": "PRIMITIVE",
                            "value": "Cancelled"
                          }
                        }
                      ]
                    }
                  }
                },
                "field": {
                  "validations": []
                }
              },
              "id": "815cc786-ac45-46cb-8f46-20b5c6582e24"
            }
          ],
          "id": "678ce543-3504-4ebe-9ec5-92a18df55369"
        }
      ]
    }
  ],
  "version": 132,
  "globalEvents": {},
  "actionChains": {}
}

export default config;
