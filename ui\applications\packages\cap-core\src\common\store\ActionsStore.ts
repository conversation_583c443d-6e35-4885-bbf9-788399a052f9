/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {action, observable} from 'mobx'

export enum ActionState {
    started = 'started',
    completed = 'completed',
    failed = 'failed'
}

export interface ActionsStore {
    actions: Map<string, ActionState>
    startAction: (actionName: string) => void
    completeAction: (actionName: string) => void
    failAction: (actionName: string) => void
    isRunning: (actionName: string) => boolean
    isCompleted: (actionName: string) => boolean
}

export class ActionsStoreImpl implements ActionsStore {
    @observable actions: Map<string, ActionState> = new Map()

    @action
    startAction = (actionName: string) => {
        this.actions.set(actionName, ActionState.started)
    }

    @action
    completeAction = (actionName: string) => {
        this.actions.set(actionName, ActionState.completed)
    }

    @action
    failAction = (actionName: string) => {
        this.actions.set(actionName, ActionState.failed)
    }

    isRunning = (actionName: string): boolean => this.actions.get(actionName) === ActionState.started

    isCompleted = (actionName: string): boolean => this.actions.get(actionName) === ActionState.completed
}
