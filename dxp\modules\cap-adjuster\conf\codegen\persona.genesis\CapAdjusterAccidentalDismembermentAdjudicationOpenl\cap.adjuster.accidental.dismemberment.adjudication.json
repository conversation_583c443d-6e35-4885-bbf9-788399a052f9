{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap.acc.adjudication": {"schema": "integration.cap.acc.adjudication.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Openl for Accidental Dismemberment", "version": "1", "title": "CAP Adjuster: Openl for Accidental Dismemberment"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/openl-acc", "description": "CAP Adjuster: Openl for Accidental Dismemberment"}], "paths": {"/openl-acc/death-amount-calculation": {"post": {"x-dxp-path": "/_api_acc_death_amount_calculation", "tags": ["/cap-adjuster/v1/openl-acc"]}}, "/openl-acc/gross-amount-calculation": {"post": {"x-dxp-path": "/_api_accidental_dismemberment_calculation", "tags": ["/cap-adjuster/v1/openl-acc"]}}, "/openl-acc/face-value-add-calculation": {"post": {"x-dxp-path": "/_api_add_face_value_calculation", "tags": ["/cap-adjuster/v1/openl-acc"]}}}}