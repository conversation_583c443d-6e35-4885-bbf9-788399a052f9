{"swagger": "2.0", "info": {"description": "API for CapStd", "version": "1", "title": "CapStd model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/CapStd/v1/command/assignBenefits": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimAssignBenefitsInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapStdAssignBenefitsOutputSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/closeLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/initLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimInitRefInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/reopenLoss": {"post": {"description": "The command that reopens claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/submitLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/updateLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/command/updateReturnToWork": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateReturnToWorkInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapStdLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapStdLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapStdKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/absenceClaimClosureToOpenItems": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/benefitToSettlementInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLtd_CapLtdToSettlementInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/BenefitToSettlementInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/capResolveCloseClaimReason": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/capStdToSettlement": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdToSettlementOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/resolveAvailableBenefits": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapSTDResolveAvailableBenefitsOutputSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/resolveLossAdditionalBenefitSettlements": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/resolveLossMainBenefitSettlements": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapStd/v1/transformation/submitLossToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SubmitLossToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AbsenceClaimClosureToOpenItemsOutputs": {"properties": {"output": {"$ref": "#/definitions/CapLeave_CapLeaveClaimClosureOpenItemsOutput"}}, "title": "AbsenceClaimClosureToOpenItemsOutputs"}, "AbsenceClaimClosureToOpenItemsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputs"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "AbsenceClaimClosureToOpenItemsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "BenefitToSettlementInputOutputs": {"properties": {"output": {"type": "array", "items": {"type": "object"}}}, "title": "BenefitToSettlementInputOutputs"}, "BenefitToSettlementInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/BenefitToSettlementInputOutputs"}}, "title": "BenefitToSettlementInputOutputsSuccess"}, "BenefitToSettlementInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/BenefitToSettlementInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "BenefitToSettlementInputOutputsSuccessBody"}, "CapDisabilityClaimAssignBenefitsInput": {"properties": {"benefits": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapAbsenceBenefitInfo"}}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimAssignBenefitsInput"}, "CapDisabilityClaimAssignBenefitsInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimAssignBenefitsInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimAssignBenefitsInputBody"}, "CapDisabilityClaimInitRefInput": {"required": ["entity"], "properties": {"absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "coverageType": {"type": "string"}, "entity": {"$ref": "#/definitions/CapStd_CapDisabilityClaimDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimInitRefInput"}, "CapDisabilityClaimInitRefInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimInitRefInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimInitRefInputBody"}, "CapDisabilityClaimUpdateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "entity": {"$ref": "#/definitions/CapStd_CapDisabilityClaimDetailEntity"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateInput"}, "CapDisabilityClaimUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimUpdateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateInputBody"}, "CapDisabilityClaimUpdateReturnToWorkInput": {"required": ["_key", "returnToWorkDateDetail"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "returnToWorkDateDetail": {"$ref": "#/definitions/CapStd_CapStdReturnToWorkDetailEntity"}}, "title": "CapDisabilityClaimUpdateReturnToWorkInput"}, "CapDisabilityClaimUpdateReturnToWorkInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimUpdateReturnToWorkInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateReturnToWorkInputBody"}, "CapLeave_CapAbsenceClaimBalanceOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimBalanceOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeave CapAbsenceClaimBalanceOpenItemInfo"}, "CapLeave_CapAbsenceClaimCoverageOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimCoverageOpenItemInfo"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "dateRange": {"$ref": "#/definitions/CapLeave_Period"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "totalGBAorDuration": {"type": "number", "description": "Total amount to be paid"}, "unpaidGBAorDuration": {"type": "number", "description": "Total amount to be unpaid"}}, "title": "CapLeave CapAbsenceClaimCoverageOpenItemInfo"}, "CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Payment Number"}, "paymentState": {"type": "string", "description": "Payment State"}}, "title": "CapLeave CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "allocationPeriod": {"$ref": "#/definitions/CapLeave_Period"}, "countOfUnposted": {"type": "integer", "format": "int64", "description": "Count Of Unposted"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "frequencyType": {"type": "string", "description": "Frequency Type"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentScheduleNumber": {"type": "string", "description": "Payment Schedule Number"}}, "title": "CapLeave CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "CapLeave_CapLeaveClaimClosureOpenItemsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimClosureOpenItemsOutput"}, "actualPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "balanceInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimBalanceOpenItemInfo"}}, "coverageInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimCoverageOpenItemInfo"}}, "issuedPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "scheduledPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo"}}}, "title": "CapLeave CapLeaveClaimClosureOpenItemsOutput", "description": "Entity for closure claim"}, "CapLeave_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave Period"}, "CapLtd_CapAbsenceBenefitInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceBenefitInfo"}, "benefitCategory": {"type": "string"}, "claimBenefitLabel": {"type": "string"}, "exists": {"type": "boolean"}, "lossType": {"type": "string"}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}, "policyProductCd": {"type": "string"}}, "title": "CapLtd CapAbsenceBenefitInfo"}, "CapLtd_CapLtdToSettlementInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdToSettlementInput"}, "benefits": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsenceBenefitInfo"}}, "capPolicyId": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLtd CapLtdToSettlementInput", "description": "Entity for policy benefits."}, "CapLtd_CapLtdToSettlementInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLtd_CapLtdToSettlementInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtd_CapLtdToSettlementInputBody"}, "CapResolveCloseClaimReasonOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveCloseClaimReasonOutputs"}, "CapResolveCloseClaimReasonOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputs"}}, "title": "CapResolveCloseClaimReasonOutputsSuccess"}, "CapResolveCloseClaimReasonOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveCloseClaimReasonOutputsSuccessBody"}, "CapStdKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapStdKrakenDeprecatedBundleRequest"}, "CapStdKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapStdKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStdKrakenDeprecatedBundleRequestBody"}, "CapStdLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntity"}}}, "title": "CapStdLoadHistoryResult"}, "CapStdLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStdLoadHistoryResult"}}, "title": "CapStdLoadHistoryResultSuccess"}, "CapStdLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStdLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStdLoadHistoryResultSuccessBody"}, "CapStdToSettlementOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapStdToSettlementOutputs"}, "CapStdToSettlementOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStdToSettlementOutputs"}}, "title": "CapStdToSettlementOutputsSuccess"}, "CapStdToSettlementOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStdToSettlementOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStdToSettlementOutputsSuccessBody"}, "CapStd_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapStd AccessTrackInfo"}, "CapStd_CapAbsenceBenefitInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceBenefitInfo"}, "benefitCategory": {"type": "string"}, "claimBenefitLabel": {"type": "string"}, "exists": {"type": "boolean"}, "lossType": {"type": "string"}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}, "policyProductCd": {"type": "string"}}, "title": "CapStd CapAbsenceBenefitInfo"}, "CapStd_CapAbsenceDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceDetailEntity"}, "activelyAtWorkDate": {"type": "string", "format": "date-time"}, "lossDate": {"type": "string", "format": "date-time"}}, "title": "CapStd CapAbsenceDetailEntity"}, "CapStd_CapAbsenceSTDFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceSTDFinancialAdjustmentEntity"}, "claimFinancialAdjustmentDeductions": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapSTDFinancialAdjustmentDeductionEntity"}}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes. Inherited from Absence."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax. Inherited from Absence case."}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}}, "title": "CapStd CapAbsenceSTDFinancialAdjustmentEntity", "description": "Parent business entity of financial adjustment parts coming from the Absence Case (deductions)."}, "CapStd_CapBenefitRelatedEventEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBenefitRelatedEventEntity"}, "eventDate": {"type": "string", "format": "date"}, "eventTypeCd": {"type": "string"}}, "title": "CapStd CapBenefitRelatedEventEntity"}, "CapStd_CapClaimSubjectInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimSubjectInfoEntity"}, "registryId": {"type": "string", "description": "Unique identifier for subject of claim"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapStd CapClaimSubjectInfoEntity", "description": "Entity for subject of claim"}, "CapStd_CapCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCoverageInfoEntity"}, "benefitDuration": {"type": "integer", "format": "int64", "description": "Defines maximum benefit duration in weeks if no limitations/exclusions are applied"}, "coverageCd": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time", "description": "Coverage Effective Date defined in Policy"}, "proratingRate": {"type": "integer", "format": "int64", "description": "Prorating Rate defined in STD Master Policy"}}, "title": "CapStd CapCoverageInfoEntity", "description": "An entity for coverage information."}, "CapStd_CapDisabilityClaimDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapStd"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDisabilityClaimDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Main object for short term disability domain attributes used for actions and transactions."}, "claimPayeeDetails": {"$ref": "#/definitions/CapStd_CapSTDClaimPayeeDetailsEntity"}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapSTDDiagnosisInformationEntity"}}, "disabilityReasonCd": {"type": "string", "description": "Defines disability reason"}, "earnings": {"$ref": "#/definitions/CapStd_CapSTDEarningsInformationEntity"}, "eligibilityVerifiedCd": {"type": "string"}, "externalTime": {"$ref": "#/definitions/CapStd_CapSTDExternalTimeEntity"}, "financialAddition": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapSTDFinancialAdditionEntity"}}, "financialAdjustment": {"$ref": "#/definitions/CapStd_CapSTDFinancialAdjustmentEntity"}, "financialAdjustmentOverride": {"$ref": "#/definitions/CapStd_CapAbsenceSTDFinancialAdjustmentEntity"}, "firstDayHospitalizationDate": {"type": "string", "format": "date-time", "description": "First Day of Hospitalization."}, "interruptionDaysNumber": {"type": "integer", "format": "int64"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "outpatientSurgeryDate": {"type": "string", "format": "date-time", "description": "Outpatient Surgery Date."}, "preExistingConditionsAssessment": {"$ref": "#/definitions/CapStd_CapStdPreExistingConditionsAssessmentEntity"}, "proofReceivedDate": {"type": "string", "format": "date", "description": "Entity to capture absence period details."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when the incident was reported."}, "returnToWorkDateDetail": {"$ref": "#/definitions/CapStd_CapStdReturnToWorkDetailEntity"}, "selectedCoverage": {"$ref": "#/definitions/CapStd_CapSTDClaimSelectedCoverageInfoEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable."}}, "title": "CapStd CapDisabilityClaimDetailEntity", "description": "Entity that encompasses short term product claim details."}, "CapStd_CapDisabilityClaimEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapStd"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDisabilityClaimEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string", "description": "Defines reason of absence"}}, "accessTrackInfo": {"$ref": "#/definitions/CapStd_AccessTrackInfo"}, "coverageType": {"type": "string", "description": "Type of coverage"}, "eventCaseInfo": {"$ref": "#/definitions/CapStd_EventCaseInfoEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "lossDetail": {"$ref": "#/definitions/CapStd_CapDisabilityClaimDetailEntity"}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "lossType": {"type": "string", "description": "Defines loss type."}, "memberRegistryTypeId": {"type": "string"}, "policy": {"$ref": "#/definitions/CapStd_CapDisabilityLossPolicyInfoEntity"}, "policyId": {"type": "string"}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "state": {"type": "string", "description": "Defines disability status."}, "subjectOfClaim": {"$ref": "#/definitions/CapStd_CapClaimSubjectInfoEntity"}}, "title": "CapStd CapDisabilityClaimEntity", "description": "Main object for short term disability domain attributes used for actions and transactions."}, "CapStd_CapDisabilityClaimEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntity"}}, "title": "CapStd_CapDisabilityClaimEntitySuccess"}, "CapStd_CapDisabilityClaimEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStd_CapDisabilityClaimEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStd_CapDisabilityClaimEntitySuccessBody"}, "CapStd_CapDisabilityLossPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDisabilityLossPolicyInfoEntity"}, "asoTypeCd": {"type": "string", "description": "Defines ASO Type from Master policy."}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "existERISAPlan": {"type": "boolean", "description": "Indicates if ERISA exist"}, "fundingTypeCd": {"type": "string", "description": "Defines Funding Type from Master policy."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isReinsuranceExists": {"type": "boolean", "description": "Defines if the claim is Reinsured."}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "masterPolicyId": {"type": "string", "description": "Identification number of the master policy in CAP subsystem."}, "masterPolicyNumber": {"type": "string", "description": "Defines masterPolicyNumber from Master policy"}, "masterPolicyProductCd": {"type": "string", "description": "Master Policy Product Code"}, "orgCustomerNumber": {"type": "string", "description": "Organization Customer Number from Master Policy"}, "planCd": {"type": "string", "description": "The attribute that represents the Policy Plan (STDPlan1, STDPlan2, LTDPlan1, LTDPlan2)."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "stdMasterInfo": {"$ref": "#/definitions/CapStd_CapSTDMasterInfoEntity"}, "term": {"$ref": "#/definitions/CapStd_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapStd CapDisabilityLossPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapStd_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapStd CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapStd_CapPolicyIdsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPolicyIdsEntity"}, "stdIndividualPolicyId": {"type": "string"}}, "title": "CapStd CapPolicyIdsEntity"}, "CapStd_CapSTDClaimPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDClaimPayeeDetailsEntity"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapStd CapSTDClaimPayeeDetailsEntity"}, "CapStd_CapSTDClaimSelectedCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDClaimSelectedCoverageInfoEntity"}, "coverageName": {"type": "string"}, "planName": {"type": "string"}}, "title": "CapStd CapSTDClaimSelectedCoverageInfoEntity"}, "CapStd_CapSTDDiagnosisInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDDiagnosisInformationEntity"}, "date": {"type": "string", "format": "date", "description": "Defines date of diagnosis."}, "icdCode": {"type": "string", "description": "Defines code of diagnosis."}, "primaryCode": {"type": "boolean", "description": "Defines if diagnosis code is primary."}}, "title": "CapStd CapSTDDiagnosisInformationEntity", "description": "Entity for diagnosis information."}, "CapStd_CapSTDEarningsInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDEarningsInformationEntity"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "annualSalaryAmount": {"$ref": "#/definitions/Money"}, "baseSalaryAmount": {"$ref": "#/definitions/Money"}, "coveredWeeklyEarnings": {"$ref": "#/definitions/Money"}, "salaryMode": {"type": "string", "description": "Describes the frequency of Salary pay."}}, "title": "CapStd CapSTDEarningsInformationEntity", "description": "Entity for Insured's Earnings information."}, "CapStd_CapSTDExternalTimeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDExternalTimeEntity"}, "externalTimeUsedDays": {"type": "integer", "format": "int64", "description": "Defines the external time used in days."}, "externalTimeUsedWeeks": {"type": "integer", "format": "int64", "description": "Defines the amount of external time used in weeks."}}, "title": "CapStd CapSTDExternalTimeEntity", "description": "Entity which store external time amounts for STD"}, "CapStd_CapSTDFinancialAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDFinancialAdditionEntity"}, "ancillaryActivityName": {"type": "string", "description": "Ancillary Activity Name"}, "financialAdditionRehabilitation": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapSTDFinancialAdditionRehabilitationEntity"}}}, "title": "CapStd CapSTDFinancialAdditionEntity", "description": "Defines financial additions details"}, "CapStd_CapSTDFinancialAdditionRehabilitationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDFinancialAdditionRehabilitationEntity"}, "rehabilitationTerm": {"$ref": "#/definitions/CapStd_Term"}}, "title": "CapStd CapSTDFinancialAdditionRehabilitationEntity", "description": "Defines financial Rehabilitation addition details"}, "CapStd_CapSTDFinancialAdjustmentDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDFinancialAdjustmentDeductionEntity"}, "amount": {"$ref": "#/definitions/Money"}, "deductionBeneficiary": {"type": "string", "description": "Defines deduction beneficiary"}, "deductionPct": {"type": "number", "description": "This attribute represents percentage Deductions to be made from the Claim payment"}, "deductionTerm": {"$ref": "#/definitions/CapStd_Term"}, "deductionType": {"type": "string", "description": "This attribute describes the target for the deduction amount that will be paid by the Claim."}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the deductions should be applied pre taxes. If the value is set to 'no', the deductions are applied post taxes."}, "nonProviderPaymentType": {"type": "string", "description": "This field describes the type of Non-Provider Payment."}, "stateProvided": {"type": "string", "description": "Ths attribute describes in which state the Child Support was provided."}}, "title": "CapStd CapSTDFinancialAdjustmentDeductionEntity", "description": "This business entity describes the Deduction amount that can be paid to the Sponsor from the claim to cover other Premiums or External payments."}, "CapStd_CapSTDFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDFinancialAdjustmentEntity"}, "claimFinancialAdjustmentOffsets": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapSTDFinancialAdjustmentOffsetEntity"}}}, "title": "CapStd CapSTDFinancialAdjustmentEntity", "description": "Entity for financial adjustment details (offsets)."}, "CapStd_CapSTDFinancialAdjustmentOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDFinancialAdjustmentOffsetEntity"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapStd_CapSTDOffsetTermEntity"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the claim"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapStd CapSTDFinancialAdjustmentOffsetEntity", "description": "Business entity that describes the Offsets of the claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapStd_CapSTDMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapCoverageInfoEntity"}}}, "title": "CapStd CapSTDMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "CapStd_CapSTDOffsetTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDOffsetTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapStd CapSTDOffsetTermEntity"}, "CapStd_CapSTDResolveAvailableBenefitsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDResolveAvailableBenefitsOutput"}, "benefits": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapAbsenceBenefitInfo"}}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapStd_MessageType"}}}, "title": "CapStd CapSTDResolveAvailableBenefitsOutput", "description": "Entity for policy benefits."}, "CapStd_CapSTDResolveAvailableBenefitsOutputSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStd_CapSTDResolveAvailableBenefitsOutput"}}, "title": "CapStd_CapSTDResolveAvailableBenefitsOutputSuccess"}, "CapStd_CapSTDResolveAvailableBenefitsOutputSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStd_CapSTDResolveAvailableBenefitsOutputSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStd_CapSTDResolveAvailableBenefitsOutputSuccessBody"}, "CapStd_CapStdAssignBenefitsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdAssignBenefitsOutput"}, "benefits": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapAbsenceBenefitInfo"}}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapStd CapStdAssignBenefitsOutput", "description": "Entity for policy benefits."}, "CapStd_CapStdAssignBenefitsOutputSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStd_CapStdAssignBenefitsOutput"}}, "title": "CapStd_CapStdAssignBenefitsOutputSuccess"}, "CapStd_CapStdAssignBenefitsOutputSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStd_CapStdAssignBenefitsOutputSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStd_CapStdAssignBenefitsOutputSuccessBody"}, "CapStd_CapStdPreExistingConditionsAssessmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdPreExistingConditionsAssessmentEntity"}, "notes": {"type": "string", "description": "Defines Pre-Existing Conditions Assessment Evaluation Notes."}, "pecaEvaluationCd": {"type": "string", "description": "Defined Pre-Existing Conditions Assessment Evaluation."}}, "title": "CapStd CapStdPreExistingConditionsAssessmentEntity", "description": "Entity for pre-existing conditions assessment"}, "CapStd_CapStdReturnToWorkDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdReturnToWorkDetailEntity"}, "actualFTRTW": {"type": "string", "format": "date-time", "description": "Actual Full-Time Return to Work date"}, "actualPTRTW": {"type": "string", "format": "date-time", "description": "Actual Part-Time Return to Work date"}, "estimatedFTRTW": {"type": "string", "format": "date-time", "description": "Estimated Full-Time Return to Work date"}, "estimatedPTRTW": {"type": "string", "format": "date-time", "description": "Estimated Part-Time Return to Work date"}}, "title": "CapStd CapStdReturnToWorkDetailEntity", "description": "Entity for return to work date details"}, "CapStd_EventCaseInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "EventCaseInfoEntity"}, "absenceDetail": {"$ref": "#/definitions/CapStd_CapAbsenceDetailEntity"}, "capPolicyIds": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapPolicyIdsEntity"}}, "caseNumber": {"type": "string"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "events": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapBenefitRelatedEventEntity"}}, "isSicknessInjury": {"type": "string"}, "reportedDate": {"type": "string", "format": "date-time"}, "state": {"type": "string"}}, "title": "CapStd EventCaseInfoEntity"}, "CapStd_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapStd MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapStd_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapStd Term"}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossCloseInputBody"}, "ClaimLossReopenInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossReopenInputBody"}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "lossSubStatusCd": {"type": "string"}}, "title": "ClaimLossSubStatusInput"}, "ClaimLossSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossSubStatusInputBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ResolveLossAdditionalBenefitSettlementsOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputs"}, "ResolveLossAdditionalBenefitSettlementsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputs"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputsSuccess"}, "ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody"}, "ResolveLossMainBenefitSettlementsOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveLossMainBenefitSettlementsOutputs"}, "ResolveLossMainBenefitSettlementsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputs"}}, "title": "ResolveLossMainBenefitSettlementsOutputsSuccess"}, "ResolveLossMainBenefitSettlementsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveLossMainBenefitSettlementsOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SubmitLossToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "SubmitLossToAccumulatorTxOutputs"}, "SubmitLossToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/SubmitLossToAccumulatorTxOutputs"}}, "title": "SubmitLossToAccumulatorTxOutputsSuccess"}, "SubmitLossToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/SubmitLossToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SubmitLossToAccumulatorTxOutputsSuccessBody"}}}