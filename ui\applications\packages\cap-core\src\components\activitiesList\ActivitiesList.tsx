/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {Activities, ActivityParams, ActivityTimeline, Offset} from '@eisgroup/bam-ui'
import {
    CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity,
    CapAdjusterEventCaseCapEventCaseCapEventCaseEntity,
    CapAdjusterLeaveLossCapLeaveCapLeaveClaimEntity,
    CapAdjusterLossSearchResultCapLossSearchEntityResponseV3,
    CapAdjusterLtdLossCapLtdCapLTDisabilityClaimEntity,
    CapAdjusterSmpLossCapSmpCapSMPClaimEntity,
    CapAdjusterStdLossCapStdCapDisabilityClaimEntity
} from '@eisgroup/cap-gateway-client'
import {BamActivity, ClaimLoss, CapGenericLoss, WorkflowCase} from '@eisgroup/cap-services'
import {useTranslate} from '@eisgroup/i18n'
import {SidebarData, Timeline, useSidebar} from '@eisgroup/ui-kit'
import {keyBy} from 'lodash'
import {observer} from 'mobx-react'
import React, {useCallback, useEffect, useMemo, useState} from 'react'
import {
    ActivityEntities,
    BAMProductModelNameList,
    CAP_EVENT_CASE,
    CAP_INTAKE,
    CLAIM_OVERVIEW,
    CLAIM_WRAPPER,
    ClaimTypesMap,
    WORKFLOW_SERVICE
} from '../../common/constants'
import {ACTIVITY_WORKBENCH_LIST} from '../../common/package-class-names'
import {activitiesListStore} from './ActivitiesListStore'

export const ActivitiesList: React.FC = observer(() => {
    const {data} = useSidebar()
    const {activityParams} = (data?.bam as SidebarData) || {}
    const {t} = useTranslate()
    const displayCreatedBySystem = t('cap-core:loss_detail_bench_activity_displayCreatedBy')
    const {notes, tasks, docs} = activitiesListStore
    const {
        fetchTaskActivities,
        fetchNotesActivities,
        fetchDocsActivities,
        fetchCaseActivities,
        getEventCase,
        getStdLoss,
        getLtdLoss,
        getSmpLoss,
        getLifeClaimLoss,
        getLifeClaimLossReference,
        searchEventCaseClaims,
        getLeaveLoss
    } = activitiesListStore

    // todo: refactor to use 'data' instead of fetching and storing locally
    const [caseState, setCaseState] = useState<WorkflowCase[]>([])
    const [eventCase, setEventCase] = useState<CapAdjusterEventCaseCapEventCaseCapEventCaseEntity>()
    const [stdLoss, setStdLoss] = useState<CapAdjusterStdLossCapStdCapDisabilityClaimEntity>()
    const [ltdLoss, setLtdLoss] = useState<CapAdjusterLtdLossCapLtdCapLTDisabilityClaimEntity>()
    const [smpLoss, setSmpLossLoss] = useState<CapAdjusterSmpLossCapSmpCapSMPClaimEntity>()
    const [leaveLoss, setLeaveLossLoss] = useState<CapAdjusterLeaveLossCapLeaveCapLeaveClaimEntity>()
    const [lifeLoss, setLifeLossLoss] = useState<CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity>()
    const [lifeLossRef, setLifeLossLossRef] = useState<CapGenericLoss[]>()
    const [claims, setClaims] = useState<CapAdjusterLossSearchResultCapLossSearchEntityResponseV3>()
    // todo: refactor to use 'data' instead of window to determine view
    const location = window.location.pathname
    const isClaim = location.includes(CLAIM_OVERVIEW)
    const isCapEventCase = location.includes(CAP_EVENT_CASE)
    const isIntake = location.includes(CAP_INTAKE)
    const modelNameRegexp = /.+?\/(.*?)\//
    const match = modelNameRegexp.exec(location)

    const eventCaseKey = caseState?.find(r => r.entityModelName?.includes(CAP_EVENT_CASE))?.entityKey
    const stdRootId = caseState?.find(r => r.entityModelName?.includes(ClaimTypesMap.STD))?.entityKey?.rootId
    const ltdRootId = caseState?.find(r => r.entityModelName?.includes(ClaimTypesMap.LTD))?.entityKey?.rootId
    const smpRootId = caseState?.find(r => r.entityModelName?.includes(ClaimTypesMap.SMP))?.entityKey?.rootId
    const leaveRootId = caseState?.find(r => r.entityModelName?.includes(ClaimTypesMap.Leave))?.entityKey?.rootId
    const lifeRootId = caseState?.find(r => r.entityModelName?.includes(ClaimTypesMap.ClaimWrapper))?.entityKey?.rootId

    const filterClaims =
        isCapEventCase || isIntake
            ? activityParams.entities.map(r => r.uri)
            : activityParams.entities
                  .filter(
                      r =>
                          !r.uri?.includes(CAP_EVENT_CASE) &&
                          (isClaim ? r.uri?.includes(CLAIM_WRAPPER) : r.uri?.includes(match?.[1]))
                  )
                  .map(c => c.uri)
    const uniqueClaims = [...new Set(filterClaims)]

    useEffect(() => {
        fetchCaseActivities(uniqueClaims).subscribe(response => {
            setCaseState(response.get())
        })
        fetchTaskActivities(uniqueClaims)
        fetchNotesActivities(activityParams.entities.map(entity => entity.uri))
        fetchDocsActivities(uniqueClaims)
    }, [])

    useEffect(() => {
        if (eventCaseKey?.rootId) {
            getEventCase(eventCaseKey.rootId).subscribe(response => {
                setEventCase(response.get())
            })
            getLifeClaimLossReference(eventCaseKey.rootId).subscribe(response => {
                setLifeLossLossRef(response.get())
            })
            searchEventCaseClaims(eventCaseKey.rootId, eventCaseKey.revisionNo).subscribe(response => {
                setClaims(response.get())
            })
        }

        if (stdRootId) {
            getStdLoss(stdRootId).subscribe(response => {
                setStdLoss(response.get())
            })
        }

        if (ltdRootId) {
            getLtdLoss(ltdRootId).subscribe(response => {
                setLtdLoss(response.get())
            })
        }

        if (smpRootId) {
            getSmpLoss(smpRootId).subscribe(response => {
                setSmpLossLoss(response.get())
            })
        }

        if (leaveRootId) {
            getLeaveLoss(leaveRootId).subscribe(response => {
                setLeaveLossLoss(response.get())
            })
        }

        if (lifeRootId) {
            getLifeClaimLoss(lifeRootId).subscribe(response => {
                setLifeLossLoss(response.get())
            })
        }
    }, [caseState])

    const docsEntities = useMemo(() => keyBy(docs, doc => `${ActivityEntities.DocumentEntity}_${doc?.docId}`), [docs])

    const entitiesById = useMemo(
        () => keyBy(activityParams?.entities, entity => `${ActivityEntities.CapLossOverview}_${entity?.id}`),
        [activityParams?.entityIds]
    )

    const tasksEntities = useMemo(() => keyBy(tasks, task => `${ActivityEntities.Task}_${task?.taskId}`), [tasks])

    const noteEntitiesIds = useMemo(
        () => keyBy(notes, note => `${ActivityEntities.NoteEntity}_${note?._key?.rootId}`),
        [notes]
    )

    const casesEntitiesIds = useMemo(
        () => keyBy(caseState, caseId => `${ActivityEntities.Case}_${caseId?.id}`),
        [caseState]
    )

    const getActivityParams = useCallback(
        (params): Offset<ActivityParams> => ({
            ...params,
            ...activityParams,
            entityIds: [
                ...Object.keys(entitiesById),
                ...Object.keys(noteEntitiesIds),
                ...Object.keys(tasksEntities),
                ...Object.keys(casesEntitiesIds),
                ...Object.keys(docsEntities)
            ]
        }),
        [activityParams, noteEntitiesIds, docsEntities, tasksEntities, casesEntitiesIds]
    )
    const modelReference = caseEntity => {
        const entityURI = caseEntity?.entityURI

        switch (true) {
            case entityURI?.includes(ClaimTypesMap.STD):
                return getLossNumber(caseEntity, stdLoss)
            case entityURI?.includes(ClaimTypesMap.LTD):
                return getLossNumber(caseEntity, ltdLoss)
            case entityURI?.includes(ClaimTypesMap.SMP):
                return getLossNumber(caseEntity, smpLoss)
            case entityURI?.includes(ClaimTypesMap.Leave):
                return getLossNumber(caseEntity, leaveLoss)
            case entityURI?.includes(ClaimTypesMap.ClaimWrapper):
                return location.includes(CLAIM_OVERVIEW)
                    ? lifeLoss?.lossNumber
                    : lifeLossRef?.find(r => r?._key?.rootId === caseEntity?.entityKey?.rootId)?.lossNumber
            default:
                return eventCase?.lossNumber
        }
    }

    const getLossNumber = (
        caseEntity,
        loss:
            | CapAdjusterStdLossCapStdCapDisabilityClaimEntity
            | CapAdjusterLtdLossCapLtdCapLTDisabilityClaimEntity
            | CapAdjusterSmpLossCapSmpCapSMPClaimEntity
            | CapAdjusterLeaveLossCapLeaveCapLeaveClaimEntity
            | undefined
    ) => {
        const isLocationCapEventCase = location.includes(CAP_EVENT_CASE)
        const caseResult = (claims?.result as CapGenericLoss[])?.find(
            r => r?._key?.rootId === caseEntity?.entityKey?.rootId
        )?.lossNumber

        return isLocationCapEventCase ? caseResult : loss?.lossNumber
    }

    const modelName = (isEventCase: boolean | undefined) => {
        return isEventCase ? 'Case' : 'Claim'
    }
    const displayCreatedByValue = (displayCreatedBy: string | undefined) => {
        return displayCreatedBy && displayCreatedBy !== WORKFLOW_SERVICE ? displayCreatedBy : displayCreatedBySystem
    }

    const renderTitle = ({entityId, displayCreatedBy, messageId}: BamActivity) => {
        const entity = entitiesById[entityId]
        const tasksEntity = tasksEntities[entityId]
        const noteEntity = noteEntitiesIds[entityId]
        const caseEntity = casesEntitiesIds[entityId]
        const docsEntity = docsEntities[entityId]

        const getTaskTitle = () => {
            const taskModel = tasksEntity.entityInfo?.modelName
            const taskTitle = BAMProductModelNameList[taskModel!]
            return {
                type: taskTitle,
                key: tasksEntity.entityRefNo,
                displayCreatedBy: displayCreatedByValue(displayCreatedBy)
            }
        }

        const getDocsTitle = () => {
            const docModel = docsEntity.entityInfo?.entityModelName
            const docTitle = BAMProductModelNameList[docModel!]
            return {
                type: docTitle,
                key: docsEntity.entityInfo?.entityBusinessKey,
                displayCreatedBy: displayCreatedByValue(displayCreatedBy)
            }
        }

        const getNoteTitle = () => {
            const isEventCase = noteEntity.primaryEntity?.entityURI?.includes(CAP_EVENT_CASE)
            return {
                type: modelName(isEventCase),
                key: noteEntity.primaryEntity?.entityBusinessKey,
                displayCreatedBy: displayCreatedByValue(displayCreatedBy)
            }
        }

        const getTitleForOtherActivities = () => {
            const otherTitle = notes?.map(value => value.primaryEntity?.entityBusinessKey)[0]
            const isEventCase = otherTitle?.includes('EC')
            return {
                type: modelName(isEventCase),
                displayCreatedBy: displayCreatedByValue(displayCreatedBy),
                key: otherTitle
            }
        }

        const getTitleForCaseActivities = () => {
            const isEventCase = caseEntity?.entityURI?.includes(CAP_EVENT_CASE)
            return {
                type: modelName(isEventCase),
                key: modelReference(caseEntity),
                displayCreatedBy: displayCreatedByValue(displayCreatedBy)
            }
        }

        if (entity) {
            const {key, name} = entity
            let title = ''
            if (name === 'CapSpecialHandling') {
                const CapSpecialHandlingCase = 'CapSpecialHandlingCase'
                const CapSpecialHandlingClaim = 'CapSpecialHandlingClaim'
                if (key.includes('EC')) {
                    title = BAMProductModelNameList[CapSpecialHandlingCase]
                } else {
                    title = BAMProductModelNameList[CapSpecialHandlingClaim]
                }
            } else {
                title = BAMProductModelNameList[name]
            }

            const getClaimNumber = (list: any[] = []): string => {
                const originSource = list.find(v => entity.uri.includes(v._key?.rootId))?.originSource._uri || ''
                return data?.lossNumber
                    ? data.lossNumber
                    : data?.paymentsStore?.allAssociatedClaims?.find((claim: ClaimLoss) =>
                          originSource.includes(claim._key.rootId)
                      )?.lossNumber || ''
            }

            if (name === 'CapPaymentSchedule') {
                return t('cap-core:loss_detail_bench_activity_title', {
                    type: title,
                    key: getClaimNumber(data?.paymentsStore?.paymentScheduleList),
                    displayCreatedBy: displayCreatedByValue(displayCreatedBy)
                })
            }

            if (name === 'PaymentDefinition') {
                return t('cap-core:loss_detail_bench_activity_title', {
                    type: title,
                    key: getClaimNumber(data?.paymentsStore?.paymentList),
                    displayCreatedBy: displayCreatedByValue(displayCreatedBy)
                })
            }

            return t('cap-core:loss_detail_bench_activity_title', {
                type: title,
                key,
                displayCreatedBy: displayCreatedByValue(displayCreatedBy)
            })
        }

        if (messageId.includes('task')) {
            return t('cap-core:loss_detail_bench_activity_title', getTaskTitle())
        }

        if (messageId.includes('documents')) {
            return t('cap-core:loss_detail_bench_activity_title', getDocsTitle())
        }

        if (messageId.includes('Note')) {
            return t('cap-core:loss_detail_bench_activity_title', getNoteTitle())
        }

        if (messageId.includes('case')) {
            return t('cap-core:loss_detail_bench_activity_title', getTitleForCaseActivities())
        }

        return t('cap-core:loss_detail_bench_activity_title', getTitleForOtherActivities())
    }

    const formatLossMsg = (messageId: string, message: string | undefined) => {
        if (messageId.includes('createLoss') || messageId.includes('initLoss')) {
            return message?.replace('created', 'is created')
        }
        if (messageId.includes('updateLoss')) {
            return message?.replace('updated', 'is updated')
        }
        if (messageId.includes('closeLoss')) {
            return message?.replace('closed', 'is closed')
        }
        if (messageId.includes('reopenLoss')) {
            return message?.replace('reopened', 'is reopened')
        }
        if (messageId.includes('submitLoss')) {
            return message?.replace('submitted', 'is submitted')
        }
        return message
    }

    const renderMessage = ({message, messageId, entityId}: BamActivity) => {
        const caseEntity = casesEntitiesIds[entityId]
        const isEventCase = caseEntity?.entityURI?.includes(CAP_EVENT_CASE)

        const tasksEntity = tasksEntities[entityId]
        const taskName = tasksEntity?.name ?? ''
        if (messageId.includes('task') && message) {
            const taskMessage: string[] = message.split(' ')
            return [taskMessage[0], taskName, taskMessage[1], 'is', taskMessage[2]].join(' ')
        }
        if (messageId.includes('Loss')) {
            message = formatLossMsg(messageId, message)
        }
        if (messageId.includes('assign_case')) {
            const caseModel = modelReference(caseEntity)
            const caseName = modelName(isEventCase)

            const splitMessage = message?.replace(/\s+\./g, '.').split('from')[1]
            message = message
                ?.replace(/\s\S+/, ` ${caseModel}`) // Replace case entity reference
                ?.replace('Case', caseName) // Replace "Case" with model name
            if (splitMessage === '.') {
                message = message
                    ?.replace('reassigned', 'assigned') // Fix "reassigned" to "assigned"
                    ?.replace(/\b(queue|assignee)\b/g, '') // Remove "queue" or "assignee"
                    ?.replace(/\bfrom\b.*$/, '.') // Replace "from" and everything after with "."
                    ?.replace(/\s+,/g, ',') // Remove any space
                    ?.replace(/\s+\./g, '.') // Remove any space
            }
            return message
        }
        if (messageId.includes('Note')) {
            return message?.replace('CapLoss', '')
        }
        return message
    }

    return (
        <Activities
            className={ACTIVITY_WORKBENCH_LIST}
            getActivityParams={getActivityParams}
            render={activities => (
                <ActivityTimeline
                    activities={activities}
                    renderActivity={(activity: BamActivity, {textInfo /* Omit */, ...other}) =>
                        activity.hide ? null : (
                            <Timeline.Item
                                {...other}
                                key={activity.id}
                                title={renderTitle(activity)}
                                description={renderMessage(activity)}
                            />
                        )
                    }
                />
            )}
        />
    )
})
