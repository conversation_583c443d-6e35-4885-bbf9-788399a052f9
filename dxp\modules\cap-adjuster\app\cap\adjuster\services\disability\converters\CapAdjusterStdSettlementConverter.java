/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.disability.converters;

import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.disability.dto.CapStdSettlement;
import dataproviders.dto.CapStdSettlementDTO;

public class CapAdjusterStdSettlementConverter<I extends CapStdSettlementDTO, A extends CapStdSettlement>
        extends GenesisRootApiModelConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.policy = intDTO.policy;
        apiDTO.policyId = intDTO.policyId;
        apiDTO.settlementDetail = intDTO.settlementDetail;
        apiDTO.settlementNumber = intDTO.settlementNumber;
        apiDTO.settlementResult = intDTO.settlementResult;
        apiDTO.settlementType = intDTO.settlementType;
        apiDTO.state = intDTO.state;
        apiDTO.settlementAbsenceInfo = intDTO.settlementAbsenceInfo;
        apiDTO.settlementLossInfo = intDTO.settlementLossInfo;
        return apiDTO;
    }
}
