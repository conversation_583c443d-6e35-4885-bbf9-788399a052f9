include classpath("common.conf")

# Showing only provided APIs in Swagger UI
core.swagger.filter.paths.visible = ${?CORE_SWAGGER_FILTER_PATHS_VISIBLE}
core.swagger.api.schemes = ["https", "http"]

# Enabling CORS
play.filters.cors {
  allowedOrigins = null
  exposedHeaders = ${core.dataproviders.response.headers.forward}
}

genesis.cap.claim.baseurl = ${?GENESIS_CAP_CLAIM_APP_URL}
genesis.cap.generic.baseurl = ${?GENESIS_CAP_GENERIC_APP_URL}
genesis.cap.openl.baseurl = ${?GENESIS_CAP_OPENL_APP_URL}
genesis.platform.baseurl = ${?GENESIS_INFRA_APP_URL}
genesis.cem.baseurl = ${?GENESIS_CEM_APP_URL}
