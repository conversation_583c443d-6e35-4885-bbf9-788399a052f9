/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CoverageTypes} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {
    STATUS_MARKER_CLOSED,
    STATUS_MARKER_INCOMPLETE,
    STATUS_MARKER_OPEN,
    STATUS_MARKER_PENDING,
    STATUS_MARKER_ELIGIBLE,
    STATUS_MARKER_NOT_ELIGIBLE
} from './package-class-names'

export enum FrequencyType {
    CRON = 'CRON',
    WEEKLY = 'WEEKLY',
    BIWEEKLY = 'BIWEEKLY',
    SEMIMONTHLY = 'SEMIMONTHLY',
    MONTHLY = 'MONTHLY',
    SINGLEOCCURRENCE = 'SINGLEOCCURRENCE'
}

export enum PhoneMaskProps {
    MASK_10_DGT = '************',
    MASK_11_DGT = '9-************',
    MAX_LENGTH = 20,
    MIN_LENGTH = 10
}

export enum Paths {
    ABSENCE_PARTIES = 'data.absenceReasonPartyInformation.parties',
    ABSENCE_REASONS = 'data.loss.lossDetail.absenceReason',
    TMP_ABSENCE_REASONS = 'data.temporaryLoss.lossDetail.absenceReason',
    TEMPORARY_MILITARY_REASON_PARTY_ID = 'data.temporaryLoss.lossDetail.absenceReason.military.0.registryTypeId',
    TEMPORARY_BONDING_REASON_PARTY_ID = 'data.temporaryLoss.lossDetail.absenceReason.bondings.0.registryTypeId'
}

export enum PersonalRelationships {
    CHILD_DAUGHTER = 'Daughter',
    EMPLOYER = 'Employer'
}

export const eligibleType = {
    ELIGIBLE: 'ELIGIBLE',
    NOT_ELIGIBLE: 'NOT_ELIGIBLE'
}
export const OFFSETS_FORM_DRAWER = 'offsetsFormDrawer'

export const SMPEntity = 'CapSMPClaimEntity'

export const SMPLossEntity = 'CapSMPClaimEntity'

export const STDLossEntity = 'CapDisabilityClaimEntity'

export const LTDLossEntity = 'CapLTDisabilityClaimEntity'

export const LeaveLossEntity = 'CapLeaveClaimEntity'

export const OTHER_CLAIM = 'otherClaim'
export const LTD_CLAIM = 'LTDClaim'
export const STD_CLAIM = 'STDClaim'
export const SMD_CLAIM = 'SMDClaim'
export const LEAVE_CLAIM = 'CapLeave'
export const UPDATE_EVENT_CASE_INFORMATION = 'Update Information'
export const LOAD_SETTLEMENTS = 'loadSettlements'
export const UPDATE_SETTLEMENT = 'UpdateSettlements'
export const LOAD_CLAIMS_ACTION = 'loadClaimsAction'

export const GEROOT = 'geroot'

export const CUSTOMER_STRING = 'Customer'
export const ORGANIZATION_CUSTOMER = 'ORGANIZATIONCUSTOMER'

export const comaCoverageName = 'Coma Benefit'

export const lossStatusMarkerClasses = new Map<string, string>([
    ['INCOMPLETE', STATUS_MARKER_INCOMPLETE],
    ['PENDING', STATUS_MARKER_PENDING],
    ['OPEN', STATUS_MARKER_OPEN],
    ['CLOSED', STATUS_MARKER_CLOSED]
])

export const deductionAmountTypesAttribute = new Map<string, string>([
    ['WEEKLY', 'deductionWeeklyAmount'],
    ['MONTHLY', 'deductionMonthlyAmount'],
    ['FIXED', 'deductionFixedAmount'],
    ['PERCENTAGE', 'deductionPct']
])

export const workFlowStates = {
    COMPLETED: 'COMPLETED'
}

export const processDefinitionKeysValues = {
    CASE_PROCESSING: 'cap_event_case_processing',
    CASED_UPDATE: 'cap_event_case_update',
    LIFE_CI_HI_CLAIM_UPDATE: 'cap_life_ci_hi_claim_wrapper_update'
}

export enum DrawerWidth {
    SUPERLARGE = 1080,
    LARGE = 808,
    SMALL = 632
}

export enum SSNMaskProps {
    MASK_11_DGT = '***********',
    MAX_LENGTH = 11
}

export const LOSS_TYPES = {
    AccidentalDismemberment: 'AccidentalDismemberment',
    PremiumWaive: 'PremiumWaive',
    AcceleratedDeath: 'AcceleratedDeath',
    Death: 'Death'
}

export const COVERAGE_TYPES = {
    ADALostTime: 'ADALostTime',
    ADANoLostTime: 'ADANoLostTime'
}

export const PARTICIPANT_OTHER = 'other'

/**
 * Customer States from Backoffice customer search API
 */
export const CustomerState = {
    unqualified: 'unqualified',
    inQualification: 'inQualification',
    qualified: 'qualified',
    customer: 'customer'
}

export const CUSTOMER_STATES_TOSHOW = [
    CustomerState.unqualified,
    CustomerState.inQualification,
    CustomerState.qualified,
    CustomerState.customer
]

export const NoDataVal = 'N/A'

export const AUTO_OFFSETS: (string | undefined)[] = ['STNY', 'STNJ', 'PMMA', 'SHTM', 'PMCT', 'PFMA', 'PFNY', 'PFCT']

export const LossTypeAndClaimTypeMapWithoutPL = new Map<string, string[]>([
    ['HI', ['HospitalIndemnity', 'PremiumWaive']],
    ['CI', ['CriticalIllness', 'HospitalIndemnity', 'PremiumWaive']],
    ['TL', ['Death', 'AcceleratedDeath', 'Accident', 'PremiumWaive']],
    ['PL', ['Death', 'AcceleratedDeath', 'Accident', 'PremiumWaive']],
    ['ACC', ['Death', 'Accident', 'HospitalIndemnity']],
    ['STD', ['STD']],
    ['SMP', ['SMP']],
    ['LTD', ['LTD']],
    ['Leave', ['Leave']]
])

export const WORKFLOW_SERVICE = 'workflow-service'

export const DEFAULT_DATE_FORMAT = 'MM/DD/YYYY'

export const YYYY_DATE_FORMAT = 'YYYY'

export const CAP_INTAKE = 'intake'

export const CAP_EVENT_CASE = 'CapEventCase'

export const EVENT_CASE = 'EventCase'

export const CLAIM_WRAPPER = 'ClaimWrapper'

export const CLAIM_OVERVIEW = 'claim-overview'

export const CAP_LOSS = 'CapLoss'

export const PERIOD = 'Period'

export const OVERVIEW = 'overview'

export const LEAVE_CLAIM_NAME = 'leave-claim'

export const CAP_PAYMENT_SCHEDULE_ENTITY = 'CapPaymentScheduleEntity'
export const CAP_PREMIUM_WAIVER_SETTLEMENT_ENTITY = 'CapPremiumWaiverSettlementEntity'

export const LOAD_RELATIONSHIPS = 'loadRelationShips'
export const CREATE_RELATIONSHIPS = 'createRelationShips'
export const useRelationship = ['Daughter', 'Son', 'Husband', 'Wife', 'DomesticPartner']

export const relatedCustomerState = {QUALIFIED: 'qualified', CUSTOMER: 'customer'}
export const relationshipTypeCd = {PERSONAL: 'Personal'}
export const relationshipState = {
    ACTIVE: 'active'
}

export const DATE_FORMAT = 'YYYY-MM-DD'

export const lossTypeModuleNameMap = new Map<string, string>([
    [CAP_EVENT_CASE, 'event_case'],
    [CLAIM_WRAPPER, 'claim'],
    [LEAVE_CLAIM, 'leave_claim']
])

export const customerModelName = {
    IndividualCustomer: 'INDIVIDUALCUSTOMER',
    OrganizationCustomer: 'ORGANIZATIONCUSTOMER'
}

export const customerModelTypes = {
    IndividualCustomer: 'IndividualCustomer',
    OrganizationCustomer: 'OrganizationCustomer'
}

export declare enum PlacementType {
    BottomLeft = 'bottomLeft',
    BottomCenter = 'bottomCenter',
    BottomRight = 'bottomRight',
    TopLeft = 'topLeft',
    TopCenter = 'topCenter',
    TopRight = 'topRight'
}

export enum BAMProductModelNameList {
    CapEventCase = 'Case',
    ClaimWrapper = 'Claim',
    CapStd = 'Claim',
    CapSmp = 'Claim',
    CapLtd = 'Claim',
    CapLeave = 'Claim',
    CapSpecialHandlingCase = 'Case',
    CapSpecialHandlingClaim = 'Claim',
    CapSmpSettlement = 'Claim',
    CapStdSettlement = 'Claim',
    CapLtdSettlement = 'Claim',
    CapLeaveSettlement = 'Claim',
    AcceleratedSettlement = 'Claim',
    AccidentalDismembermentSettlement = 'Claim',
    CISettlement = 'Claim',
    HISettlement = 'Claim',
    DeathSettlement = 'Claim',
    LifeIntakeSettlement = 'Claim',
    PremiumWaiverSettlement = 'Claim',
    CapPaymentSchedule = 'Claim',
    PaymentDefinition = 'Claim'
}

export const DISABILITY_LOSS_TYPES = {
    SMP: 'SMP',
    STD: 'STD',
    LTD: 'LTD',
    LEAVE: 'Leave'
} as const

export const P_AND_C_CLAIM_TYPE_OPTIONS = [
    {displayValue: 'auto', code: 'CLAIM_AU'},
    {displayValue: 'home', code: 'CLAIM_HO'}
]

export const DENTAL_CLAIM_TYPE_OPTIONS = [{displayValue: 'dental', code: 'dental'}]

export const DEFAULT_PRORATING_RATE = 7
export const DEFAULT_MONTH_DAYS = 30
export const FREQUENCY_MONTHLY_DAYS = 30
export const FREQUENCY_SEMIMONTHLY_DAYS = 15
export const FREQUENCY_WEEKLY_DAYS = 7
export const FREQUENCY_BIWEEKLY_DAYS = 14

export const INDIVIDUAL_ACCUMULATOR_TYPES = [
    CoverageTypes.STD_CORE,
    CoverageTypes.SMP_NJTDB,
    CoverageTypes.LTD_CORE,
    CoverageTypes.STD_BU,
    CoverageTypes.LTD_BU
]

export const LIFE_CLAIM_TYPES = ['CI', 'HI', 'ACC', 'TL', 'PL']

export const SPECIAL_ACCUMULATOR_TYPES = [
    CoverageTypes.SMP_MAPFL,
    CoverageTypes.SMP_MAPML,
    CoverageTypes.SMP_NYDBL,
    CoverageTypes.SMP_NYPFL
]

export enum ApprovalPeriodStates {
    Approved = 'Approved',
    Cancelled = 'Cancelled',
    TBD = 'TBD',
    Completed = 'Completed'
}
export const GrossAmountModeTypeMap = new Map<string, number>([
    [FrequencyType.MONTHLY, FREQUENCY_MONTHLY_DAYS],
    [FrequencyType.SEMIMONTHLY, FREQUENCY_SEMIMONTHLY_DAYS],
    [FrequencyType.WEEKLY, FREQUENCY_WEEKLY_DAYS],
    [FrequencyType.BIWEEKLY, FREQUENCY_BIWEEKLY_DAYS]
])

// Types merged from cap-cse-system

export const CLAIM_GET_PARTIES = 'getParties'

export const CLAIM_CREATE_CUSTOMER_AND_PARTY_ROLE = 'createCustomerAndPartyRole'

export const CLAIM_INIT_PARTY_ROLE_FOR_CUSTOMER = 'initPartyRoleforCustomer'

export const CLAIM_UPDATE_CUSTOMER_AND_PARTY_ROLE = 'updateCustomerAndPartyRole'

export const CLAIM_PARTY_FORM_DRAWER_KEY = 'claimPartyFormDrawer'
export const EDIT_CLAIM_PARTY_FORM_DRAWER_KEY = 'editClaimPartyFormDrawer'

export const POST_RECOVERY_DRAWER_FORM_ID = 'postRecoveryDrawer'

export const ADA_NO_LOST_TIME = 'ADANoLostTime'

export const NA = t('cap-core:not_available')

export const CLAIM_PARTY_FORM_ID = 'PartyForm'

export const PAYMENT_VARIATION_UNDERPAYMENT = 'underpayment'

// Vendors selection
export const VENDORS_VIEW_DETAILS = 'vendorViewDetails'
export const VENDORS_EDIT_SERVICES = 'vendorsEditServices'
export const VENDORS_VIEW_RECORD = 'vendorsViewRecord'
export const VENDORS_DELETE = 'vendorsDelete'
export const VENDORS_DRAWER = 'vendorsDrawer'

export const PartyTableActionsMap = {
    EDITCONTACTINFO: 'partyActionEditContactInfo',
    EDITPARTYINFO: 'partyActionEditPartyInfo'
}

export interface IndividualPartyRoleInitialValue {
    roles: string[]
    associatedWith: string[]
}
export const LifeClaimTypesMap = {
    ACC: 'Accident',
    CI: 'CI',
    HI: 'HI',
    PL: 'Permanent Life',
    TL: 'Life'
}

export const DisabilityClaimTypesMap = {
    Leave: 'Leave',
    LTD: 'LTD',
    STD: 'STD',
    SMP: 'SMP'
}
export const ClaimTypesMap = {
    CI: 'CI',
    HI: 'HI',
    TL: 'TL',
    ACC: 'ACC',
    STD: 'CapStd',
    SMP: 'CapSmp',
    LTD: 'CapLtd',
    Leave: 'CapLeave',
    Absence: 'Absence',
    ClaimWrapper: 'ClaimWrapper',
    PL: 'PL',
    ADA: 'Leave'
}

export const PaymentListClaimTypesMap = {
    CI: 'CI',
    HI: 'HI',
    TL: 'Life',
    ACC: 'Accident',
    STD: 'STD',
    SMP: 'SMP',
    LTD: 'LTD',
    Leave: 'Leave',
    PL: 'PL'
}

export const ClaimTypesNameMap = {
    CI: 'Critical Illness',
    HI: 'Hospital Indemnity',
    TL: 'Term Life',
    ACC: 'Accident',
    PL: 'Whole Life',
    ADA: 'ADA - Work Accommodation'
}
export const LeaveClaimNameMap = {
    Leave: 'ADA - Work Accomodation'
}

export const SettlementModelName = {
    STD_SETTLEMENT: 'CapStdSettlement',
    SMP_SETTLEMENT: 'CapSmpSettlement',
    LTD_SETTLEMENT: 'CapLtdSettlement',
    LEAVE_SETTLEMENT: 'CapLeaveSettlement',
    CI_SETTLEMENT: 'CISettlement',
    HI_SETTLEMENT: 'HISettlement',
    DEATH_SETTLEMENT: 'DeathSettlement',
    PREMIUMWAIVER_SETTLEMENT: 'PremiumWaiverSettlement',
    ACCELERATED_SETTLEMENT: 'AcceleratedSettlement',
    ACCIDENTALDISMEMBERMENT_SETTLEMENT: 'AccidentalDismembermentSettlement'
}

export enum ReserveType {
    COVERAGE = 'INDEMNITY_RESERVE',
    EXPENSE = 'EXPENSE_RESERVE',
    EXGRATIA = 'EX_GRATIA_RESERVE'
}

export const PartyRoleCds = {
    PAYEE: 'Payee',
    CONTINGENT_BENEFICIARY: 'ContingentBeneficiary',
    PRIMARY_BENEFICIARY: 'PrimaryBeneficiary',
    MEMBER: 'Member',
    SUBJECTOFCLAIMS: 'SubjectOfClaims',
    EMPLUYER: 'Employer'
}

export enum PaymentStatus {
    Pending = 'Pending',
    IssueRequested = 'IssueRequested',
    Approved = 'Approved',
    Canceled = 'Canceled',
    Issued = 'Issued',
    StopRequested = 'StopRequested',
    FAILED = 'Failed'
}

export const PAYMENT_ACTIONS_MAP = {
    REQUEST_ISSUE: 'requestIssue',
    ISSUE: 'issue',
    CANCEL: 'cancel',
    CANCELRECOVERY: 'cancelRecovery',
    REQUEST_STOP: 'requestStop',
    APPROVE: 'approve',
    DISAPPROVE: 'disapprove',
    FAILOUNTBOUND: 'failOutBound'
}

export const PAYMENT_FAILED_MESSAGE = {
    REQUEST_STOP: 'StopRequested',
    VOIDED: 'Voided',
    DECLINED: 'Declined',
    STOPPED: 'Stopped'
}

export const SCHEDULE_PAYMENT_STATE_DISPLAY_MAP = new Map<string, string>([
    ['Open', 'PendingApproval'],
    ['Active', 'PendingPost'],
    ['Suspended', 'Suspended'],
    ['Canceled', 'Canceled'],
    ['Processing', 'PendingPost']
])

export const LifeCoverageTypesMap = {
    PremiumWaiver: 'cap-core:premium_waiver'
}

export const LeavePaidCoverageCodeMap = {
    NYPFL: 'NYPFL',
    MAPFL: 'MAPFL',
    CompanyPaid: 'CompanyPaid'
}

export const PaymentSelectActions = {
    CREATE_PAYMENT: 'createPayment',
    UPDATE_PAYMENT: 'updatePayment',
    SUSPEND_ALL_PAYMENTS: 'suspendAllPayments',
    UNSUSPEND_ALL_PAYMENTS: 'unsuspendAllPayments',
    CANCEL_ALL_PAYMENTS: 'cancelAllPayments',
    GENERATE_PAYMENT: 'generatePayment',
    POST_RECOVERY: 'postRecovery'
}
export const PaymentScheduleActions = {
    ACTIVATE_PAYMENT: 'activate',
    SUSPEND_PAYMENT: 'suspend',
    UNSUSPEND_PAYMENT: 'unsuspend',
    GENERATE_PAYMENT: 'generate',
    CANCEL_PAYMENT: 'cancel'
}

export enum RtwPeriodType {
    PTRTW = 'PTRTW',
    FTRTW = 'FTRTW',
    // value corresponds to key on CapReturnToWorkDateDetailEntity
    ACTUAL_FTRTW = 'actualFTRTW',
    ACTUAL_PTRTW = 'actualPTRTW',
    ESTIMATED_FTRTW = 'estimatedFTRTW',
    ESTIMATED_PTRTW = 'estimatedPTRTW'
}

export enum otherAbsencePeriodType {
    FIRST_DAY_HOSPITALIZATION = 'firstDayHospitalization',
    OUTPATIENT_SURGERY_DATE = 'outpatientSurgeryDate'
}

// Merged from event-case
export const CHANGE_EVENT_CASE_SUBSTATUS = 'changeCaseSubstatus'
export const CLOSE_EVENT_CASES = 'closeEventCases'
export const LOAD_EVENT_CASE = 'loadEventCase'
export const SILENT_LOAD_EVENT_CASE = 'silentLoadEventCase'
export const POLL_LOAD_EVENT_CASE = 'pollLoadEventCase'
export const SILENT_POLL_LOAD_EVENT_CASE = 'silentPollLoadEventCase'
export const REOPEN_EVENT_CASES = 'reopenEventCases'
export const UPDATE_EVENT_CASE = 'updateEventCase'
export const UPDATE_EVENT_CASE_DRAFT = 'updateEventCaseDraft'

// Refactored
export const ASSIGN_FORM_DRAWER_KEY = 'AssignFormDrawer'
export const REPORTING_PARTY = 'REPORTING_PARTY'
export const ASSOCIATEWITH = {
    Case: 'Case',
    ...LOSS_TYPES
}
export const PolicyTypesMap = {
    CP: 'CertificatePolicy',
    MP: 'MasterPolicy',
    INDIVIDUAL: 'IndividualPolicy'
}
export const APPROVAL_PERIOD_APPROVAL_STATUS = ['Approved', 'Cancelled', 'TBD', 'Completed']
export const UPDATE_CLAIM_WRAPPER = 'UpdateClaimWrapper'
export const CREATE_NEW_PARTY = 'CreateNewParty'
export enum MANUAL_CLOSE_FROM {
    CASE = 'Case',
    LIFE_CLAIM = 'LifeClaim',
    LTD = 'Ltd',
    SMP = 'Smp',
    STD = 'Std',
    LEAVE = 'Leave'
}
export enum ClaimLossTypeToModelName {
    STD = 'CapStd',
    LTD = 'CapLtd',
    SMP = 'CapSmp',
    Leave = 'CapLeave',
    ACC = 'ClaimWrapper',
    TL = 'ClaimWrapper',
    CI = 'ClaimWrapper',
    HI = 'ClaimWrapper',
    PL = 'ClaimWrapper'
}

export enum ActivityEntities {
    DocumentEntity = 'DocumentEntity',
    NoteEntity = 'NoteEntity',
    Task = 'Task',
    Case = 'Case',
    CapLossOverview = 'CapLossOverview'
}

export enum SETTLEMENT_MODEL_NAME {
    DEATH_SETTLEMENT = 'DeathSettlement',
    PREMIUM_WAIVER_SETTLEMENT = 'PremiumWaiverSettlement',
    CI_SETTLEMENT = 'CISettlement',
    HI_SETTLEMENT = 'HISettlement',
    ACCELERATED_SETTLEMENT = 'AcceleratedSettlement',
    ACCIDENTAL_DISMEMBERMENT_SETTLEMENT = 'AccidentalDismembermentSettlement'
}

export const LifeAndDisabilityScope = {
    TL: [
        'Death',
        'AcceleratedDeath',
        'AccidentalDismemberment',
        'PremiumWaive',
        'HospitalIndemnity',
        'CriticalIllness',
        'Accident'
    ],
    DISABILITY: ['STD', 'SMP', 'LTD', 'Leave']
}

export const BALANCE_ACTIONS_MAP = {
    ADD_EXTERNAL_OVERPAYMENT: 'addExternalOverpayment',
    WAIVE_OVERPAYMENT: 'waiveOverpayment',
    PAY_UNDERPAYMENT: 'payUnderpayment',
    REDUCE_PAYMENT: 'reducePayment',
    POST_RECOVERY: 'postRecovery',
    CANCEL_EXTERNAL_OVERPAYMENT: 'cancelExternalOverpayment',
    CANCEL_WAIVE_OVERPAYMENT: 'cancelWaiveOverpayment'
}

export const BALANCE_ACTION_FORM_ID = 'balanceActionForm'
export const BALANCE_ACTION_ADD_EXTERNAL_OVERPAYMENT_FORM_ID = 'balanceActionFormAddExternalOverpayment'
export const BALANCE_ACTION_CANCEL_EXTERNAL_OVERPAYMENT_FORM_ID = 'balanceActionFormCancelExternalOverpayment'
export const BALANCE_ACTION_CANCEL_WAIVE_OVERPAYMENT_FORM_ID = 'balanceActionFormCancelWaiveOverpayment'
export const BALANCE_ACTION_REDUCE_PAYMENT_FORM_ID = 'balanceActionFormReducePayment'

export const PAYMENT_LEVEL = {
    Claim: 'Claim',
    EventCase: 'EventCase'
}
export const TRANSACTIONTYPE = {
    PAYMENT: 'Payment',
    RECOVERY: 'Recovery',
    UNDERPAYMENT: 'Underpayment'
}

export const ASO_TYPE = {
    ATP: 'ATP',
    APTWithCheck: 'ATPWithCheck'
}
export const DeductionTypes = {
    CHILD_SUPPORT: 'CHSP',
    WAGE_GARNISHMENT: 'WAGA'
}

export const DEDUCTION_TYPES_WITH_REQUIRED_BENEFICIARY = [DeductionTypes.CHILD_SUPPORT, DeductionTypes.WAGE_GARNISHMENT]

export const grossAmountModeToolTips = new Map<string, string>([
    ['MONTHLY', 'Monthly Amount'],
    ['WEEKLY', 'Weekly Amount'],
    ['SEMIMONTHLY', 'Semi-Monthly Amount'],
    ['BiWEEKLY', 'Biweekly Amount']
])

export const grossAmountModeDesc = new Map<string, string>([
    ['MONTHLY', 'Per Month'],
    ['WEEKLY', 'Per Week'],
    ['SEMIMONTHLY', 'Per Bi-Month'],
    ['BiWEEKLY', 'Per Bi-week']
])

export const PolicyInfoMap = new Map<string, string>([
    ['CISettlement', 'ci'],
    ['HISettlement', 'hi'],
    ['AccidentalDismembermentSettlement', 'accidentalDismemberment'],
    ['AcceleratedSettlement', 'accelerated'],
    ['DeathSettlement', 'death']
])

export enum CLAIM_COVERAGE_NAME {
    BURN = 'Burn',
    SURGERY = 'Surgery',
    ANESTHESIA = 'Anesthesia',
    BURN_SKIN_GRAFT = 'Burn Skin Graft',
    REHABILITATION_UNIT_CONFINEMENT = 'Rehabilitation Unit Confinement',
    SKILLED_NURSING_FACILITY = 'Skilled Nursing Facility',
    HOSPITAL_CONFINEMENT = 'Hospital Confinement',
    WELLNESS_BENEFIT = 'Wellness Benefit'
}

export const eligibleStatusMarkerClasses = new Map<string, string>([
    ['ELIGIBLE', STATUS_MARKER_ELIGIBLE],
    ['NOT_ELIGIBLE', STATUS_MARKER_NOT_ELIGIBLE]
])

export const SETTLEMENTS_FORM_TYPE = {
    APPROVAL_PERIOD: 'ApprovalPeriod',
    BENEFIT_PERIOD: 'BenefitPeriod',
    ELIMINATION_PERIOD: 'EliminationPeriod'
}

export const BENEFIT_PERIOD_STATUS = {
    NOT_STARTED: 'NotStarted',
    COMPLETED: 'Completed',
    IN_PROGRESS: 'InProgress'
}

export const RoundingType = {
    Nearest: 'rounding_factor_Nearest',
    NextHigher: 'rounding_factor_Next_Higher',
    NextLower: 'rounding_factor_Next_Lower'
}

export const EVENT_TYPE_CD = {
    DLW: 'DLW',
    ADOPTION_PLACEMENT: 'ADOPTION_PLACEMENT',
    BABY_BORN: 'BABY_BORN',
    CERTIFIED_THROUGH: 'CERTIFIED_THROUGH',
    DEPLOYMENT_DATE: 'DEPLOYMENT_DATE',
    EMERGENCY_ROOM: 'EMERGENCY_ROOM',
    DUE_DATE: 'DUE_DATE',
    TREATMENT_DATE: 'TREATMENT_DATE',
    HOSPITAL_ADMISSION: 'HOSPITAL_ADMISSION',
    HOSPITAL_DISCHARGE: 'HOSPITAL_DISCHARGE',
    NEXT_ASSESSMENT: 'NEXT_ASSESSMENT',
    RTW: 'RTW',
    SURGERY: 'SURGERY'
}

export const RELATIONSHIP = {
    GUARDIAN: 'Guardian',
    PARENT: 'Parent',
    SPOUSE: 'Spouse',
    CHILD: 'Child'
}

export const RELATIONSHIP_STATE = {
    DELETED: 'deleted'
}

export const PROMISE_STATUS = {
    FULLFILLED: 'fulfilled'
}

export const POLL_COUNT = 3
export const POLL_DELAY = 3000
export const WAIT_DELAY = 3000

export const CALC_PAYMENTS = 'paymentWizardCalcPayments'
export const CREATE_PAYMENTS = 'paymentWizardCreatePayments'
export const CALCULATE_PAYMENTS = 'calculatePayments'
export const CHANGE_PAYMENT_STATUS = 'changePaymentStatus'
export const LOAD_ASSOCIATED_SETTLEMENTS = 'loadAssociatedSettlements'
export const LOAD_PAYMENTS = 'loadPayments'
export const LOAD_PAYMENT_TEMPLATE = 'loadPaymentTemplate'
export const GENERATE_RECOVERY = 'generateRecovery'
export const ALLOCATION_LOB_CODE = {
    ABSENCE: 'Disability',
    LIFE: 'Life'
}
export enum ADD_OR_EDIT {
    ADD = 'add',
    EDIT = 'edit'
}

export const SettlementModelNamesForDisability = {
    CapStdSettlement: 'CapStdSettlement',
    CapSmpSettlement: 'CapSmpSettlement',
    CapLtdSettlement: 'CapLtdSettlement',
    CapLeaveSettlement: 'CapLeaveSettlement'
}

export const OTHER = 'OTHER'
export const MANUAL_EOB = 'EOB'
export const AUTO_EOB = 'AutoEOB'

export const ORGANIZATION_CUSTOMER_STATE = {
    UNQUALIFIED: 'unqualified',
    IN_QUALIFICATION: 'inQualification',
    QUALIFIED: 'qualified',
    CUSTOMER: 'customer',
    ARCHIVED: 'archived',
    INVALID: 'invalid',
    ANONYMIZED: 'anonymized'
}

export const LossModelNames = {
    CapEventCase: 'CapEventCase',
    CapStd: 'CapStd',
    CapLtd: 'CapLtd',
    CapLeave: 'CapLeave',
    CapSmp: 'CapSmp',
    ClaimWrapper: 'ClaimWrapper'
}
