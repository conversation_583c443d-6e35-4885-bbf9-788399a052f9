/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapEventCase, ClaimWrapper} from '@eisgroup/cap-event-case-models'
import {
    backofficeCustomerCommonService,
    backofficeCustomerRelationshipService,
    backofficeIndCustomerService,
    backofficeOrgCustomerService,
    backofficeProviderService,
    ClaimParty,
    claimPartyService,
    CustomerType,
    CustomerWithPartyRolesRequest,
    CustomerWithPartyRolesResponse,
    FormInputType,
    IndividualCustomer,
    IOCustomer,
    OrganizationCustomer,
    PartyRole,
    Provider,
    Relationship
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {opt, RxResult} from '@eisgroup/common-types'
import {PaymentMethod, PaymentMethodCustomer, paymentMethodService} from '@eisgroup/common-business-components'
import {Right} from '@eisgroup/data.either'
import {action, IObservableArray, observable, runInAction, toJS} from 'mobx'
import {Observable} from 'rxjs'
import {DrawerFormStateType} from '../../components/form-drawer'
import {PreferredContactTypes} from '../../components/preferred-contact-info/PreferredContactInfo'
import {ifExistsInParties} from '../../utils/ClaimPartyUtils'
import {createIndividualCustomer} from '../../utils/CustomerUtils'
import {EntityLink} from '../../utils/EntityLink'
import {CREATE_NEW_PARTY} from '../constants'
import {OpenPartyDrawerPayload, PaymentMethodWithFormPreferred} from '../Types'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'
import {CustomerStore} from './CustomerStore'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapBeneficiaryRole = ClaimWrapper.CapBeneficiaryRole
import CapClaimWrapperEntity = ClaimWrapper.CapClaimWrapperEntity

export const PROVIDER_LIMIT_COUNT = 100

export interface ClaimPartyStore extends BaseRootStore {
    /**
     * Parties representing person party and its roles.
     */
    parties: IObservableArray<ClaimParty>
    beneficiaries: IObservableArray<ClaimParty>
    drawerFormStateType?: DrawerFormStateType
    openedDrawerKey?: string
    /**
     * Describes which type of input the form has.
     */
    formInputType?: FormInputType
    /**
     * Describes formInputType original type.
     */
    originalFormInputType?: FormInputType
    /**
     * Whether to display the return button in party information
     */
    showBackButton?: boolean
    /**
     * Describes type of customer that is beeing inputed.
     */
    customerType: CustomerType
    /**
     * Object used for storing person party data during its creation or modification.
     */
    temporaryFormParty: ClaimParty
    /**
     * whether to load
     */
    isLoading?: boolean
    /**
     * Related parties to main insured
     */

    relatedParties: IObservableArray<IndividualCustomer>
    /**
     * Action used to open the drawer
     */
    openDrawer: (drawerKey: string, drawerStateType?: DrawerFormStateType) => void
    /**
     * Action used to close the drawer and accept changes.
     */
    closeDrawer: (isGuardian?: boolean) => void
    /**
     * Handles form input change
     */
    handleFormInputTypeChange: (formInputType: FormInputType) => void
    /**
     * Handles original formInputType change
     */
    setOriginalFormInputType: (formInputType: FormInputType) => void
    /**
     * Action for initializing party form drawer.
     */
    initPartyDetailDrawer: (payload: OpenPartyDrawerPayload) => void
    /**
     * Changes customer type
     */
    changeCustomerType: (customerType: CustomerType) => void
    /**
     * Action for creating a new temporary form party that is used for data gather.
     */
    newFormParty: (isBeneficiary: boolean, role?: string, associatedWith?: string) => void
    /**
     * Action to set temporary form party that is used for data gather.
     */
    setFormParty: (party: ClaimParty) => void
    /**
     * Action for creating customer
     */
    createCustomer: (request: IOCustomer) => Promise<IOCustomer>
    /**
     * Action for  creating new part with customer.
     */
    createNewParty: (request: CustomerWithPartyRolesRequest) => Promise<IOCustomer>

    updateCustomer: (customer: IOCustomer) => Promise<IOCustomer>
    /**
     * Returns customer by provided root id.
     */
    searchCustomerByRootId: (rootId: string) => Promise<IOCustomer>
    /**
     * Updates parties
     */
    updateParties: (parties: ClaimParty[]) => void
    /**
     * Updates party to parties
     */
    updateParty: (request: CustomerWithPartyRolesResponse) => void
    /**
     * Updates related parties
     */
    updateRelatedParties: (relatedParties: IndividualCustomer[]) => void
    setLoadingStatus: (isLoading: boolean) => void

    /**
     * func to set beneficiary beneficiaryType
     */
    setPartyBeneficiaryType: (beneficiaryType?: string, customer?: IndividualCustomer | OrganizationCustomer) => void
    updateRole: (partyRole: PartyRole) => void

    updatePartiesWithEventCase: (eventCase: CapEventCaseEntity, newCreateCustomer?: IOCustomer | IOCustomer[]) => void
    updatePartiesWithClaimWrapper: (
        claimWrapper: CapClaimWrapperEntity,
        newCreateCustomer?: IOCustomer | IOCustomer[]
    ) => void
    providerServicesMapper: Map<string, string[]>
    providers: Provider[]
    paymentMethods: PaymentMethodWithFormPreferred[]
    onPreferredPaymentMethodChange: (id: string) => void
    /**
     * loadpayments
     * the second params is for edit party,init data
     */
    loadPaymentMethods: (customer: PaymentMethodCustomer, partyRole?: PartyRole) => RxResult<PaymentMethod[]>
    checkAddressId?: string
    setCheckAddressId: (checkAddressId?: string) => void
    updateCustomerCem: (customer: IndividualCustomer) => Promise<IndividualCustomer>
    updateOrganizationCustomerCem: (customer: OrganizationCustomer) => Promise<OrganizationCustomer>
    clearCustomerAndPayments: () => void
    getCustomerRelationships: (customerRootId: string) => Promise<Relationship[]>
}

export class ClaimPartyStoreImpl extends BaseRootStoreImpl implements ClaimPartyStore {
    customerStore: CustomerStore | undefined

    @observable parties: IObservableArray<ClaimParty> = observable<ClaimParty>([])

    @observable beneficiaries: IObservableArray<ClaimParty> = observable<ClaimParty>([])

    @observable openedDrawerKey?: string

    @observable drawerFormStateType?: DrawerFormStateType

    @observable formInputType?: FormInputType

    @observable originalFormInputType?: FormInputType

    @observable showBackButton?: boolean = true

    @observable customerType: CustomerType

    @observable temporaryFormParty: ClaimParty = {
        customer: createIndividualCustomer(PreferredContactTypes.PHONE)
    }

    @observable isLoading?: boolean

    @observable relatedParties: IObservableArray<IndividualCustomer> = observable<IndividualCustomer>([])

    @observable providerServicesMapper: Map<string, string[]>

    @observable providers: Provider[]

    @observable paymentMethods: PaymentMethodWithFormPreferred[] = []

    @observable checkAddressId: string | undefined

    constructor(parentStore?) {
        super()
        if (parentStore) {
            this.actionsStore = parentStore.actionsStore
            this.customerStore = parentStore.customerStore
        }
        this.providerServicesMapper = new Map<string, string[]>([])
    }

    @action
    setCheckAddressId = (checkAddressId?: string) => {
        this.checkAddressId = checkAddressId
    }

    @action
    loadPaymentMethods = (customer: PaymentMethodCustomer, partyRole?: PartyRole): RxResult<PaymentMethod[]> => {
        return this.call(() => paymentMethodService.loadPaymentMethods(customer)).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    if (partyRole) {
                        const {paymentMethodId, checkAddressId} = partyRole
                        let preCheckAddressId = checkAddressId
                        this.paymentMethods = payload.map(v =>
                            v._key?.id === paymentMethodId ? {...v, formPreferred: true} : {...v, formPreferred: false}
                        )
                        // if have preferred address, should pre-fill
                        if (!checkAddressId) {
                            preCheckAddressId = customer?.communicationInfo?.addresses?.find(v => v.preferred)?._key?.id
                        }
                        this.checkAddressId = preCheckAddressId
                    } else {
                        this.paymentMethods = payload
                    }
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    updatePaymentMethods = (
        paymentMethods: PaymentMethod[],
        customer: PaymentMethodCustomer | IOCustomer
    ): RxResult<PaymentMethodCustomer> => {
        return this.call(() =>
            paymentMethodService.updatePaymentMethods(paymentMethods, customer as PaymentMethodCustomer)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    updatePaymentMethodCustomer = (customer?: PaymentMethodCustomer | IOCustomer) => {
        if (customer) {
            this.loadPaymentMethods(customer as PaymentMethodCustomer).subscribe()
        } else {
            this.paymentMethods = []
        }
    }

    @action
    onPreferredPaymentMethodChange = (id: string) => {
        this.paymentMethods = this.paymentMethods.map(v =>
            v._key?.id === id ? {...v, formPreferred: true} : {...v, formPreferred: false}
        )
    }

    @action
    clearCustomerAndPayments = () => {
        this.paymentMethods = []
    }

    @action
    openDrawer = (drawerKey: string, drawerStateType?: DrawerFormStateType) => {
        this.drawerFormStateType = drawerStateType
        this.openedDrawerKey = drawerKey
    }

    @action
    closeDrawer = () => {
        this.drawerFormStateType = undefined
        this.openedDrawerKey = undefined
        this.setLoadingStatus(false)
        this.newFormParty()
        this.setCheckAddressId(undefined)
        this.clearCustomerAndPayments()
    }

    @action
    setLoadingStatus = (isLoading: boolean) => {
        this.isLoading = isLoading
    }

    @action
    handleFormInputTypeChange = (formInputType: FormInputType) => {
        this.formInputType = formInputType
    }

    @action
    setOriginalFormInputType = (originalFormInputType: FormInputType) => {
        this.originalFormInputType = originalFormInputType
    }

    @action
    initPartyDetailDrawer = (payload: OpenPartyDrawerPayload) => {
        this.changeCustomerType(payload.customerType)
        this.formInputType = payload.formInputType
        this.showBackButton = payload.showBackButton
    }

    @action
    changeCustomerType = (customerType: CustomerType) => {
        this.customerType = customerType
    }

    @action
    newFormParty = (isBeneficiary = false, role?: string, associatedWith?: string) => {
        if (!isBeneficiary) {
            this.temporaryFormParty = {
                customer: createIndividualCustomer(PreferredContactTypes.PHONE),
                partyRole: {
                    roleCd: role ? [role] : []
                } as PartyRole
            }
        } else {
            this.temporaryFormParty = {
                customer: createIndividualCustomer(PreferredContactTypes.PHONE),
                partyRole: {
                    ...ClaimWrapper.factory.newByType<CapBeneficiaryRole>(CapBeneficiaryRole),
                    roleCd: role ? [role] : []
                } as PartyRole
            }
        }
    }

    @action
    setFormParty = (party: ClaimParty) => {
        this.temporaryFormParty = {
            ...this.temporaryFormParty,
            ...party
        }
    }

    @action
    setPartyBeneficiaryType = (beneficiaryType?: string, customer?: IndividualCustomer | OrganizationCustomer) => {
        this.temporaryFormParty = {
            ...this.temporaryFormParty,
            customer: customer || this.temporaryFormParty.customer,
            partyRole: {
                ...this.temporaryFormParty.partyRole,
                beneficiaryDesignations: [
                    {
                        ...this.temporaryFormParty.partyRole?.beneficiaryDesignations?.[0],
                        beneficiaryType
                    }
                ]
            } as PartyRole
        }
    }

    @action
    getCustomerRelationships = (customerRootId: string): Promise<Relationship[]> => {
        return this.promiseCall(() => backofficeCustomerRelationshipService().getCustomerRelationships(customerRootId))
    }

    @action
    createCustomer = (customer: IOCustomer): Promise<IOCustomer> => {
        return this.promiseCall(() => claimPartyService.createCustomer(customer))
    }

    @action
    createNewParty = (request: CustomerWithPartyRolesRequest): Promise<IOCustomer> => {
        const {customerType} = request
        const customer = request.customer
        const customerRootId = customer._key?.rootId
        const modelName = customer._modelName
        const registryTypeId = `geroot://Customer/${modelName}//${customerRootId}`
        return this.promiseCall(async () => {
            let result
            if (customerType === CustomerType.Individual) {
                result = await this.promiseCall(() =>
                    backofficeIndCustomerService().saveIndividualCustomer({
                        ...customer,
                        details: {
                            ...customer.details,
                            person: {
                                ...(customer as IndividualCustomer).details.person,
                                registryTypeId
                            }
                        }
                    } as IndividualCustomer)
                )
            } else {
                result = await this.promiseCall(() =>
                    backofficeOrgCustomerService().saveOrganizationCustomer({
                        ...customer,
                        details: {
                            ...customer.details,
                            legalEntity: {
                                ...(customer as OrganizationCustomer).details.legalEntity,
                                registryTypeId
                            }
                        }
                    })
                )
            }
            runInAction(() => this.updateParty({customer: result} as CustomerWithPartyRolesResponse))
            return result
        }, CREATE_NEW_PARTY)
    }

    @action
    updateParty = (response: CustomerWithPartyRolesResponse): void => {
        const idx = this.parties.findIndex(p => ifExistsInParties(p, opt(response.customer.details)))
        if (idx !== -1) {
            this.parties[idx] = response
        } else {
            this.parties.push(response)
        }
    }

    updateCustomerDataInCustomerStore = <T extends IOCustomer>(customer: T): T => {
        if (this.customerStore) {
            this.customerStore.updateCustomerInAssociatedList(customer)
        }
        return customer
    }

    @action
    updateCustomer = (customer: IOCustomer): Promise<IOCustomer> => {
        return this.promiseCall(() => claimPartyService.updateCustomer(customer))
    }

    @action
    updateCustomerCem = (customer: IndividualCustomer): Promise<IndividualCustomer> => {
        return this.promiseCall(() => backofficeIndCustomerService().updateIndividualCustomer(customer)).then(
            this.updateCustomerDataInCustomerStore
        )
    }

    @action
    updateOrganizationCustomerCem = (customer: OrganizationCustomer): Promise<OrganizationCustomer> => {
        return this.promiseCall(() => backofficeOrgCustomerService().updateOrganizationCustomer(customer)).then(
            this.updateCustomerDataInCustomerStore
        )
    }

    @action
    searchCustomerByRootId = (rootId: string): Promise<IOCustomer> => {
        return this.promiseCall(() =>
            (this.customerStore
                ? this.customerStore.getOrLoadCustomer(rootId)
                : backofficeCustomerCommonService().searchCustomerByRootId(rootId)
            ).then(response => {
                const idx = this.parties.findIndex(p => p.customer?._key?.rootId === response._key?.rootId)
                runInAction(() => {
                    if (idx !== -1) {
                        this.parties[idx].customer = response
                    } else {
                        this.parties.push({customer: response})
                    }
                })
                return response
            })
        )
    }

    @action
    updatePartiesWithEventCase = (eventCase: CapEventCaseEntity, customer): void => {
        const partyRoles = [
            eventCase.memberRole,
            eventCase.lossDetail?.employmentDetail?.employerRole,
            eventCase.lossDetail?.subjectOfCaseRole,
            ...(eventCase.lossDetail?.additionalRole || [])
        ].filter(Boolean) as PartyRole[]
        const partyRegistryIds = [
            ...(eventCase.lossDetail?.additionalRole || []).map(v => v.registryId),
            eventCase.lossDetail?.subjectOfCaseRole?.registryId,
            eventCase.lossDetail?.employmentDetail?.employerRole?.registryId,
            eventCase.memberRole?.registryId
        ].filter(Boolean)
        if (!eventCase.memberRole && eventCase.memberRegistryTypeId) {
            partyRegistryIds.push(eventCase.memberRegistryTypeId)
            partyRoles.push({
                registryId: eventCase.memberRegistryTypeId,
                roleCd: ['Member'],
                checkAddressId: eventCase.lossDetail?.memberCheckAddressId,
                paymentMethodId: eventCase.lossDetail?.memberPaymentMethodId
            } as PartyRole)
        } else if (eventCase.memberRole) {
            // replace memberRole
            partyRoles[0] = {
                ...eventCase.memberRole,
                checkAddressId: eventCase.lossDetail?.memberCheckAddressId,
                paymentMethodId: eventCase.lossDetail?.memberPaymentMethodId
            }
        }
        const rootIds = [...new Set(partyRegistryIds.map(v => EntityLink.from(v || '').rootId))]
        if (!rootIds.length) {
            this.updateParties([])
            return
        }
        this.updatePartiesWithCustomerRootIds(rootIds, partyRoles, true, customer)
    }

    @action
    updatePartiesWithClaimWrapper = (claimWrapper: CapClaimWrapperEntity, customer): void => {
        const partyRoles = claimWrapper.lossDetail?.beneficiaryRole || ([] as PartyRole[])
        const partyRegistryIds = partyRoles.map(v => v.registryId).filter(Boolean)
        const rootIds = partyRegistryIds.map(v => EntityLink.from(v || '').rootId)
        const representativeRegistryIds = partyRoles.map(v => v.representativeRegistryId).filter(Boolean)
        const representativeRootIds = representativeRegistryIds.map(v => EntityLink.from(v || '').rootId)
        if (!rootIds.length) {
            this.updateBeneficiaries([])
            return
        }
        this.updatePartiesWithCustomerRootIds([...rootIds, ...representativeRootIds], partyRoles, true, customer, true)
    }

    getTempParties = (partyRoles, customers) => {
        const partiesResult = [] as CustomerWithPartyRolesResponse[]
        partyRoles.forEach((partyRole: PartyRole) => {
            const curCustomer = customers.filter(customer => {
                const registryId =
                    customer._modelName === 'INDIVIDUALCUSTOMER'
                        ? customer.details.person.registryTypeId
                        : customer.details.legalEntity.registryTypeId
                return partyRole?.registryId === registryId
            })[0]
            let representativeCustomer
            if (partyRole.representativeRegistryId) {
                representativeCustomer = customers.filter(
                    customer => partyRole?.representativeRegistryId === customer.details?.person?.registryTypeId
                )[0]
            }
            if (curCustomer) {
                let partyItem = {
                    customer: curCustomer,
                    partyRole
                } as CustomerWithPartyRolesResponse
                if (representativeCustomer) {
                    partyItem = {
                        ...partyItem,
                        representativeCustomer
                    } as CustomerWithPartyRolesResponse
                }
                partiesResult.push(partyItem)
            }
        })
        return partiesResult
    }

    updatePartiesWithCustomerRootIds = (rootIds, partyRoles, needCallDxp, customer, updateBeneficiaries?) => {
        this.promiseCall(() =>
            (this.customerStore && !needCallDxp
                ? this.customerStore.getOrLoadCustomer(rootIds)
                : backofficeCustomerCommonService().searchCustomersByRootId(rootIds)
            ).then(r => {
                runInAction(() => {
                    const customers = this.getAllCustomers(r, customer)
                    const parties = this.getTempParties(partyRoles, customers)
                    this.mapVendorServicesToCustomer(customers)
                    if (!updateBeneficiaries) {
                        this.updateParties(parties)
                    } else {
                        this.updateBeneficiaries(parties)
                    }
                })
            })
        )
    }

    @action
    mapVendorServicesToCustomer = (customers: IOCustomer[]): void => {
        const customerNumbers = customers.map(customer => customer.customerNumber || '')
        if (customerNumbers) {
            this.promiseCall(() => backofficeProviderService().searchProvidersByCustomerIDs(customerNumbers)).then(
                vendors => {
                    runInAction(() => {
                        this.providers = vendors
                    })
                    vendors.forEach(v => {
                        const customerRootId = v.customer?._key?.rootId || ''
                        const providerServices = v.provider?.serviceCd || ['']
                        const party = this.parties.find(p => p.partyRole?.registryId === v.partyRole?.party?._uri)
                        const partyIndex = this.parties.indexOf(party ?? {})
                        runInAction(() => {
                            this.providerServicesMapper.set(customerRootId, providerServices)
                            if (party && partyIndex) {
                                party.provider = v.provider || {}
                                this.parties.replace(
                                    this.parties.map((partyItem, index) => (index === partyIndex ? party : partyItem))
                                )
                            }
                        })
                    })
                }
            )
        }
    }

    @action
    updateParties = (parties: ClaimParty[]) => this.parties.replace(parties)

    @action
    updateBeneficiaries = (beneficiaries: ClaimParty[]) => this.beneficiaries.replace(beneficiaries)

    @action
    updateRelatedParties = (relatedParties: IndividualCustomer[]) => this.relatedParties.replace(relatedParties)

    @action
    updateRole = (partyRole: PartyRole): void => {
        if (!partyRole?.party && !partyRole?.registryId) {
            return
        }
        const partyToUpd = this.parties.find(p => {
            const regId = (p.customer as IndividualCustomer)?.details?.person?.registryTypeId
                ? (p.customer as IndividualCustomer)?.details?.person?.registryTypeId
                : (p.customer as OrganizationCustomer)?.details?.legalEntity?.registryTypeId
            return !!(regId && (regId === partyRole.party?._uri || regId === partyRole.registryId))
        })
        if (partyToUpd) {
            partyToUpd.partyRole = partyRole
            this.updateParty(partyToUpd as CustomerWithPartyRolesResponse)
        }
    }

    private getAllCustomers(r, customer: any) {
        const customers = r
        if (customer) {
            const tmpCustomers = Array.isArray(toJS(customer)) ? toJS(customer) : [toJS(customer)]
            tmpCustomers.forEach(c => {
                if (r.map(v => v._key?.rootId).indexOf(c._key.rootId) === -1) {
                    customers.push(c)
                }
            })
        }
        return customers
    }
}
