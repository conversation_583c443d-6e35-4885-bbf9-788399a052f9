import {action, computed, toJS} from 'mobx'
import {omit} from 'lodash'
import {PaginationConfig} from '@eisgroup/ui-kit'
import {ISuggestion} from '../../search/AutocompletableSearch'
import {CaseTableStore, CaseTableStoreInitType} from './CaseTableStore'
import {CaseAdvanceFilterStore} from './CaseAdvanceFilterStore'
import {CaseSuggestionStore, CaseSuggestionStoreInitType} from './CaseSuggestionStore'
import {SorterResult} from '../types/SorterResultType'
import {CaseSearchResultType} from '../types/CaseSearchResultType'

export class CaseSearchRootStore {
    searchResultTableStore = new CaseTableStore()

    searchFilterStore = new CaseAdvanceFilterStore()

    searchInputStore = new CaseSuggestionStore()

    private onSelectCaseCallBack?: (value?: string) => void

    private onFocusCallback?: () => void

    private onBlurCallback?: () => void

    @action
    init = (props?: {
        selectCaseCallback?: (value?: string) => void
        onFocusCallback?: () => void
        onBlurCallback?: () => void
        suggestionInitProps?: CaseSuggestionStoreInitType
        caseTableInitProps?: CaseTableStoreInitType
    }) => {
        this.onSelectCaseCallBack = props?.selectCaseCallback
        this.onFocusCallback = props?.onFocusCallback
        this.onBlurCallback = props?.onBlurCallback
        this.searchInputStore.init(props?.suggestionInitProps)
        this.searchResultTableStore.init(props?.caseTableInitProps)
    }

    /**
     * get suggestions for autocomplete component
     */
    @computed
    get suggestions(): ISuggestion[] {
        return this.searchInputStore.searchSuggestionsResult.map(suggestion => {
            return {
                key: suggestion._key.rootId,
                value: suggestion.caseNumber,
                type: suggestion.claimNumber,
                hints: [{hint: suggestion?.subjectOfClaimInfo?.customerName ?? ''}]
            }
        })
    }

    /**
     * Called when autocomplete input changes
     * @param searchValue current search value
     */
    @action
    changeAutocompleteValue = (searchValue: string | undefined, changeSearchValue = true) => {
        const currentValue = searchValue?.trim()

        if (!currentValue) {
            this.resetAll()
            this.onSelectCaseCallBack?.(currentValue)
            this.onBlurCallback?.()
        }
        if (changeSearchValue) {
            this.searchInputStore.changeSearchValue(currentValue)
        }
    }

    /**
     * Called when autocomplete searching
     * @param searchValue current search value
     */
    @action
    searchAutocompleteSuggestions = (searchValue: string | undefined) => {
        const trimValue = searchValue?.trim()

        if ((trimValue?.length ?? 0) >= this.searchInputStore.searchingThreshold) {
            this.searchInputStore.searchSuggestions(trimValue)
        } else {
            this.searchInputStore.setSuggestionsCount(0)
            this.searchInputStore.setSuggestionsResult([])
        }

        if (this.searchResultTableStore.showCaseTable) {
            this.loadInitCaseTable()
        }
    }

    /**
     * Called when select one suggestion
     * @param value current key
     * @param suggestion current suggestion
     * @returns caseNumber
     */
    @action
    onAutocompleteSuggestionSelected = (value: string, suggestion?: ISuggestion) => {
        // when a suggestion selected
        const caseNumber = this.searchInputStore.searchSuggestionsResult.find(
            result => result?._key?.rootId === value
        )?.caseNumber

        this.searchInputStore.changeSearchValue(caseNumber)
        this.searchInputStore.changeSelectedSuggestionId(value)

        this.onSelectCaseCallBack?.(caseNumber)
        this.onBlurCallback?.()
    }

    @action
    onViewAllOrPressEnter = () => {
        // need to clear selected row if search again
        this.searchResultTableStore.changeCaseTablePagination()
        this.changeCaseTableRowSelection()
        this.loadInitCaseTable()
    }

    /**
     * Called when click advance search submit.
     * @param filter filter object.
     */
    onAdvanceSearchSubmit = (filter: object) => {
        this.onSelectCaseCallBack?.()
        this.searchFilterStore.changeAdvanceSearchFilter(filter)
        this.searchFilterStore.changeAdvanceSearchVisible(false)
        this.searchResultTableStore.changeCaseTablePagination()
        this.changeCaseTableRowSelection()

        this.loadInitCaseTable()
    }

    /**
     * Called when click advance search reset.
     */
    onAdvanceSearchReset = () => {
        this.onSelectCaseCallBack?.()
        this.searchFilterStore.changeAdvanceSearchFilter({})
        this.searchFilterStore.changeAdvanceSearchVisible(false)
        // need to reload search result and back to first page
        this.searchResultTableStore.changeCaseTablePagination()
        this.changeCaseTableRowSelection()
        this.loadInitCaseTable()
    }

    onRemoveSingleFilter = (filterKey: string) => {
        this.onSelectCaseCallBack?.()
        const newFilters = omit(toJS(this.searchFilterStore.advanceFilters), filterKey)
        this.searchFilterStore.changeAdvanceSearchFilter(newFilters)
        this.searchResultTableStore.changeCaseTablePagination()
        this.loadInitCaseTable()
    }

    onRemoveAllFilters = () => {
        this.onSelectCaseCallBack?.()
        this.onAdvanceSearchReset()
    }

    @action
    changeCaseTableRowSelection = (row?: CaseSearchResultType) => {
        this.searchResultTableStore.changeSelectedRow(row)
        this.onSelectCaseCallBack?.(row?.caseNumber)
        this.onBlurCallback?.()
    }

    @action
    onRowSelectionChange = (selectedRowKeys: string[] | number[], selectedRows: CaseSearchResultType[]) => {
        const firstSelectedRow = selectedRows?.[0]
        this.changeCaseTableRowSelection(firstSelectedRow)
    }

    /**
     * Called when user press enter on autocomplete or click advance form
     */
    @action
    loadInitCaseTable = () => {
        const loadCaseTable = this.loadResultTableWithFilters()
        return loadCaseTable(this.searchResultTableStore.caseTablePagination)
    }

    /**
     * When user change case table pagination, pagesize etc.
     */
    @action
    onCaseTableChange = (
        pagination: PaginationConfig,
        filters?: Record<string, string[]>,
        sorter?: SorterResult<CaseSearchResultType>
    ) => {
        const loadCaseTable = this.loadResultTableWithFilters()
        return loadCaseTable(pagination, filters, sorter)
    }

    @action
    private loadResultTableWithFilters = () => {
        this.searchResultTableStore.changeShowCaseTable(true)
        const currentSearchFilters = {
            ...(this.searchInputStore.searchValue ? {content: this.searchInputStore.searchValue} : {}),
            ...this.searchFilterStore.advanceFilters
        }
        return this.searchResultTableStore.searchTableCases(currentSearchFilters)
    }

    @action
    resetAll = () => {
        this.searchInputStore.resetAll()
        this.searchFilterStore.resetAll()
        this.searchResultTableStore.resetAll()
    }
}
