/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapBalance, CapBalanceChangeLog, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {action, observable, runInAction} from 'mobx'
import {flatMap} from 'rxjs/operators'
import {Observable} from 'rxjs'
import {
    balanceService,
    claimAcceleratedSettlementService,
    claimAccidentalDismembermentSettlementService,
    claimCISettlementService,
    claimDeathSettlementService,
    claimHISettlementService,
    claimPremiumWaiverSettlementService,
    dateComparison,
    financialService
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import {
    CapAdjusterPaymentDefinitionCapGenerateOverpaymentWaiveRequest,
    CapAdjusterPaymentDefinitionCapUnderpaymentGenerationRequest,
    CapAdjusterPaymentDefinitionCapGenerateExternalBalanceRequest,
    CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery,
    CapAdjusterPaymentDefinitionCapWithholdingUnderpaymentGenerationRequest,
    CapAdjusterPaymentDefinitionPaymentDefinitionOverpaymentWaiveApprovalInput,
    CapAdjusterPaymentDefinitionCapRefExternalBalanceCancelInput,
    CapAdjusterPaymentDefinitionCapRefOverpaymentWaiveCancelInput
} from '@eisgroup/cap-gateway-client'
import {Either, Right} from '@eisgroup/data.either'
import {BaseRootStoreImpl, EventCaseStore, BalanceStore} from '..'
import {SETTLEMENT_MODEL_NAME} from '../../constants'
import CapBalanceChangeLogEntity = CapBalanceChangeLog.CapBalanceChangeLogEntity
import CapBalanceEntity = CapBalance.CapBalanceEntity
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity
import CapBalanceItemEntity = CapBalance.CapBalanceItemEntity
import CapBalanceItemActualAllocationEntity = CapBalance.CapBalanceItemActualAllocationEntity
import CapBalanceItemScheduledAllocationEntity = CapBalance.CapBalanceItemScheduledAllocationEntity
import PaymentApprovalResult = PaymentDefinition.PaymentApprovalResult
import {LOAD_SETTLEMENT} from '../CommonActionNames'

const getEitherResult = (result: Either<ErrorMessage, any>) => {
    if (result.isRight) {
        return result.get()?.result || []
    }
    return undefined
}

export const WAIVE_OVERPAYMENT = 'waiveOverpayment'
export const PAY_UNDERPAYMENT = 'payUnderpayment'
export const ADD_EXTERNAL_OVERPAYMENT = 'addExternalOverpayment'
export const LOAD_BALANCE = 'loadBalance'
export const LOAD_BALANCE_CHANGE_LOG = 'loadBalanceChangeLog'
export const CANCEL_EXTERNAL_OVERPAYMENT = 'cancelExternalOverpayment'
export const CANCEL_WAIVE_OVERPAYMENT = 'cancelWaiveOverpayment'

export class BalanceStoreImpl extends BaseRootStoreImpl implements BalanceStore {
    balanceChangeLogCount: number

    eventCaseStore: EventCaseStore

    balanceSource: string

    @observable balance: CapBalanceEntity

    @observable balanceChangeLog: CapBalanceChangeLogEntity[] = []

    constructor(eventCaseStore: EventCaseStore, balanceSource: string) {
        super()
        this.eventCaseStore = eventCaseStore
        this.balanceSource = balanceSource
    }

    combineAllocationWithSameSource = (balanceitems: CapBalanceItemEntity[]) => {
        return balanceitems.map(balanceItem => {
            const allocations = balanceItem.actualAllocations.filter(v => v?.allocationLobCd === 'Life') ?? []
            const allocationsWithoutLife =
                balanceItem.actualAllocations.filter(v => v?.allocationLobCd !== 'Life') ?? []

            const groupedAllocation = allocations.reduce((acc, currentValue) => {
                const uri = currentValue.allocationSource?._uri ?? ''
                if (!acc[uri]) {
                    acc[uri] = [currentValue]
                } else {
                    acc[uri].push(currentValue)
                }
                return acc
            }, {})

            for (const key in groupedAllocation) {
                if (groupedAllocation[key].length > 1) {
                    const allocationBalancedGrossAmountSum = groupedAllocation[key].reduce(
                        (accumulator: number, currentValue: CapBalanceItemActualAllocationEntity) => {
                            const amount = currentValue.allocationBalancedGrossAmount?.amount || 0
                            return accumulator + amount
                        },
                        0
                    )
                    const allocationNetAmountSum = groupedAllocation[key].reduce(
                        (accumulator: number, currentValue: CapBalanceItemActualAllocationEntity) => {
                            const amount = currentValue.allocationNetAmount?.amount || 0
                            return accumulator + amount
                        },
                        0
                    )
                    const allocationGrossAmountSum = groupedAllocation[key].reduce(
                        (accumulator: number, currentValue: CapBalanceItemActualAllocationEntity) => {
                            const amount = currentValue.allocationGrossAmount?.amount || 0
                            return accumulator + amount
                        },
                        0
                    )

                    let scheduledAllocations = [] as CapBalanceItemScheduledAllocationEntity[]

                    groupedAllocation[key].forEach((allocation: CapBalanceItemActualAllocationEntity) => {
                        scheduledAllocations = scheduledAllocations.concat(allocation.scheduledAllocations)
                    })

                    groupedAllocation[key] = [
                        {
                            ...groupedAllocation[key][0],
                            allocationGrossAmount: {
                                amount: allocationGrossAmountSum,
                                currency: groupedAllocation[key][0].allocationGrossAmount.currency
                            },
                            allocationNetAmount: {
                                amount: allocationNetAmountSum,
                                currency: groupedAllocation[key][0].allocationNetAmount.currency
                            },
                            allocationBalancedGrossAmount: {
                                amount: allocationBalancedGrossAmountSum,
                                currency: groupedAllocation[key][0].allocationBalancedGrossAmount.currency
                            },
                            scheduledAllocations
                        }
                    ]
                }
            }
            const resultAllocations = [...Object.values(groupedAllocation)] as CapBalanceItemActualAllocationEntity[]

            return {
                ...balanceItem,
                actualAllocations: allocationsWithoutLife.concat(...resultAllocations)
            }
        })
    }

    @action
    loadBalance = (payeeLink: string): void => {
        this.call<any>(() => balanceService.loadBalance(this.balanceSource, payeeLink), LOAD_BALANCE).subscribe(
            either => {
                either.map(r =>
                    runInAction(() => {
                        const balanceItems =
                            r?.balanceItems.filter(v => v?.scheduledNetAmount?.amount !== v?.actualNetAmount?.amount) ||
                            []
                        this.combineAllocationWithSameSource(balanceItems)

                        this.balance = {
                            ...r,
                            balanceItems: this.combineAllocationWithSameSource(balanceItems)
                        }
                    })
                )
            }
        )
    }

    @action
    loadSettlementByType = (rootId: string, revisionNo: string, modelName: string): any => {
        switch (modelName) {
            case SETTLEMENT_MODEL_NAME.PREMIUM_WAIVER_SETTLEMENT:
                return this.loadSettlementByRootId(rootId, revisionNo, claimPremiumWaiverSettlementService)
            case SETTLEMENT_MODEL_NAME.DEATH_SETTLEMENT:
                return this.loadSettlementByRootId(rootId, revisionNo, claimDeathSettlementService)
            case SETTLEMENT_MODEL_NAME.ACCELERATED_SETTLEMENT:
                return this.loadSettlementByRootId(rootId, revisionNo, claimAcceleratedSettlementService)
            case SETTLEMENT_MODEL_NAME.ACCIDENTAL_DISMEMBERMENT_SETTLEMENT:
                return this.loadSettlementByRootId(rootId, revisionNo, claimAccidentalDismembermentSettlementService)
            case SETTLEMENT_MODEL_NAME.CI_SETTLEMENT:
                return this.loadSettlementByRootId(rootId, revisionNo, claimCISettlementService)
            case SETTLEMENT_MODEL_NAME.HI_SETTLEMENT:
                return this.loadSettlementByRootId(rootId, revisionNo, claimHISettlementService)
            default:
                return undefined
        }
    }

    @action
    waiveOverpayment = (
        params: CapAdjusterPaymentDefinitionCapGenerateOverpaymentWaiveRequest
    ): RxResult<CapPaymentEntity> => {
        return this.call<any>(() => financialService.waiveOverpayment(params), WAIVE_OVERPAYMENT)
    }

    @action
    payUnderpayment = (
        params: CapAdjusterPaymentDefinitionCapUnderpaymentGenerationRequest
    ): RxResult<CapPaymentEntity> => {
        return this.call<any>(() => financialService.payUnderpayment(params), PAY_UNDERPAYMENT)
    }

    @action
    generateWithholdingUnderpayment = (
        params: CapAdjusterPaymentDefinitionCapWithholdingUnderpaymentGenerationRequest
    ): RxResult<CapPaymentEntity> => {
        const setErrorMsg = e => {
            let errorMsg = e.message
            if (e.errors && e.errors.length) {
                errorMsg = e.errors?.[0]?.message
            }
            return errorMsg
        }
        return this.call<any>(
            () =>
                financialService.generateWithholdingUnderpayment(params).pipe(
                    flatMap(r =>
                        r.fold(
                            e =>
                                errorToRxResult({
                                    code: String(e.errors ? e.errors?.[0]?.code : e.code),
                                    message: setErrorMsg(e)
                                } as ErrorMessage),
                            payload => {
                                return Observable.of(Right(payload))
                            }
                        )
                    )
                ),
            PAY_UNDERPAYMENT
        )
    }

    private loadSettlementByRootId = (rootId: string, revisionNo: string, serviceLoadApi: any): any => {
        return this.call<any>(
            () =>
                serviceLoadApi.loadSettlement({
                    rootId,
                    revisionNo
                }),
            LOAD_SETTLEMENT
        )
    }

    @action
    addExternalOverpayment = (
        params: CapAdjusterPaymentDefinitionCapGenerateExternalBalanceRequest
    ): RxResult<CapPaymentEntity> => {
        return this.call<any>(() => financialService.addExternalOverpayment(params), ADD_EXTERNAL_OVERPAYMENT)
    }

    loadBalanceChangeLogByCount = (params, page) => {
        return financialService.getBalanceChangeLog(params, page).map(getEitherResult).toPromise()
    }

    @action
    getBalanceChangeLog = (
        params: CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery,
        nextCount?: number
    ) => {
        return this.call<any>(
            () => financialService.getBalanceChangeLog(params, 0, nextCount),
            LOAD_BALANCE_CHANGE_LOG
        ).subscribe(either => {
            either.map(r => {
                let tempBalanceChangeLogs = [] as CapBalanceChangeLogEntity[]
                runInAction(() => {
                    const count = r?.count || 0
                    const pageCount = Math.ceil(count / 100)
                    if (pageCount > 1) {
                        const promiseList: any[] = []
                        for (let i = 0; i < pageCount; i++) {
                            promiseList.push(this.loadBalanceChangeLogByCount(params, i * 100))
                        }
                        Promise.all(promiseList).then(response => {
                            response.forEach(v => {
                                tempBalanceChangeLogs = tempBalanceChangeLogs.concat(v) as []
                            })
                        })
                    } else {
                        tempBalanceChangeLogs = r.result
                    }
                    this.balanceChangeLog = tempBalanceChangeLogs.sort((a, b) =>
                        dateComparison(a.creationDate).isAfter(b.creationDate) ? -1 : 1
                    )
                    this.balanceChangeLogCount = r.count
                })
                this.loadBalance(params.payee?.matches?.[0] || '')
            })
        })
    }

    @action
    validateOverpaymentWaiveAuthority = (
        params: CapAdjusterPaymentDefinitionPaymentDefinitionOverpaymentWaiveApprovalInput
    ): RxResult<PaymentApprovalResult> => {
        return this.call<any>(() => financialService.validateOverpaymentWaiveAuthority(params))
    }

    @action
    cancelExternalOverpayment = (
        params: CapAdjusterPaymentDefinitionCapRefExternalBalanceCancelInput
    ): RxResult<CapPaymentEntity> => {
        return this.call<any>(() => financialService.cancelExternalOverpayment(params), CANCEL_EXTERNAL_OVERPAYMENT)
    }

    @action
    cancelOverpaymentWaive = (
        params: CapAdjusterPaymentDefinitionCapRefOverpaymentWaiveCancelInput
    ): RxResult<CapPaymentEntity> => {
        return this.call<any>(() => financialService.cancelOverpaymentWaive(params), CANCEL_WAIVE_OVERPAYMENT)
    }
}
