{"swagger": "2.0", "info": {"description": "API for AcceleratedSettlement", "version": "1", "title": "AcceleratedSettlement model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capsettlement/AcceleratedSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/command/approveSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/command/closeSettlement": {"post": {"description": "The command that closes settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/command/initSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeSettlementInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AcceleratedSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AcceleratedSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/AcceleratedSettlementKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/CapAcceleratedSettlementAdjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapAcceleratedSettlementAdjudicationInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/adjudicateSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/approveSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/disapproveSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/initSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/readjudicateSettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/AcceleratedSettlement/v1/transformation/settlementToChildSettlementRefreshInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AcceleratedSettlementKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "AcceleratedSettlementKrakenDeprecatedBundleRequest"}, "AcceleratedSettlementKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/AcceleratedSettlementKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AcceleratedSettlementKrakenDeprecatedBundleRequestBody"}, "AcceleratedSettlementLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntity"}}}, "title": "AcceleratedSettlementLoadHistoryResult"}, "AcceleratedSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AcceleratedSettlementLoadHistoryResult"}}, "title": "AcceleratedSettlementLoadHistoryResultSuccess"}, "AcceleratedSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AcceleratedSettlementLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AcceleratedSettlementLoadHistoryResultSuccessBody"}, "AcceleratedSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "AcceleratedSettlement AccessTrackInfo"}, "AcceleratedSettlement_BaseLifeAccumulatorRemaining": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeAccumulatorRemaining"}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorType": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "amountUnit": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Claim Coverage Prefix"}, "limitAmount": {"type": "number", "description": "Accumulator limit amount."}, "policyTerm": {"$ref": "#/definitions/AcceleratedSettlement_Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "usedAmount": {"type": "number", "description": "Accumulator used amount."}}, "title": "AcceleratedSettlement BaseLifeAccumulatorRemaining"}, "AcceleratedSettlement_BaseLifeFormulaCalculationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeFormulaCalculationDetails"}, "calculatedResult": {"$ref": "#/definitions/Money"}, "calculatedTerm": {"$ref": "#/definitions/AcceleratedSettlement_Term"}, "formulaContent": {"type": "string", "description": "Formula Content."}, "limitLevelType": {"type": "string", "description": "Limit level type."}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeFormulaParameter"}}}, "title": "AcceleratedSettlement BaseLifeFormulaCalculationDetails"}, "AcceleratedSettlement_BaseLifeFormulaParameter": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeFormulaParameter"}, "paramExtension": {"type": "object", "description": "Extension of parameter. E.g currency"}, "paramName": {"type": "string", "description": "Parameter name."}, "paramValue": {"type": "number", "description": "Value of parameter."}}, "title": "AcceleratedSettlement BaseLifeFormulaParameter"}, "AcceleratedSettlement_BaseLifeGrossBenefitAmount": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeGrossBenefitAmount"}, "totalApprovedAmount": {"$ref": "#/definitions/Money"}}, "title": "AcceleratedSettlement BaseLifeGrossBenefitAmount"}, "AcceleratedSettlement_BaseLifePolicyCoverageLimitLevel": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifePolicyCoverageLimitLevel"}, "limitLevelType": {"type": "string", "description": "Type of limit level"}, "timePeriodCd": {"type": "string", "description": "Time period Code"}}, "title": "AcceleratedSettlement BaseLifePolicyCoverageLimitLevel"}, "AcceleratedSettlement_BaseLifeRelatedSettlmentInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeRelatedSettlmentInfo"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/definitions/AcceleratedSettlement_Period"}, "incidentDate": {"type": "string", "format": "date-time"}}, "title": "AcceleratedSettlement BaseLifeRelatedSettlmentInfo"}, "AcceleratedSettlement_BaseLifeSettlementAccumulatorDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementAccumulatorDetails"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "accumulatorCoverage": {"type": "string", "description": "Accumulator Coverage"}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}, "autoAdjudicatedAmount": {"type": "number", "description": "Auto adjudicated amount for each accumulator details."}}, "title": "AcceleratedSettlement BaseLifeSettlementAccumulatorDetails"}, "AcceleratedSettlement_BaseLifeSettlementAccumulatorDetailsExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/AcceleratedSettlement_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "AcceleratedSettlement BaseLifeSettlementAccumulatorDetailsExtension"}, "AcceleratedSettlement_BaseLifeSettlementApprovalResultDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseLifeSettlementApprovalResultDetails"}, "authorityLimit": {"$ref": "#/definitions/Money"}, "calculatedAmount": {"$ref": "#/definitions/Money"}, "entityRefNo": {"type": "string"}, "messageCd": {"type": "string"}, "modelScope": {"type": "string"}, "settlementApprovalCd": {"type": "string"}}, "title": "AcceleratedSettlement BaseLifeSettlementApprovalResultDetails"}, "AcceleratedSettlement_CapAcceleratedCertInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedCertInfoEntity"}, "capBenefitInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementCoverageInfoEntity"}}, "eligibilityInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapMainCoverageEligibilityInfoEntity"}}, "title": "AcceleratedSettlement CapAcceleratedCertInfoEntity"}, "AcceleratedSettlement_CapAcceleratedMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedMasterInfoEntity"}, "capBenefitInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementCoverageInfoEntity"}}, "eligibilityInfos": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_CapMainCoverageEligibilityInfoEntity"}}}, "title": "AcceleratedSettlement CapAcceleratedMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "AcceleratedSettlement_CapAcceleratedSettlementApprovalResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementApprovalResultEntity"}, "approvalStatus": {"type": "string"}, "resultDetails": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeSettlementApprovalResultDetails"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementApprovalResultEntity"}, "AcceleratedSettlement_CapAcceleratedSettlementAttrOptionsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementAttrOptionsEntity"}, "attrName": {"type": "string", "description": "Attribute Name"}, "options": {"type": "array", "items": {"type": "string", "description": "Options of attribute, e.g. Mandatory"}}}, "title": "AcceleratedSettlement CapAcceleratedSettlementAttrOptionsEntity"}, "AcceleratedSettlement_CapAcceleratedSettlementBenefitInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementBenefitInfoEntity"}, "benefitAmount": {"$ref": "#/definitions/Money"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "maxBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementBenefitInfoEntity", "description": "An entity for benefit information."}, "AcceleratedSettlement_CapAcceleratedSettlementCoverageConfigEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementCoverageConfigEntity"}, "accidentInd": {"type": "boolean", "description": "Indicator to mark if the death is caused by a accident and could be covered by ADD, cause of loss = accident."}, "accumulationCategoryGroup": {"type": "string", "description": "Category group to indicate the accumulating group a benefit is belonged to in settlement amount calculation."}, "applicableBurnDegrees": {"type": "array", "items": {"type": "string", "description": "Applicable burn degrees lookup configuration for UI"}}, "applicableReductionAmountTypes": {"type": "array", "items": {"type": "string", "description": "Applicable reduction amount types lookup configuration for UI"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementAttrOptionsEntity"}}, "benefitCategory": {"type": "string", "description": "Benefit Category"}, "benefitLevelMapping": {"type": "array", "items": {"type": "string", "description": "Claim fields mapping to policy fields configuration."}}, "calculationFormulaId": {"type": "string", "description": "Formula Id to define the formulas that are used in settlement calculation for different benefits."}, "grossAmountMode": {"type": "string", "description": "Mode of gross amount used to define if the gross amount calculated is based on the defined time frequency. This will determines the payment frequency for the recurring payment."}, "groupUnit": {"type": "string", "description": "Indicate the amount type for group accumulator limit level amount"}, "incurralPeriodUnit": {"type": "string", "description": "Incurral Period Unit."}, "limitLevels": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string", "description": "Indicate the amount type for accumulator limit level amount"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementCoverageConfigEntity"}, "AcceleratedSettlement_CapAcceleratedSettlementCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementCoverageInfoEntity"}, "coverageCd": {"type": "string", "description": "The value presents the value of policy coverage code"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementCoverageInfoEntity", "description": "An entity for coverage information."}, "AcceleratedSettlement_CapAcceleratedSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "AcceleratedSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "benefitCd": {"type": "string", "description": "Policy Benefit Code"}, "cancelReason": {"type": "string", "description": "The settlement cancelled reason"}, "coverageCd": {"type": "string", "description": "Policy Coverage Code"}, "dateRange": {"$ref": "#/definitions/AcceleratedSettlement_Period"}, "dependentRegistryId": {"type": "string", "description": "Individual dependent identifier which is associated with the case"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string", "description": "The attribute represents the eligibility rules decision override reason."}, "grossAmount": {"$ref": "#/definitions/Money"}, "icdOrCptId": {"type": "string", "description": "Id of icd/cpt entity which used to bind settlement with icd/cpt"}, "incidentDate": {"type": "string", "format": "date-time", "description": "To record the date treatment is received or the offical diagosis date, override this date on UI"}, "isCancelled": {"type": "boolean", "description": "Whether the settlement is cancelled"}, "isGrossAmountOverrided": {"type": "boolean", "description": "This attribute defines if GrossAmount is overrided"}, "numberOfUnits": {"type": "integer", "format": "int64"}, "numberOfUnitsOverrideInd": {"type": "boolean", "description": "numberOfUnits Override Indicator"}, "paidToDate": {"type": "string", "format": "date", "description": "Paid to date"}, "planCd": {"type": "string", "description": "Policy Plan Code"}, "planId": {"type": "string", "description": "Policy Plan Id"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of Loss received date, override this date on UI"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementDetailEntity", "description": "Entity that encompasses state-mandated product settlement details."}, "AcceleratedSettlement_CapAcceleratedSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "AcceleratedSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/AcceleratedSettlement_AccessTrackInfo"}, "cashAmountsADBInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapCashAmountsADBEntity"}, "claimCoverageName": {"type": "string", "description": "Claim Coverage Name Display on select coverage popup"}, "claimCoveragePrefix": {"type": "string", "description": "Used to distinguish the type of coverage as prefix"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "coverageBasedConfiguration": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementCoverageConfigEntity"}, "policy": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "settlementAbsenceInfo": {"type": "object", "description": "The object that includes information taken from Absence case."}, "settlementApprovalResult": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementApprovalResultEntity"}, "settlementDetail": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementLossInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}}, "title": "AcceleratedSettlement CapAcceleratedSettlementEntity", "description": "The object that encompasses attributes set for Accelerated Settlement."}, "AcceleratedSettlement_CapAcceleratedSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntity"}}, "title": "AcceleratedSettlement_CapAcceleratedSettlementEntitySuccess"}, "AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AcceleratedSettlement_CapAcceleratedSettlementEntitySuccessBody"}, "AcceleratedSettlement_CapAcceleratedSettlementLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementLossInfoEntity"}, "caseNumber": {"type": "string"}, "claimEligibilityEvaluationCd": {"type": "string", "description": "Claim eligibility evaluation code."}, "claimNumber": {"type": "string"}, "claimType": {"type": "string", "description": "Type of claim."}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed."}, "dateOfLifeExpectancyPrescribed": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed with life expectancy prescribed with terminal illness"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossNumber": {"type": "string"}, "lossType": {"type": "string", "description": "Type of loss. Inherited from claim level."}, "overrideFaceValueAmount": {"$ref": "#/definitions/Money"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementLossInfoEntity", "description": "Business entity that houses the information from loss to use in settlement."}, "AcceleratedSettlement_CapAcceleratedSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementPolicyInfoEntity"}, "acceleratedCertInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedCertInfoEntity"}, "acceleratedMasterInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedMasterInfoEntity"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/AcceleratedSettlement_Term"}, "terminationAge": {"type": "integer", "format": "int64"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "AcceleratedSettlement CapAcceleratedSettlementPolicyInfoEntity", "description": "Entity for AcceleratedLoss Settlement Policy Information."}, "AcceleratedSettlement_CapAcceleratedSettlementResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementResultEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/definitions/Money"}, "autoAdjudicatedDuration": {"type": "number", "description": "Indicates the duration of Auto Adjudication Number of Units (Guideline value in Days, Times, Visits, Round Trips and whatever)"}, "benefitAmountPerUnit": {"$ref": "#/definitions/Money"}, "benefitCd": {"type": "string", "description": "Benefit Code"}, "claimCoverageName": {"type": "string", "description": "Claim coverage name"}, "dateRange": {"$ref": "#/definitions/AcceleratedSettlement_Period"}, "dependentRegistryId": {"type": "string", "description": "Individual dependent identifier which is associated with the case"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "formulaCalculationDetails": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeFormulaCalculationDetails"}}, "grossAmountMode": {"type": "string", "description": "Mode of gross amount used to define if the gross amount calculated is based on the defined time frequency. This will determines the payment frequency for the recurring payment."}, "grossBenefitAmount": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if this Claim is the subject to Auto Adjudication."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_MessageType"}}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "Number of occurences/visits."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of Loss received date"}, "reserve": {"type": "number"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementResultEntity", "description": "Business entity defines AcceleratedLoss settlement result."}, "AcceleratedSettlement_CapAcceleratedSettlementRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementRulesInput"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeAccumulatorRemaining"}}, "cashAmountsADBInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapCashAmountsADBEntity"}, "claimCoverageName": {"type": "string", "description": "Claim Coverage Name"}, "claimCoveragePrefix": {"type": "string"}, "coverageConfiguration": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementCoverageConfigEntity"}, "details": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementLossInfoEntity"}, "officialDeathDate": {"type": "string", "format": "date-time"}, "policy": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/definitions/AcceleratedSettlement_BaseLifeRelatedSettlmentInfo"}}, "settlement": {"$ref": "#/definitions/EntityLink"}, "wrapperInfo": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementWrapperInfoEntity"}}, "title": "AcceleratedSettlement CapAcceleratedSettlementRulesInput"}, "AcceleratedSettlement_CapAcceleratedSettlementWrapperInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedSettlementWrapperInfoEntity"}, "claimSubjectId": {"type": "string", "description": "Unique identifier for subject of claim"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID."}}, "title": "AcceleratedSettlement CapAcceleratedSettlementWrapperInfoEntity"}, "AcceleratedSettlement_CapCashAmountsADBEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCashAmountsADBEntity"}, "adbAmount": {"$ref": "#/definitions/Money"}, "deathBenefit": {"$ref": "#/definitions/Money"}, "indebtedness": {"type": "number"}, "restoredDeathBenefit": {"type": "number"}}, "title": "AcceleratedSettlement CapCashAmountsADBEntity", "description": "An entity for ADB Amount Entity."}, "AcceleratedSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "AcceleratedSettlement CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "AcceleratedSettlement_CapMainCoverageEligibilityInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMainCoverageEligibilityInfoEntity"}, "waitingPeriodDefCd": {"type": "string"}, "waitingPeriodModeCd": {"type": "string"}}, "title": "AcceleratedSettlement CapMainCoverageEligibilityInfoEntity"}, "AcceleratedSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "AcceleratedSettlement MessageType", "description": "Holds information of message type."}, "AcceleratedSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "AcceleratedSettlement Period"}, "AcceleratedSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "AcceleratedSettlement Term"}, "AdjudicateSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateSettlementToAccumulatorTxOutputs"}, "AdjudicateSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateSettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateSettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveSettlementToAccumulatorTxOutputs"}, "ApproveSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveSettlementToAccumulatorTxOutputs"}}, "title": "ApproveSettlementToAccumulatorTxOutputsSuccess"}, "ApproveSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveSettlementToAccumulatorTxOutputsSuccessBody"}, "CapAcceleratedSettlementAdjudicationInputOutputs": {"properties": {"output": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementRulesInput"}}, "title": "CapAcceleratedSettlementAdjudicationInputOutputs"}, "CapAcceleratedSettlementAdjudicationInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapAcceleratedSettlementAdjudicationInputOutputs"}}, "title": "CapAcceleratedSettlementAdjudicationInputOutputsSuccess"}, "CapAcceleratedSettlementAdjudicationInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapAcceleratedSettlementAdjudicationInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAcceleratedSettlementAdjudicationInputOutputsSuccessBody"}, "CapLifeSettlementInitInput": {"required": ["entity"], "properties": {"claimCoverageName": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "claimWrapperIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementDetailEntity"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapLifeSettlementInitInput"}, "CapLifeSettlementInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeSettlementInitInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLifeSettlementInitInputBody"}, "CapSettlementReadjudicateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/AcceleratedSettlement_CapAcceleratedSettlementDetailEntity"}}, "title": "CapSettlementReadjudicateInput"}, "CapSettlementReadjudicateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementReadjudicateInputBody"}, "DisapproveSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveSettlementToAccumulatorTxOutputs"}, "DisapproveSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveSettlementToAccumulatorTxOutputs"}}, "title": "DisapproveSettlementToAccumulatorTxOutputsSuccess"}, "DisapproveSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveSettlementToAccumulatorTxOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitSettlementToAccumulatorTxOutputs"}, "InitSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitSettlementToAccumulatorTxOutputs"}}, "title": "InitSettlementToAccumulatorTxOutputsSuccess"}, "InitSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitSettlementToAccumulatorTxOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ReadjudicateSettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateSettlementToAccumulatorTxOutputs"}, "ReadjudicateSettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateSettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateSettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateSettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateSettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateSettlementToAccumulatorTxOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SettlementToChildSettlementRefreshInputOutputs": {"properties": {"output": {"type": "object"}}, "title": "SettlementToChildSettlementRefreshInputOutputs"}, "SettlementToChildSettlementRefreshInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputs"}}, "title": "SettlementToChildSettlementRefreshInputOutputsSuccess"}, "SettlementToChildSettlementRefreshInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/SettlementToChildSettlementRefreshInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SettlementToChildSettlementRefreshInputOutputsSuccessBody"}}}