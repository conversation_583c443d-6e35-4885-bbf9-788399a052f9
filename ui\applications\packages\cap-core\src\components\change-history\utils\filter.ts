/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import moment from 'moment'
import {isEqual} from 'lodash'
import {HistoryTableRow, tableFiltersProps} from './types'

/**
 *
 * @param tableData
 * @param filters
 * @returns
 * This method is used to filter table data. It filters the table data according to the parameters passed in from outside and returns the filtered data.
 */
export const historyTableFilter = (tableData: Array<HistoryTableRow>, filters: tableFiltersProps) => {
    return tableData.filter(record => {
        return Object.keys(filters).every(key => {
            const filtersValue = filters[key]
            if (isEqual(filtersValue, [])) {
                return true
            }
            if (!filtersValue) {
                return true
            }
            if (key === 'date') {
                const currentValue = dateUtils(record.currentDate).toMoment
                const startValue = moment(filtersValue?.[0]).startOf('day')
                const endValue = moment(filtersValue?.[1]).add(1, 'd').startOf('day')
                return currentValue.isBetween(startValue, endValue)
            }
            return filtersValue.includes(record[key])
        })
    })
}
