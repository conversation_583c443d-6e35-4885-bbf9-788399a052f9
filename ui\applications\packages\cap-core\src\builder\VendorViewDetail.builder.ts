import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "SECTION_TITLE",
      "id": "9d5a7f60-b025-4bc7-bbab-beb654f261c5",
      "props": {
        "text": "cap-core:general_info"
      }
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{name}",
            "label": "cap-core:name"
          },
          "id": "805bba82-6e07-4ee3-a292-e231126b8a6c"
        },
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{type}",
            "label": "cap-core:type",
            "customCss": "padding-left: 0 !important;"
          },
          "id": "fb6c16de-02e8-4dfd-8057-c5bfc9f14b5c"
        }
      ],
      "id": "d3057bd2-4e41-40dd-8825-932d379f74ad"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{taxId}",
            "label": "cap-core:tax_id"
          },
          "id": "04627eb6-4266-45ac-8807-954edd32994b"
        }
      ],
      "id": "cec1599b-3ae0-4082-8877-eca2da5609cf"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "condition": {
          "display": {
            "conditionInputType": "form",
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "isOrgCustomer"
                  },
                  "operator": "$ne",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "true"
                  }
                }
              ]
            }
          }
        }
      },
      "components": [
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{dob}#date",
            "label": "cap-core:date_of_birth"
          },
          "id": "c82ce12b-ef53-4b1d-90ab-236c5d10f0e4"
        },
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{gender}",
            "label": "cap-core:gender",
            "customCss": "padding-left: 0 !important;"
          },
          "id": "a5c88448-fffc-4c7e-bc78-f6e8d23b4fd8"
        }
      ],
      "id": "61af7395-705c-4469-8634-229815a1b8fe"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{phoneNumber}#phone",
            "label": "cap-core:phone_number"
          },
          "id": "cacbe048-792a-464f-8294-445a616828c2"
        },
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{email}",
            "label": "cap-core:email",
            "customCss": "padding-left: 0 !important;"
          },
          "id": "11a8c554-6938-4186-9005-199fe02e0661"
        }
      ],
      "id": "546b48dc-114b-41aa-b427-05ac4cd92c45"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{contactPreference}",
            "label": "cap-core:contact_preference"
          },
          "id": "53e72dc1-b57c-4e54-80dd-55b84f1dd568"
        },
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "template": "{addressType}",
            "customCss": "padding-left: 0 !important;",
            "label": "cap-core:address_drawer_type"
          },
          "id": "5397aafb-28ba-44f3-865e-117e515a79b1"
        }
      ],
      "id": "24efa39a-d375-4fe2-a6ef-4ef7a438f2b4"
    },
    {
      "type": "ROW",
      "props": {
        "md": 12
      },
      "components": [
        {
          "type": "COMPONENT_SLOT",
          "props": {
            "md": 12,
            "slotId": "VENDOR_ADDRESS"
          },
          "components": [],
          "id": "88cb12e3-4fee-4ee0-b3a9-0ba5167125f2"
        }
      ],
      "id": "ce12d5ae-92ac-49ef-8081-a607b492a08e"
    },
    {
      "type": "SECTION_TITLE",
      "id": "f4764e21-7d9c-4b73-9f89-200dfa968bb0",
      "props": {
        "text": "cap-core:claim_party_info_form_service_type"
      }
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "LOOKUP_LABEL",
          "props": {
            "md": 12,
            "name": "input-5397aafb-28ba-44f3-865e-117e515a79bf",
            "lookupName": "ProviderService",
            "label": "cap-core:claim_party_info_form_service_type",
            "relatedFieldName": "serviceType"
          },
          "id": "5397aafb-28ba-44f3-865e-117e515a79bf"
        }
      ],
      "id": "ce12d5ae-92ac-49ef-8081-a607b49ea08e"
    }
  ],
  "version": 80
}

export default config;
