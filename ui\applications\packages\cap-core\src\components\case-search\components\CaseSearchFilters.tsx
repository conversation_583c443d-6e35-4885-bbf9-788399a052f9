import {Popover} from '@eisgroup/ui-kit'
import {observer} from 'mobx-react'
import React, {FC, memo, RefObject} from 'react'
import {ActionFilterMedium} from '@eisgroup/ui-kit-icons'
import {AdvancedSearch} from '@eisgroup/form'
import classNames from 'classnames'
import {useTranslate} from '@eisgroup/i18n'
import {
    PARTY_CUSTOMER_SEARCH_FILTER_ICON,
    PARTY_CUSTOMER_SEARCH_FILTER_ICON_SELECTED,
    PARTY_CUSTOMER_SEARCH_FILTER_POPOVER
} from '../../../common/package-class-names'

export interface CaseSearchFiltersProps {
    className?: string
    popoverRef?: RefObject<HTMLDivElement>
    visible: boolean
    setVisible: (visible: boolean) => void
    advancedSearchForm: React.ReactNode
    filterData?: object
    onAdvanceSubmit: (data: object) => void
    onAdvanceReset?: () => void
}

const MemoAdevancedSearch = memo(AdvancedSearch)

export const CaseSearchFilters: FC<CaseSearchFiltersProps> = observer(props => {
    const {popoverRef, visible, setVisible, advancedSearchForm, filterData, onAdvanceReset, onAdvanceSubmit} = props
    const {t} = useTranslate()
    return (
        <Popover
            overlayClassName={classNames(PARTY_CUSTOMER_SEARCH_FILTER_POPOVER, props.className)}
            visible={visible}
            onVisibleChange={setVisible}
            content={
                <MemoAdevancedSearch
                    withLabel
                    initialValues={filterData ?? {}}
                    confirmFormLabel={t('cap-core:search')}
                    cancelFormLabel={t('cap-core:clear')}
                    onSubmit={onAdvanceSubmit}
                    onReset={onAdvanceReset}
                >
                    {advancedSearchForm}
                </MemoAdevancedSearch>
            }
            placement='bottomLeft'
            size='medium'
            getPopupContainer={() => popoverRef?.current!}
            trigger='click'
        >
            <ActionFilterMedium
                className={classNames(PARTY_CUSTOMER_SEARCH_FILTER_ICON, {
                    [`${PARTY_CUSTOMER_SEARCH_FILTER_ICON_SELECTED}`]: visible
                })}
                onClick={() => setVisible(!visible)}
            />
        </Popover>
    )
})
