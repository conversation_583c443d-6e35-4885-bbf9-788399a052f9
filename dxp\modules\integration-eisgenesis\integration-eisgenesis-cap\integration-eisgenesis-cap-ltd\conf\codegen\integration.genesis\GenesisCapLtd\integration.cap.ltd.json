{"swagger": "2.0", "info": {"description": "API for CapLtd", "version": "1", "title": "CapLtd model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/CapLtd/v1/command/assignBenefits": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimAssignBenefitsInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLtdAssignBenefitsOutputSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/closeLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/initLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimInitRefInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/reopenLoss": {"post": {"description": "The command that reopens claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/submitLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/updateLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/command/updateReturnToWork": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateReturnToWorkInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLtdLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLtdLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapLtdKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/absenceClaimClosureToOpenItems": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/benefitToSettlementInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLtd_CapLtdToSettlementInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/BenefitToSettlementInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/capLtdToSettlement": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdToSettlementOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/capResolveCloseClaimReason": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/resolveAvailableBenefits": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStd_CapSTDResolveAvailableBenefitsOutputSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/resolveLossAdditionalBenefitSettlements": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLtd/v1/transformation/resolveLossMainBenefitSettlements": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AbsenceClaimClosureToOpenItemsOutputs": {"properties": {"output": {"$ref": "#/definitions/CapLeave_CapLeaveClaimClosureOpenItemsOutput"}}, "title": "AbsenceClaimClosureToOpenItemsOutputs"}, "AbsenceClaimClosureToOpenItemsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputs"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "AbsenceClaimClosureToOpenItemsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "BenefitToSettlementInputOutputs": {"properties": {"output": {"type": "array", "items": {"type": "object"}}}, "title": "BenefitToSettlementInputOutputs"}, "BenefitToSettlementInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/BenefitToSettlementInputOutputs"}}, "title": "BenefitToSettlementInputOutputsSuccess"}, "BenefitToSettlementInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/BenefitToSettlementInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "BenefitToSettlementInputOutputsSuccessBody"}, "CapDisabilityClaimAssignBenefitsInput": {"properties": {"benefits": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsenceBenefitInfo"}}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimAssignBenefitsInput"}, "CapDisabilityClaimAssignBenefitsInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimAssignBenefitsInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimAssignBenefitsInputBody"}, "CapDisabilityClaimInitRefInput": {"required": ["entity"], "properties": {"absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "coverageType": {"type": "string"}, "entity": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimInitRefInput"}, "CapDisabilityClaimInitRefInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimInitRefInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimInitRefInputBody"}, "CapDisabilityClaimUpdateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "entity": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimDetailEntity"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateInput"}, "CapDisabilityClaimUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimUpdateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateInputBody"}, "CapDisabilityClaimUpdateReturnToWorkInput": {"required": ["_key", "returnToWorkDateDetail"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "returnToWorkDateDetail": {"$ref": "#/definitions/CapLtd_CapLtdReturnToWorkDetailEntity"}}, "title": "CapDisabilityClaimUpdateReturnToWorkInput"}, "CapDisabilityClaimUpdateReturnToWorkInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimUpdateReturnToWorkInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateReturnToWorkInputBody"}, "CapLeave_CapAbsenceClaimBalanceOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimBalanceOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeave CapAbsenceClaimBalanceOpenItemInfo"}, "CapLeave_CapAbsenceClaimCoverageOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimCoverageOpenItemInfo"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "dateRange": {"$ref": "#/definitions/CapLeave_Period"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "totalGBAorDuration": {"type": "number", "description": "Total amount to be paid"}, "unpaidGBAorDuration": {"type": "number", "description": "Total amount to be unpaid"}}, "title": "CapLeave CapAbsenceClaimCoverageOpenItemInfo"}, "CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Payment Number"}, "paymentState": {"type": "string", "description": "Payment State"}}, "title": "CapLeave CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "allocationPeriod": {"$ref": "#/definitions/CapLeave_Period"}, "countOfUnposted": {"type": "integer", "format": "int64", "description": "Count Of Unposted"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "frequencyType": {"type": "string", "description": "Frequency Type"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentScheduleNumber": {"type": "string", "description": "Payment Schedule Number"}}, "title": "CapLeave CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "CapLeave_CapLeaveClaimClosureOpenItemsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimClosureOpenItemsOutput"}, "actualPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "balanceInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimBalanceOpenItemInfo"}}, "coverageInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimCoverageOpenItemInfo"}}, "issuedPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "scheduledPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo"}}}, "title": "CapLeave CapLeaveClaimClosureOpenItemsOutput", "description": "Entity for closure claim"}, "CapLeave_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave Period"}, "CapLtdKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapLtdKrakenDeprecatedBundleRequest"}, "CapLtdKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLtdKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtdKrakenDeprecatedBundleRequestBody"}, "CapLtdLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntity"}}}, "title": "CapLtdLoadHistoryResult"}, "CapLtdLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLtdLoadHistoryResult"}}, "title": "CapLtdLoadHistoryResultSuccess"}, "CapLtdLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLtdLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtdLoadHistoryResultSuccessBody"}, "CapLtdToSettlementOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapLtdToSettlementOutputs"}, "CapLtdToSettlementOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLtdToSettlementOutputs"}}, "title": "CapLtdToSettlementOutputsSuccess"}, "CapLtdToSettlementOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLtdToSettlementOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtdToSettlementOutputsSuccessBody"}, "CapLtd_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapLtd AccessTrackInfo"}, "CapLtd_CapAbsenceBenefitConfigurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceBenefitConfigurationEntity"}, "accumulationCategoryGroup": {"type": "string", "description": "Category group to indicate the accumulating group a benefit is belonged to in settlement amount calculation."}, "attrOptions": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsenceSettlementAttrOptions"}}, "benefitCategory": {"type": "string", "description": "Benefit Category"}, "benefitLevelMapping": {"type": "array", "items": {"type": "string", "description": "Claim fields mapping to policy fields configuration."}}, "calculationFormulaId": {"type": "string", "description": "Formula Id to define the formulas that are used in settlement calculation for different benefits."}, "colaApplies": {"type": "boolean", "description": "Is COLA applicable"}, "coverageType": {"type": "string", "description": "Coverage Type"}, "deductionsAllowed": {"type": "boolean", "description": "Deductions Allowed"}, "groupAccumulatorLimitLevel": {"type": "string", "description": "Defines the limitLevel for the group accumulator"}, "groupUnit": {"type": "string", "description": "Indicate the amount type for group accumulator limit level amount"}, "interestApply": {"type": "boolean", "description": "Interests apply"}, "limitLevels": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsencePolicyBenefitLimitLevel"}}, "partialDisability": {"type": "boolean", "description": "Applies for partial disability"}, "policyBenefitCd": {"type": "string", "description": "Policy Benefit Code"}, "policyInfo": {"type": "object", "description": "Policy mapped values"}, "recurrentPayment": {"type": "boolean", "description": "Recurrent payment"}, "taxable": {"type": "boolean", "description": "Taxable"}, "unit": {"type": "string", "description": "Indicate the amount type for accumulator limit level amount"}}, "title": "CapLtd CapAbsenceBenefitConfigurationEntity", "description": "Entity for benefit configuration"}, "CapLtd_CapAbsenceBenefitInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceBenefitInfo"}, "benefitCategory": {"type": "string"}, "claimBenefitLabel": {"type": "string"}, "exists": {"type": "boolean"}, "lossType": {"type": "string"}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}, "policyProductCd": {"type": "string"}}, "title": "CapLtd CapAbsenceBenefitInfo"}, "CapLtd_CapAbsenceDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceDetailEntity"}, "activelyAtWorkDate": {"type": "string", "format": "date-time"}, "lossDate": {"type": "string", "format": "date-time"}}, "title": "CapLtd CapAbsenceDetailEntity"}, "CapLtd_CapAbsenceLTDFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceLTDFinancialAdjustmentEntity"}, "claimFinancialAdjustmentDeductions": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapLTDFinancialAdjustmentDeductionEntity"}}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes. Inherited from Absence."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax. Inherited from Absence case."}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}}, "title": "CapLtd CapAbsenceLTDFinancialAdjustmentEntity", "description": "Parent business entity of financial adjustment parts coming from the Absence Case (deductions)."}, "CapLtd_CapAbsencePolicyBenefitLimitLevel": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsencePolicyBenefitLimitLevel"}, "limitLevelType": {"type": "string", "description": "Type of limit level"}, "timePeriodCd": {"type": "string", "description": "Time period Code"}}, "title": "CapLtd CapAbsencePolicyBenefitLimitLevel"}, "CapLtd_CapAbsenceSettlementAttrOptions": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceSettlementAttrOptions"}, "attrName": {"type": "string", "description": "Attribute Name"}, "options": {"type": "array", "items": {"type": "string", "description": "Options of attribute, e.g. Mandatory"}}}, "title": "CapLtd CapAbsenceSettlementAttrOptions"}, "CapLtd_CapBenefitRelatedEventEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBenefitRelatedEventEntity"}, "eventDate": {"type": "string", "format": "date"}, "eventTypeCd": {"type": "string"}}, "title": "CapLtd CapBenefitRelatedEventEntity"}, "CapLtd_CapClaimSubjectInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimSubjectInfoEntity"}, "registryId": {"type": "string", "description": "Unique identifier for subject of claim"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapLtd CapClaimSubjectInfoEntity", "description": "Entity for subject of claim"}, "CapLtd_CapCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCoverageInfoEntity"}, "benefitDuration": {"type": "string", "description": "Defines maximum benefit duration in weeks if no limitations/exclusions are applied"}, "coverageCd": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time", "description": "Coverage Effective Date defined in Policy"}, "proratingRate": {"type": "integer", "format": "int64", "description": "Prorating Rate defined in LTD Master Policy"}}, "title": "CapLtd CapCoverageInfoEntity", "description": "An entity for coverage information."}, "CapLtd_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapLtd CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapLtd_CapLTDClaimPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDClaimPayeeDetailsEntity"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLtd CapLTDClaimPayeeDetailsEntity"}, "CapLtd_CapLTDClaimSelectedCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDClaimSelectedCoverageInfoEntity"}, "coverageName": {"type": "string"}, "planName": {"type": "string"}}, "title": "CapLtd CapLTDClaimSelectedCoverageInfoEntity"}, "CapLtd_CapLTDDiagnosisInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDDiagnosisInformationEntity"}, "date": {"type": "string", "format": "date", "description": "Defines date of diagnosis."}, "icdCode": {"type": "string", "description": "Defines code of diagnosis."}, "primaryCode": {"type": "boolean", "description": "Defines if diagnosis code is primary."}}, "title": "CapLtd CapLTDDiagnosisInformationEntity", "description": "Entity for diagnosis information."}, "CapLtd_CapLTDEarningsInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDEarningsInformationEntity"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "annualSalaryAmount": {"$ref": "#/definitions/Money"}, "baseSalaryAmount": {"$ref": "#/definitions/Money"}, "coveredMonthlyEarnings": {"$ref": "#/definitions/Money"}, "salaryMode": {"type": "string", "description": "Describes the frequency of Salary pay."}}, "title": "CapLtd CapLTDEarningsInformationEntity", "description": "Entity for Insured's Earnings information."}, "CapLtd_CapLTDExternalTimeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDExternalTimeEntity"}, "externalTimeUsedDays": {"type": "integer", "format": "int64", "description": "Defines the external time used in days"}, "externalTimeUsedMonths": {"type": "integer", "format": "int64", "description": "Defines the external time used in months"}}, "title": "CapLtd CapLTDExternalTimeEntity", "description": "Entity which store external time amounts for LTD"}, "CapLtd_CapLTDFinancialAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDFinancialAdditionEntity"}, "ancillaryActivityName": {"type": "string", "description": "Ancillary Activity Name."}, "financialAdditionRehabilitation": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapLTDFinancialAdditionRehabilitationEntity"}}}, "title": "CapLtd CapLTDFinancialAdditionEntity", "description": "Defines financial addition details."}, "CapLtd_CapLTDFinancialAdditionRehabilitationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDFinancialAdditionRehabilitationEntity"}, "rehabilitationTerm": {"$ref": "#/definitions/CapLtd_Term"}}, "title": "CapLtd CapLTDFinancialAdditionRehabilitationEntity", "description": "Defines rehabilitation addition details."}, "CapLtd_CapLTDFinancialAdjustmentDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDFinancialAdjustmentDeductionEntity"}, "amount": {"$ref": "#/definitions/Money"}, "deductionBeneficiary": {"type": "string", "description": "Defines deduction beneficiary"}, "deductionPct": {"type": "number", "description": "This attribute represents percentage Deductions to be made from the Claim payment"}, "deductionTerm": {"$ref": "#/definitions/CapLtd_Term"}, "deductionType": {"type": "string", "description": "This attribute describes the target for the deduction amount that will be paid by the Claim."}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the deductions should be applied pre taxes. If the value is set to 'no', the deductions are applied post taxes."}, "nonProviderPaymentType": {"type": "string", "description": "This field describes the type of Non-Provider Payment."}, "stateProvided": {"type": "string", "description": "Ths attribute describes in which state the Child Support was provided."}}, "title": "CapLtd CapLTDFinancialAdjustmentDeductionEntity", "description": "This business entity describes the Deduction amount that can be paid to the Sponsor from the claim to cover other Premiums or External payments."}, "CapLtd_CapLTDFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDFinancialAdjustmentEntity"}, "claimFinancialAdjustmentOffsets": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapLTDFinancialAdjustmentOffsetEntity"}}}, "title": "CapLtd CapLTDFinancialAdjustmentEntity", "description": "Entity for financial adjustment details (offsets)."}, "CapLtd_CapLTDFinancialAdjustmentOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDFinancialAdjustmentOffsetEntity"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapLtd_CapLTDOffsetTermEntity"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the claim"}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapLtd CapLTDFinancialAdjustmentOffsetEntity", "description": "Business entity that describes the Offsets of the claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapLtd_CapLTDMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapCoverageInfoEntity"}}}, "title": "CapLtd CapLTDMasterInfoEntity"}, "CapLtd_CapLTDOffsetTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDOffsetTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapLtd CapLTDOffsetTermEntity"}, "CapLtd_CapLTDisabilityClaimDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapLtd"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLTDisabilityClaimDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Main object for long term disability domain attributes used for actions and transactions."}, "claimPayeeDetails": {"$ref": "#/definitions/CapLtd_CapLTDClaimPayeeDetailsEntity"}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapLTDDiagnosisInformationEntity"}}, "disabilityReasonCd": {"type": "string", "description": "Defines disability reason"}, "earnings": {"$ref": "#/definitions/CapLtd_CapLTDEarningsInformationEntity"}, "eligibilityVerifiedCd": {"type": "string"}, "externalTime": {"$ref": "#/definitions/CapLtd_CapLTDExternalTimeEntity"}, "financialAddition": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapLTDFinancialAdditionEntity"}}, "financialAdjustment": {"$ref": "#/definitions/CapLtd_CapLTDFinancialAdjustmentEntity"}, "financialAdjustmentOverride": {"$ref": "#/definitions/CapLtd_CapAbsenceLTDFinancialAdjustmentEntity"}, "interruptionDaysNumber": {"type": "integer", "format": "int64"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "preExistingConditionsAssessment": {"$ref": "#/definitions/CapLtd_CapLtdPreExistingConditionsAssessmentEntity"}, "proofReceivedDate": {"type": "string", "format": "date", "description": "Entity to capture absence period details."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when the incident was reported."}, "returnToWorkDateDetail": {"$ref": "#/definitions/CapLtd_CapLtdReturnToWorkDetailEntity"}, "selectedCoverage": {"$ref": "#/definitions/CapLtd_CapLTDClaimSelectedCoverageInfoEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable."}}, "title": "CapLtd CapLTDisabilityClaimDetailEntity", "description": "Entity that encompasses long term product claim details."}, "CapLtd_CapLTDisabilityClaimEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapLtd"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLTDisabilityClaimEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string", "description": "Defines reason of absence"}}, "accessTrackInfo": {"$ref": "#/definitions/CapLtd_AccessTrackInfo"}, "availableBenefitConfigurations": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsenceBenefitConfigurationEntity"}}, "coverageType": {"type": "string", "description": "Type of coverage"}, "eventCaseInfo": {"$ref": "#/definitions/CapLtd_EventCaseInfoEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "externalTime": {"$ref": "#/definitions/CapLtd_CapLTDExternalTimeEntity"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "lossDetail": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimDetailEntity"}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "lossType": {"type": "string", "description": "Defines loss type."}, "memberRegistryTypeId": {"type": "string"}, "policy": {"$ref": "#/definitions/CapLtd_CapLTDisabilityLossPolicyInfoEntity"}, "policyId": {"type": "string"}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "state": {"type": "string", "description": "Defines disability status."}, "subjectOfClaim": {"$ref": "#/definitions/CapLtd_CapClaimSubjectInfoEntity"}}, "title": "CapLtd CapLTDisabilityClaimEntity", "description": "Main object for long term disability domain attributes used for actions and transactions."}, "CapLtd_CapLTDisabilityClaimEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntity"}}, "title": "CapLtd_CapLTDisabilityClaimEntitySuccess"}, "CapLtd_CapLTDisabilityClaimEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLtd_CapLTDisabilityClaimEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtd_CapLTDisabilityClaimEntitySuccessBody"}, "CapLtd_CapLTDisabilityLossPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDisabilityLossPolicyInfoEntity"}, "asoTypeCd": {"type": "string", "description": "Defines ASO Type from Master policy."}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "existERISAPlan": {"type": "boolean", "description": "Indicates if ERISA exist"}, "fundingTypeCd": {"type": "string", "description": "Defines Funding Type from Master policy."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isReinsuranceExists": {"type": "boolean", "description": "Defines if the claim is Reinsured."}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "ltdMasterInfo": {"$ref": "#/definitions/CapLtd_CapLTDMasterInfoEntity"}, "masterPolicyId": {"type": "string", "description": "Identification number of the master policy in CAP subsystem."}, "masterPolicyNumber": {"type": "string", "description": "Defines masterPolicyNumber from Master policy"}, "masterPolicyProductCd": {"type": "string", "description": "Master Policy Product Code"}, "orgCustomerNumber": {"type": "string", "description": "Organization Customer Number from Master Policy"}, "planCd": {"type": "string", "description": "The attribute that represents the Policy Plan (STDPlan1, STDPlan2, LTDPlan1, LTDPlan2)."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/CapLtd_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapLtd CapLTDisabilityLossPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapLtd_CapLtdAssignBenefitsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdAssignBenefitsOutput"}, "benefits": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsenceBenefitInfo"}}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapLtd CapLtdAssignBenefitsOutput", "description": "Entity for policy benefits."}, "CapLtd_CapLtdAssignBenefitsOutputSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLtd_CapLtdAssignBenefitsOutput"}}, "title": "CapLtd_CapLtdAssignBenefitsOutputSuccess"}, "CapLtd_CapLtdAssignBenefitsOutputSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLtd_CapLtdAssignBenefitsOutputSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtd_CapLtdAssignBenefitsOutputSuccessBody"}, "CapLtd_CapLtdPreExistingConditionsAssessmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdPreExistingConditionsAssessmentEntity"}, "notes": {"type": "string", "description": "Defines Pre-Existing Conditions Assessment Evaluation Notes."}, "pecaEvaluationCd": {"type": "string", "description": "Defined Pre-Existing Conditions Assessment Evaluation."}}, "title": "CapLtd CapLtdPreExistingConditionsAssessmentEntity", "description": "Entity for pre-existing conditions assessment"}, "CapLtd_CapLtdReturnToWorkDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdReturnToWorkDetailEntity"}, "actualFTRTW": {"type": "string", "format": "date-time", "description": "Actual Full-Time Return to Work date"}, "actualPTRTW": {"type": "string", "format": "date-time", "description": "Actual Part-Time Return to Work date"}, "estimatedFTRTW": {"type": "string", "format": "date-time", "description": "Estimated Full-Time Return to Work date"}, "estimatedPTRTW": {"type": "string", "format": "date-time", "description": "Estimated Part-Time Return to Work date"}}, "title": "CapLtd CapLtdReturnToWorkDetailEntity", "description": "Entity for return to work date details"}, "CapLtd_CapLtdToSettlementInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdToSettlementInput"}, "benefits": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapAbsenceBenefitInfo"}}, "capPolicyId": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLtd CapLtdToSettlementInput", "description": "Entity for policy benefits."}, "CapLtd_CapLtdToSettlementInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLtd_CapLtdToSettlementInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtd_CapLtdToSettlementInputBody"}, "CapLtd_CapPolicyIdsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPolicyIdsEntity"}, "ltdIndividualPolicyId": {"type": "string"}}, "title": "CapLtd CapPolicyIdsEntity"}, "CapLtd_EventCaseInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "EventCaseInfoEntity"}, "absenceDetail": {"$ref": "#/definitions/CapLtd_CapAbsenceDetailEntity"}, "capPolicyIds": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapPolicyIdsEntity"}}, "caseNumber": {"type": "string"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "events": {"type": "array", "items": {"$ref": "#/definitions/CapLtd_CapBenefitRelatedEventEntity"}}, "isSicknessInjury": {"type": "string"}, "reportedDate": {"type": "string", "format": "date-time"}, "state": {"type": "string"}}, "title": "CapLtd EventCaseInfoEntity"}, "CapLtd_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapLtd Term"}, "CapResolveCloseClaimReasonOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveCloseClaimReasonOutputs"}, "CapResolveCloseClaimReasonOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputs"}}, "title": "CapResolveCloseClaimReasonOutputsSuccess"}, "CapResolveCloseClaimReasonOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveCloseClaimReasonOutputsSuccessBody"}, "CapStd_CapAbsenceBenefitInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceBenefitInfo"}, "benefitCategory": {"type": "string"}, "claimBenefitLabel": {"type": "string"}, "exists": {"type": "boolean"}, "lossType": {"type": "string"}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}, "policyProductCd": {"type": "string"}}, "title": "CapStd CapAbsenceBenefitInfo"}, "CapStd_CapSTDResolveAvailableBenefitsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDResolveAvailableBenefitsOutput"}, "benefits": {"type": "array", "items": {"$ref": "#/definitions/CapStd_CapAbsenceBenefitInfo"}}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapStd_MessageType"}}}, "title": "CapStd CapSTDResolveAvailableBenefitsOutput", "description": "Entity for policy benefits."}, "CapStd_CapSTDResolveAvailableBenefitsOutputSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStd_CapSTDResolveAvailableBenefitsOutput"}}, "title": "CapStd_CapSTDResolveAvailableBenefitsOutputSuccess"}, "CapStd_CapSTDResolveAvailableBenefitsOutputSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStd_CapSTDResolveAvailableBenefitsOutputSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStd_CapSTDResolveAvailableBenefitsOutputSuccessBody"}, "CapStd_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapStd MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossCloseInputBody"}, "ClaimLossReopenInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossReopenInputBody"}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "lossSubStatusCd": {"type": "string"}}, "title": "ClaimLossSubStatusInput"}, "ClaimLossSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossSubStatusInputBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ResolveLossAdditionalBenefitSettlementsOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputs"}, "ResolveLossAdditionalBenefitSettlementsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputs"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputsSuccess"}, "ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody"}, "ResolveLossMainBenefitSettlementsOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveLossMainBenefitSettlementsOutputs"}, "ResolveLossMainBenefitSettlementsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputs"}}, "title": "ResolveLossMainBenefitSettlementsOutputsSuccess"}, "ResolveLossMainBenefitSettlementsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveLossMainBenefitSettlementsOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}