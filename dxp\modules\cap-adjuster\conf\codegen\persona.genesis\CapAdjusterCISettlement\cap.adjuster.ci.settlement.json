{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.ci.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Critical Illness Settlements API", "version": "1", "title": "CAP Adjuster: Critical Illness Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-ci-settlements", "description": "CAP Adjuster: Critical Illness Settlements API"}], "paths": {"/losses-ci-settlements/{rootId}/{revisionNo}": {"get": {"summary": "Get critical illness settlement by rootId and revisionNo", "x-dxp-path": "/api/capsettlement/CISettlement/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}, "/losses-ci-settlements/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for critical illness settlement", "x-dxp-path": "/api/capsettlement/CISettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}, "/losses-ci-settlements/draft": {"post": {"summary": "Init critical illness settlement", "x-dxp-path": "/api/capsettlement/CISettlement/v1/command/initSettlement", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}, "/losses-ci-settlements/adjudicate": {"post": {"summary": "Adjudicate critical illness settlement", "x-dxp-path": "/api/capsettlement/CISettlement/v1/command/adjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}, "put": {"summary": "Readjudicate critical illness settlement", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/CISettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}, "/losses-ci-settlements/approve": {"post": {"summary": "Approve critical illness settlement", "x-dxp-path": "/api/capsettlement/CISettlement/v1/command/approveSettlement", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}, "/losses-ci-settlements/disapprove": {"post": {"summary": "Disapprove critical illness settlement", "x-dxp-path": "/api/capsettlement/CISettlement/v1/command/disapproveSettlement", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}, "/losses-ci-settlements/adjudication-input": {"post": {"summary": "CI settlement adjudication input", "x-dxp-path": "/api/capsettlement/CISettlement/v1/transformation/CapCISettlementAdjudicationInput", "tags": ["/cap-adjuster/v1/losses-ci-settlements"]}}}}