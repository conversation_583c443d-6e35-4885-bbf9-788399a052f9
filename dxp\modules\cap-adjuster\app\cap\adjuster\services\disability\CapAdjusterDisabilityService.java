/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.disability;

import java.util.List;
import java.util.concurrent.CompletionStage;

import cap.adjuster.services.disability.dto.CapStdSettlement;
import com.google.inject.ImplementedBy;

import cap.adjuster.services.disability.impl.CapAdjusterDisabilityServiceImpl;
import cap.adjuster.services.financial.dto.CapPayment;

/**
 * Service that provides methods for CAP disability
 */
@ImplementedBy(CapAdjusterDisabilityServiceImpl.class)
public interface CapAdjusterDisabilityService {

    /**
     * Get payments associated with disability loss
     *
     * @param rootId disability identifier
     * @param revisionNo disability revision number
     * @return list of payments related to absence loss
     */
    CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo);

    /**
     * Get settlements associated with disability loss
     *
     * @param rootId     disability loss identifier
     * @param revisionNo disability loss revision number
     * @return disability loss settlements
     */
    CompletionStage<List<CapStdSettlement>> getSettlements(String rootId, Integer revisionNo);
}
