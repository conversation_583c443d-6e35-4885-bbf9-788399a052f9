import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "COMPONENT_SLOT",
      "props": {
        "md": 24,
        "span": 24,
        "slotId": "YTD_EARNINGS",
        "pull": 0
      },
      "id": "4d01262a-f393-4216-b948-2f4b79fc4400"
    }
  ],
  "version": 19
}

export default config;
