import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "SELECT_INPUT",
      "props": {
        "md": 12,
        "name": "externalOverpayment",
        "label": "cap-core:balance_actions_drawer_external_dropdown_label",
        "options": [],
        "field": {
          "validations": [
            {
              "skipOnEmpty": false,
              "type": "required-named",
              "fieldValue": {
                "type": "value",
                "valueType": "string",
                "value": "cap-core:balance_actions_drawer_external_dropdown_label"
              }
            },
            {
              "skipOnEmpty": false,
              "type": "required"
            }
          ]
        },
        "mode": "default",
        "events": [
          {
            "id": "9e1ba265-c4c4-44ef-8cd0-672571ba4008",
            "eventName": "OVERPAYMENT_CHANGED",
            "dispatchEventProperty": "onChange"
          }
        ]
      },
      "id": "7e46667f-6823-48af-b451-ef6d9415c517"
    },
    {
      "type": "MONEY_INPUT",
      "props": {
        "md": 12,
        "name": "externalBalanceAmount",
        "label": "cap-core:balance_actions_drawer_external_overpayment_amount_label",
        "allowDecimal": true,
        "currencyDisplay": "symbol",
        "initialAmount": null,
        "field": {
          "validations": [
            {
              "skipOnEmpty": false,
              "type": "required-named",
              "fieldValue": {
                "type": "value",
                "valueType": "string",
                "value": "cap-core:balance_actions_drawer_external_overpayment_amount_label"
              }
            },
            {
              "skipOnEmpty": false,
              "type": "required"
            }
          ]
        },
        "disabled": true
      },
      "id": "9831f0bf-69f1-4331-82a1-aa6fa4e1928f"
    },
    {
      "type": "TEXT_AREA",
      "props": {
        "md": 24,
        "name": "comment",
        "label": "cap-core:balance_actions_drawer_reason_label",
        "autosizeItem": true,
        "autosize": {
          "minRows": 6,
          "maxRows": 6
        },
        "field": {
          "validations": [
            {
              "skipOnEmpty": false,
              "type": "required-named",
              "fieldValue": {
                "type": "value",
                "valueType": "string",
                "value": "cap-core:balance_actions_drawer_reason_label"
              }
            },
            {
              "skipOnEmpty": false,
              "type": "required"
            }
          ]
        }
      },
      "id": "76888589-8466-49ef-9f4e-cc846c40fed6"
    }
  ],
  "version": 32,
  "actionChains": {
    "OVERPAYMENT_CHANGED": {
      "type": "API",
      "condition": {},
      "apiSettings": {
        "method": "onOverpaymentChanged"
      },
      "nestedActions": {}
    }
  }
}

export default config;
