/**
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {backofficeCustomerCommonService, claimService, IOCustomer} from '@eisgroup/cap-services'
import {CapAdjusterClaimSearchCapDynamicSearchRequest} from '@eisgroup/cap-gateway-client'

export interface CasesClaimsSearch {
    searchCasesClaims: (searchCriteria: CapAdjusterClaimSearchCapDynamicSearchRequest) => Promise<any>
    searchCustomerByLinks: (registryTypeIds: string[]) => Promise<IOCustomer[]>
}

export class CasesClaimsSearchService implements CasesClaimsSearch {
    searchCasesClaims = async (searchCriteria: CapAdjusterClaimSearchCapDynamicSearchRequest): Promise<any> => {
        return claimService.searchCasesClaims(searchCriteria).toPromise()
    }

    searchCustomerByLinks = async (registryTypeIds: string[]): Promise<IOCustomer[]> => {
        return backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(registryTypeIds)
    }
}

export const casesClaimsSearchService = new CasesClaimsSearchService()
