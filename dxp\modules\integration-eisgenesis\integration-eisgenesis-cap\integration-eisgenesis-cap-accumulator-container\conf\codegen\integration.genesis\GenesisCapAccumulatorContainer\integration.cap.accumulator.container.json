{"swagger": "2.0", "info": {"description": "API for CapAccumulatorContainer", "version": "1", "title": "CapAccumulatorContainer model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/accumulator/loadAccumulators": {"post": {"description": "Loads Accumulator Container by Key", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Request to load accumulator containers. Matches the structure of the accumulator container key.", "required": false, "schema": {"$ref": "#/definitions/AccumulatorContainerLoadRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/accumulator/loadAccumulators/{policyNumber}": {"get": {"description": "Loads Accumulator Container by Policy Number", "produces": ["application/json"], "parameters": [{"name": "policyNumber", "in": "path", "description": "Policy number which will be used as a criteria to find accumulators", "required": true, "type": "string"}, {"name": "fromDate", "in": "query", "description": "Specifies from which date accumulator should be affected by transactions", "required": false, "type": "string", "format": "date-time"}, {"name": "toDate", "in": "query", "description": "Specifies to which date accumulator should be affected by transactions", "required": false, "type": "string", "format": "date-time"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/command/extractTransaction": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/BenefitDeterminationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainerSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/command/writeTransaction": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorTransactionEntryInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/link/": {"post": {"description": "Returns all cap accumulator records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/PersonalAccumulatorContainer_PersonalAccumulatorContainerEntityListSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AccumulatorContainerLoadRequest": {"properties": {"customerURI": {"type": "string", "description": "Customer URI string which will be used as key to retrieve accumulator container"}, "fromDate": {"type": "string", "format": "date-time", "description": "Specifies from which date accumulator should be affected by transactions. The minimum supported (far-past) date will be taken if absent"}, "policyURI": {"type": "string", "description": "Policy URI string which will be used as key to retrieve accumulator container"}, "toDate": {"type": "string", "format": "date-time", "description": "Specifies to which date accumulator should be affected by transactions. Date of today will be taken if absent"}}, "title": "AccumulatorContainerLoadRequest", "description": "Request to load accumulator containers. Matches the structure of the accumulator container key."}, "AccumulatorContainerLoadRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/AccumulatorContainerLoadRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AccumulatorContainerLoadRequestBody"}, "BenefitDeterminationInput": {"properties": {"customerURI": {"type": "string"}, "resourceURI": {"$ref": "#/definitions/EntityLink"}}, "title": "BenefitDeterminationInput"}, "BenefitDeterminationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/BenefitDeterminationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "BenefitDeterminationInputBody"}, "CapAccumulatorContainer_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorContainer AccessTrackInfo"}, "CapAccumulatorContainer_CapAccumulator": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulator"}, "accessTrackInfo": {"$ref": "#/definitions/CapAccumulatorContainer_AccessTrackInfo"}, "amountType": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "limitAmount": {"type": "number", "description": "Maximum amount that can be spent. Probably taken directly from Policy."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/CapAccumulatorContainer_Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "reservedAmount": {"type": "number", "description": "Amount that is reserved to be used up."}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactions": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "type": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "usedAmount": {"type": "number", "description": "Amount that is already used."}}, "title": "CapAccumulatorContainer CapAccumulator", "description": "CapAccumulator aggregates values of a single accumulator type."}, "CapAccumulatorContainer_CapAccumulatorContainerEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapAccumulatorContainer"}, "_modelType": {"type": "string", "example": "CapAccumulatorContainer"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAccumulatorContainerEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapAccumulatorContainer_AccessTrackInfo"}, "accumulators": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulator"}}, "customerURI": {"type": "string"}, "lastTransaction": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string"}}, "title": "CapAccumulatorContainer CapAccumulatorContainerEntity", "description": "Responsible for storing all accumulators associated with a single policy or customer."}, "CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntity"}}}, "title": "CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccess"}, "CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccumulatorContainer_CapAccumulatorContainerEntityListSuccessBody"}, "CapAccumulatorContainer_CapAccumulatorContainerEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntity"}}, "title": "CapAccumulatorContainer_CapAccumulatorContainerEntitySuccess"}, "CapAccumulatorContainer_CapAccumulatorContainerEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccumulatorContainer_CapAccumulatorContainerEntitySuccessBody"}, "CapAccumulatorContainer_CapAccumulatorTransactionData": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorTransactionData"}, "amount": {"type": "number", "description": "Unit independent amount changed with this transaction. It can be Days/Money."}, "caseNumber": {"type": "string", "description": "Case number for which accumulator transaction is created"}, "claimNumber": {"type": "string", "description": "Claim number for which accumulator transaction is created"}, "condition": {"type": "string", "description": "Specific coverage's condition for which an accumulator is tracked."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/CapAccumulatorContainer_Term"}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactionDate": {"type": "string", "format": "date-time", "description": "Date when the transaction was received and consumed. Not necessary when the transaction itself has happened."}, "type": {"type": "string", "description": "Transaction type, is used to track what kind of change was done."}}, "title": "CapAccumulatorContainer CapAccumulatorTransactionData", "description": "Container for transaction related information."}, "CapAccumulatorContainer_CapAccumulatorTransactionEntryInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorTransactionEntryInput"}, "customerURI": {"type": "string", "description": "Primary Insured"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorTransactionData"}}, "durable": {"type": "boolean", "description": "Specifies if transaction is short lived at the time of transaction date or long lasting."}, "policyURI": {"type": "string"}, "sourceURI": {"type": "string"}, "transactionTimestamp": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorContainer CapAccumulatorTransactionEntryInput", "description": "Input for transaction write command invoked on an accumulator container lifecycle."}, "CapAccumulatorContainer_CapAccumulatorTransactionEntryInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorTransactionEntryInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccumulatorContainer_CapAccumulatorTransactionEntryInputBody"}, "CapAccumulatorContainer_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorContainer Term"}, "CapAccumulatorTransaction_CapAccumulatorTransactionData": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorTransactionData"}, "amount": {"type": "number", "description": "Unit independent amount changed with this transaction. It can be Days/Money."}, "caseNumber": {"type": "string", "description": "Case number for which accumulator transaction is created"}, "claimNumber": {"type": "string", "description": "Claim number for which accumulator transaction is created"}, "condition": {"type": "string", "description": "Specific coverage's condition for which an accumulator is tracked."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/CapAccumulatorTransaction_Term"}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactionDate": {"type": "string", "format": "date-time", "description": "Date when the transaction was received and consumed. Not necessary when the transaction itself has happened."}, "type": {"type": "string", "description": "Transaction type, is used to track what kind of change was done."}}, "title": "CapAccumulatorTransaction CapAccumulatorTransactionData", "description": "Container for transaction related information."}, "CapAccumulatorTransaction_CapAccumulatorTransactionEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapAccumulatorTransaction"}, "_modelType": {"type": "string", "example": "CapAccumulatorTransactionEntry"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAccumulatorTransactionEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "customerURI": {"type": "string", "description": "Primary Insured"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorTransaction_CapAccumulatorTransactionData"}}, "durable": {"type": "boolean", "description": "Specifies if transaction is short lived at the time of transaction date or long lasting."}, "policyURI": {"type": "string"}, "sourceURI": {"type": "string"}, "transactionTimestamp": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorTransaction CapAccumulatorTransactionEntity", "description": "Transaction entry holds multiple transaction data entries."}, "CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainer": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorTransactionEntityContainer"}, "entries": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorTransaction_CapAccumulatorTransactionEntity"}}}, "title": "CapAccumulatorTransaction CapAccumulatorTransactionEntityContainer", "description": "Contains multiple transaction entries. Used in commands with multi-transaction output."}, "CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainerSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainer"}}, "title": "CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainerSuccess"}, "CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainerSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainerSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccumulatorTransaction_CapAccumulatorTransactionEntityContainerSuccessBody"}, "CapAccumulatorTransaction_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorTransaction Term"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "PersonalAccumulatorContainer_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "PersonalAccumulatorContainer AccessTrackInfo"}, "PersonalAccumulatorContainer_CapAccumulator": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulator"}, "accessTrackInfo": {"$ref": "#/definitions/PersonalAccumulatorContainer_AccessTrackInfo"}, "amountType": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "limitAmount": {"type": "number", "description": "Maximum amount that can be spent. Probably taken directly from Policy."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/PersonalAccumulatorContainer_Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "reservedAmount": {"type": "number", "description": "Amount that is reserved to be used up."}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactions": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "type": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "usedAmount": {"type": "number", "description": "Amount that is already used."}}, "title": "PersonalAccumulatorContainer CapAccumulator", "description": "CapAccumulator aggregates values of a single accumulator type."}, "PersonalAccumulatorContainer_CapAccumulatorContainerEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorContainerEntity"}, "accessTrackInfo": {"$ref": "#/definitions/PersonalAccumulatorContainer_AccessTrackInfo"}, "accumulators": {"type": "array", "items": {"$ref": "#/definitions/PersonalAccumulatorContainer_CapAccumulator"}}, "customerURI": {"type": "string"}, "lastTransaction": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string"}}, "title": "PersonalAccumulatorContainer CapAccumulatorContainerEntity", "description": "AccumulatorContainer is responsible for storing all accumulators associated with a single policy or customer."}, "PersonalAccumulatorContainer_PersonalAccumulatorContainerEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "PersonalAccumulatorContainer"}, "_modelType": {"type": "string", "example": "PersonalAccumulatorContainer"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "PersonalAccumulatorContainerEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accumulatorContainers": {"type": "array", "items": {"$ref": "#/definitions/PersonalAccumulatorContainer_CapAccumulatorContainerEntity"}}}, "title": "PersonalAccumulatorContainer PersonalAccumulatorContainerEntity", "description": "PersonalAccumulatorContainer is responsible for storing all accumulator containers associated with a customer."}, "PersonalAccumulatorContainer_PersonalAccumulatorContainerEntityListSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "array", "items": {"$ref": "#/definitions/PersonalAccumulatorContainer_CapAccumulatorContainerEntity"}}}, "title": "PersonalAccumulatorContainer_PersonalAccumulatorContainerEntityListSuccess"}, "PersonalAccumulatorContainer_PersonalAccumulatorContainerEntityListSuccessBody": {"properties": {"body": {"$ref": "#/definitions/PersonalAccumulatorContainer_PersonalAccumulatorContainerEntityListSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "PersonalAccumulatorContainer_PersonalAccumulatorContainerEntityListSuccessBody"}, "PersonalAccumulatorContainer_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "PersonalAccumulatorContainer Term"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}