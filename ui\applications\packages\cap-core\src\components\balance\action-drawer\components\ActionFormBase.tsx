import {UIEngine, UIEngineProps} from '@eisgroup/builder'
import React, {FC} from 'react'
import {DrawerActions, editorConfig, DrawerActionsProps} from '../../../..'

interface ActionFormBaseProps {
    uiEngineProps: UIEngineProps
    drawerActionProps: DrawerActionsProps
    children?: React.ReactNode
}

export const ActionFormBase: FC<ActionFormBaseProps> = props => {
    const {uiEngineProps, drawerActionProps, children} = props
    return (
        <UIEngine {...editorConfig} {...uiEngineProps}>
            {children}
            <DrawerActions {...drawerActionProps} />
        </UIEngine>
    )
}
