{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.accelerated.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Accelerated Settlements API", "version": "1", "title": "CAP Adjuster: Accelerated Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-accelerated-settlements", "description": "CAP Adjuster: Accelerated Settlements API"}], "paths": {"/losses-accelerated-settlements/{rootId}/{revisionNo}": {"get": {"summary": "Get accelerated settlement by rootId and revisionNo", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}, "/losses-accelerated-settlements/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for accelerated settlement", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}, "/losses-accelerated-settlements/draft": {"post": {"summary": "Init accelerated settlement", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/command/initSettlement", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}, "/losses-accelerated-settlements/adjudicate": {"post": {"summary": "Adjudicate accelerated settlement", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/command/adjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}, "put": {"summary": "Readjudicate accelerated settlement", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}, "/losses-accelerated-settlements/approve": {"post": {"summary": "Approve accelerated settlement", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/command/approveSettlement", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}, "/losses-accelerated-settlements/disapprove": {"post": {"summary": "Disapprove accelerated settlement", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/command/disapproveSettlement", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}, "/losses-accelerated-settlements/adjudication-input": {"post": {"summary": "Accelerated settlement adjudication input", "x-dxp-path": "/api/capsettlement/AcceleratedSettlement/v1/transformation/CapAcceleratedSettlementAdjudicationInput", "tags": ["/cap-adjuster/v1/losses-accelerated-settlements"]}}}}