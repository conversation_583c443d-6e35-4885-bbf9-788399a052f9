/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    SearchCustomersRequest,
    IndividualCustomer,
    OrganizationCustomer,
    SearchByPartyTypeCriteria,
    CensusClass
} from '@eisgroup/cap-services'
import {FetchOptionsResult} from '@eisgroup/form'
import {DataSourceItemType} from '@eisgroup/ui-kit'

import {BaseRootStore} from './BaseRootStore'
import {EmployeeFormTypes} from '../Types'
import {EventCaseStore} from './EventCaseStore'

export interface EmploymentStore extends BaseRootStore {
    eventCaseStore: EventCaseStore
    /**
     * Organizations that where found.
     */
    companySearchResults: OrganizationCustomer[]
    /**
     * Organizations that was selected.
     */
    selectedCompany?: OrganizationCustomer
    /**
     * Customer that was selected from employee search results.
     */
    employee?: IndividualCustomer
    /**
     * Set employee value.
     */
    setEmployee: (employee?: IndividualCustomer) => void
    /**
     * Customer form add new member form
     */
    addNewMemberEmployee?: IndividualCustomer
    /**
     * Type of intake wizard employee form.
     */
    employeeFormType: EmployeeFormTypes
    /**
     * Employees that where found.
     */
    employeeSearchResults: IndividualCustomer[]
    /**
     * Returns employer by provided root id.
     */
    searchEmployerByRootId: (rootId: string) => void
    /**
     * Returns employer by provided root id.
     */
    employerHasGroupSponsor: (registryTypeId: string) => Promise<any>
    /**
     * Returns employer and subscribe by provided root id.
     */
    searchEmployeeByRootIdSubscribe: (rootId: string) => Promise<IndividualCustomer>
    /**
     * Action to select company.
     */
    selectCompany: (company?: OrganizationCustomer) => void
    /**
     * Action to select an employee.
     */
    selectEmployee: (employee?: IndividualCustomer) => void
    /**
     * Action to search for policies.
     */
    searchCompany: (searchCriteria: SearchByPartyTypeCriteria) => void
    /**
     * Action to search for employees.
     */
    searchEmployee: (searchCriteria: SearchCustomersRequest) => void
    /**
     * Action to remove company.
     */
    removeSelectedCompany: () => void
    /**
     * Action to change employee form type.
     */
    changeEmployeeFormType: (type: EmployeeFormTypes, employee?: IndividualCustomer) => void
    /**
     * Associated Census classes for selected company
     */
    censusClasses: CensusClass[]
    /**
     * Loads census classes for selected company
     */
    loadCensusClassForSelectedCompany: () => void
    /**
     *  Adds employment details to eventCase
     */
    addEmploymentDetailsToEventCase: (eventCase?: any, company?: OrganizationCustomer) => any

    /**
     * Saves employee to CRM
     */
    saveUpdateEmployee: () => Promise<IndividualCustomer>

    /**
     * search employee by customerNumber or RootId
     */
    searchEmployeeOrEmployerByCustomerNumber: (customerNumber: string, isOrganizationCustomer?: boolean) => void

    /**
     * search employee by RegistryTypeId
     */
    searchEmployeeByRegistryTypeId: (registryTypeId: string) => void

    /**
     * search employee by customerNumber or RegistryTypeId
     */
    searchEmployeeByRootId: (RegistryTypeId: string) => void

    /**
     * customer that was passed from url
     */
    givenEmployee?: IndividualCustomer
    workDetailVisible: boolean
    /**
     * workDetail AutocompletableSearch clear
     */
    isClearWorkDetailAutocompletable: boolean
    /**
     * control AutocompletableSearch clear
     */
    setAutocompletableSearchClear: (isClear: boolean) => void
    /**
     * store givenEmployee
     */
    setGivenEmployee: (employee?: IndividualCustomer) => void
    /**
     * Search employees by rootIds
     */
    retrieveEmployeeByRootIds: (rootIds: string[]) => any

    updateEmployee: (employee?: IndividualCustomer) => void

    isOrganizationCustomerIntake: boolean

    searchCompanyByName: (searchText: string) => Promise<FetchOptionsResult<DataSourceItemType>>
}
