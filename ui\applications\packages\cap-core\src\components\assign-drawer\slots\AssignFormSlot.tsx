/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {AssigneeType} from '@eisgroup/cap-services'
import {StateMapperParams, withFormState} from '@eisgroup/form'
import React from 'react'
import {DrawerFormStateType, QueueStore, UserStore} from '../../..'
import {AssignForm, AssignFormProps} from '../AssignForm'

export interface AssignFormState {
    readonly drawerFormState: DrawerFormStateType
    readonly searchUserLabel: string
}

function withStore<P>(
    Component: React.ComponentType<P>,
    userStore: UserStore,
    queueStore: QueueStore,
    other: any = {}
): React.ComponentType<P> {
    return props => <Component {...props} userStore={userStore} queueStore={queueStore} {...other} />
}

export const getAssignFormSlot = (userStore: UserStore, queueStore: QueueStore) => {
    return withFormState<AssignFormProps>(
        withStore(AssignForm, userStore, queueStore, {assigneeType: AssigneeType.User}),
        ({state}: StateMapperParams<AssignFormState>) => ({
            drawerFormState: state.drawerFormState,
            searchUserLabel: state.searchUserLabel
        })
    )
}
