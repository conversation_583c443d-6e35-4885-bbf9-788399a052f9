{"swagger": "2.0", "x-dxp-spec": {"imports": {"payment.search": {"schema": "integration.cap.payment.definition.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Financial Search API", "version": "1", "title": "CAP Adjuster: Financial Search API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/financial", "description": "CAP Adjuster: Financial API"}], "paths": {"/financial/payment/issue": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/payment/issuePayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/request-issue": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/payment/requestIssuePayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/cancel": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/payment/cancelPayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/fail-outbound": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/payment/failOutboundPayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/request-stop": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/payment/requestStopPayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/generate-overpayment-waive": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/overpaymentWaive/generateOverpaymentWaive", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/generate-under-payment": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/underpayment/generateUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/generate-external-balance": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/externalBalance/generateExternalBalance", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/generate-recovery": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/recovery/generateRecovery", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/cancel-recovery": {"post": {"summary": "Payment cancel recovery operation", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/recovery/cancelRecovery", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/approve": {"post": {"summary": "Approve underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/underpayment/approveUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/cancel": {"post": {"summary": "Cancel underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/underpayment/cancelUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/request-issue": {"post": {"summary": "Request issue underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/underpayment/requestIssueUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/issue": {"post": {"summary": "Issue underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/underpayment/issueUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/fail-outbound": {"post": {"summary": "Fail Outbound underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/underpayment/failOutboundUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/request-stop": {"post": {"summary": "Request stop underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/underpayment/requestStopUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/underpayment/generate-withholding": {"post": {"summary": "Generated withholding underpayment", "x-dxp-path": "/api/cappayment/PaymentDefinition/v1/underpayment/generateWithholdingUnderpayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payments/load": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/entities/loadPayments", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payments/validate-overpayment-waive-authority": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/transformation/overpaymentWaive/validateOverpaymentWaiveAuthority", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/cancel-external-balance": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/externalBalance/cancelExternalBalance", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/cancel-overpayment-waive": {"post": {"x-dxp-path": "/api/cappayment/PaymentDefinition/v1/command/overpaymentWaive/cancelOverpaymentWaive", "tags": ["/cap-adjuster/v1/financial"]}}}}