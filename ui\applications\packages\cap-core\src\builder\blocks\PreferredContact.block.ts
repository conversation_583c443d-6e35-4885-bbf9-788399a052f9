import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "block",
  "blockId": "PreferredContact",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "PHONE_INPUT",
          "props": {
            "md": 8,
            "name": "phones.0.value",
            "label": "cap-core:phone_number",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "min-length",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 10
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "max-length",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "number",
                    "value": 20
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "mandatory_by_value",
                  "fieldValues": {
                    "fieldName": {
                      "type": "field",
                      "value": "~preferredContactMethod"
                    },
                    "fieldValue": {
                      "type": "value",
                      "valueType": "string",
                      "value": "Phone"
                    }
                  }
                }
              ]
            }
          },
          "id": "d55b7b29-53ad-45fe-9ceb-99e403b11569"
        },
        {
          "type": "MASKED_INPUT",
          "props": {
            "md": 8,
            "name": "emails.0.value",
            "label": "cap-core:email_address",
            "maskType": "email",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "mandatory_by_value",
                  "fieldValues": {
                    "fieldName": {
                      "type": "field",
                      "value": "~preferredContactMethod"
                    },
                    "fieldValue": {
                      "type": "value",
                      "valueType": "string",
                      "value": "Email"
                    }
                  }
                }
              ]
            }

          },
          "id": "832880e8-0a39-4222-a0df-4712249166ce"
        },
        {
          "type": "RADIO_INPUT",
          "props": {
            "md": 8,
            "name": "preferredContactMethod",
            "label": "cap-core:contact_preference",
            "options": [
              {
                "label": "cap-core:phone",
                "value": "Phone"
              },
              {
                "label": "cap-core:email",
                "value": "Email"
              }
            ],
            "defaultValue": "preferredContactMethod"
          },
          "id": "0293553a-b6ba-4456-9272-0f3322570ebf"
        }
      ],
      "id": "8e918b0a-b161-49ee-be92-b9c06ca30e5b"
    }
  ],
  "version": 15
}

export default config;
