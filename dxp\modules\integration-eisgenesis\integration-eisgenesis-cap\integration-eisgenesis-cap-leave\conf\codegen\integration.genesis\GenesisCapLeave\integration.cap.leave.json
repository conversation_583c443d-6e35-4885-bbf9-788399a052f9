{"swagger": "2.0", "info": {"description": "API for CapLeave", "version": "1", "title": "CapLeave model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/CapLeave/v1/command/closeLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/initLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimInitRefInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/reopenLoss": {"post": {"description": "The command that reopens claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/submitLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/updateLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/command/updateReturnToWork": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDisabilityClaimUpdateReturnToWorkInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLeaveLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLeaveLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapLeaveKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/transformation/absenceClaimClosureToOpenItems": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/transformation/capLeaveToSettlement": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLeaveToSettlementOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/transformation/capResolveCloseClaimReason": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/transformation/resolveLossAdditionalBenefitSettlements": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/transformation/resolveLossMainBenefitSettlements": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapLeave/v1/transformation/submitLossToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SubmitLossToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AbsenceClaimClosureToOpenItemsOutputs": {"properties": {"output": {"$ref": "#/definitions/CapLeave_CapLeaveClaimClosureOpenItemsOutput"}}, "title": "AbsenceClaimClosureToOpenItemsOutputs"}, "AbsenceClaimClosureToOpenItemsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputs"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "AbsenceClaimClosureToOpenItemsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AbsenceClaimClosureToOpenItemsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AbsenceClaimClosureToOpenItemsOutputsSuccessBody"}, "CapDisabilityClaimInitRefInput": {"required": ["entity"], "properties": {"absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "coverageType": {"type": "string"}, "entity": {"$ref": "#/definitions/CapLeave_CapLeaveClaimDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimInitRefInput"}, "CapDisabilityClaimInitRefInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimInitRefInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimInitRefInputBody"}, "CapDisabilityClaimUpdateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "entity": {"$ref": "#/definitions/CapLeave_CapLeaveClaimDetailEntity"}, "policyId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateInput"}, "CapDisabilityClaimUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimUpdateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateInputBody"}, "CapDisabilityClaimUpdateReturnToWorkInput": {"required": ["_key", "returnToWorkDateDetail"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "returnToWorkDateDetail": {"$ref": "#/definitions/CapLeave_CapLeaveReturnToWorkDetailEntity"}}, "title": "CapDisabilityClaimUpdateReturnToWorkInput"}, "CapDisabilityClaimUpdateReturnToWorkInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDisabilityClaimUpdateReturnToWorkInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDisabilityClaimUpdateReturnToWorkInputBody"}, "CapLeaveKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapLeaveKrakenDeprecatedBundleRequest"}, "CapLeaveKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLeaveKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveKrakenDeprecatedBundleRequestBody"}, "CapLeaveLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntity"}}}, "title": "CapLeaveLoadHistoryResult"}, "CapLeaveLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLeaveLoadHistoryResult"}}, "title": "CapLeaveLoadHistoryResultSuccess"}, "CapLeaveLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLeaveLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveLoadHistoryResultSuccessBody"}, "CapLeaveToSettlementOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapLeaveToSettlementOutputs"}, "CapLeaveToSettlementOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLeaveToSettlementOutputs"}}, "title": "CapLeaveToSettlementOutputsSuccess"}, "CapLeaveToSettlementOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLeaveToSettlementOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeaveToSettlementOutputsSuccessBody"}, "CapLeave_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapLeave AccessTrackInfo"}, "CapLeave_BenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BenefitDurationEntity"}, "benefitDuration": {"type": "integer", "format": "int64", "description": "Benefit Duration defined in SMP Policy"}, "benefitDurationTypeCd": {"type": "string", "description": "Benefit Duration Type defined in SMP Policy"}, "benefitDurationUnitsCd": {"type": "string", "description": "Benefit Duration Units in SMP Policy"}}, "title": "CapLeave BenefitDurationEntity", "description": "Benefit Durations defined in SMP Policy."}, "CapLeave_CapAbsenceClaimBalanceOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimBalanceOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLeave CapAbsenceClaimBalanceOpenItemInfo"}, "CapLeave_CapAbsenceClaimCoverageOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimCoverageOpenItemInfo"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "dateRange": {"$ref": "#/definitions/CapLeave_Period"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "totalGBAorDuration": {"type": "number", "description": "Total amount to be paid"}, "unpaidGBAorDuration": {"type": "number", "description": "Total amount to be unpaid"}}, "title": "CapLeave CapAbsenceClaimCoverageOpenItemInfo"}, "CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Payment Number"}, "paymentState": {"type": "string", "description": "Payment State"}}, "title": "CapLeave CapAbsenceClaimPaymentDefinitionOpenItemInfo"}, "CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "allocationPeriod": {"$ref": "#/definitions/CapLeave_Period"}, "countOfUnposted": {"type": "integer", "format": "int64", "description": "Count Of Unposted"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "frequencyType": {"type": "string", "description": "Frequency Type"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentScheduleNumber": {"type": "string", "description": "Payment Schedule Number"}}, "title": "CapLeave CapAbsenceClaimScheduledPaymentOpenItemInfo"}, "CapLeave_CapAbsenceDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceDetailEntity"}, "activelyAtWorkDate": {"type": "string", "format": "date-time"}, "lossDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave CapAbsenceDetailEntity"}, "CapLeave_CapBenefitRelatedEventEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBenefitRelatedEventEntity"}, "eventDate": {"type": "string", "format": "date"}, "eventTypeCd": {"type": "string"}}, "title": "CapLeave CapBenefitRelatedEventEntity"}, "CapLeave_CapClaimSubjectInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimSubjectInfoEntity"}, "registryId": {"type": "string", "description": "Unique identifier for subject of claim"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapLeave CapClaimSubjectInfoEntity", "description": "Entity for subject of claim"}, "CapLeave_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapLeave CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapLeave_CapLeaveClaimClosureOpenItemsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimClosureOpenItemsOutput"}, "actualPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "balanceInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimBalanceOpenItemInfo"}}, "coverageInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimCoverageOpenItemInfo"}}, "issuedPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimPaymentDefinitionOpenItemInfo"}}, "scheduledPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapAbsenceClaimScheduledPaymentOpenItemInfo"}}}, "title": "CapLeave CapLeaveClaimClosureOpenItemsOutput", "description": "Entity for closure claim"}, "CapLeave_CapLeaveClaimDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapLeave"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLeaveClaimDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Main object for Leave domain attributes used for actions and transactions."}, "claimPayeeDetails": {"$ref": "#/definitions/CapLeave_CapLeaveClaimPayeeDetailsEntity"}, "disabilityReasonCd": {"type": "string", "description": "Defines disability reason"}, "earnings": {"$ref": "#/definitions/CapLeave_CapLeaveEarningsInformationEntity"}, "externalTime": {"$ref": "#/definitions/CapLeave_CapLeaveExternalTimeEntity"}, "financialAdjustment": {"$ref": "#/definitions/CapLeave_CapLeaveFinancialAdjustmentEntity"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "leavePriorEarning": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEarningEntity"}}, "leavePriorQuarterEarnings": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapLeaveClaimPriorQuarterEarningsEntity"}}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when the incident was reported."}, "returnToWorkDateDetail": {"$ref": "#/definitions/CapLeave_CapLeaveReturnToWorkDetailEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable."}}, "title": "CapLeave CapLeaveClaimDetailEntity", "description": "Entity that encompasses short term product claim details."}, "CapLeave_CapLeaveClaimEarningEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimEarningEntity"}, "weekEarningAmount": {"$ref": "#/definitions/Money"}, "weekEndDate": {"type": "string", "format": "date"}, "weekNo": {"type": "integer", "format": "int64"}, "workDaysNumber": {"type": "integer", "format": "int64"}}, "title": "CapLeave CapLeaveClaimEarningEntity", "description": "Business entity for Leave prior-earning detail."}, "CapLeave_CapLeaveClaimEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapLeave"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLeaveClaimEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string", "description": "Defines reason of absence"}}, "accessTrackInfo": {"$ref": "#/definitions/CapLeave_AccessTrackInfo"}, "coverageType": {"type": "string", "description": "Type of coverage (FML, Military, ADA - Leave, ADA - Workplace Accomodation, Corporate - paid, Corporate - unpaid, Jury, PTO, Sick Leave). Link to third party system"}, "eventCaseInfo": {"$ref": "#/definitions/CapLeave_EventCaseInfoEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "externalReference": {"$ref": "#/definitions/CapLeave_ExternalReference"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "lossDetail": {"$ref": "#/definitions/CapLeave_CapLeaveClaimDetailEntity"}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "lossNumberThirdParty": {"type": "string", "description": "A unique loss number."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "lossSubStatuses": {"$ref": "#/definitions/CapLeave_CapLeaveClaimSubStatusEntity"}, "lossType": {"type": "string", "description": "Defines loss type."}, "memberRegistryTypeId": {"type": "string"}, "policy": {"$ref": "#/definitions/CapLeave_CapLeavePolicyInfoEntity"}, "policyId": {"type": "string"}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "state": {"type": "string", "description": "Defines disability status."}, "subjectOfClaim": {"$ref": "#/definitions/CapLeave_CapClaimSubjectInfoEntity"}}, "title": "CapLeave CapLeaveClaimEntity", "description": "Main object for short term disability domain attributes used for actions and transactions."}, "CapLeave_CapLeaveClaimEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntity"}}, "title": "CapLeave_CapLeaveClaimEntitySuccess"}, "CapLeave_CapLeaveClaimEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLeave_CapLeaveClaimEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLeave_CapLeaveClaimEntitySuccessBody"}, "CapLeave_CapLeaveClaimPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimPayeeDetailsEntity"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLeave CapLeaveClaimPayeeDetailsEntity"}, "CapLeave_CapLeaveClaimPriorQuarterEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimPriorQuarterEarningsEntity"}, "quarterEarningsAmount": {"$ref": "#/definitions/Money"}, "quarterEndDate": {"type": "string", "format": "date", "description": "End date of the quarter."}, "quarterNo": {"type": "integer", "format": "int64", "description": "The number of the quarter which relates to insured's entered prior weekly earning figures."}, "workWeeksNumber": {"type": "integer", "format": "int64", "description": "Number of weeks worked throughout the quarter."}}, "title": "CapLeave CapLeaveClaimPriorQuarterEarningsEntity", "description": "Participant prior quarterly earnings detail entity."}, "CapLeave_CapLeaveClaimSubStatusEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveClaimSubStatusEntity"}, "claimReasonCds": {"type": "string"}, "claimSubStatusCd": {"type": "string"}}, "title": "CapLeave CapLeaveClaimSubStatusEntity"}, "CapLeave_CapLeaveCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveCoverageInfoEntity"}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_BenefitDurationEntity"}}, "coverageCd": {"type": "string"}, "proratingRate": {"type": "integer", "format": "int64", "description": "Prorating Rate defined in SMP Policy"}}, "title": "CapLeave CapLeaveCoverageInfoEntity", "description": "An entity for coverage information."}, "CapLeave_CapLeaveEarningsInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveEarningsInformationEntity"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "annualSalaryAmount": {"$ref": "#/definitions/Money"}, "baseSalaryAmount": {"$ref": "#/definitions/Money"}, "coveredWeeklyEarnings": {"$ref": "#/definitions/Money"}, "salaryMode": {"type": "string", "description": "Describes the frequency of Salary pay."}}, "title": "CapLeave CapLeaveEarningsInformationEntity", "description": "Entity for Insured's Earnings information."}, "CapLeave_CapLeaveExternalTimeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveExternalTimeEntity"}, "externalTimeUsedDays": {"type": "integer", "format": "int64", "description": "Defines the amount of external time used in days."}, "externalTimeUsedWeeks": {"type": "integer", "format": "int64", "description": "Defines the amount of external time used in weeks."}}, "title": "CapLeave CapLeaveExternalTimeEntity", "description": "Entity which store external time amounts for Leave"}, "CapLeave_CapLeaveFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveFinancialAdjustmentEntity"}, "claimFinancialAdjustmentOffsets": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapLeaveFinancialAdjustmentOffsetEntity"}}}, "title": "CapLeave CapLeaveFinancialAdjustmentEntity", "description": "Parent business entity of financial adjustment parts (offsets)."}, "CapLeave_CapLeaveFinancialAdjustmentOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveFinancialAdjustmentOffsetEntity"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapLeave_CapLeaveOffsetTermEntity"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the Leave claim."}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapLeave CapLeaveFinancialAdjustmentOffsetEntity", "description": "Business entity that describes the Offsets of the Leave claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapLeave_CapLeaveMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapLeaveCoverageInfoEntity"}}}, "title": "CapLeave CapLeaveMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "CapLeave_CapLeaveOffsetTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveOffsetTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave CapLeaveOffsetTermEntity"}, "CapLeave_CapLeavePolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeavePolicyInfoEntity"}, "asoTypeCd": {"type": "string", "description": "Defines ASO Type from Master policy."}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "fundingTypeCd": {"type": "string", "description": "Defines Funding Type from Master policy."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isReinsuranceExists": {"type": "boolean", "description": "Defines if the claim is Reinsured."}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "leaveMasterInfo": {"$ref": "#/definitions/CapLeave_CapLeaveMasterInfoEntity"}, "masterPolicyNumber": {"type": "string", "description": "Defines masterPolicyNumber from Master policy"}, "masterPolicyProductCd": {"type": "string", "description": "Master Policy Product Code"}, "orgCustomerNumber": {"type": "string", "description": "Organization Customer Number from Master Policy"}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/CapLeave_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapLeave CapLeavePolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapLeave_CapLeaveReturnToWorkDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLeaveReturnToWorkDetailEntity"}, "actualFTRTW": {"type": "string", "format": "date-time", "description": "Actual Full-Time Return to Work date"}, "actualPTRTW": {"type": "string", "format": "date-time", "description": "Actual Part-Time Return to Work date"}, "estimatedFTRTW": {"type": "string", "format": "date-time", "description": "Estimated Full-Time Return to Work date"}, "estimatedPTRTW": {"type": "string", "format": "date-time", "description": "Estimated Part-Time Return to Work date"}}, "title": "CapLeave CapLeaveReturnToWorkDetailEntity", "description": "Entity for return to work date details"}, "CapLeave_EventCaseInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "EventCaseInfoEntity"}, "absenceDetail": {"$ref": "#/definitions/CapLeave_CapAbsenceDetailEntity"}, "caseNumber": {"type": "string"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "events": {"type": "array", "items": {"$ref": "#/definitions/CapLeave_CapBenefitRelatedEventEntity"}}, "reportedDate": {"type": "string", "format": "date-time"}, "state": {"type": "string"}}, "title": "CapLeave EventCaseInfoEntity"}, "CapLeave_ExternalReference": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ExternalReference"}, "reference": {"$ref": "#/definitions/EntityLink"}, "sourceId": {"type": "string", "description": "Entity ID in external system"}, "sourceSubType": {"type": "string", "description": "Entity subtype in external system"}, "sourceSystem": {"type": "string", "description": "External system name/description"}, "sourceType": {"type": "string", "description": "Entity type in external system"}}, "title": "CapLeave ExternalReference", "description": "Holds external(3rd party) system reference related information"}, "CapLeave_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave Period"}, "CapLeave_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapLeave Term"}, "CapResolveCloseClaimReasonOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveCloseClaimReasonOutputs"}, "CapResolveCloseClaimReasonOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputs"}}, "title": "CapResolveCloseClaimReasonOutputsSuccess"}, "CapResolveCloseClaimReasonOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveCloseClaimReasonOutputsSuccessBody"}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossCloseInputBody"}, "ClaimLossReopenInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossReopenInputBody"}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "lossSubStatusCd": {"type": "string"}}, "title": "ClaimLossSubStatusInput"}, "ClaimLossSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossSubStatusInputBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ResolveLossAdditionalBenefitSettlementsOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputs"}, "ResolveLossAdditionalBenefitSettlementsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputs"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputsSuccess"}, "ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveLossAdditionalBenefitSettlementsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveLossAdditionalBenefitSettlementsOutputsSuccessBody"}, "ResolveLossMainBenefitSettlementsOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveLossMainBenefitSettlementsOutputs"}, "ResolveLossMainBenefitSettlementsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputs"}}, "title": "ResolveLossMainBenefitSettlementsOutputsSuccess"}, "ResolveLossMainBenefitSettlementsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveLossMainBenefitSettlementsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveLossMainBenefitSettlementsOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SubmitLossToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "SubmitLossToAccumulatorTxOutputs"}, "SubmitLossToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/SubmitLossToAccumulatorTxOutputs"}}, "title": "SubmitLossToAccumulatorTxOutputsSuccess"}, "SubmitLossToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/SubmitLossToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SubmitLossToAccumulatorTxOutputsSuccessBody"}}}