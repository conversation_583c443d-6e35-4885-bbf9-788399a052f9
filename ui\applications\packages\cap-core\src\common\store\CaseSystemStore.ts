/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {CaseSystemService, EntityKeyParams} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {BaseRootStore} from './BaseRootStore'
import {ICaseSystem} from '../Types'

export interface CaseSystemStore<CS extends ICaseSystem, CSService extends CaseSystemService> extends BaseRootStore {
    caseSystem?: CS
    caseSystemService: CSService
    loadEventCase: (caseSystemParams: EntityKeyParams) => RxResult<CS>
    updateCaseSystem: (updatedCaseSystem: CS) => RxResult<CS>
    refreshCaseSystem: () => RxResult<CS>
}
