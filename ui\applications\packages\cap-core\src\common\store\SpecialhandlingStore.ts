/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {LossParamsWithModelName, specialHandlingService, ClaimLoss, asoTypeCd} from '@eisgroup/cap-services'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {action, observable, runInAction} from 'mobx'
import {errorToRxResult} from '@eisgroup/common'
import {RxResult} from '@eisgroup/common-types'
import {Observable} from 'rxjs'
import {Right} from '@eisgroup/data.either'
import {CapSpecialHandling} from '@eisgroup/cap-models'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'
import {storeBindingFactory} from './StoreHOCs'
import {getSpecialHandlingKeys} from '../utils'
import {SpecialHandlingTypes} from '../Types'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapSpecialHandlingEntity = CapSpecialHandling.CapSpecialHandlingEntity

export interface SpecialHandlingStore extends BaseRootStore {
    specialHandling: CapSpecialHandlingEntity
    getStoreSpecialHandlingKeys: (specialHandlings: SpecialHandlingTypes) => string[]
    getSpecialHandling: ({rootId, revisionNo, modelName}: LossParamsWithModelName) => RxResult<CapSpecialHandlingEntity>
    updateSpecialHandling: (specialHandling: CapSpecialHandlingEntity) => RxResult<CapSpecialHandlingEntity>
    createSpecialHandling: (
        loss: ClaimLoss | CapEventCaseEntity,
        specialHandling?: CapSpecialHandlingEntity
    ) => RxResult<CapSpecialHandlingEntity>
}

export class SpecialHandlingStoreImpl extends BaseRootStoreImpl implements SpecialHandlingStore {
    @observable specialHandling: CapSpecialHandlingEntity =
        CapSpecialHandling.factory.newByType<CapSpecialHandlingEntity>(CapSpecialHandlingEntity)

    @action
    private payLoadToSpecialHandling = (response: CapSpecialHandlingEntity) => {
        this.specialHandling = {
            ...this.specialHandling,
            ...response
        }
    }

    private produceSpecialHandlingWithAso = (loss: ClaimLoss | CapEventCaseEntity): CapSpecialHandlingEntity => {
        const atpInd = loss?.policy?.asoTypeCd === asoTypeCd.ATP
        const atpWithCheckInd = loss?.policy?.asoTypeCd === asoTypeCd.ATPWithCheck
        return {
            atpInd,
            atpWithCheckInd,
            ...this.specialHandling
        }
    }

    @action
    getStoreSpecialHandlingKeys = (specialHandlings: SpecialHandlingTypes) => {
        return getSpecialHandlingKeys(this.specialHandling, specialHandlings)
    }

    @action
    getSpecialHandling = ({
        rootId,
        revisionNo,
        modelName
    }: LossParamsWithModelName): RxResult<CapSpecialHandlingEntity> => {
        return this.call(() =>
            specialHandlingService
                .getSpecialHandling({
                    rootId,
                    revisionNo,
                    modelName
                })
                .flatMap(r =>
                    r.fold(errorToRxResult, response => {
                        runInAction(() => this.payLoadToSpecialHandling(response[0] as CapSpecialHandlingEntity))
                        return Observable.of(Right(response))
                    })
                )
        )
    }

    @action
    updateSpecialHandling = (specialHandling: CapSpecialHandlingEntity): RxResult<CapSpecialHandlingEntity> => {
        return this.call(() =>
            specialHandlingService.updateSpecialHandling(specialHandling).flatMap(r =>
                r.fold(errorToRxResult, payload => {
                    runInAction(() => this.payLoadToSpecialHandling(payload))
                    return Observable.of(Right(payload))
                })
            )
        )
    }

    @action
    createSpecialHandling = (
        loss: ClaimLoss | CapEventCaseEntity,
        specialHandling?: CapSpecialHandlingEntity
    ): RxResult<CapSpecialHandlingEntity> => {
        return this.call(() =>
            specialHandlingService
                .createSpecialHandling(
                    {
                        rootId: loss?._key.rootId,
                        revisionNo: loss?._key.revisionNo,
                        modelName: loss?._modelName
                    },
                    specialHandling || this.produceSpecialHandlingWithAso(loss)
                )
                .flatMap(r =>
                    r.fold(errorToRxResult, payload => {
                        runInAction(() => this.payLoadToSpecialHandling(payload))
                        return Observable.of(Right(payload))
                    })
                )
        )
    }
}

export interface BindableProps<S = any> {
    store: S
}

export const {createViewLoader, connectToStore} = storeBindingFactory<SpecialHandlingStore>(
    () => new SpecialHandlingStoreImpl()
)
