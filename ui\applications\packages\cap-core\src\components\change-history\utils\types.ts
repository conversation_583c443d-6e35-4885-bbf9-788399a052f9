/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapRelationship, CapSpecialHandling} from '@eisgroup/cap-models'
import {CapPaymentTemplate} from '@eisgroup/cap-financial-models'
import {
    CapLtd,
    CapLtdSettlement,
    CapSmp,
    CapSmpSettlement,
    CapStd,
    CapStdSettlement
} from '@eisgroup/cap-disability-models'
import {
    CapEventCase,
    AcceleratedSettlement,
    AccidentalDismembermentSettlement,
    CISettlement,
    ClaimWrapper,
    DeathSettlement,
    HISettlement,
    PremiumWaiverSettlement
} from '@eisgroup/cap-event-case-models'
import {ChangeHistoryPatch, ChangeHistoryRecordWithUser, ClaimLoss, ClaimParty} from '@eisgroup/cap-services'
import {EisDate, EisDateTime} from '@eisgroup/ui-temporals'
import {has} from 'lodash'

import {patchModels} from './model-patch'

import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapSpecialHandlingEntity = CapSpecialHandling.CapSpecialHandlingEntity
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity
import {Settlements} from '../../../common/Types'

export const EVENT_CASE_MODEL_NAME = 'CapEventCase'
export const SPECIAL_HANDLING_MODEL_NAME = 'CapSpecialHandling'
export const PAYMENT_TEMPLATE_MODEL_NAME = 'CapPaymentTemplate'
export const CAP_RELATIONSHIP_MODEL_NAME = 'CapRelationship'

export const ChangeHistoryClaimModelNames = {
    CapStd: 'CapStd',
    CapLtd: 'CapLtd',
    CapSmp: 'CapSmp',
    ClaimWrapper: 'ClaimWrapper'
} as const

type ChangeHistoryClaimModelNamesKeys = keyof typeof ChangeHistoryClaimModelNames

export const ChangeHistorySettlementModelNamesDisabilityClaims = {
    CapStdSettlement: 'CapStdSettlement',
    CapSmpSettlement: 'CapSmpSettlement',
    CapLtdSettlement: 'CapLtdSettlement'
} as const

export const ChangeHistorySettlementModelNamesLifeClaims = {
    CISettlement: 'CISettlement',
    HISettlement: 'HISettlement',
    DeathSettlement: 'DeathSettlement',
    AcceleratedSettlement: 'AcceleratedSettlement',
    AccidentalDismembermentSettlement: 'AccidentalDismembermentSettlement',
    PremiumWaiverSettlement: 'PremiumWaiverSettlement'
} as const

export const ChangeHistorySettlementModelNames = {
    ...ChangeHistorySettlementModelNamesDisabilityClaims,
    ...ChangeHistorySettlementModelNamesLifeClaims
}

type ChangeHistorySettlementModelNamesKeys = keyof typeof ChangeHistorySettlementModelNames

export type ChangeHistoryModelNames =
    | typeof EVENT_CASE_MODEL_NAME
    | ChangeHistoryClaimModelNamesKeys
    | ChangeHistorySettlementModelNamesKeys

/**
 * All model definitions history table supports
 * Any model that needs to be supported needs to be added here, otherwise its history records will be ignored
 */
export const ModelNameToModelDefinition = patchModels({
    [EVENT_CASE_MODEL_NAME]: CapEventCase.model,
    [ChangeHistoryClaimModelNames.CapStd]: CapStd.model,
    [ChangeHistoryClaimModelNames.CapSmp]: CapSmp.model,
    [ChangeHistoryClaimModelNames.CapLtd]: CapLtd.model,
    [ChangeHistoryClaimModelNames.ClaimWrapper]: ClaimWrapper.model,
    [ChangeHistorySettlementModelNames.CapStdSettlement]: CapStdSettlement.model,
    [ChangeHistorySettlementModelNames.CapSmpSettlement]: CapSmpSettlement.model,
    [ChangeHistorySettlementModelNames.CapLtdSettlement]: CapLtdSettlement.model,
    [ChangeHistorySettlementModelNames.CISettlement]: CISettlement.model,
    [ChangeHistorySettlementModelNames.HISettlement]: HISettlement.model,
    [ChangeHistorySettlementModelNames.DeathSettlement]: DeathSettlement.model,
    [ChangeHistorySettlementModelNames.AcceleratedSettlement]: AcceleratedSettlement.model,
    [ChangeHistorySettlementModelNames.AccidentalDismembermentSettlement]: AccidentalDismembermentSettlement.model,
    [ChangeHistorySettlementModelNames.PremiumWaiverSettlement]: PremiumWaiverSettlement.model,
    [SPECIAL_HANDLING_MODEL_NAME]: CapSpecialHandling.model,
    [PAYMENT_TEMPLATE_MODEL_NAME]: CapPaymentTemplate.model,
    [CAP_RELATIONSHIP_MODEL_NAME]: CapRelationship.model
} as const)

export const ModelNameToRootReference = {
    [EVENT_CASE_MODEL_NAME]: 'CapEventCaseDetailEntity',
    [ChangeHistoryClaimModelNames.CapStd]: 'CapDisabilityClaimDetailEntity',
    [ChangeHistoryClaimModelNames.CapSmp]: 'CapSMPClaimDetailEntity',
    [ChangeHistoryClaimModelNames.CapLtd]: 'CapLTDisabilityClaimDetailEntity',
    [ChangeHistoryClaimModelNames.ClaimWrapper]: 'CapClaimWrapperDetailEntity',
    [ChangeHistorySettlementModelNames.CapStdSettlement]: 'CapStdSettlementDetailEntity',
    [ChangeHistorySettlementModelNames.CapSmpSettlement]: 'CapSmpSettlementDetailEntity',
    [ChangeHistorySettlementModelNames.CapLtdSettlement]: 'CapLtdSettlementDetailEntity',
    [ChangeHistorySettlementModelNames.CISettlement]: 'CapCISettlementDetailEntity',
    [ChangeHistorySettlementModelNames.HISettlement]: 'CapHISettlementDetailEntity',
    [ChangeHistorySettlementModelNames.DeathSettlement]: 'CapDeathSettlementDetailEntity',
    [ChangeHistorySettlementModelNames.AcceleratedSettlement]: 'CapAcceleratedSettlementDetailEntity',
    [ChangeHistorySettlementModelNames.AccidentalDismembermentSettlement]:
        'CapAccidentalDismembermentSettlementDetailEntity',
    [ChangeHistorySettlementModelNames.PremiumWaiverSettlement]: 'CapPremiumWaiverSettlementDetailEntity',
    [SPECIAL_HANDLING_MODEL_NAME]: 'CapSpecialHandlingEntity',
    [PAYMENT_TEMPLATE_MODEL_NAME]: 'CapPaymentScheduleSettlementInfoInput',
    [CAP_RELATIONSHIP_MODEL_NAME]: 'CapRelationshipEntity'
} as const

export type Patch = ChangeHistoryPatch['patch'][number]

export interface ChangeHistoryRecordFlatten extends Omit<ChangeHistoryRecordWithUser, 'forwardPatch' | 'inversePatch'> {
    forwardPatch: Patch
    inversePatch: Patch
}

export interface ChangeHistoryRecordFlattenExtended {
    originalRecord: ChangeHistoryRecordFlatten

    oldValue?: ChangeHistoryValue
    newValue?: ChangeHistoryValue
    lookupName?: string

    parentEntity: string

    /** Absolute path of the changed attribute */
    pathFull: string
    /** Relative path of the changed attribute from the parent entity */
    pathToAttribute: string
    /** Path to the parent entity */
    pathToParent: string
}

export interface HistoryTableRow {
    date?: string | Date
    currentDate?: string | Date | EisDateTime
    component: string
    reference: string
    entity: string
    attribute: string
    original?: string | string[]
    new?: string | string[]
    adjustedBy: string
    metadata: {
        timestamp: string | Date
        path: string
        lookupName?: string
        lossIdentification?: LossIdentification
        refExtension?: string
    }
    eventCaseInformation: {
        link: string
        number: string
    }
}

export interface OptionsListeProps {
    component: string[]
    adjustedBy: string[]
    attribute: string[]
    entity: string[]
    reference: string[]
}

export const tableFiltersTitleReference = {
    date: 'cap-core:history_table_date_and_time',
    component: 'cap-core:history_table_component',
    reference: 'cap-core:history_table_reference',
    entity: 'cap-core:history_table_entity',
    attribute: 'cap-core:history_table_attribute',
    original: 'cap-core:history_table_original',
    new: 'cap-core:history_table_new',
    adjustedBy: 'cap-core:history_table_adjusted_by'
} as const

export interface tableFiltersProps {
    date?: string | Date[] | string[]
    currentDate?: string | Date[] | string[]
    component?: string[]
    reference?: string[]
    entity?: string[]
    attribute?: string[]
    original?: string | string[]
    adjustedBy?: string[]
}

export interface LossIdentification {
    modelName?: typeof EVENT_CASE_MODEL_NAME | keyof typeof ChangeHistoryClaimModelNames
    rootId?: string
    revisionNo?: string
}

export interface LossExtractData {
    component: HistoryTableRow['component']
    reference: HistoryTableRow['reference']
    lossIdentification?: LossIdentification
    refExtension?: string
}

export interface Entities {
    eventCase: CapEventCaseEntity
    claims: ClaimLoss[]
    settlements: Settlements[]
    specialHandlings: CapSpecialHandlingEntity[]
    paymentTemplates: CapPaymentTemplateEntity[]
    parties: ClaimParty[]
    capRelationships: CapRelationship.CapRelationshipEntity[]
}

export const ClaimModelToType = {
    CapStd: 'STD',
    CapLtd: 'LTD',
    CapSmp: 'SMD',
    CapLeave: 'Leave',
    CI: 'CI',
    HI: 'HI',
    TL: 'Life',
    PL: 'PL',
    ACC: 'Accident',
    STD: 'STD',
    LTD: 'LTD',
    SMP: 'SMD'
} as const

export const ClaimSettlementToClaimType = {
    DeathSettlement: 'Life',
    AcceleratedSettlement: 'Life',
    CISettlement: 'CI',
    HISettlement: 'HI',
    AccidentalDismembermentSettlement: 'Accident'
} as const

export type ChangeHistoryValueInteger = {num: number}
export type ChangeHistoryValueDecimal = {bigDecimal: number}
export type ChangeHistoryValueString = {value: string | Date | EisDateTime | EisDate}
export type ChangeHistoryValueTypeString = {valueType: string}
export type ChangeHistoryValueUri = {_uri: {value: string}}
export type ChangeHistoryValueNumeric = ChangeHistoryValueInteger | ChangeHistoryValueDecimal
export type ChangeHistoryValueCollection =
    | ChangeHistoryValueNumeric[]
    | ChangeHistoryValueString[]
    | ChangeHistoryValueUri[]
export type ChangeHistoryMoney = {
    currency: ChangeHistoryValue
    amount: ChangeHistoryValueNumeric
}
export type ChangeHistoryValue =
    | ChangeHistoryValueNumeric
    | ChangeHistoryValueString
    | ChangeHistoryValueTypeString
    | ChangeHistoryValueCollection
    | ChangeHistoryMoney
    | ChangeHistoryValueUri

export function isHistoryValueMoney(value: ChangeHistoryValue): value is ChangeHistoryMoney {
    return has(value, 'currency') && has(value, 'amount')
}

export function isHistoryValueString(value: ChangeHistoryValue): value is ChangeHistoryValueString {
    return has(value, 'value')
}

export function isHistoryValueTypeString(value: ChangeHistoryValue): value is ChangeHistoryValueTypeString {
    return has(value, 'valueType')
}

export function isHistoryValueInteger(value: ChangeHistoryValue): value is ChangeHistoryValueInteger {
    return has(value, 'num')
}

export function isHistoryValueDecimal(value: ChangeHistoryValue): value is ChangeHistoryValueDecimal {
    return has(value, 'bigDecimal')
}

export function isHistoryValueUri(value: ChangeHistoryValue): value is ChangeHistoryValueUri {
    return has(value, '_uri')
}

export interface LabelAnnotation {
    name: string
}

export const LABEL_FEATURE_ID = 'class com.eisgroup.genesis.factory.model.features.Label'

export function isEventCaseModel(modelName: string): boolean {
    return modelName === EVENT_CASE_MODEL_NAME
}

export function isClaimModel(modelName: string): boolean {
    return Object.keys(ChangeHistoryClaimModelNames).includes(modelName)
}

export function isSettlementModel(modelName: string): boolean {
    return Object.keys(ChangeHistorySettlementModelNames).includes(modelName)
}

export function isSpecialHandlingModel(modelName: string): boolean {
    return modelName === SPECIAL_HANDLING_MODEL_NAME
}

export function isPaymentTemplateModel(modelName: string): boolean {
    return modelName === PAYMENT_TEMPLATE_MODEL_NAME
}

export function isCapRelationshipModel(modelName: string): boolean {
    return modelName === CAP_RELATIONSHIP_MODEL_NAME
}
