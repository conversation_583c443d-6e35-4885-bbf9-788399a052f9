/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {isEmpty} from 'lodash'
import {Button} from '@eisgroup/ui-kit'
import * as React from 'react'
import classNames from 'classnames'
import {ADDABLE_TABLE_BORDER, ADDABLE_TABLE_BUTTON, ADDABLE_TABLE_HEADER} from '../../common/package-class-names'

export interface AddableTableWrapperProps {
    /**
     * Section label
     */
    readonly sectionLabel?: string
    /**
     * Default add button label
     */
    readonly buttonLabel?: string
    /**
     * Callback on button click
     */
    readonly onAddClick?: () => void
    /**
     * Should table contain border
     */
    readonly bordered?: boolean
    /**
     * ClassName for a component
     */
    readonly className?: string
    /**
     * Custom button to display
     */
    readonly customButton?: React.ReactNode
    /**
     * Customize the area under the title
     */
    readonly customAreaUnderTitle?: React.ReactNode
    /**
     * Indicates if button is disabled
     */
    readonly disabled?: boolean
    /**
     * adds possibility to hide button
     */
    readonly hideButton?: boolean
    /**
     * Unlike original addable table wrapper, minor wrapper only adds "Add" button at the
     * bottom of the table. No other modifications will be made to table.
     */
    readonly isMinorWrapper?: boolean
}

export const AddableTableWrapper: React.FunctionComponent<AddableTableWrapperProps> = ({
    onAddClick,
    sectionLabel,
    customButton,
    customAreaUnderTitle,
    buttonLabel,
    bordered,
    children,
    className,
    disabled,
    isMinorWrapper,
    hideButton
}) => {
    const renderAddButton = () => (
        <Button
            onClick={() => onAddClick && onAddClick()}
            disabled={disabled}
            className={ADDABLE_TABLE_BUTTON}
            style={hideButton ? {display: 'none'} : undefined}
            type='link'
            icon='action-add-small'
        >
            &nbsp;{buttonLabel}
        </Button>
    )

    const renderAddableTableWrapper = () => (
        <div className={classNames(className, bordered ? ADDABLE_TABLE_BORDER : undefined)}>
            {sectionLabel && (
                <div className={ADDABLE_TABLE_HEADER}>
                    <div>
                        {isEmpty(sectionLabel?.trim()) ? null : <h4>{sectionLabel}</h4>}
                        {customAreaUnderTitle || ''}
                    </div>
                    {customButton || renderAddButton()}
                </div>
            )}
            {children}
        </div>
    )

    const renderMinorTableWrapper = () => (
        <>
            {children}
            {renderAddButton()}
        </>
    )

    return isMinorWrapper ? renderMinorTableWrapper() : renderAddableTableWrapper()
}
