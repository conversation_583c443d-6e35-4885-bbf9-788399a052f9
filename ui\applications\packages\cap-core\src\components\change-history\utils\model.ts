/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {BusinessType, isBusinessEntity, ModelAnnotations, ModelDefinition} from '@eisgroup/models-api'
import {isArray} from 'lodash'

import {LABEL_FEATURE_ID, LabelAnnotation, ModelNameToModelDefinition, ModelNameToRootReference} from './types'

/**
 * Recursively traverses and transforms complex object into collection of primitive values and their paths
 * @returns {path: string; value: any}[]
 */
export function flattenObjectRecord(rootPath: string, obj: BusinessType | undefined): {path: string; value: any}[] {
    if (!obj) {
        return []
    }

    const records: {path: string; value: any}[] = []

    for (const key in obj) {
        if (!key.startsWith('_')) {
            if (isBusinessEntity(obj[key])) {
                records.push(...flattenObjectRecord(`${rootPath}.${key}`, obj[key]))
            } else if (isArray(obj[key])) {
                if (obj[key] && isBusinessEntity(obj[key][0])) {
                    for (let i = 0; i < obj[key].length; i++) {
                        const element = obj[key][i]
                        records.push(...flattenObjectRecord(`${rootPath}.${key}.${i}`, element))
                    }
                } else {
                    records.push({
                        path: `${rootPath}.${key}`,
                        value: obj[key]
                    })
                }
            } else {
                records.push({
                    path: `${rootPath}.${key}`,
                    value: obj[key]
                })
            }
        }
    }

    return records
}

/**
 * Uses model definition to find attribute object by a given path
 * @param path - attribute path
 * @param modelName - model name
 * @returns ModelDefinition.Attribute | null
 */
export function getAttribute(path: string, modelName: string): ModelDefinition.Attribute | null {
    if (!ModelNameToModelDefinition[modelName]) {
        return null
    }
    return ModelDefinition.findAttribute(path, ModelNameToModelDefinition[modelName]).orElse(null as any)
}

/**
 * Gets model's root entity type name
 * @param modelName - model name
 */
export function getModelRootEntityTypeName(modelName: string): string {
    const model: ModelDefinition.DomainModel = ModelNameToModelDefinition[modelName]
    return model.root.type
}

/**
 * example: .lossDetail.financialAdjustment.deductions
 * will return: deductions
 */
export function extractAttributeNameFromPath(path: string): string {
    return path.split('.').splice(-1).join('.')
}

/**
 * example: .lossDetail.financialAdjustment
 * will return: .lossDetail.financialAdjustment
 */
export function extractParentPathFromAttributePath(path: string): string {
    const attributes = path.split('.')
    attributes.splice(-1)
    return attributes.join('.')
}

/**
 * example: .lossDetail.financialAdjustment.deductions.0
 * will return: .lossDetail.financialAdjustment.deductions
 */
export function removeIndexFromPathEnd(path: string): string {
    return path.replace(/.\d+$/, '')
}

/**
 * Looks for Lookup annotation for a given attribute path and returns lookupName if exists
 */
export function getLookupNameByAttributePath(path: string, modelName: string): string | undefined {
    return ModelDefinition.findAttribute(path, ModelNameToModelDefinition[modelName])
        .map<string | undefined>(attribute => {
            return ModelAnnotations.extractAnnotations(attribute.features).find(ModelAnnotations.isLookup)?.lookupName
        })
        .orElseGet(() => undefined)
}

/**
 * Looks for Label annotation for a given attribute path
 */
export function getLabelAnnotationByAttributePath(path: string, modelName: string): LabelAnnotation | undefined {
    const attribute = getAttribute(path, modelName)
    return attribute?.features?.[LABEL_FEATURE_ID] as LabelAnnotation
}

/**
 * Gets attribute entity type or its parent's entity type if given attribute is primitive
 */
export function getEntityType(attribute: ModelDefinition.Attribute, path: string, modelName: string): string {
    if (isAttributePrimitive(attribute)) {
        return extractEntityTypeForPrimitiveAttribute(path, modelName) || getModelRootEntityTypeName(modelName)
    }
    return ModelDefinition.isUnionEntityType(attribute.type) ? attribute.name : attribute.type.type
}

/**
 * Finds entity type that attribute on the given path belongs to.
 * Returns undefined if no attribute's parent found.
 */
function extractEntityTypeForPrimitiveAttribute(path: string, modelName: string): string | undefined {
    const parentPath = extractParentPathFromAttributePath(path)
    const parentAttribute = getAttribute(parentPath, modelName)

    return parentAttribute && ModelDefinition.isEntityType(parentAttribute.type)
        ? (parentAttribute.type as ModelDefinition.EntityType).type
        : undefined
}

export function isAttributePrimitive(attribute: ModelDefinition.Attribute): boolean {
    return ModelDefinition.isPrimitiveType(attribute.type)
}

/**
 * Checks if entity is the root reference like CapEventCaseDetailEntity.
 * It is needed for manual translations specified in 18n resource files.
 */
export function isRootEntityReference(entityName: string, modelName: string): boolean {
    return ModelNameToRootReference[modelName] === entityName
}

export function isRootEntity(entityName: string, modelName: string): boolean {
    return getModelRootEntityTypeName(modelName) === entityName
}
