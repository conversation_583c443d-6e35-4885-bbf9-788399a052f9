/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {withFormState} from '@eisgroup/form'
import {PaymentDefinition} from '@eisgroup/cap-financial-models'
import {
    PaymentAppliedWithholdings,
    PaymentAppliedWithholdingsProps
} from '../../components/balance/PaymentAppliedWithholdings'

import CapPaymentEntity = PaymentDefinition.CapPaymentEntity

type PaymentAppliedWithholdingsOtherProps = {
    issuedPaymentsWhichContainsWithholdings?: PaymentDefinition.CapPaymentEntity[]
}

function withStore<P>(
    Component: React.ComponentType<P>,
    other: PaymentAppliedWithholdingsOtherProps = {}
): React.ComponentType<P> {
    return props => <Component {...props} {...other} />
}

export const getPaymentAppliedWithholdingsSlot = (issuedPaymentsWhichContainsWithholdings: CapPaymentEntity[]) => {
    return withFormState<PaymentAppliedWithholdingsProps>(
        withStore(PaymentAppliedWithholdings, {
            issuedPaymentsWhichContainsWithholdings
        })
    )
}
