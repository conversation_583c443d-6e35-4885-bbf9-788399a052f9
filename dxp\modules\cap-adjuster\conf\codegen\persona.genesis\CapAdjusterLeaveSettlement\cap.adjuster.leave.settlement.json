{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.leave.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Leave Settlements API", "version": "1", "title": "CAP Adjuster: Leave Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-leave-settlements", "description": "CAP Adjuster: Leave Settlements API"}], "paths": {"/losses-leave/settlements/readjudicate": {"patch": {"summary": "Readjudicate Leave settlement", "x-dxp-path": "/api/capsettlement/CapLeaveSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/settlements/initApprovalPeriod": {"post": {"summary": "Initialize approval period", "x-dxp-path": "/api/capsettlement/CapLeaveSettlement/v1/command/initApprovalPeriod", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/settlements/updateApprovalPeriod": {"post": {"summary": "Updates specific approval period", "x-dxp-path": "/api/capsettlement/CapLeaveSettlement/v1/command/updateApprovalPeriod", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/settlements/rules/bundle": {"post": {"summary": "Bundle Leave settlement", "x-dxp-path": "/api/capsettlement/CapLeaveSettlement/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-leave"]}}, "/losses-leave/settlements/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/capsettlement/CapLeaveSettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-leave"]}}}}