/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {EventCaseService, eventCaseService} from '@eisgroup/cap-services'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {CaseSystemPaymentStoreImpl} from './CaseSystemPaymentStoreImpl'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import {EventCaseStore} from '../EventCaseStore'

export class PaymentsStoreImpl extends CaseSystemPaymentStoreImpl<CapEventCaseEntity, EventCaseService> {
    constructor(eventCaseStore: EventCaseStore) {
        super(eventCaseService, eventCaseStore.eventCase, eventCaseStore)
    }
}
