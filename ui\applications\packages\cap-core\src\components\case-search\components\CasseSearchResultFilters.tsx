import {t} from '@eisgroup/i18n'
import {Button} from '@eisgroup/ui-kit'
import {ActionCloseSmall} from '@eisgroup/ui-kit-icons'
import {observer} from 'mobx-react'
import React, {FC} from 'react'
import {
    CASE_SEARCH_RESULT_FILTER_TAGS,
    CASE_SEARCH_RESULT_FILTER_TAGS_ITEM,
    CASE_SEARCH_RESULT_FILTER_TAGS_ITEM_VALUE,
    CASE_SEARCH_RESULT_FILTER_TITLE
} from '../../../common/package-class-names'

export type CasseSearchResultFiltersProps = {
    searchMode: 'singleSearch' | 'advanceSearch'
    filters: {
        key: string
        labelKey?: string
        value: string
    }[]
    onRemoveFilter: (filterKey: string) => void
    onRemoveAllFilters: (allFilterKeys: string[]) => void
    pageNum: number
    pageSize: number
    total: number
    searchContent?: string
}

export const CasseSearchResultFilters: FC<CasseSearchResultFiltersProps> = observer(props => {
    const {searchMode, pageNum, pageSize, total, searchContent} = props
    let searchResultTitleKey = ''

    switch (searchMode) {
        case 'singleSearch':
            searchResultTitleKey = searchContent
                ? 'cap-core:show_search_result_title_single_search'
                : 'cap-core:show_search_result_title_full_search'
            break
        case 'advanceSearch':
            searchResultTitleKey = 'cap-core:show_search_result_title_advance_search'
            break
    }
    const currentStart = pageSize * (pageNum - 1) + 1
    const pageStart = currentStart <= total ? currentStart : total
    const pageEnd = pageSize * pageNum <= total ? pageSize * pageNum : total
    return (
        <div>
            <div className={CASE_SEARCH_RESULT_FILTER_TITLE}>
                <label>
                    {t(searchResultTitleKey, {X: pageStart, Y: pageEnd, Z: total, searchValue: searchContent})}
                </label>
            </div>
            {searchMode === 'advanceSearch' ? (
                <div className={CASE_SEARCH_RESULT_FILTER_TAGS}>
                    {props.filters?.map(v => {
                        return (
                            <div key={v.key} className={CASE_SEARCH_RESULT_FILTER_TAGS_ITEM}>
                                <span>{t(v.labelKey)}</span>
                                <div className={CASE_SEARCH_RESULT_FILTER_TAGS_ITEM_VALUE}>{v.value}</div>
                                <ActionCloseSmall onClick={() => props.onRemoveFilter(v.key)} />
                            </div>
                        )
                    })}
                    {props.filters?.length ? (
                        <Button
                            type='link'
                            onClick={() => props.onRemoveAllFilters(props.filters.map(filter => filter.key))}
                        >
                            {t('cap-core:clear_all')}
                        </Button>
                    ) : null}
                </div>
            ) : null}
        </div>
    )
})
