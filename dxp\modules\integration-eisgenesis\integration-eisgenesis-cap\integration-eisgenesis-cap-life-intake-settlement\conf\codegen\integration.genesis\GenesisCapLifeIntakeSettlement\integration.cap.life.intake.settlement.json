{"swagger": "2.0", "info": {"description": "API for LifeIntakeSettlement", "version": "1", "title": "LifeIntakeSettlement model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capsettlement/LifeIntakeSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/rules/{entryPoint}": {"post": {"description": "endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LifeIntakeSettlementKrakenBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/LifeIntakeSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/LifeIntakeSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/command/approveSettlement": {"post": {"description": "The command that approves adjudicated settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/command/initSettlement": {"post": {"description": "The command that initiate creation of new settlement image with unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeIntakeRequestSettlementAdjudicationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/LifeIntakeSettlement/v1/command/closeSettlement": {"post": {"description": "The command that closes settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"LifeIntakeSettlement_BaseLifeBenefitInfo": {"required": ["_type"], "properties": {"benefitTypeCd": {"type": "string"}, "_type": {"type": "string", "example": "BaseLifeBenefitInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "BaseLifeBenefitInfo"}, "LifeIntakeSettlement_CapLifeIntakeSettlementResultEntity": {"required": ["_type"], "properties": {"activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "coverageTypeCd": {"type": "string", "description": "This attribute describes the coverage type."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem"}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when lifeIntake was reported."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored in CAP subsystem."}, "reserve": {"type": "number"}, "_type": {"type": "string", "example": "CapLifeIntakeSettlementResultEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeSettlementResultEntity", "description": "Business entity that defines settlement result."}, "LifeIntakeSettlementKrakenBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "LifeIntakeSettlementKrakenBundleRequest"}, "CapLifeIntakeRequestSettlementAdjudicationInputBody": {"properties": {"body": {"$ref": "#/definitions/CapLifeIntakeRequestSettlementAdjudicationInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeIntakeRequestSettlementAdjudicationInputBody"}, "LifeIntakeSettlement_CapLifeIntakeAcceleratedLossSettlementLossInfoEntity": {"required": ["_type"], "properties": {"activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations, acquired for Death claim type during Life Intake."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened, acquired for Death claim type during Life Intake."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time, acquired for Death claim type during Life Intake."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when LifeIntake was reported, acquired for Death claim type during Life Intake."}, "_type": {"type": "string", "example": "CapLifeIntakeAcceleratedLossSettlementLossInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeAcceleratedLossSettlementLossInfoEntity", "description": "Business entity that houses the information from loss AcceleratedDeath details to use in settlement."}, "LifeIntakeSettlement_CapLifeCustomerInformationEntity": {"required": ["_type"], "properties": {"genderCd": {"type": "string", "description": "Is used to identify customer gender"}, "registryTypeId": {"type": "string", "description": "Is used to corelate which party this is pulled for"}, "_type": {"type": "string", "example": "CapLifeCustomerInformationEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeCustomerInformationEntity", "description": "Contains customer information that is needed for calculations"}, "CapSettlementReadjudicateInput": {"required": ["_key"], "properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "_updateStrategy": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "entity": {"type": "object"}}, "title": "CapSettlementReadjudicateInput"}, "LifeIntakeSettlement_MessageType": {"required": ["_type"], "properties": {"severity": {"type": "string", "description": "Defines severity of the messages."}, "code": {"type": "string", "description": "Defines the message's code."}, "source": {"type": "string", "description": "Defines source of the message."}, "message": {"type": "string", "description": "Defines message"}, "_type": {"type": "string", "example": "MessageType"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "MessageType", "description": "Holds information of message type."}, "LoadEntityByBusinessKeyRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntity"}}, "title": "LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccess"}, "LifeIntakeSettlement_CapLifeIntakeSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"memberInfo": {"description": "Contains member customer related information", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeCustomerInformationEntity"}, "policies": {"type": "array", "items": {"description": "Entity for LifeIntake Settlement Policy Information.", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementPolicyInfoEntity"}}, "applicabilityResult": {"description": "Business entity defines a collection of Applicability results", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeApplicabilityResultEntity"}, "settlementLossInfo": {"description": "Business entity that houses the information from loss to use in settlement.", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementLossInfoEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "accessTrackInfo": {"description": "Core base type that stores information who created or last updated entities. It is part of extended settlements, e.g. CapAbsenceSettlementEntity", "$ref": "#/definitions/LifeIntakeSettlement_AccessTrackInfo"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"description": "Business entity defines settlement result.", "$ref": "#/definitions/LifeIntakeSettlement_CapSettlementResult"}, "settlementAbsenceInfo": {"type": "object", "description": "The object that includes information taken from Absence case."}, "policyIds": {"type": "array", "items": {"type": "string", "description": "Indicates all available policyIds."}}, "settlementDetail": {"type": "object", "description": "This Business entity houses the detail of the LifeIntake Settlement."}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "absence": {"$ref": "#/definitions/EntityLink"}, "_modelName": {"type": "string", "example": "LifeIntakeSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_timestamp": {"type": "string", "example": "2021-12-21T14:18:14.775+08:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapLifeIntakeSettlementEntity"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "CapLifeIntakeSettlementEntity", "description": "The object that encompasses attributes set for LifeIntake Settlement."}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "LifeIntakeSettlement_CapLifeIntakeSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"capCoverageInfo": {"type": "array", "items": {"description": "List of coverage infos", "$ref": "#/definitions/LifeIntakeSettlement_BaseLifeCoverageInfo"}}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "insureds": {"type": "array", "items": {"description": "An entity for insureds information.", "$ref": "#/definitions/LifeIntakeSettlement_CapInsuredInfo"}}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "term": {"description": "Policy effective and expiration dates.", "$ref": "#/definitions/LifeIntakeSettlement_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "_type": {"type": "string", "example": "CapLifeIntakeSettlementPolicyInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeSettlementPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "LoadSingleEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "CapSettlementReadjudicateInputBody": {"properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapSettlementReadjudicateInputBody"}, "LifeIntakeSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "The unique registry ID that identifies the subject of the claim."}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "LifeIntakeSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/LifeIntakeSettlementLoadHistoryResult"}}, "title": "LifeIntakeSettlementLoadHistoryResultSuccess"}, "LifeIntakeSettlement_CapLifeIntakeSettlementLossInfoEntity": {"required": ["_type"], "properties": {"lossDesc": {"type": "string", "description": "Free form description of a loss event."}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "acceleratedLossSettlementLossInfo": {"description": "Business entity that houses the information from loss AcceleratedDeath details to use in settlement.", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeAcceleratedLossSettlementLossInfoEntity"}, "deathSettlementLossInfo": {"description": "Business entity that houses the information from loss Death details to use in settlement.", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeDeathSettlementLossInfoEntity"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when lifeIntake was reported."}, "_type": {"type": "string", "example": "CapLifeIntakeSettlementLossInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeSettlementLossInfoEntity", "description": "Business entity that houses the information from loss to use in settlement."}, "IdentifierRequestBody": {"properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "IdentifierRequestBody"}, "LoadEntityRootRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityRootRequest"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "LifeIntakeSettlement_CapLifeIntakeApplicabilityResultEntity": {"required": ["_type"], "properties": {"messages": {"type": "array", "items": {"description": "Holds information of messages.", "$ref": "#/definitions/LifeIntakeSettlement_MessageType"}}, "settlementResults": {"type": "array", "items": {"description": "Business entity that defines settlement result.", "$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementResultEntity"}}, "_type": {"type": "string", "example": "CapLifeIntakeApplicabilityResultEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeApplicabilityResultEntity", "description": "Business entity defines a collection of Applicability results"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "LifeIntakeSettlement_CapSettlementResult": {"required": ["_type"], "properties": {"reserve": {"type": "number"}, "_type": {"type": "string", "example": "CapSettlementResult"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapSettlementResult", "description": "Business entity defines settlement result."}, "LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LifeIntakeSettlement_CapLifeIntakeSettlementEntitySuccessBody"}, "LifeIntakeSettlement_CapLifeIntakeDeathSettlementLossInfoEntity": {"required": ["_type"], "properties": {"activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations, acquired for Death claim type during Life Intake."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened, acquired for Death claim type during Life Intake."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time, acquired for Death claim type during Life Intake."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when LifeIntake was reported, acquired for Death claim type during Life Intake."}, "_type": {"type": "string", "example": "CapLifeIntakeDeathSettlementLossInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeDeathSettlementLossInfoEntity", "description": "Business entity that houses the information from loss Death details to use in settlement."}, "LoadEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "LifeIntakeSettlement_BaseLifeCoverageInfo": {"required": ["_type"], "properties": {"benefits": {"type": "array", "items": {"$ref": "#/definitions/LifeIntakeSettlement_BaseLifeBenefitInfo"}}, "coverageCd": {"type": "string"}, "_type": {"type": "string", "example": "BaseLifeCoverageInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "BaseLifeCoverageInfo"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "LifeIntakeSettlementKrakenBundleRequestBody": {"properties": {"body": {"$ref": "#/definitions/LifeIntakeSettlementKrakenBundleRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LifeIntakeSettlementKrakenBundleRequestBody"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "LifeIntakeSettlement_Term": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Term"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "Term"}, "EntityLinkRequestBody": {"properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "CapLifeIntakeRequestSettlementAdjudicationInput": {"properties": {"settlementType": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "policyIds": {"type": "array", "items": {"type": "string"}}, "entity": {"type": "object"}}, "title": "CapLifeIntakeRequestSettlementAdjudicationInput"}, "LifeIntakeSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/LifeIntakeSettlementLoadHistoryResultSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LifeIntakeSettlementLoadHistoryResultSuccessBody"}, "LifeIntakeSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}, "createdOn": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccessTrackInfo"}, "LifeIntakeSettlementLoadHistoryResult": {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/LifeIntakeSettlement_CapLifeIntakeSettlementEntity"}}, "count": {"type": "integer", "format": "int64"}}, "title": "LifeIntakeSettlementLoadHistoryResult"}}}