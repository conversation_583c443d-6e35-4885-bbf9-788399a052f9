/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.ltdloss;

import cap.adjuster.services.financial.dto.CapPayment;
import cap.adjuster.services.ltdloss.dto.CapLtdSettlement;
import cap.adjuster.services.ltdloss.impl.CapAdjusterLtdLossServiceImpl;
import com.google.inject.ImplementedBy;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * Service that provides methods for CAP LTD
 */
@ImplementedBy(CapAdjusterLtdLossServiceImpl.class)
public interface CapAdjusterLtdLossService {

    /**
     * Get payments associated with ltd loss
     *
     * @param rootId ltd loss identifier
     * @param revisionNo ltd loss revision number
     * @return list of payments related to absence loss
     */
    CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo);

    /**
     * Get settlements associated with ltd loss
     *
     * @param rootId     ltd loss identifier
     * @param revisionNo ltd loss revision number
     * @return ltd loss settlements
     */
    CompletionStage<List<CapLtdSettlement>> getSettlements(String rootId, Integer revisionNo);
}
