{"swagger": "2.0", "x-dxp-spec": {"imports": {"std": {"schema": "integration.cap.std.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: STD Loss API", "version": "1", "title": "CAP Adjuster: STD Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-std", "description": "CAP Adjuster: STD Loss API"}], "paths": {"/losses-std/{rootId}/{revisionNo}": {"get": {"summary": "Search STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/caploss/CapStd/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/rules/bundle": {"post": {"summary": "Rules bundle for STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/draft": {"post": {"summary": "Init STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std": {"post": {"summary": "Create STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-std"]}, "put": {"summary": "Update STD loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapStd/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/closeLoss": {"post": {"summary": "Close STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/command/closeLoss", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/reopenLoss": {"post": {"summary": "Reopen STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/command/reopenLoss", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/submitLoss": {"post": {"summary": "Submit STD loss", "x-dxp-path": "/api/caploss/CapStd/v1/command/submitLoss", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/setLossSubStatus": {"post": {"summary": "Set STD loss sub-status", "x-dxp-path": "/api/caploss/CapStd/v1/command/setLossSubStatus", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/updateReturnToWork": {"post": {"summary": "Updates Return To Work Entity", "x-dxp-path": "/api/caploss/CapStd/v1/command/updateReturnToWork", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/retrieve-closure-open-items": {"post": {"summary": "Retrieve STD loss closure open items", "x-dxp-path": "/api/caploss/CapStd/v1/transformation/absenceClaimClosureToOpenItems", "tags": ["/cap-adjuster/v1/losses-std"]}}}}