/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {BusinessTypes} from '@eisgroup/cap-event-case-models'
import {CapPaymentSchedule, CapPaymentTemplate, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {
    CapAdjusterFinancialCommonVerifyPaymentScheduleActivationInputs,
    CapAdjusterPaymentDefinitionCapRecoveryGenerationRequest,
    CapFinancialPayment
} from '@eisgroup/cap-gateway-client'
import {
    backOfficeWorkflowService,
    CaseSystemService,
    claimLeaveService,
    ClaimLoss,
    claimLtdService,
    ClaimParty,
    claimPaymentTemplatesService,
    claimSmpService,
    claimStdService,
    claimWrapperService,
    createCommonPollingObservable,
    createWorkflowPollingObservable,
    CSClaimWrapperService,
    dateUtils,
    financialService,
    IndividualCustomer,
    LossParams,
    LossParamsWithModelName,
    LossParamsWithVersion,
    OrganizationCustomer
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import {Either, Right} from '@eisgroup/data.either'
import {StepStatus} from '@eisgroup/react-components'
import _, {delay} from 'lodash'
import {action, computed, IObservableArray, observable, runInAction} from 'mobx'
import {Observable} from 'rxjs'
import {EventCaseStore} from '../EventCaseStore'
import {aggregateTaxesByYear, TaxYearSummary} from '../../../components/financial-info/summary-of-taxes/utils'
import {CreateOrUpdate, filteredPayments, getChangedRecord} from '../../../components/payments/Utils'
import {SelectOptionProps} from '../../../components/select-input-wrapper/SelectInputWrapper'
import {
    filterRelationships,
    getEitherResult,
    getErrorMessage,
    getPreviewPaymentScheduleErrorMsg,
    pollPaymentsAfterClaimUpdate,
    searchCustomerRelationshipWithTypeCd
} from '../../../utils/CaseSystemPaymentStoreUtils'
import {
    CALCULATE_PAYMENTS,
    ClaimTypesMap,
    CREATE_PAYMENTS,
    GENERATE_RECOVERY,
    LifeClaimTypesMap,
    LOAD_ASSOCIATED_SETTLEMENTS,
    LOAD_PAYMENT_TEMPLATE,
    LOAD_PAYMENTS,
    workFlowStates
} from '../../constants'
import {Allocation, ClaimStates, ICaseSystem, PaymentScheduledState, Settlements} from '../../Types'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {CaseSystemPaymentStore, CaseSystemPaymentWizardStepKey} from '../CaseSystemPaymentStore'
import CapLoss = BusinessTypes.CapLoss
import CapBuildPaymentScheduleInput = CapPaymentSchedule.CapBuildPaymentScheduleInput
import CapPaymentScheduleEntity = CapPaymentSchedule.CapPaymentScheduleEntity
import CapPaymentDetailsTemplateEntity = CapPaymentTemplate.CapPaymentDetailsTemplateEntity
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity

export class CaseSystemPaymentStoreImpl<
        CS extends ICaseSystem,
        CSService extends CaseSystemService | CSClaimWrapperService<CS>
    >
    extends BaseRootStoreImpl
    implements CaseSystemPaymentStore<CS, CSService>
{
    initialPaymentTemplate: CapPaymentTemplateEntity = {
        ...CapPaymentTemplate.factory.newByType<CapPaymentTemplateEntity>(CapPaymentTemplateEntity),
        paymentDetailsTemplate: {
            ...CapPaymentTemplate.factory.newByType<CapPaymentDetailsTemplateEntity>(CapPaymentDetailsTemplateEntity),
            paymentAllocationTemplates: []
        }
    }

    @observable caseSystem?: CS

    @observable caseStore?: EventCaseStore

    @observable caseSystemService: CSService

    @observable payments: CapPaymentEntity[] = []

    @observable payeeList: [IndividualCustomer | OrganizationCustomer]

    @observable paymentTemplate: CapPaymentTemplateEntity = this.initialPaymentTemplate

    @observable paymentSchedule: CapPaymentScheduleEntity

    @observable paymentScheduleList: CapPaymentScheduleEntity[] = []

    @observable paymentList: [] = []

    @observable allAssociatedClaims: IObservableArray<ClaimLoss> = observable<ClaimLoss>([])

    @observable allAssociatedSettlements: Settlements[] = []

    @observable currentPaymentTemplate: CapPaymentTemplateEntity

    @observable paymentTemplates: CapPaymentTemplateEntity[] = []

    @observable originalPaymentTemplate: CapPaymentTemplateEntity

    @observable originalPaymentDetailStepAllocations: Allocation[] = []

    @observable activeStep: CaseSystemPaymentWizardStepKey = CaseSystemPaymentWizardStepKey.Detail

    @observable stepsStatuses: Record<string, StepStatus> = {}

    @observable closeDrawer = false

    @observable associatedLosses: ClaimLoss[]

    @observable formItemChanged = false

    @observable currentPayee = ''

    @observable representBeneficiary = ''

    @observable createOrUpdatePayment = ''

    @observable hasEnterStep2 = false

    @observable showOnBehalfOf = false

    @observable onBehalfOfOptions: SelectOptionProps[]

    constructor(caseSystemService: CSService, caseSystem?: CS, caseStore?: EventCaseStore) {
        super()
        this.caseSystemService = caseSystemService
        this.caseSystem = caseSystem
        this.caseStore = caseStore
    }

    @computed get taxYearSummaries(): TaxYearSummary[] {
        return aggregateTaxesByYear(this.paymentSchedule, this.payments)
    }

    @computed
    get associatedSettlements(): Settlements[] {
        return this.allAssociatedSettlements.filter(v => v.state === 'Approved' || v.state === 'Closed')
    }

    @computed
    get associatedClaims(): ClaimLoss[] {
        return this.allAssociatedClaims.filter(v => v.state === ClaimStates.OPEN || v.state === ClaimStates.PENDING)
    }

    @action
    setCaseSystem = (caseSystem: CS) => {
        this.caseSystem = caseSystem
    }

    @action
    setAssociatedClaims = (associatedClaims: CapLoss[]) => {
        this.allAssociatedClaims.splice(0, Infinity, ...(associatedClaims as ClaimLoss[]))
    }

    @action
    setCreateOrUpdatePayment = (mode: string) => {
        this.createOrUpdatePayment = mode
    }

    @action
    pushSettlement = (response: any[], rootId: string) => {
        response.forEach(v => {
            this.allAssociatedSettlements = this.allAssociatedSettlements.filter(
                val => val._key.rootId !== v._key.rootId
            )
            this.allAssociatedSettlements.push({...v, associatedClaimRootId: rootId})
        })
    }

    @action
    loadAssociatedSettlements = (losses: (CapLoss & {claimType?: string})[]) => {
        const requestParams = (loss: CapLoss): LossParams => {
            return {
                rootId: loss._key.rootId,
                revisionNo: loss._key.revisionNo
            }
        }
        this.actionsStore.startAction(LOAD_ASSOCIATED_SETTLEMENTS)

        Observable.of(...losses)
            .map(loss => {
                if (Object.keys(LifeClaimTypesMap).includes(loss.claimType ?? loss._modelName ?? '')) {
                    return claimWrapperService
                        .loadCoverages(requestParams(loss))
                        .map(response => this.pushSettlement(response.get(), loss._key.rootId))
                }
                if (loss._modelName === ClaimTypesMap.STD) {
                    return claimStdService
                        .loadSettlements(requestParams(loss))
                        .map(response => this.pushSettlement(response.get(), loss._key.rootId))
                }
                if (loss._modelName === ClaimTypesMap.SMP) {
                    return claimSmpService
                        .loadSettlements(requestParams(loss))
                        .map(response => this.pushSettlement(response.get(), loss._key.rootId))
                }
                if (loss._modelName === ClaimTypesMap.LTD) {
                    return claimLtdService
                        .loadSettlements(requestParams(loss))
                        .map(response => this.pushSettlement(response.get(), loss._key.rootId))
                }
                return claimLeaveService
                    .loadSettlements(requestParams(loss))
                    .map(response => this.pushSettlement(response.get(), loss._key.rootId))
            })
            .mergeAll()
            .subscribe({
                complete: () => {
                    this.actionsStore.completeAction(LOAD_ASSOCIATED_SETTLEMENTS)
                }
            })
    }

    @action
    updatePaymentTemplate = (paymentTemplate?: CapPaymentTemplateEntity) => {
        this.paymentTemplate = paymentTemplate || this.initialPaymentTemplate
    }

    @action
    setOriginalPaymentTemplate = (paymentTemplate: CapPaymentTemplateEntity) => {
        this.originalPaymentTemplate = paymentTemplate
    }

    @action
    updateCurrentPayee = (payee?: string) => {
        this.currentPayee = payee || ''
    }

    @action
    updatePayeeInfo = (payeeList: [IndividualCustomer | OrganizationCustomer]) => {
        this.payeeList = payeeList
    }

    @action
    updateCurrGuardian = (representBeneficiary?: string) => {
        this.representBeneficiary = representBeneficiary || ''
    }

    @action
    calculatePayments = (
        params: CapBuildPaymentScheduleInput,
        payeeUri?: string
    ): RxResult<CapPaymentScheduleEntity> => {
        return this.call(
            () =>
                financialService.previewPaymentSchedule(params).flatMap(either =>
                    either.fold(getErrorMessage, payload => {
                        runInAction(() => {
                            const res = payload as CapPaymentScheduleEntity
                            const payments = (res.payments ?? []).filter(
                                payment => payment.paymentDetails?.payeeRoleDetails?.registryId === payeeUri
                            )
                            this.paymentSchedule = {
                                ...res,
                                payments
                            }
                            this.updateActiveStepStatus(StepStatus.Success)
                            this.changeToNextStep(CaseSystemPaymentWizardStepKey.Allocations)
                        })
                        return Observable.of(Right(payload))
                    })
                ),
            CALCULATE_PAYMENTS
        )
    }

    @action
    updateActiveStepStatus = (stepStatus: StepStatus) => {
        this.stepsStatuses = {
            ...this.stepsStatuses,
            [this.activeStep]: stepStatus
        }
    }

    @action
    changeToNextStep = (key: CaseSystemPaymentWizardStepKey) => this.changeStep(key)

    @action
    changeStep = (key: CaseSystemPaymentWizardStepKey) => {
        this.activeStep = key
    }

    @action
    setHasEnterStep2 = (hasEnterStep2: boolean) => {
        this.hasEnterStep2 = hasEnterStep2
    }

    @action
    setShowOnBehalfOf = (showOnBehalfOf: boolean) => {
        this.showOnBehalfOf = showOnBehalfOf
    }

    @action
    setOnBehalfOfOptions = (onBehalfOfOptions: SelectOptionProps[]) => {
        this.onBehalfOfOptions = onBehalfOfOptions
    }

    @action
    setOriginalPaymentDetailStepAllocations = (allocations: Allocation[]) => {
        this.originalPaymentDetailStepAllocations = allocations
    }

    @action
    resetPaymentWizard = () => {
        this.closeDrawer = false
        this.activeStep = CaseSystemPaymentWizardStepKey.Detail
        this.formItemChanged = false
        this.hasEnterStep2 = false
        this.representBeneficiary = ''
        this.paymentTemplate = this.currentPaymentTemplate ? this.currentPaymentTemplate : this.initialPaymentTemplate
    }

    @action
    searchCustomerRelationship = (chosenValue: string, beneficiaries: ClaimParty[]): void => {
        const guardianRegistryId = beneficiaries?.find(
            item => item?.partyRole?.representativeRegistryId === chosenValue
        )?.partyRole?.registryId

        if (guardianRegistryId) {
            this.setShowOnBehalfOf(true)
        } else {
            this.setShowOnBehalfOf(false)
        }
        searchCustomerRelationshipWithTypeCd(chosenValue).then(relationships => {
            if (relationships) {
                // filter out chosenValue and duplicate beneficiary
                const onBehalfOfOption = filterRelationships(relationships, chosenValue)
                this.setOnBehalfOfOptions(onBehalfOfOption as SelectOptionProps[])
            }
        })
    }

    @action
    loadPaymentsAfterClaimUpdate = (
        caseParams: LossParamsWithModelName,
        claimParams: LossParamsWithModelName
    ): void => {
        pollPaymentsAfterClaimUpdate(caseParams, claimParams).subscribe(() => {
            this.loadCurrentPayments(caseParams)
        })
    }

    @action
    loadDataAfterPaymentScheduleCreateOrUpdate = (lossParamsWithModelName: LossParamsWithModelName) => {
        const TAKE_COUNT = 20
        const POLL_DELAY = 500
        const POLL_PERIOD = 1000
        this.call(
            () =>
                createCommonPollingObservable(
                    scheduleResult =>
                        scheduleResult.length === 0 ||
                        scheduleResult[0]._key.revisionNo === this.paymentScheduleList?.[0]?._key?.revisionNo ||
                        scheduleResult[0].state === PaymentScheduledState.Open,
                    () => claimPaymentTemplatesService.loadClaimPaymentSchedules([lossParamsWithModelName], 1),
                    TAKE_COUNT,
                    POLL_DELAY,
                    POLL_PERIOD
                ),
            LOAD_PAYMENTS
        ).subscribe(either => {
            this.loadCurrentPaymentTemplate([lossParamsWithModelName])
            this.loadCurrentPayments(lossParamsWithModelName, undefined, undefined, undefined, false)
            runInAction(() => {
                const schedules = either.get()
                this.paymentScheduleList = schedules
                this.paymentSchedule = schedules[0]
            })
            this.resetPaymentWizard()
        })
    }

    @action
    loadCurrentPayments = (
        {rootId, revisionNo, modelName}: LossParamsWithModelName,
        recordRootId?: string,
        entityUris?: string[],
        processDefinitionKeys?: string[],
        updateSchedules = true
    ) => {
        const eventCaseParams = {
            rootId: this.caseSystem?._key?.rootId,
            revisionNo: this.caseSystem?._key?.revisionNo,
            modelName: this.caseSystem?._modelName
        } as LossParamsWithModelName
        const associatedClaimsParams = this.allAssociatedClaims.map(claim => ({
            rootId: claim._key.rootId,
            revisionNo: claim._key.revisionNo,
            modelName: claim._modelName
        }))
        if (updateSchedules && (eventCaseParams.rootId || associatedClaimsParams.length)) {
            const lossParams = eventCaseParams.rootId
                ? [eventCaseParams, ...associatedClaimsParams]
                : associatedClaimsParams
            this.loadPaymentSchedules(lossParams)
        }
        const withResponse = (either: Either<ErrorMessage, CapFinancialPayment[]>) => {
            runInAction(() => {
                const result = either.get()
                const newPayments = recordRootId ? getChangedRecord(result, recordRootId) : result
                this.payments = filteredPayments(newPayments)
            })
        }
        // invoke financial/{rootId}/{revisionNo}/payment-with-scheduled
        if (entityUris && entityUris.length > 0) {
            this.call(
                () =>
                    financialService.loadPayments({
                        rootId,
                        revisionNo,
                        modelName
                    }),
                LOAD_PAYMENTS
            ).subscribe(withResponse)
        } else {
            this.call(
                () =>
                    financialService.loadPaymentWithScheduled({
                        rootId,
                        revisionNo,
                        modelName
                    }),
                LOAD_PAYMENTS
            ).subscribe(withResponse)
        }
    }

    @action
    pollPaymentsWithScheduled = (params: LossParamsWithModelName) => {
        this.call(() => financialService.pollPaymentsWithScheduled(params), LOAD_PAYMENTS).subscribe(either =>
            runInAction(() => {
                this.payments = filteredPayments(either.get())
            })
        )
    }

    @action
    updatePayments = (payments: CapPaymentEntity[]) => {
        this.payments = payments
    }

    @action
    createPayment = (params: CapBuildPaymentScheduleInput): RxResult<CapPaymentTemplateEntity> => {
        return this.call(() => financialService.buildPaymentScheduleFlow(params), CREATE_PAYMENTS)
    }

    @action
    loadCurrentPaymentTemplate = (lossParams: LossParamsWithModelName | LossParamsWithModelName[]) => {
        // invoke payment-template/search and filter latest
        const params = !Array.isArray(lossParams) ? [lossParams] : lossParams
        this.call(
            () => claimPaymentTemplatesService.loadPaymentTemplatesByEventCase(params),
            LOAD_PAYMENT_TEMPLATE
        ).subscribe(either => {
            runInAction(() => {
                const result = either.get() || []
                const all = [...this.paymentTemplates, ...result]
                this.paymentTemplates = [
                    ...new Map(all.map(v => [`${v._key.rootId}-${v._key.revisionNo}`, v])).values()
                ]
                this.currentPaymentTemplate = result.sort((a, b) => b._timestamp.getTime() - a._timestamp.getTime())[0]
                this.setOriginalPaymentTemplate(this.currentPaymentTemplate)
                if (this.createOrUpdatePayment === CreateOrUpdate.UPDATE && !_.isEmpty(either.get())) {
                    this.updatePaymentTemplate(this.currentPaymentTemplate)
                }
            })
        })
    }

    @action
    loadPaymentSchedules = (lossParams: LossParamsWithModelName[]) => {
        // invoke financial/payment-schedule/search
        this.call(() => claimPaymentTemplatesService.loadClaimPaymentSchedules(lossParams)).subscribe(either => {
            runInAction(() => {
                const result = either.get()
                this.paymentScheduleList = result
                this.paymentSchedule = result.sort(
                    (a, b) => dateUtils(b._timestamp).getTime() - dateUtils(a._timestamp).getTime()
                )?.[0]
            })
        })
    }

    @action
    pollLoadPaymentsAfterWorkflow = (lossParams: LossParamsWithModelName[], entityUri: string) => {
        return createWorkflowPollingObservable(
            workflowResult =>
                !(workflowResult.length === 0 || workflowResult.every(v => v.statusCd === workFlowStates.COMPLETED)),
            () =>
                backOfficeWorkflowService.searchWorkflowProcessByEntityUri(entityUri, {
                    statusCd: undefined,
                    processDefinitionKeys: ['paymentToPaymentHubIntegration']
                }),
            () => this.loadPayments(lossParams)
        )
    }

    loadPaymentByCount = (page, lossParams) => {
        return claimPaymentTemplatesService.loadPayments(lossParams, page).map(getEitherResult).toPromise()
    }

    @action
    loadPayments = (lossParams: LossParamsWithModelName[]): RxResult<any[]> => {
        return this.call<any>(() => claimPaymentTemplatesService.loadPayments(lossParams, 0)).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    const count = payload?.count || 0
                    const result = payload?.result || []
                    const pageCount = Math.ceil(count / 100)
                    this.paymentList = result
                    if (pageCount > 1) {
                        const promiseList: any[] = []
                        for (let i = 1; i < pageCount; i++) {
                            promiseList.push(this.loadPaymentByCount(i * 100, lossParams))
                        }
                        Promise.all(promiseList).then(response => {
                            response.forEach(v => {
                                this.paymentList = this.paymentList.concat(v) as []
                            })
                        })
                    }
                })
                return Observable.of(Right(this.paymentList))
            })
        )
    }

    @action
    reCalculatePayments = (params: CapBuildPaymentScheduleInput, paymentTemplate: CapPaymentTemplateEntity) => {
        this.call(() =>
            financialService.previewPaymentSchedule(params).flatMap(either =>
                either.fold(
                    e => {
                        return errorToRxResult({
                            code: String(e.errors ? e.errors?.[0]?.code : e.code),
                            message: String(e.errors ? getPreviewPaymentScheduleErrorMsg(e) : e.message)
                        } as ErrorMessage)
                    },
                    payload => {
                        return Observable.of(Right(payload))
                    }
                )
            )
        ).subscribe(either => {
            runInAction(() => {
                this.paymentSchedule = either.get()
                this.updatePaymentTemplate({
                    ...paymentTemplate,
                    buildPaymentScheduleInput: params
                })
                delay(() => {
                    this.setFormItemChanged(false)
                }, 300)
            })
        })
    }

    @action
    setFormItemChanged = (value: boolean) => {
        this.formItemChanged = value
    }

    @action
    checkPaymentSchedule = (paymentSchedule, userId: string): RxResult<CapPaymentScheduleEntity> => {
        // Manually add the parameter _modelName must be filled in, otherwise the call to the interface will report errors
        // dxp/modules/integration-eisgenesis/integration-eisgenesis-cap/integration-eisgenesis-cap-common/conf/codegen/integration.genesis/GenesisCapCommon/integration.cap.common.json
        const requestParams = {
            schedule: paymentSchedule,
            userRequest: {
                _type: 'CapUserCarryingEntity',
                _modelName: 'CommonFinancialInternal',
                userId
            }
        } as unknown as CapAdjusterFinancialCommonVerifyPaymentScheduleActivationInputs
        return this.call<CapPaymentScheduleEntity>(() =>
            claimPaymentTemplatesService.verifyPaymentScheduleActivation(requestParams)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentSchedule = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    activatePaymentSchedule = ({rootId, revisionNo}: LossParams): RxResult<CapPaymentScheduleEntity> => {
        const requested = {
            _key: {
                rootId,
                revisionNo
            }
        } as LossParamsWithVersion
        return this.call<CapPaymentScheduleEntity>(() =>
            claimPaymentTemplatesService.activatePaymentSchedule(requested)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentSchedule = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    suspendPaymentSchedule = ({rootId, revisionNo}: LossParams): RxResult<CapPaymentScheduleEntity> => {
        const requested = {
            _key: {
                rootId,
                revisionNo
            }
        } as LossParamsWithVersion
        return this.call<CapPaymentScheduleEntity>(() =>
            claimPaymentTemplatesService.suspendPaymentSchedule(requested)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentSchedule = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    unsuspendPaymentSchedule = ({rootId, revisionNo}: LossParams): RxResult<CapPaymentScheduleEntity> => {
        const requested = {
            _key: {
                rootId,
                revisionNo
            }
        } as LossParamsWithVersion
        return this.call<CapPaymentScheduleEntity>(() =>
            claimPaymentTemplatesService.unsuspendPaymentSchedule(requested)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentSchedule = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    generatePaymentSchedule = ({rootId, revisionNo}: LossParams): RxResult<CapPaymentScheduleEntity> => {
        const requested = {
            _key: {
                rootId,
                revisionNo
            }
        } as LossParamsWithVersion
        return this.call<CapPaymentScheduleEntity>(() =>
            claimPaymentTemplatesService.generatePaymentSchedule(requested)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentSchedule = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    cancelPaymentSchedule = ({rootId, revisionNo}: LossParams): RxResult<CapPaymentScheduleEntity> => {
        const requested = {
            _key: {
                rootId,
                revisionNo
            }
        } as LossParamsWithVersion
        return this.call<CapPaymentScheduleEntity>(() =>
            claimPaymentTemplatesService.cancelPaymentSchedule(requested)
        ).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                runInAction(() => {
                    this.paymentSchedule = payload
                })
                return Observable.of(Right(payload))
            })
        )
    }

    @action
    generateRecovery = (
        params: CapAdjusterPaymentDefinitionCapRecoveryGenerationRequest
    ): RxResult<CapPaymentEntity> => {
        return this.call(() => financialService.generateRecovery(params), GENERATE_RECOVERY)
    }
}
