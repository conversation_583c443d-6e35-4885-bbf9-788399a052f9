{"swagger": "2.0", "x-dxp-spec": {"imports": {"accelerated": {"schema": "integration.cap.hi.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Hospital Indemnity Loss API", "version": "1", "title": "CAP Adjuster: Hospital Indemnity Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-hi", "description": "CAP Adjuster: Hospital Indemnity Loss API"}], "paths": {"/losses-hi/{rootId}/{revisionNo}": {"get": {"summary": "Get hospital indemnity loss by rootId and revisionNo", "x-dxp-path": "/api/caploss/HILoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-hi"]}}, "/losses-hi/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for hospital indemnity loss", "x-dxp-path": "/api/caploss/HILoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-hi"]}}, "/losses-hi": {"post": {"summary": "Create hospital indemnity loss", "x-dxp-path": "/api/caploss/HILoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-hi"]}, "put": {"summary": "Update hospital indemnity loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/HILoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-hi"]}}, "/losses-hi/draft": {"post": {"summary": "Init hospital indemnity loss", "x-dxp-path": "/api/caploss/HILoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-hi"]}}}}