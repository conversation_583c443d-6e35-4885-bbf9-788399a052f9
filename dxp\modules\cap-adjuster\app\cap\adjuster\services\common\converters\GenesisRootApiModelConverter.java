/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.converters;

import cap.adjuster.services.common.dto.GenesisRootApiModel;
import dataproviders.common.dto.GenesisRootDTO;

public class GenesisRootApiModelConverter<I extends GenesisRootDTO, A extends GenesisRootApiModel>
        extends GenesisApiModelConverter<I, A> {

    @Override
    public I convertToInternalDTO(A apiDTO, I intDTO) {
        super.convertToInternalDTO(apiDTO, intDTO);

        intDTO.modelName = apiDTO.modelName;
        intDTO.modelVersion = apiDTO.modelVersion;
        intDTO.timestamp = apiDTO.timestamp;

        return intDTO;
    }

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);

        apiDTO.modelName = intDTO.modelName;
        apiDTO.modelVersion = intDTO.modelVersion;
        apiDTO.timestamp = intDTO.timestamp;

        return apiDTO;
    }
}
