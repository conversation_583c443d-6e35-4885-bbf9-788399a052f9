import {action, observable, reaction, runInAction} from 'mobx'
import {SearchCaseSuggestionServiceType, searchCaseSuggestionsService} from '../services'
import {DEFAULT_SEARCHING_WORD_THRESHOLD} from '../constants'
import {CaseSearchResultType} from '../types'
import {BaseRootStoreImpl} from '../../../common/store'

export type CaseSuggestionStoreInitType = {
    searchingThreshold?: number
    searchingCaseSuggestionService?: SearchCaseSuggestionServiceType
}

export class CaseSuggestionStore extends BaseRootStoreImpl {
    @observable searchingThreshold: number = DEFAULT_SEARCHING_WORD_THRESHOLD

    @observable searchValue: string | undefined

    @observable selectedSuggestionId: string | undefined

    @observable suggestionsCount = 0

    @observable searchingItemsForSuggestions = false

    @observable.shallow searchSuggestionsResult: CaseSearchResultType[] = []

    private searchingCaseSuggestionsService: SearchCaseSuggestionServiceType = searchCaseSuggestionsService

    constructor() {
        super()
        reaction(
            () => this.searchSuggestionsResult,
            searchSuggestionsResult => {
                const selectedSuggestionId = searchSuggestionsResult?.find(
                    item => item._key.rootId === this.selectedSuggestionId
                )?._key?.rootId
                this.changeSelectedSuggestionId(selectedSuggestionId)
            }
        )
    }

    @action
    setSuggestionsResult = (result: CaseSearchResultType[]) => {
        this.searchSuggestionsResult = result
    }

    @action
    setSuggestionsCount = (count: number) => {
        this.suggestionsCount = count
    }

    @action
    searchSuggestions = async content => {
        try {
            this.searchingItemsForSuggestions = true
            const {count, items} = await this.searchingCaseSuggestionsService(content)
            this.setSuggestionsCount(count)
            this.setSuggestionsResult(items)
        } catch (error) {
            this.errorStore.handleError(error)
        } finally {
            runInAction(() => {
                this.searchingItemsForSuggestions = false
            })
        }
    }

    @action
    changeSearchingThreshold = (searchingThreshold: number) => {
        this.searchingThreshold = searchingThreshold
    }

    @action
    changeSearchValue = (searchValue: string | undefined) => {
        this.searchValue = searchValue
    }

    @action
    changeSelectedSuggestionId = (id?: string) => {
        this.selectedSuggestionId = id
    }

    init = (params?: CaseSuggestionStoreInitType) => {
        const {searchingThreshold, searchingCaseSuggestionService} = params ?? {}
        if (searchingThreshold !== undefined) {
            this.changeSearchingThreshold(searchingThreshold)
        }
        if (searchingCaseSuggestionService) {
            this.searchingCaseSuggestionsService = searchingCaseSuggestionService
        }
    }

    @action
    resetAll = () => {
        this.searchingThreshold = DEFAULT_SEARCHING_WORD_THRESHOLD
        this.searchValue = undefined
        this.suggestionsCount = 0
        this.searchingItemsForSuggestions = false
        this.searchSuggestionsResult = []
        this.selectedSuggestionId = undefined
    }
}
