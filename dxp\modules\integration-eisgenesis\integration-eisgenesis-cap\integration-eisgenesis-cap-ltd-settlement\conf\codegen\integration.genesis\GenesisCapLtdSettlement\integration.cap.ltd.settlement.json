{"swagger": "2.0", "info": {"description": "API for CapLtdSettlement", "version": "1", "title": "CapLtdSettlement model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capsettlement/CapLtdSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/command/approveSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapApproveSettlementRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/command/closeSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/command/createAdditionalBenefitSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRequestAdditionalBenefitSettlementCreationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/command/initSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRequestLtdSettlementAdjudicationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLtdSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapLtdSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapLtdSettlementKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/CapLTDSettlementAdjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapLTDSettlementAdjudicationInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/adjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/approveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/disapproveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/initDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/initLtdSettlementBenefitToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitLtdSettlementBenefitToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/readjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/resolveClaimAdditionalSettlementsBySettlement": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ResolveClaimAdditionalSettlementsBySettlementOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapLtdSettlement/v1/transformation/settlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/SettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AdjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputs"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "CapApproveSettlementRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "userId": {"type": "string"}}, "title": "CapApproveSettlementRequest"}, "CapApproveSettlementRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapApproveSettlementRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapApproveSettlementRequestBody"}, "CapLTDSettlementAdjudicationInputOutputs": {"properties": {"output": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementRulesInput"}}, "title": "CapLTDSettlementAdjudicationInputOutputs"}, "CapLTDSettlementAdjudicationInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLTDSettlementAdjudicationInputOutputs"}}, "title": "CapLTDSettlementAdjudicationInputOutputsSuccess"}, "CapLTDSettlementAdjudicationInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLTDSettlementAdjudicationInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLTDSettlementAdjudicationInputOutputsSuccessBody"}, "CapLtdSettlementKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapLtdSettlementKrakenDeprecatedBundleRequest"}, "CapLtdSettlementKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLtdSettlementKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtdSettlementKrakenDeprecatedBundleRequestBody"}, "CapLtdSettlementLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntity"}}}, "title": "CapLtdSettlementLoadHistoryResult"}, "CapLtdSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLtdSettlementLoadHistoryResult"}}, "title": "CapLtdSettlementLoadHistoryResultSuccess"}, "CapLtdSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLtdSettlementLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtdSettlementLoadHistoryResultSuccessBody"}, "CapLtdSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapLtdSettlement AccessTrackInfo"}, "CapLtdSettlement_BaseAbsencePolicyBenefitLimitLevel": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseAbsencePolicyBenefitLimitLevel"}, "limitLevelType": {"type": "string", "description": "Type of limit level"}, "timePeriodCd": {"type": "string", "description": "Time period Code"}}, "title": "CapLtdSettlement BaseAbsencePolicyBenefitLimitLevel"}, "CapLtdSettlement_BaseAbsenceSettlementAttrOptions": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseAbsenceSettlementAttrOptions"}, "attrName": {"type": "string", "description": "Attribute Name"}, "options": {"type": "array", "items": {"type": "string", "description": "Options of attribute, e.g. Mandatory"}}}, "title": "CapLtdSettlement BaseAbsenceSettlementAttrOptions"}, "CapLtdSettlement_BaseAbsenceSettlementBenefitConfiguration": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseAbsenceSettlementBenefitConfiguration"}, "accumulationCategoryGroup": {"type": "string", "description": "Category group to indicate the accumulating group a benefit is belonged to in settlement amount calculation."}, "attrOptions": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_BaseAbsenceSettlementAttrOptions"}}, "benefitCategory": {"type": "string", "description": "Benefit Category"}, "benefitLevelMapping": {"type": "array", "items": {"type": "string", "description": "Claim fields mapping to policy fields configuration."}}, "calculationFormulaId": {"type": "string", "description": "Formula Id to define the formulas that are used in settlement calculation for different benefits."}, "colaApplies": {"type": "boolean", "description": "Is COLA applicable"}, "coverageType": {"type": "string", "description": "Coverage Type"}, "deductionsAllowed": {"type": "boolean", "description": "Deductions Allowed"}, "groupAccumulatorLimitLevel": {"type": "string", "description": "Defines the limitLevel for the group accumulator"}, "groupUnit": {"type": "string", "description": "Indicate the amount type for group accumulator limit level amount"}, "interestApply": {"type": "boolean", "description": "Interests apply"}, "limitLevels": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_BaseAbsencePolicyBenefitLimitLevel"}}, "partialDisability": {"type": "boolean", "description": "Applies for partial disability"}, "policyBenefitCd": {"type": "string", "description": "Policy Benefit Code"}, "policyInfo": {"type": "object", "description": "Policy mapped values"}, "recurrentPayment": {"type": "boolean", "description": "Recurrent payment"}, "taxable": {"type": "boolean", "description": "Taxable"}, "unit": {"type": "string", "description": "Indicate the amount type for accumulator limit level amount"}}, "title": "CapLtdSettlement BaseAbsenceSettlementBenefitConfiguration"}, "CapLtdSettlement_CapApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapApprovalPeriodEntity"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLtdSettlement CapApprovalPeriodEntity"}, "CapLtdSettlement_CapBaseDisabilityAccumulatorExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityAccumulatorExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Accumulator Unit code."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapLtdSettlement_Term"}}, "title": "CapLtdSettlement CapBaseDisabilityAccumulatorExtension", "description": "Entity for Accumulator extension for rules."}, "CapLtdSettlement_CapBaseDisabilityFormulaCalculationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaCalculationDetails"}, "calculatedResult": {"$ref": "#/definitions/Money"}, "formulaAppliedAttribute": {"type": "string", "description": "Attribute name which formula calculation applied"}, "formulaContent": {"type": "string", "description": "Formula Content."}, "formulaId": {"type": "string", "description": "Formula Id."}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityFormulaParameter"}}, "policyRoundingAmount": {"type": "string", "description": "Policy Rounding Amount"}, "policyRoundingFactorCd": {"type": "string", "description": "Policy Rounding Factor Cd"}, "roundingResult": {"$ref": "#/definitions/Money"}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}}, "title": "CapLtdSettlement CapBaseDisabilityFormulaCalculationDetails", "description": "An entity for formula calculation details."}, "CapLtdSettlement_CapBaseDisabilityFormulaParameter": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaParameter"}, "paramExtension": {"type": "object", "description": "Extension of parameter. E.g currency"}, "paramName": {"type": "string", "description": "Parameter name."}, "paramValue": {"type": "number", "description": "Value of parameter."}}, "title": "CapLtdSettlement CapBaseDisabilityFormulaParameter", "description": "An entity for formula parameter."}, "CapLtdSettlement_CapBaseDisabilityPaymentAdditionDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAdditionDetails"}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type"}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentAdditionDetailsColaEntity"}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentAdditionDetails", "description": "Entity for the payment addition information."}, "CapLtdSettlement_CapBaseDisabilityPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAdditionDetailsColaEntity"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "COLA anniversary date"}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapLtdSettlement_CapBaseDisabilityPaymentAllocationBalanceAdjustment": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationBalanceAdjustment"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentAllocationBalanceAdjustment", "description": "Entity for the payment allocation information."}, "CapLtdSettlement_CapBaseDisabilityPaymentAllocationDeductionDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationDeductionDetails"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentAllocationDeductionDetails", "description": "Details of 3rd party deduction payment allocation."}, "CapLtdSettlement_CapBaseDisabilityPaymentAllocationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationDetails"}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentAllocationBalanceAdjustment"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentAllocationDeductionDetails"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationPayableItem": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentAllocationPayableItem"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentAllocationDetails", "description": "Entity for the payment allocation information."}, "CapLtdSettlement_CapBaseDisabilityPaymentAllocationPayableItem": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationPayableItem"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapLtdSettlement_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentAllocationPayableItem", "description": "Stores details for what it is paid."}, "CapLtdSettlement_CapBaseDisabilityPaymentDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentDetails"}, "payeeRoleDetails": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentPayeeRoleDetails"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentAdditionDetails"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentAllocationDetails"}}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentDetails", "description": "Payment transaction details."}, "CapLtdSettlement_CapBaseDisabilityPaymentPayeeRoleDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentPayeeRoleDetails"}, "registryId": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLtdSettlement CapBaseDisabilityPaymentPayeeRoleDetails", "description": "Payment Payee Role details."}, "CapLtdSettlement_CapBaseDisabilitySettlementResultAccumulatorExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilitySettlementResultAccumulatorExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Accumulator Unit code."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapLtdSettlement_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapLtdSettlement CapBaseDisabilitySettlementResultAccumulatorExtension", "description": "Entity for Accumulat extension details."}, "CapLtdSettlement_CapBeneficiaryDesignationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBeneficiaryDesignationEntity"}, "beneficiaryDesignationAmount": {"$ref": "#/definitions/Money"}, "beneficiaryPercentage": {"type": "number", "description": "Percentage of Benefit Coverage Designation must be within range of 0-100 and cannot exceed 2 decimal places."}}, "title": "CapLtdSettlement CapBeneficiaryDesignationEntity"}, "CapLtdSettlement_CapBeneficiaryRole": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBeneficiaryRole"}, "beneficiaryDesignation": {"$ref": "#/definitions/CapLtdSettlement_CapBeneficiaryDesignationEntity"}, "beneficiaryPaymentMethodId": {"type": "string", "description": "Beneficiary Preferred Payment method Id."}, "beneficiaryType": {"type": "string", "description": "Type of beneficiary, Primary and Contingent."}, "checkAddressId": {"type": "string", "description": "Address for beneficiary chech payment method."}, "hasWillReceiveBenefit": {"type": "boolean", "description": "If the beneficiary is willing to receive the benefit."}, "isQualifiedToReceiveBenefit": {"type": "boolean", "description": "If the beneficiary is qualified to receive the benefit."}, "isRetainedAssetApproved": {"type": "boolean", "description": "If Retained Asset Account is approved to use by beneficairy."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "The date of proof of Loss is received."}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}, "representativeRegistryId": {"type": "string", "description": "uri to guardian."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapLtdSettlement CapBeneficiaryRole", "description": "Entity for beneficiary role"}, "CapLtdSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "insuredRoleNameCd": {"type": "string", "description": "Insured Role Name"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapLtdSettlement CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapLtdSettlement_CapLTDBenefitMonthlyAmt": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDBenefitMonthlyAmt"}, "maxMonthlyBenefitPct": {"type": "number", "description": "Maximum percentage of monthly earnings covered by policy"}, "monthlyBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLtdSettlement CapLTDBenefitMonthlyAmt", "description": "Defines details of 'Monthly Amount' benefit type"}, "CapLtdSettlement_CapLTDBenefitMonthlySalaryPct": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDBenefitMonthlySalaryPct"}, "benefitPct": {"type": "number", "description": "Percentage of monthly earnings covered by policy."}, "maxMonthlyBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLtdSettlement CapLTDBenefitMonthlySalaryPct", "description": "An entity that stores details of 'Percentage' benefit type."}, "CapLtdSettlement_CapLTDCertInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDCertInfoEntity"}, "capBenefitInfo": {"type": "object", "description": "An entity for benefit information."}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDCoverageInfoEntity"}}}, "title": "CapLtdSettlement CapLTDCertInfoEntity", "description": "An entity for policy information when policy type is individual (certificate) policy."}, "CapLtdSettlement_CapLTDClaimDetailSelectedCoverageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDClaimDetailSelectedCoverageEntity"}, "coverageName": {"type": "string"}, "planName": {"type": "string"}}, "title": "CapLtdSettlement CapLTDClaimDetailSelectedCoverageEntity"}, "CapLtdSettlement_CapLTDCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDCoverageInfoEntity"}, "annualEarningsAmount": {"$ref": "#/definitions/Money"}, "approvedAmount": {"$ref": "#/definitions/Money"}, "benefitDuration": {"type": "string", "description": "Defines maximum benefit duration in months if no limitations/exclusions are applied"}, "benefitTypeCd": {"type": "string", "description": "Defines type of which formula to apply to calculate gross benefit amount"}, "catDisabilityBenefitCd": {"type": "string", "description": "Identifies if catastrophic disability applies to the claim."}, "catDisabilityBenefitPct": {"type": "number", "description": "Catastrophic Disability percentage. Defines by how much to increase the AGBA."}, "catatrophicDisabilityEp": {"type": "integer", "format": "int64", "description": "Catastrophic Disability EP"}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "coverageCd": {"type": "string"}, "currentEarningsReducedPct": {"type": "number"}, "earningsDefinitionCd": {"type": "string", "description": "Earning definition Cd defined in policy"}, "eliminationPeriodCd": {"type": "integer", "format": "int64", "description": "Elimination Period"}, "employmentStatus": {"type": "string", "description": "Employment Status in Policy"}, "employmentTerminiationDate": {"type": "string", "format": "date", "description": "Employment Terminiation Date in Policy"}, "guaranteedIssueAmount": {"$ref": "#/definitions/Money"}, "individualRecordTypeCd": {"type": "string", "description": "Employment Status in LTD Policy"}, "isAcceleratedSurvivorBenefitApplicable": {"type": "boolean", "description": "Defines if policy has Accelerated Survivor Benefit"}, "isCatastrophicBenefitApplicable": {"type": "boolean", "description": "Defines if policy has catastrophic disability benefit"}, "isEstatePayable": {"type": "boolean", "description": "Estate Payable"}, "isInfectiousAndContagiousDiseaseBenefitApplicable": {"type": "boolean", "description": "Infectious And Contagious Disease Benefit applies to the claim."}, "isProgressiveIllnessBenefitApplicable": {"type": "boolean", "description": "Defines if policy has progressive illness benefit"}, "isSelfBill": {"type": "boolean", "description": "Is Self Bill defined in LTD policy"}, "lookBackPeriodCd": {"type": "integer", "format": "int64", "description": "Pre-Existing Condition: Look Back Period"}, "maxCatDisabilityBenefitAmount": {"$ref": "#/definitions/Money"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}, "maxInfectiousDiseaseBenefitDuration": {"type": "string", "description": "Max Infectious Disease Benefit Duration Added to approval period"}, "maxPartialEarningsPct": {"type": "number"}, "minMonthlyBenefitAmount": {"$ref": "#/definitions/Money"}, "minMonthlyBenefitPct": {"type": "number", "description": "Min Monthly Benefit Pct defined in LTD policy"}, "minMonthlyBenefitTypeCd": {"type": "string", "description": "Min Monthly Benefit Type Cd defined in LTD policy"}, "minPartialEarningsPct": {"type": "number"}, "monthlyAmtDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDBenefitMonthlyAmt"}, "monthlyEarnings": {"$ref": "#/definitions/Money"}, "monthlyPctDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDBenefitMonthlySalaryPct"}, "numberOfMonths": {"type": "integer", "format": "int64", "description": "Number Of Months in Disability Provisions coverage"}, "occupationTestCd": {"type": "string", "description": "Occupation Test in Disability Provisions coverage"}, "optionERISA": {"type": "boolean", "description": "The amount of money Insured person earns per year"}, "partialDisabilityType": {"type": "string", "description": "Defines partial disability type"}, "partyTypeCd": {"type": "string", "description": "Party Type Cd defined in Policy"}, "payrollFrequencyCd": {"type": "string", "description": "Payroll Frequency in Policy"}, "planCd": {"type": "string", "description": "Describes planCd defined in LTD policy"}, "planName": {"type": "string", "description": "Describes planName defined in LTD policy"}, "rehabIncentiveBenefitCd": {"type": "string", "description": "Identifies if rehabilitation applies to the claim."}, "rehabIncentiveBenefitPct": {"type": "number", "description": "Defines rehabilitation benefit percentage."}, "riskStateCd": {"type": "string", "description": "Risk State Cd defined in LTD Policy"}, "roundingAmount": {"type": "integer", "format": "int64", "description": "Rounding Amount defined in LTD policy"}, "roundingFactorCd": {"type": "string", "description": "Rounding Factor Cd defined in LTD policy"}, "salaryChangeCd": {"type": "string", "description": "Salary Change Cd defined in LTD policy"}, "survivorBenefitAmount": {"type": "string", "description": "Survivor Ben<PERSON><PERSON> Amount"}, "survivorBenefitWaitingPeriod": {"type": "integer", "format": "int64", "description": "Survivor Benefit Waiting Period"}, "taxabilityCd": {"type": "string", "description": "For selected coverage defines if monthly benefits are taxable or tax-fr"}, "workIncentiveBenefit": {"type": "string", "description": "Defines work incentive benefit"}, "workIncentiveBenefitDuration": {"type": "integer", "format": "int64", "description": "The duration of work incentive benefit."}, "workIncentiveMaxBenefitCd": {"type": "string", "description": "Defines work incentive benefit Max value label"}, "workweekTypeCd": {"type": "integer", "format": "int64", "description": "Member work week type defined in Policy"}}, "title": "CapLtdSettlement CapLTDCoverageInfoEntity", "description": "An entity for coverage information."}, "CapLtdSettlement_CapLTDGrossBenefitAmount": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDGrossBenefitAmount"}, "buyUpCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "coreCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "maxMonthlyBenefitAmount": {"$ref": "#/definitions/Money"}, "minMonthlyBenefitAmount": {"$ref": "#/definitions/Money"}, "totalApprovedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapLtdSettlement CapLTDGrossBenefitAmount", "description": "Entity for LTD Settlement Gross Benefit Amount calculation"}, "CapLtdSettlement_CapLTDMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDCoverageInfoEntity"}}}, "title": "CapLtdSettlement CapLTDMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "CapLtdSettlement_CapLTDSettlementAbsencePeriodInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementAbsencePeriodInfoEntity"}, "absenceTypeCd": {"type": "string", "description": "This attribute describes the type of absence in time perspective."}, "actualRtwDate": {"type": "string", "format": "date-time"}, "estimatedRtwDate": {"type": "string", "format": "date-time"}, "intermittentPeriodDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementIntermittentPeriodDetailsEntity"}, "period": {"$ref": "#/definitions/CapLtdSettlement_Period"}, "reducedPeriodDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementReducedPeriodDetailsEntity"}}, "title": "CapLtdSettlement CapLTDSettlementAbsencePeriodInfoEntity", "description": "Entity that contains Absence periods information"}, "CapLtdSettlement_CapLTDSettlementAbsenceReasonInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementAbsenceReasonInfoEntity"}, "absenceReasons": {"type": "array", "items": {"type": "string", "description": "Absence reasons"}}, "pregnancies": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementPregInfoEntity"}}}, "title": "CapLtdSettlement CapLTDSettlementAbsenceReasonInfoEntity", "description": "Entity that contains Absence reason information"}, "CapLtdSettlement_CapLTDSettlementAccumulatorDetailsRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementAccumulatorDetailsRulesInput"}, "amountType": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "customerURI": {"type": "string", "description": "Primary Insured."}, "extension": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityAccumulatorExtension"}, "limitAmount": {"type": "number", "description": "Accumulator limit amount."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string", "description": "Policy of accumulator."}, "remainingAmount": {"type": "number", "description": "Accumulator remaining amount."}, "reservedAmount": {"type": "number", "description": "Accumulator reserved amount."}, "resource": {"$ref": "#/definitions/EntityLink"}, "term": {"$ref": "#/definitions/CapLtdSettlement_Term"}, "type": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}, "usedAmount": {"type": "number", "description": "Accumulator used amount."}}, "title": "CapLtdSettlement CapLTDSettlementAccumulatorDetailsRulesInput", "description": "Accumulator details for rules."}, "CapLtdSettlement_CapLTDSettlementApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementApprovalPeriodEntity"}, "adlLossDate": {"type": "string", "format": "date-time", "description": "Date when an individual loses the ability to perform one or more Activities of Daily Living"}, "approvalPeriod": {"$ref": "#/definitions/CapLtdSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string", "description": "Period approval status."}, "approverPerson": {"type": "string", "description": "User that approved the period."}, "cancelReason": {"type": "string", "description": "Additional note why the period is canceled."}, "dateOfStatusChange": {"type": "string", "format": "date-time", "description": "The date when the status was changed."}, "frequencyType": {"type": "string", "description": "Defines the frequency of payments during the approved period."}, "hasCatastrophicDisabilityBenefit": {"type": "boolean", "description": "Catastrophic Disability benefit Applied to approval period"}, "interestDetail": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementApprovalPeriodInterestDetailEntity"}, "isInfectiousAndContagiousDiseaseBenefitAdded": {"type": "boolean", "description": "Infectious and Contagious Disease benefit Added to approval period"}, "isRehabilitationBenefitApplied": {"type": "boolean", "description": "Rehabilitation Benefit Applied to approval period"}, "maxInfectiousDiseaseBenefitDuration": {"type": "string", "description": "Max Infectious Disease Benefit Duration Added to approval period"}, "partialDisability": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementApprovalPeriodPartialDisability"}, "payee": {"$ref": "#/definitions/EntityLink"}, "progressiveIllnessDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementProgressiveIllnessDetailsEntity"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}}, "title": "CapLtdSettlement CapLTDSettlementApprovalPeriodEntity", "description": "Entity for the settlement periods that are subject for approval."}, "CapLtdSettlement_CapLTDSettlementApprovalPeriodInterestDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementApprovalPeriodInterestDetailEntity"}, "interestCalculateOnAmount": {"$ref": "#/definitions/Money"}, "interestPaidUpToDate": {"type": "string", "format": "date-time", "description": "Interest apid up to date"}}, "title": "CapLtdSettlement CapLTDSettlementApprovalPeriodInterestDetailEntity", "description": "Interest rate additional information"}, "CapLtdSettlement_CapLTDSettlementApprovalPeriodPartialDisability": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementApprovalPeriodPartialDisability"}, "currentEarningsAmount": {"$ref": "#/definitions/Money"}, "isPartialDisability": {"type": "boolean", "description": "Defines if Insured was for partial disabled during the approved period."}}, "title": "CapLtdSettlement CapLTDSettlementApprovalPeriodPartialDisability", "description": "Partial disability information."}, "CapLtdSettlement_CapLTDSettlementBenefitOptionsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementBenefitOptionsEntity"}, "catDisabilityBenefitCd": {"type": "string", "description": "Identifies if catastrophic disability applies to the claim."}, "catastrophicBenefitPct": {"type": "number", "description": "Catastrophic Disability percentage. Defines by how much to increase the AGBA."}, "maxCatDisabilityBenefitAmount": {"$ref": "#/definitions/Money"}, "monthlyEarnings": {"$ref": "#/definitions/Money"}, "rehabIncentiveBenefitCd": {"type": "string"}, "rehabIncentiveBenefitPct": {"type": "number"}}, "title": "CapLtdSettlement CapLTDSettlementBenefitOptionsEntity"}, "CapLtdSettlement_CapLTDSettlementColaAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementColaAdditionEntity"}, "accumulatedPercentage": {"type": "number", "description": "Accumulated COLA percentage which should be applied to AGBA"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "Anniversary date of the COLA period"}, "colaPercentage": {"type": "number", "description": "Percentage of COLA period"}, "colaPeriod": {"$ref": "#/definitions/CapLtdSettlement_Period"}}, "title": "CapLtdSettlement CapLTDSettlementColaAdditionEntity", "description": "Entity which contains cola addition calculation details."}, "CapLtdSettlement_CapLTDSettlementColaAdjustmentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementColaAdjustmentDetailsEntity"}, "colaAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementColaAdditionEntity"}}, "colaBenefitAdjustmentPct": {"type": "string", "description": "Increase of benefits in percentage"}, "colaCPIType": {"type": "string", "description": "Defines COLA consumer price index type."}, "colaEliminationPeriodMonths": {"type": "integer", "format": "int64", "description": "Number after full months of payments when COLA becomes payable."}, "colaEliminationPeriodType": {"type": "string", "description": "Defines COLA elimination period type"}, "colaIncreaseTypeCd": {"type": "string", "description": "Cola adjustment percentage calculation increase type."}, "colaNumberOfAdjustmentsCd": {"type": "string", "description": "Number of how many years COLA adjustment pcd will increase benefit at each anniversary of payments for the duration of the claim"}, "maxColaBenefitAdjustmentPct": {"type": "number", "description": "Used to limit CPI based percentage."}}, "title": "CapLtdSettlement CapLTDSettlementColaAdjustmentDetailsEntity", "description": "Entity which contains information about cola adjustment configuration and calculation details."}, "CapLtdSettlement_CapLTDSettlementFinancialAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementFinancialAdditionEntity"}, "ancillaryActivityName": {"type": "string", "description": "Ancilarry Activity Name for the Claim."}, "financialAdditionRehabilitation": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementFinancialAdditionRehabilitationEntity"}}}, "title": "CapLtdSettlement CapLTDSettlementFinancialAdditionEntity"}, "CapLtdSettlement_CapLTDSettlementFinancialAdditionRehabilitationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementFinancialAdditionRehabilitationEntity"}, "rehabilitationTerm": {"$ref": "#/definitions/CapLtdSettlement_Term"}}, "title": "CapLtdSettlement CapLTDSettlementFinancialAdditionRehabilitationEntity", "description": "Entity that stores rehabiliation details for the claim."}, "CapLtdSettlement_CapLTDSettlementFinancialAdjustmentOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementFinancialAdjustmentOffsetEntity"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetMonthlyAmount": {"$ref": "#/definitions/Money"}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapLtdSettlement_Term"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the claim."}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapLtdSettlement CapLTDSettlementFinancialAdjustmentOffsetEntity", "description": "Business entity that describes the Offsets of the claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapLtdSettlement_CapLTDSettlementFinancialAdjustmentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementFinancialAdjustmentTaxEntity"}, "financialAdjustmentTaxFederal": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementFinancialAdjustmentTaxFederalEntity"}, "jurisdictionType": {"type": "string", "description": "Defines jurisdiction type."}, "taxType": {"type": "string", "description": "Defines the type of a tax."}, "term": {"$ref": "#/definitions/CapLtdSettlement_Term"}}, "title": "CapLtdSettlement CapLTDSettlementFinancialAdjustmentTaxEntity", "description": "This business entity describes the withholding taxes, and the amounts that will be applied to the Claim payments."}, "CapLtdSettlement_CapLTDSettlementFinancialAdjustmentTaxFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementFinancialAdjustmentTaxFederalEntity"}, "federalTaxAmount": {"$ref": "#/definitions/Money"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions. Value is inherited from Absence Case."}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation. Value is inherited from Absence Case."}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage. Value is inherited from Absence Case."}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation. Value is inherited from Absence Case."}, "federalTaxTerm": {"$ref": "#/definitions/CapLtdSettlement_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type. Value is inherited from Absence Case."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapLtdSettlement CapLTDSettlementFinancialAdjustmentTaxFederalEntity", "description": "Defines Federal tax details."}, "CapLtdSettlement_CapLTDSettlementIntermittentActualAbsenceEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementIntermittentActualAbsenceEntity"}, "absenceDate": {"type": "string", "format": "date-time", "description": "Absence date."}, "absenceSeconds": {"type": "integer", "format": "int64", "description": "Absence in seconds."}}, "title": "CapLtdSettlement CapLTDSettlementIntermittentActualAbsenceEntity", "description": "Reduced Absence period details"}, "CapLtdSettlement_CapLTDSettlementIntermittentPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementIntermittentPeriodDetailsEntity"}, "actualAbsences": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementIntermittentActualAbsenceEntity"}}}, "title": "CapLtdSettlement CapLTDSettlementIntermittentPeriodDetailsEntity", "description": "Intermittent Absence period details"}, "CapLtdSettlement_CapLTDSettlementLossInfoClaimPayeeDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementLossInfoClaimPayeeDetails"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLtdSettlement CapLTDSettlementLossInfoClaimPayeeDetails"}, "CapLtdSettlement_CapLTDSettlementLossInfoTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementLossInfoTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapLtdSettlement CapLTDSettlementLossInfoTypicalWorkWeekEntity"}, "CapLtdSettlement_CapLTDSettlementPaymentsRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementPaymentsRulesInput"}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "paymentDetails": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityPaymentDetails"}, "paymentNumber": {"type": "string", "description": "Unique payment ID."}, "paymentSubType": {"type": "string", "description": "Payment transaction subtype."}, "state": {"type": "string", "description": "Payment transaction state."}}, "title": "CapLtdSettlement CapLTDSettlementPaymentsRulesInput", "description": "Payment detail for rules."}, "CapLtdSettlement_CapLTDSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementPolicyInfoEntity"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "eliminationPeriodCd": {"type": "integer", "format": "int64", "description": "Elimination Period."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "ltdCertInfo": {"$ref": "#/definitions/CapLtdSettlement_CapLTDCertInfoEntity"}, "ltdMasterInfo": {"$ref": "#/definitions/CapLtdSettlement_CapLTDMasterInfoEntity"}, "packageCd": {"type": "string", "description": "Policy Package Cd"}, "planCd": {"type": "string", "description": "Policy plan code"}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/CapLtdSettlement_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapLtdSettlement CapLTDSettlementPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapLtdSettlement_CapLTDSettlementPregInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementPregInfoEntity"}, "cptCodes": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementCptEntity"}}, "deliveryDate": {"type": "string", "format": "date", "description": "Date of birth"}, "deliveryTypeCd": {"type": "string", "description": "Type of Birth"}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementDiagnosisInfoEntity"}}, "dueDate": {"type": "string", "format": "date-time"}, "eliminationPeriodCd": {"type": "integer", "format": "int64", "description": "Elimination Period."}, "numberOfBirths": {"type": "integer", "format": "int64"}}, "title": "CapLtdSettlement CapLTDSettlementPregInfoEntity", "description": "Entity that contains Pregnancy details"}, "CapLtdSettlement_CapLTDSettlementProgressiveIllnessDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementProgressiveIllnessDetailsEntity"}, "hasProgressiveIllnessBenefit": {"type": "boolean", "description": "Defines if allocation is protected by progressive illness."}, "progressiveCoveredMonthlyEarningsAmount": {"$ref": "#/definitions/Money"}, "progressiveIllnessCondition": {"type": "string", "description": "Defines progressive illness condition."}}, "title": "CapLtdSettlement CapLTDSettlementProgressiveIllnessDetailsEntity"}, "CapLtdSettlement_CapLTDSettlementReducedPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementReducedPeriodDetailsEntity"}, "secondsFri": {"type": "integer", "format": "int64", "description": "Seconds Friday"}, "secondsMon": {"type": "integer", "format": "int64", "description": "Seconds Monday"}, "secondsSat": {"type": "integer", "format": "int64", "description": "Seconds Saturday"}, "secondsSun": {"type": "integer", "format": "int64", "description": "Seconds Sunday"}, "secondsThu": {"type": "integer", "format": "int64", "description": "Seconds Thursday"}, "secondsTue": {"type": "integer", "format": "int64", "description": "Seconds Tuesday"}, "secondsWed": {"type": "integer", "format": "int64", "description": "Seconds Wednesday"}}, "title": "CapLtdSettlement CapLTDSettlementReducedPeriodDetailsEntity", "description": "Actual absences."}, "CapLtdSettlement_CapLTDSettlementResultAccumulatorDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementResultAccumulatorDetailEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilitySettlementResultAccumulatorExtension"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapLtdSettlement CapLTDSettlementResultAccumulatorDetailEntity", "description": "Accumulator details entity."}, "CapLtdSettlement_CapLTDSettlementResultApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementResultApprovalPeriodEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementResultAccumulatorDetailEntity"}}, "adlLossDate": {"type": "string", "format": "date-time", "description": "Date when an individual loses the ability to perform one or more Activities of Daily Living"}, "approvalPeriod": {"$ref": "#/definitions/CapLtdSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string", "description": "Period approval status."}, "approverPerson": {"type": "string", "description": "User that approved the period."}, "benefitDuration": {"type": "number", "description": "Benefit duration for specific approval period."}, "cancelReason": {"type": "string", "description": "Additional note why the period is canceled."}, "catastrophicDisabilityGbaAmount": {"$ref": "#/definitions/Money"}, "dateOfStatusChange": {"type": "string", "format": "date-time", "description": "The date when the status was changed."}, "frequencyType": {"type": "string", "description": "Defines the frequency of payments during the approved period."}, "hasCatastrophicDisabilityBenefit": {"type": "boolean", "description": "Catastrophic Disability benefit Applied to approval period"}, "interestDetail": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementApprovalPeriodInterestDetailEntity"}, "isInfectiousAndContagiousDiseaseBenefitAdded": {"type": "boolean", "description": "Infectious and Contagious Disease benefit Added to approval period"}, "isRehabilitationBenefitApplied": {"type": "boolean", "description": "Rehabilitation Benefit Applied to approval period"}, "partialDisability": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementApprovalPeriodPartialDisability"}, "payee": {"$ref": "#/definitions/EntityLink"}, "progressiveIllnessDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementProgressiveIllnessDetailsEntity"}, "progressiveMonthlyGbaAmount": {"$ref": "#/definitions/Money"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}}, "title": "CapLtdSettlement CapLTDSettlementResultApprovalPeriodEntity", "description": "Entity for the settlement periods that are subject for approval."}, "CapLtdSettlement_CapLTDSettlementRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLTDSettlementRulesInput"}, "absence": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementAbsenceInfoEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementAccumulatorDetailsRulesInput"}}, "benefitConfiguration": {"$ref": "#/definitions/CapLtdSettlement_BaseAbsenceSettlementBenefitConfiguration"}, "currentDateTime": {"type": "string", "format": "date-time", "description": "Current system date and time."}, "details": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementDetailEntity"}, "isBenefitSettlement": {"type": "boolean"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementLossInfoEntity"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementPaymentsRulesInput"}}, "policy": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLtdRelatedSettlementInfoEntity"}}, "settlement": {"$ref": "#/definitions/EntityLink"}, "settlementType": {"type": "string", "description": "Defines settlement type"}}, "title": "CapLtdSettlement CapLTDSettlementRulesInput"}, "CapLtdSettlement_CapLtdCoverageOverrideEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdCoverageOverrideEntity"}, "approvedAmountOverride": {"$ref": "#/definitions/Money"}, "approvedAmountOverrideReason": {"type": "string"}, "benefitOverridePctReason": {"type": "string"}, "benefitOverridePercent": {"type": "number"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string"}, "maxMonthlyBenefitOverrideAmount": {"$ref": "#/definitions/Money"}, "maxMonthlyBenefitOverrideAmountReason": {"type": "string"}}, "title": "CapLtdSettlement CapLtdCoverageOverrideEntity"}, "CapLtdSettlement_CapLtdRelatedSettlementInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdRelatedSettlementInfoEntity"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "eligibilityEvaluationCd": {"type": "string"}, "eliminationPeriodThroughDate": {"type": "string", "format": "date"}, "grossBenefitAmount": {"$ref": "#/definitions/Money"}, "isBenefitSettlement": {"type": "boolean"}}, "title": "CapLtdSettlement CapLtdRelatedSettlementInfoEntity", "description": "An entity for related settlement info."}, "CapLtdSettlement_CapLtdSettlementAbsenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementAbsenceInfoEntity"}, "absencePeriods": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementAbsencePeriodInfoEntity"}}, "absenceReason": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementAbsenceReasonInfoEntity"}, "finalPtoDate": {"type": "string", "format": "date-time"}, "financialAdjustmentTax": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementFinancialAdjustmentTaxEntity"}}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes. Inherited from LTD Claim."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax. Inherited from LTD Claim."}, "isOtherIncome": {"type": "boolean"}, "jobClassificationCd": {"type": "string"}, "lastWorkDates": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementAbsenceInfoLastWorkDateEntity"}}, "returnToWorkDate": {"type": "string", "format": "date-time", "description": "Return to Work date"}, "typicalWorkWeek": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementLossInfoTypicalWorkWeekEntity"}, "wasDaysUsedAfterLdw": {"type": "boolean"}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}}, "title": "CapLtdSettlement CapLtdSettlementAbsenceInfoEntity", "description": "The object that includes information taken from Absence case."}, "CapLtdSettlement_CapLtdSettlementAbsenceInfoLastWorkDateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementAbsenceInfoLastWorkDateEntity"}, "lastWorkDate": {"type": "string", "format": "date-time"}}, "title": "CapLtdSettlement CapLtdSettlementAbsenceInfoLastWorkDateEntity"}, "CapLtdSettlement_CapLtdSettlementCptEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementCptEntity"}, "cptCode": {"type": "string"}}, "title": "CapLtdSettlement CapLtdSettlementCptEntity"}, "CapLtdSettlement_CapLtdSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapLtdSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLtdSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "actualCost": {"$ref": "#/definitions/Money"}, "additionalBenefitNotes": {"type": "string", "description": "The attribute represents additional benefit notes"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementApprovalPeriodEntity"}}, "approvedAmountOverride": {"$ref": "#/definitions/Money"}, "approvedAmountOverrideReason": {"type": "string"}, "beneficiaryRole": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapBeneficiaryRole"}}, "benefitCd": {"type": "string"}, "benefitOverridePctReason": {"type": "string"}, "benefitOverridePercent": {"type": "number"}, "birthDate": {"type": "string", "format": "date", "description": "The attribute represents members birthday"}, "buyupCoverageOverrides": {"$ref": "#/definitions/CapLtdSettlement_CapLtdCoverageOverrideEntity"}, "claimBenefitLabel": {"type": "string"}, "coreCoverageOverrides": {"$ref": "#/definitions/CapLtdSettlement_CapLtdCoverageOverrideEntity"}, "coverageCd": {"type": "string"}, "dateOfDeath": {"type": "string", "format": "date", "description": "The attribute represents members date of Death"}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "The attribute represents members date of Diagnosis"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string"}, "eliminationPeriodOverrideEndDate": {"type": "string", "format": "date-time", "description": "The attribute that represents the overridden end date of elimination period."}, "eliminationPeriodOverrideReason": {"type": "string", "description": "The attribute that represents the reason of elimination period end date override."}, "lastWorkDateOverride": {"type": "string", "format": "date-time"}, "maxBenefitOverridePeriod": {"$ref": "#/definitions/CapLtdSettlement_Period"}, "maxBenefitOverrideReason": {"type": "string"}, "maxMonthlyBenefitOverrideAmount": {"$ref": "#/definitions/Money"}, "maxMonthlyBenefitOverrideAmountReason": {"type": "string"}, "monthlyEarningsOverrideAmount": {"$ref": "#/definitions/Money"}, "monthlyEarningsOverrideReason": {"type": "string"}, "overridenGrossAmount": {"$ref": "#/definitions/Money"}, "paidToDate": {"type": "string", "format": "date", "description": "Paid to date"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of Loss received date, override this date on UI"}, "proratingRateOverride": {"type": "string", "description": "The attribute represents the overridden Prorating rate value for LTD Claim."}, "survivorBenefitOverrides": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSurvivorBenefitOverrideEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The taxable percentage override defined by user manually that will be taxable."}}, "title": "CapLtdSettlement CapLtdSettlementDetailEntity", "description": "This Business entity houses the detail of the Ltd Settlement."}, "CapLtdSettlement_CapLtdSettlementDiagnosisInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementDiagnosisInfoEntity"}, "icdCode": {"type": "string"}, "primaryCode": {"type": "boolean"}}, "title": "CapLtdSettlement CapLtdSettlementDiagnosisInfoEntity"}, "CapLtdSettlement_CapLtdSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapLtdSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapLtdSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapLtdSettlement_AccessTrackInfo"}, "assessmentResults": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementResultEntity"}}, "benefitConfiguration": {"$ref": "#/definitions/CapLtdSettlement_BaseAbsenceSettlementBenefitConfiguration"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "maxBenefitAgeMonths": {"type": "integer", "format": "int64", "description": "The maxBenefitAge"}, "maxBenefitDurationMonths": {"type": "integer", "format": "int64", "description": "The maxBenefitAge"}, "maxBenefitType": {"type": "string", "description": "The maxBenefitType"}, "policy": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "remainingBenefitDurationDays": {"type": "integer", "format": "int64", "description": "The remaining amount of days for the benefit."}, "remainingBenefitDurationMonths": {"type": "integer", "format": "int64", "description": "The remaining amount of months for the benefit."}, "settlementAbsenceInfo": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementAbsenceInfoEntity"}, "settlementDetail": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementLossInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}, "usedBenefitDurationDays": {"type": "integer", "format": "int64", "description": "The amount of days used by the benefit."}, "usedBenefitDurationMonths": {"type": "integer", "format": "int64", "description": "The amount of months used by the benefit."}}, "title": "CapLtdSettlement CapLtdSettlementEntity", "description": "The object that encompasses attributes set for LTD Settlement."}, "CapLtdSettlement_CapLtdSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntity"}}, "title": "CapLtdSettlement_CapLtdSettlementEntitySuccess"}, "CapLtdSettlement_CapLtdSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapLtdSettlement_CapLtdSettlementEntitySuccessBody"}, "CapLtdSettlement_CapLtdSettlementLossInfoEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementLossInfoEarningsEntity"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "baseSalaryAmount": {"$ref": "#/definitions/Money"}, "salaryMode": {"type": "string"}}, "title": "CapLtdSettlement CapLtdSettlementLossInfoEarningsEntity"}, "CapLtdSettlement_CapLtdSettlementLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementLossInfoEntity"}, "absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations. Value is copied from LTD Claim domain."}, "claimPayeeDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementLossInfoClaimPayeeDetails"}, "coverageType": {"type": "string", "description": "Type of coverage"}, "disabilityReasonCd": {"type": "string", "description": "Disability reason. Value is copied from LTD Claim domain."}, "earnings": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementLossInfoEarningsEntity"}, "eligibilityVerifiedCd": {"type": "string"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "financialAddition": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementFinancialAdditionEntity"}}, "financialAdjustmentOffsets": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementFinancialAdjustmentOffsetEntity"}}, "interruptionDaysNumber": {"type": "integer", "format": "int64"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time. Value is copied from LTD Claim domain."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date and time when the incident happened. Value is copied from LTD Claim domain."}, "lossNumber": {"type": "string"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Type of loss."}, "memberRegistryTypeId": {"type": "string"}, "reportedDate": {"type": "string", "format": "date-time"}, "selectedCoverage": {"$ref": "#/definitions/CapLtdSettlement_CapLTDClaimDetailSelectedCoverageEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable. Inherited from Claim."}}, "title": "CapLtdSettlement CapLtdSettlementLossInfoEntity", "description": "Business entity that houses the information from LTD claim to use in settlement."}, "CapLtdSettlement_CapLtdSettlementResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSettlementResultEntity"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementResultApprovalPeriodEntity"}}, "approvedAmount": {"$ref": "#/definitions/Money"}, "autoAdjudicatedDuration": {"type": "integer", "format": "int64", "description": "Indicates the duration of Auto Adjudication Case Duration Guideline value in days."}, "beneficiaryRole": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapBeneficiaryRole"}}, "benefitAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementResultAccumulatorDetailEntity"}}, "benefitBeginDate": {"type": "string", "format": "date-time"}, "benefitCd": {"type": "string"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "calculatedLastWorkDate": {"type": "string", "format": "date-time"}, "colaAdjustmentDetails": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementColaAdjustmentDetailsEntity"}, "coverageCd": {"type": "string", "description": "Coverage code"}, "coverageMonthlyLimitAmount": {"$ref": "#/definitions/Money"}, "coverageName": {"type": "string"}, "coveredEarningsAmount": {"$ref": "#/definitions/Money"}, "currentEarningsReducedPct": {"type": "number"}, "dateOfDeath": {"type": "string", "format": "date", "description": "The attribute represents members date of Death"}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "The attribute represents members date of Diagnosis"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "eliminationPeriodDuration": {"type": "integer", "format": "int64"}, "eliminationPeriodThroughDate": {"type": "string", "format": "date-time", "description": "This attribute defines end date of elimination period. Elimination period is time (starting from date of loss) an Insured must be disabled before qualifying for LTD benefits. No benefits are payable during the elimination period."}, "formulaCalculationDetails": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_CapBaseDisabilityFormulaCalculationDetails"}}, "grossBenefitAmount": {"$ref": "#/definitions/CapLtdSettlement_CapLTDGrossBenefitAmount"}, "individualRecordTypeCd": {"type": "string"}, "infectiousAndContagiousDiseaseBenefitEndDate": {"type": "string", "format": "date-time"}, "isAcceleratedSurvivorBenefitApplicable": {"type": "boolean", "description": "Defines if policy has Accelerated Survivor Benefit"}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if this Claim is the subject to Auto Adjudication."}, "isEligibilityEvaluationCdOverriden": {"type": "boolean", "description": "This attribute describes if Eligibility evaluation Cd is overriden"}, "isMaxBenefitPeriodOverriden": {"type": "boolean"}, "isSelfBill": {"type": "boolean"}, "maxBenefitDuration": {"type": "string"}, "maxBenefitEndDate": {"type": "string", "format": "date-time"}, "maxBenefitStartDate": {"type": "string", "format": "date-time"}, "maxPartialEarningsPct": {"type": "number"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapLtdSettlement_MessageType"}}, "minPartialEarningsPct": {"type": "number"}, "monthlyBenefitAmount": {"$ref": "#/definitions/Money"}, "options": {"$ref": "#/definitions/CapLtdSettlement_CapLTDSettlementBenefitOptionsEntity"}, "partialDisabilityCd": {"type": "string"}, "planCd": {"type": "string"}, "planName": {"type": "string"}, "proratingRate": {"type": "string", "description": "Prorating rate, used to prorate AGBA, deductions and taxes. The values of the prorating rate can be 1/5 or 1/7."}, "reserve": {"type": "number"}, "roundingAmount": {"type": "integer", "format": "int64"}, "roundingFactorCd": {"type": "string"}, "taxablePercentage": {"type": "number", "description": "The calculated percentage of gross benefit amount that will be taxable."}, "totalBenefitDuration": {"type": "number", "description": "The total benefit duration for claim."}, "workIncentiveBenefitCd": {"type": "string"}, "workIncentiveBenefitDuration": {"type": "integer", "format": "int64"}, "workIncentiveMaxBenefitCd": {"type": "string", "description": "Defines work incentive benefit Max value label"}, "workIncentiveRemainingDays": {"type": "integer", "format": "int64"}, "workIncentiveUsedDays": {"type": "integer", "format": "int64"}}, "title": "CapLtdSettlement CapLtdSettlementResultEntity", "description": "Business entity defines LTD settlement result."}, "CapLtdSettlement_CapLtdSurvivorBenefitOverrideEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLtdSurvivorBenefitOverrideEntity"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "isEligibilityOverridden": {"type": "boolean", "description": "Indicates if Eligibility is overridden"}, "isMonthlyBenefitAmountOverridden": {"type": "boolean", "description": "Indicates if Monthly Benefit Amount is overridden"}, "isSurvivorBenefitAmountOverridden": {"type": "boolean", "description": "Indicates if Survivor <PERSON><PERSON><PERSON> Amount is overridden"}, "monthlyBenefitAmountOverride": {"$ref": "#/definitions/Money"}, "monthlyBenefitAmountOverrideReason": {"type": "string"}, "survivorBenefitAmountOverride": {"$ref": "#/definitions/Money"}, "survivorBenefitAmountOverrideReason": {"type": "string"}}, "title": "CapLtdSettlement CapLtdSurvivorBenefitOverrideEntity"}, "CapLtdSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapLtdSettlement MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapLtdSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapLtdSettlement Period"}, "CapLtdSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapLtdSettlement Term"}, "CapRequestAdditionalBenefitSettlementCreationInput": {"required": ["entity"], "properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementDetailEntity"}, "policyId": {"type": "string"}}, "title": "CapRequestAdditionalBenefitSettlementCreationInput"}, "CapRequestAdditionalBenefitSettlementCreationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRequestAdditionalBenefitSettlementCreationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRequestAdditionalBenefitSettlementCreationInputBody"}, "CapRequestLtdSettlementAdjudicationInput": {"required": ["entity"], "properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementDetailEntity"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapRequestLtdSettlementAdjudicationInput"}, "CapRequestLtdSettlementAdjudicationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRequestLtdSettlementAdjudicationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRequestLtdSettlementAdjudicationInputBody"}, "CapSettlementReadjudicateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapLtdSettlement_CapLtdSettlementDetailEntity"}}, "title": "CapSettlementReadjudicateInput"}, "CapSettlementReadjudicateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementReadjudicateInputBody"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputs"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputs"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "InitLtdSettlementBenefitToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitLtdSettlementBenefitToAccumulatorTxOutputs"}, "InitLtdSettlementBenefitToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitLtdSettlementBenefitToAccumulatorTxOutputs"}}, "title": "InitLtdSettlementBenefitToAccumulatorTxOutputsSuccess"}, "InitLtdSettlementBenefitToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitLtdSettlementBenefitToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitLtdSettlementBenefitToAccumulatorTxOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "ResolveClaimAdditionalSettlementsBySettlementOutputs": {"properties": {"out": {"type": "object"}}, "title": "ResolveClaimAdditionalSettlementsBySettlementOutputs"}, "ResolveClaimAdditionalSettlementsBySettlementOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResolveClaimAdditionalSettlementsBySettlementOutputs"}}, "title": "ResolveClaimAdditionalSettlementsBySettlementOutputsSuccess"}, "ResolveClaimAdditionalSettlementsBySettlementOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResolveClaimAdditionalSettlementsBySettlementOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResolveClaimAdditionalSettlementsBySettlementOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "SettlementToAccumulatorTxOutputs"}, "SettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/SettlementToAccumulatorTxOutputs"}}, "title": "SettlementToAccumulatorTxOutputsSuccess"}, "SettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/SettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SettlementToAccumulatorTxOutputsSuccessBody"}}}