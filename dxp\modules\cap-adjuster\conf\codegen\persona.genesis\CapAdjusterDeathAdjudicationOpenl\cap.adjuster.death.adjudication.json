{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap.death.adjudication": {"schema": "integration.cap.death.adjudication.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Openl for DEATH", "version": "1", "title": "CAP Adjuster: Openl for DEATH"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/openl-life", "description": "CAP Adjuster: Openl for DEATH"}], "paths": {"/openl-life/face-value-tl-calculation": {"post": {"x-dxp-path": "/_api_term_life_face_value_calculation", "tags": ["/cap-adjuster/v1/openl-life"]}}}}