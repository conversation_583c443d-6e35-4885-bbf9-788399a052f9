/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.relationships.dto;

import dataproviders.common.dto.GenesisSearchRequestDTO;

public class GenesisTripletSearchRequestDTO extends GenesisSearchRequestDTO {

    public GenesisTripletDTO triplet;

    public GenesisTripletSearchRequestDTO(GenesisTripletDTO triplet) {
        this.triplet = triplet;
    }
}
