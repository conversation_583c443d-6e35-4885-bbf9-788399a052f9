import React from 'react'
import {observer} from 'mobx-react'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {t} from '@eisgroup/i18n'
import {Subscription} from '@eisgroup/dispatch'
import {CaseSystemService, ClaimParty, CSClaimWrapperService} from '@eisgroup/cap-services'
import {IObservableArray} from 'mobx'
import {BalanceStore} from '../../../common/store/BalanceStore'
import {
    FormDrawer,
    DrawerWidth,
    CaseSystemPaymentStore,
    BALANCE_ACTIONS_MAP,
    ClaimsForReducePayment,
    StoreTypeForBalanceProps,
    ICaseSystem
} from '../../..'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapFinancialAdjustmentWithholdingEntity = CapEventCase.CapFinancialAdjustmentWithholdingEntity
import {WaiveOverpaymentActionForm} from './components/WaiveOverpaymentActionForm'
import {PayUnderPaymentActionForm} from './components/PayUnderPaymentActionForm'
import {AddExternalOverpaymentActionForm} from './components/AddExternalOverpaymentActionForm'
import {CancelExternalOverpaymentActionForm} from './components/CancelExternalOverpaymentActionForm'
import {CancelWaiveOverpaymentActionForm} from './components/CancelWaiveOverpaymentActionForm'
import {ReducePaymentActionForm} from './components/ReducePaymentActionForm'

export interface BalanceActionDrawerProps<
    CS extends ICaseSystem,
    CSService extends CaseSystemService | CSClaimWrapperService<CS>
> {
    actionKey: string
    eventCase: CapEventCaseEntity
    payeeLink: string
    reducePaymentMode: string
    payeeDisplay: string
    store: StoreTypeForBalanceProps<CS, CSService>
    claims: ClaimsForReducePayment[]
    parties: IObservableArray<ClaimParty>
    beneficiaries: IObservableArray<ClaimParty>
    closeDrawer: () => void
    balanceStore: BalanceStore
    currentWithholding: CapFinancialAdjustmentWithholdingEntity
    paymentsStore: CaseSystemPaymentStore<any, any>
    getBalanceChangeLog: (nextCount?: number) => void
    onUpdateDeleteWithHolding: (eventCase: CapEventCaseEntity) => Subscription
    getLoadPayments: () => void
}

export const BalanceActionDrawer: React.FC<
    BalanceActionDrawerProps<ICaseSystem, CaseSystemService | CSClaimWrapperService<ICaseSystem>>
> = observer(props => {
    const getTotalBalanceAmount = () => props.balanceStore.balance?.totalBalanceAmount?.amount ?? 0

    const getTotalBalance = () => Math.abs(props.balanceStore.balance?.totalBalanceAmount?.amount ?? 0)

    const getPayUnderpaymentAmount = (key: string) => {
        const amount = getTotalBalanceAmount()
        if (BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.PAY_UNDERPAYMENT && amount >= 0) {
            return props.balanceStore.balance?.totalBalanceAmount
        }
        return undefined
    }

    const formToRender = (key: string): React.ReactNode => {
        const totalBalance = getTotalBalance()
        const amount = getPayUnderpaymentAmount(key)
        switch (BALANCE_ACTIONS_MAP[key]) {
            case BALANCE_ACTIONS_MAP.WAIVE_OVERPAYMENT:
                return (
                    <WaiveOverpaymentActionForm {...props} totalBalance={totalBalance} payUnderpaymentAmount={amount} />
                )
            case BALANCE_ACTIONS_MAP.PAY_UNDERPAYMENT:
                return (
                    <PayUnderPaymentActionForm
                        {...props}
                        totalBalance={totalBalance}
                        getPayUnderpaymentAmount={getPayUnderpaymentAmount}
                    />
                )
            case BALANCE_ACTIONS_MAP.ADD_EXTERNAL_OVERPAYMENT:
                return <AddExternalOverpaymentActionForm {...props} />
            case BALANCE_ACTIONS_MAP.CANCEL_EXTERNAL_OVERPAYMENT:
                return <CancelExternalOverpaymentActionForm {...props} />
            case BALANCE_ACTIONS_MAP.CANCEL_WAIVE_OVERPAYMENT:
                return <CancelWaiveOverpaymentActionForm {...props} />
            case BALANCE_ACTIONS_MAP.REDUCE_PAYMENT:
                return <ReducePaymentActionForm {...props} />
            default:
                return null
        }
    }

    return (
        <FormDrawer
            formTitle={t(`cap-core:balance_actions_${props.actionKey?.toLowerCase()}`)}
            onFormCancel={props.closeDrawer}
            drawerWidth={DrawerWidth.SMALL}
            formToRender={formToRender(props.actionKey)}
        />
    )
})
