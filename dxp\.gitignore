# Use global syntax for excludes
syntax: glob

# Exclude IntelliJ files
.idea
.bsp
.vagrant
.node
*.iml
*.iws
*.ipr

# Exclude Eclipse files
.settings
.metadata
.workspace
*.project
*.classpath
*.springBeans
*.springWebflow
maven-eclipse.xml

# Common tools files
*.pmd
*.checkstyle
target
**/bin/**
test-output
.toDelete

# Logs
logs
logsPath_IS_UNDEFINED
*.log
velocity.log*

# Runtime data
pids
*.pid
*.seed

# Coverage tools
lib-cov
coverage

# Dependency directory
node_modules

# Generated code
gen
dist
transpiled
build
build-ts
generated
plugin_gen.xml
plugin.xml_gen
*.ucls
dependency-reduced-pom.xml

# Generated css
*/public/styles/*.css

**/src/automatic/**/*.ts

# Generated package.json
/package.json
/package-lock.json

*.tgz

# Vertx
.vertx

# Mac OSX
.DS_Store

# Eclipse Trace
*._trace

# Eclipse XText & Xtend
*/xtend-gen/*
*/xtext-gen/*
*/src-gen/*

# DCEVM related
extraClasspath.properties
hotswap-agent.properties

# Gradle
*/.gradle/*

# Gateway selection
*/gateway/Gateway.js

# ECTS internals
etcs-automation/etcs-automation-tests/external_run_id.txt
etcs-automation/dxp-automation-tests/external_run_id.txt
etcs-automation/smoke_results.txt

# Deployment scripts
docker/dev/connect.bat

# Version backups
*.versionBackups

# VSCode folders
.vscode
*.hprof
prototypes/conversion/conversion-mapping-services/MODELS.json
*.xlsx#
.npmrc