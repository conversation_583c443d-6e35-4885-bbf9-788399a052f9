/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.converters;

import cap.adjuster.services.common.dto.GenesisApiModelRootKey;
import core.services.converters.CommonDTOConverter;
import dataproviders.common.dto.GenesisRootEntityKeyDTO;

public class GenesisApiModelRootKeyConverter<I extends GenesisRootEntityKeyDTO, A extends GenesisApiModelRootKey>
        extends CommonDTOConverter<I, A> {

    @Override
    public I convertToInternalDTO(A apiDTO, I intDTO) {
        intDTO.rootId = apiDTO.rootId;
        intDTO.revisionNo = apiDTO.revisionNo;
        return intDTO;
    }

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO.rootId = intDTO.rootId;
        apiDTO.revisionNo = intDTO.revisionNo;
        return apiDTO;
    }
}
