/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.specialHandling;


import cap.adjuster.services.specialHandling.dto.CapGenericSpecialHandling;
import cap.adjuster.services.specialHandling.impl.CapAdjusterSpecialHandlingServiceImpl;
import com.google.inject.ImplementedBy;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * Service that provides methods for CAP Special Handling
 */
@ImplementedBy(CapAdjusterSpecialHandlingServiceImpl.class)
public interface CapAdjusterSpecialHandlingService {

    /**
     * Get special handling related to claim
     *
     * @param rootId     claim identifier
     * @param revisionNo claim revision number
     * @param modelName  model name
     * @return list of special handling related to claim
     */
    CompletionStage<List<CapGenericSpecialHandling>> getSpecialHandling(String rootId, Integer revisionNo, String modelName);

    /**
     * Get all special handling associated with event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     * @return list of special handling related to event case
     */
    CompletionStage<List<CapGenericSpecialHandling>> getAllSpecialHandlingInCase(String rootId, Integer revisionNo);

}
