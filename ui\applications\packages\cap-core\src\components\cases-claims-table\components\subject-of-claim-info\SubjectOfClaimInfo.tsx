/**
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'

export const SubjectOfClaimInfo = ({customerInfo}) => {
    if (!customerInfo) {
        return null
    }

    return (
        <div>
            <div>{customerInfo.customerName}</div>
            {customerInfo.address && <div>{customerInfo.address}</div>}
            {customerInfo.postalCode && <div>{customerInfo.postalCode}</div>}
        </div>
    )
}
