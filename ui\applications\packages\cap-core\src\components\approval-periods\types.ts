import {
    CapLeaveSettlement,
    CapLtdSettlement,
    CapSmpSettlement,
    CapStdSettlement,
    BusinessTypes
} from '@eisgroup/cap-disability-models'
import {ClaimDisabilityLossSettlement, ClaimParty, dateUtils} from '@eisgroup/cap-services'
import {IObservableArray} from 'mobx'
import {BaseRootStore, EventCaseStore, PartyInformationStore} from '../../common/store'
import {KrakenEvaluationContextProps} from '../../kraken'
import {periodsOverlap} from '../../utils'
import {DrawerFormStateType, FormDrawerStore} from '../form-drawer'
import CapLeaveApprovalPeriodEntity = CapLeaveSettlement.CapLeaveApprovalPeriodEntity
import CapLTDSettlementAbsencePeriodInfoEntity = CapLtdSettlement.CapLTDSettlementAbsencePeriodInfoEntity
import CapLTDSettlementApprovalPeriodEntity = CapLtdSettlement.CapLTDSettlementApprovalPeriodEntity
import CapSMPSettlementAbsencePeriodInfoEntity = CapSmpSettlement.CapSMPSettlementAbsencePeriodInfoEntity
import CapSMPSettlementApprovalPeriodEntity = CapSmpSettlement.CapSMPSettlementApprovalPeriodEntity
import CapSTDSettlementAbsencePeriodInfoEntity = CapStdSettlement.CapSTDSettlementAbsencePeriodInfoEntity
import CapSTDSettlementApprovalPeriodEntity = CapStdSettlement.CapSTDSettlementApprovalPeriodEntity
import CapDisabilityClaim = BusinessTypes.CapDisabilityClaim
import Period = BusinessTypes.Period

export type ClaimDisabilityApprovalPeriodEntity =
    | CapSTDSettlementApprovalPeriodEntity
    | CapLTDSettlementApprovalPeriodEntity
    | CapSMPSettlementApprovalPeriodEntity
    | CapLeaveApprovalPeriodEntity
export type ClaimDisabilityAbsencePeriodInfoEntity =
    | CapSTDSettlementAbsencePeriodInfoEntity
    | CapLTDSettlementAbsencePeriodInfoEntity
    | CapSMPSettlementAbsencePeriodInfoEntity

export interface DisabilityClaimViewStore extends BaseRootStore {
    partyInformationStore: PartyInformationStore
    formDrawerStore: FormDrawerStore
    allClaimsBeneficiaries: IObservableArray<ClaimParty>
    eventCaseStore: EventCaseStore
    reAdjudicateSettlement: (settlement) => void
    settlement?: ClaimDisabilityLossSettlement
    getApprovalPeriodFactory: () => ClaimDisabilityApprovalPeriodEntity
    loss?: CapDisabilityClaim
}

export interface ApprovalPeriodsProps {
    readonly viewStore: DisabilityClaimViewStore
    readonly krakenRulesEvaluationContext: KrakenEvaluationContextProps
    readonly isHiddenBtn?: boolean
}

export interface ApprovalPeriodsDrawerProps {
    readonly viewStore: DisabilityClaimViewStore
    readonly drawerFormStateType: DrawerFormStateType
    readonly approvalPeriodIndex: number
    readonly krakenRulesEvaluationContext: KrakenEvaluationContextProps
}

export const getAbsencePeriodRange = (absencePeriods: ClaimDisabilityAbsencePeriodInfoEntity[]) =>
    absencePeriods.length
        ? `${
              dateUtils(
                  Math.min(...absencePeriods.map(v => new Date(dateUtils(v.period?.startDate).toDate || '').getTime()))
              ).render
          } - ${
              dateUtils(
                  Math.max(...absencePeriods.map(v => new Date(dateUtils(v.period?.endDate).toDate || '').getTime()))
              ).render
          }`
        : ''

export function approvalPeriodsOverlap(periods: Period[]): boolean {
    const sortedPeriods = [...periods].sort(
        (a, b) => dateUtils(a.startDate).getTime() - dateUtils(b.startDate).getTime()
    )
    for (let i = 0; i < sortedPeriods.length - 1; i++) {
        if (periodsOverlap(sortedPeriods[i], sortedPeriods[i + 1])) {
            return true
        }
    }
    return false
}
