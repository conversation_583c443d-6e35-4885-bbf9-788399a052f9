/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'
import classNames from 'classnames'
import {Collapse, ColumnProps, Table} from '@eisgroup/ui-kit'
import {t} from '@eisgroup/i18n'
import {Subscription} from '@eisgroup/dispatch'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {
    BALANCE_TABLE_RECALCULATION_PAYMENTS,
    BALANCE_TABLE_RECALCULATION_PAYMENTS_TOP,
    FINANCIAL_INFORMATION_BALANCE_SUB_TABLE,
    BALANCE_TABLE_RECALCULATION_PAYMENTS_WRAPPER,
    ListActions,
    MoneyFormat,
    ClaimsForReducePayment
} from '../..'
import CapFinancialAdjustmentWithholdingEntity = CapEventCase.CapFinancialAdjustmentWithholdingEntity
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity

const {Panel} = Collapse

export interface PaymentWithholdingTableProps {
    loading?: boolean
    claims: ClaimsForReducePayment[]
    eventCase: CapEventCaseEntity
    payeeLink: string
    hasReducePaymentPrivilege: boolean
    editWithholding: (withholding: CapFinancialAdjustmentWithholdingEntity) => void
    onUpdateDeleteWithHolding: (eventCase: CapEventCaseEntity) => Subscription
}

export const PaymentWithholdingTable: React.FC<PaymentWithholdingTableProps> = observer(props => {
    const {
        loading,
        claims,
        eventCase,
        payeeLink,
        hasReducePaymentPrivilege,
        editWithholding,
        onUpdateDeleteWithHolding
    } = props
    const withholdings = eventCase.lossDetail?.financialAdjustment?.withholdings || []
    const isWithholdingAssociatedWithClaim = (withholding: CapFinancialAdjustmentWithholdingEntity): boolean => {
        return claims.some(
            claim => claim?.rootId && withholding.lossSources.some(source => source._uri.includes(claim.rootId))
        )
    }
    const displayWithholdings =
        eventCase.lossDetail?.financialAdjustment?.withholdings.filter(
            v => v.withholdingPayee?._uri === payeeLink && isWithholdingAssociatedWithClaim(v)
        ) || []
    const getColumns = (): ColumnProps<CapFinancialAdjustmentWithholdingEntity>[] => {
        return [
            {
                width: '30%',
                key: 'remainingBalanceType',
                title: t('cap-core:remaining_balance_type'),
                render: (text: string, record: CapFinancialAdjustmentWithholdingEntity) => {
                    return record.withholdingPct ? t('cap-core:percentage') : t('cap-core:amount')
                }
            },
            {
                key: 'withholdingAmount',
                width: '20%',
                title: t('cap-core:withholding_amount'),
                align: 'right',
                render: (text: string, record: CapFinancialAdjustmentWithholdingEntity) => {
                    return record.withholdingPct ? (
                        `${record.withholdingPct}%`
                    ) : (
                        <MoneyFormat
                            value={
                                record?.withholdingWeeklyAmount?.amount || record?.withholdingMonthlyAmount?.amount || 0
                            }
                        />
                    )
                }
            },
            {
                key: 'claims',
                title: t('cap-core:balance_actions_drawer_claim_label'),
                align: 'right',
                render: (text: string, record: CapFinancialAdjustmentWithholdingEntity) => {
                    const claimNumbers = claims
                        .filter(
                            claim =>
                                record.lossSources.findIndex(s => claim?.rootId && s._uri.includes(claim?.rootId)) > -1
                        )
                        .map(n => n.lossNumber)
                    return claimNumbers.join(', ')
                }
            },
            {
                width: '1%',
                dataIndex: 'listActions',
                render: (text: string, record: any, index: number) => (
                    <ListActions
                        hideRemoveAction={!hasReducePaymentPrivilege}
                        hideEditAction={!hasReducePaymentPrivilege}
                        handleEdit={() => editWithholding(record)}
                        handleRemove={() => onRemoveClick(record)}
                        removePopMessage={t('cap-core:remove_payment_withholding_popconfirm_msg')}
                    />
                )
            }
        ]
    }

    const onRemoveClick = (record: any) => {
        const filteredWithholdings = withholdings.filter(
            v => v._key?.id !== record._key?.id || v.withholdingPayee !== record.withholdingPayee
        )

        const eventCaseParam = {
            ...eventCase,
            lossDetail: {
                ...eventCase.lossDetail,
                financialAdjustment: {
                    ...eventCase.lossDetail?.financialAdjustment,
                    withholdings: filteredWithholdings
                }
            }
        }
        onUpdateDeleteWithHolding(eventCaseParam as CapEventCaseEntity)
    }

    return (
        <div
            className={classNames(
                FINANCIAL_INFORMATION_BALANCE_SUB_TABLE,
                BALANCE_TABLE_RECALCULATION_PAYMENTS_WRAPPER
            )}
        >
            <Collapse
                defaultActiveKey='withholding'
                isWhite
                accordion
                bordered={false}
                className={BALANCE_TABLE_RECALCULATION_PAYMENTS}
            >
                <Panel
                    key='withholding'
                    header={
                        <div className={BALANCE_TABLE_RECALCULATION_PAYMENTS_TOP}>
                            <div>{t('cap-core:payment_withholdings')}</div>
                        </div>
                    }
                >
                    <div>
                        <Table
                            pagination={false}
                            dataSource={displayWithholdings}
                            columns={getColumns()}
                            rowKey={(row, idx) => row._key?.id?.toString() || idx.toString()}
                            loading={loading}
                        />
                    </div>
                </Panel>
            </Collapse>
        </div>
    )
})
