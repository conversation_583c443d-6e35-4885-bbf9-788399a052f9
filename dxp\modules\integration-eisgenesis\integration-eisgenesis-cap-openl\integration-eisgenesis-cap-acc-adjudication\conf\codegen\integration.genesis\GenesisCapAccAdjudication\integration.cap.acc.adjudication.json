{"openapi": "3.0.1", "info": {"description": "Auto-generated OpenAPI schema from the OpenL rules", "title": "claim-life-accidental-dismemberment-adjudication", "version": "1.0.0"}, "servers": [{"url": "/claim-life-accidental-dismemberment-adjudication", "variables": {}}], "security": [{"BasicAuth": [], "SSOToken": []}], "paths": {"/_api_acc_death_amount_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimFaceValueCalculationResultEntity _api_acc_death_amount_calculation(org.openl.generated.beans.CapClaimWrapperFaceValueCalculationInput request)", "operationId": "_api_acc_death_amount_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperFaceValueCalculationInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimFaceValueCalculationResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimFaceValueCalculationResultEntity _api_acc_death_amount_calculation(CapClaimWrapperFaceValueCalculationInput)"}}, "/_api_accidental_dismemberment_adjudication": {"post": {"description": "Rules method: org.openl.generated.beans.CapAccidentalDismembermentSettlementResultEntity _api_accidental_dismemberment_adjudication(org.openl.generated.beans.CapAccidentalDismembermentSettlementRulesInput request)", "operationId": "_api_accidental_dismemberment_adjudication", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapAccidentalDismembermentSettlementResultEntity _api_accidental_dismemberment_adjudication(CapAccidentalDismembermentSe"}}, "/_api_accidental_dismemberment_applicability": {"post": {"description": "Rules method: org.openl.generated.beans.CapAccidentalDismembermentLossApplicabilityResult _api_accidental_dismemberment_applicability(org.openl.generated.beans.CapAccidentalDismembermentLossApplicabilityInput request)", "operationId": "_api_accidental_dismemberment_applicability", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAccidentalDismembermentLossApplicabilityInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAccidentalDismembermentLossApplicabilityResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapAccidentalDismembermentLossApplicabilityResult _api_accidental_dismemberment_applicability(CapAccidentalDismemberment"}}, "/_api_accidental_dismemberment_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapAccidentalDismembermentSettlementResultEntity _api_accidental_dismemberment_calculation(org.openl.generated.beans.CapAccidentalDismembermentSettlementRulesInput request)", "operationId": "_api_accidental_dismemberment_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapAccidentalDismembermentSettlementResultEntity _api_accidental_dismemberment_calculation(CapAccidentalDismembermentSet"}}, "/_api_add_face_value_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimFaceValueCalculationResultEntity _api_add_face_value_calculation(org.openl.generated.beans.CapClaimWrapperFaceValueCalculationInput request)", "operationId": "_api_add_face_value_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperFaceValueCalculationInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimFaceValueCalculationResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimFaceValueCalculationResultEntity _api_add_face_value_calculation(CapClaimWrapperFaceValueCalculationInput)"}}}, "components": {"schemas": {"AccumulatorRemainingsEntity": {"type": "object", "properties": {"accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorType": {"type": "string"}, "amountUnit": {"type": "string"}, "coverage": {"type": "string"}, "limitAmount": {"type": "number"}, "policyTerm": {"$ref": "#/components/schemas/Term"}, "remainingAmount": {"type": "number"}, "usedAmount": {"type": "number"}}}, "BaseLifeGrossBenefitAmount": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "totalApprovedAmount": {"$ref": "#/components/schemas/Money"}}}, "BaseLifePolicyCoverageLimitLevel": {"type": "object", "properties": {"limitLevelType": {"type": "string"}, "timePeriodCd": {"type": "string"}}}, "BaseLifeSettlementAccumulatorDetails": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorAmount": {"type": "number"}, "accumulatorAmountUnit": {"type": "string"}, "accumulatorCoverage": {"type": "string"}, "accumulatorCustomerUri": {"type": "string"}, "accumulatorExtension": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorPolicyUri": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time"}, "accumulatorType": {"type": "string"}, "autoAdjudicatedAmount": {"type": "number"}}}, "BaseLifeSettlementAccumulatorDetailsExtension": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorUnitCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapAccidentalDismembermentBenefitInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitPct": {"type": "number"}, "burnInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapBurnInfoEntity"}}, "childBenefitAmount": {"$ref": "#/components/schemas/Money"}, "childBenefitPct": {"type": "number"}, "completionPeriod": {"type": "integer", "format": "int32"}, "confinementSeparationPeriod": {"type": "integer", "format": "int32"}, "incurralPeriod": {"type": "integer", "format": "int32"}, "maxBenefitAmount": {"$ref": "#/components/schemas/Money"}, "maxBenefitNumber": {"type": "integer", "format": "int32"}, "partialOrChipPct": {"type": "number"}, "reductionInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapReductionAmountInfoEntity"}}, "repairedThroughSurgeryPeriod": {"type": "integer", "format": "int32"}, "spouseBenefitAmount": {"$ref": "#/components/schemas/Money"}, "spouseBenefitPct": {"type": "number"}, "surgeryPerformancePeriod": {"type": "integer", "format": "int32"}, "timePeriodCd": {"type": "string"}, "waitingPeriod": {"type": "integer", "format": "int32"}}}, "CapAccidentalDismembermentCertInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapAccidentalDismembermentBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccidentalDismembermentCoverageInfoEntity"}}}}, "CapAccidentalDismembermentCoverageInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "childOrganizedSportBenefitPct": {"type": "number"}, "coverageCd": {"type": "string"}, "isChildOrganizedSportApplied": {"type": "boolean"}}}, "CapAccidentalDismembermentLossApplicabilityInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "eventDate": {"type": "string", "format": "date-time"}, "lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/CapLifeLossPolicyInfoEntity"}}}}, "CapAccidentalDismembermentLossApplicabilityResult": {"type": "object", "properties": {"applicability": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}}}, "CapAccidentalDismembermentMasterInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapAccidentalDismembermentBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccidentalDismembermentCoverageInfoEntity"}}}}, "CapAccidentalDismembermentRelatedSettlmentEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "admissionConfinementSeparationPeriod": {"$ref": "#/components/schemas/Period"}, "benefitCategory": {"type": "string"}, "benefitCd": {"type": "string"}, "burnDegree": {"type": "string"}, "burnLink": {"$ref": "#/components/schemas/EntityLink"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "dependentRegistryId": {"type": "string"}, "incidentDate": {"type": "string", "format": "date-time"}}}, "CapAccidentalDismembermentSettlementAttrOptionsEntity": {"type": "object", "properties": {"attrName": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}}}, "CapAccidentalDismembermentSettlementCoverageConfigEntity": {"type": "object", "properties": {"accidentInd": {"type": "boolean"}, "accumulationCategoryGroup": {"type": "string"}, "applicableLossTypes": {"type": "array", "items": {"type": "string"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementAttrOptionsEntity"}}, "benefitCategory": {"type": "string"}, "calculationFormulaId": {"type": "string"}, "grossAmountMode": {"type": "string"}, "groupUnit": {"type": "string"}, "incurralPeriodUnit": {"type": "string"}, "limitLevels": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string"}}}, "CapAccidentalDismembermentSettlementDeductionEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "deductionBeneficiary": {"type": "string"}, "deductionPct": {"type": "number", "format": "double"}, "deductionTerm": {"$ref": "#/components/schemas/Term"}, "deductionType": {"type": "string"}, "isPrePostTax": {"type": "boolean"}, "nonProviderPaymentType": {"type": "string"}, "stateProvided": {"type": "string"}}}, "CapAccidentalDismembermentSettlementDetailEntity": {"type": "object", "properties": {"_archived": {"type": "boolean"}, "_key": {"$ref": "#/components/schemas/EntityKey"}, "_modelName": {"type": "string"}, "_modelType": {"type": "string"}, "_modelVersion": {"type": "string"}, "_timestamp": {"type": "string"}, "_type": {"type": "string"}, "_version": {"type": "string"}, "benefitCd": {"type": "string"}, "burnDegree": {"type": "string"}, "burnLink": {"$ref": "#/components/schemas/EntityLink"}, "childOrganizedSportInd": {"type": "boolean"}, "chipFractureInd": {"type": "boolean"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "dependentRegistryId": {"type": "string"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "incidentDate": {"type": "string", "format": "date-time"}, "isGrossAmountOverrided": {"type": "boolean"}, "multipleDislocationsInd": {"type": "boolean"}, "multipleFracturesInd": {"type": "boolean"}, "numberOfUnits": {"type": "integer", "format": "int32"}, "partialDislocationInd": {"type": "boolean"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}, "reductionAmountType": {"type": "string"}, "isCancelled": {"type": "boolean", "description": "Whether the settlement is cancelled"}, "cancelReason": {"type": "string", "description": "The settlement cancelled reason"}}}, "CapAccidentalDismembermentSettlementLifeIntakeInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "deductions": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementDeductionEntity"}}, "taxes": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementTaxEntity"}}}}, "CapAccidentalDismembermentSettlementLossInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "absence": {"$ref": "#/components/schemas/EntityLink"}, "age": {"type": "integer", "format": "int32"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "claimType": {"type": "string"}, "coverageType": {"type": "string"}, "firstTreatmentDate": {"type": "string", "format": "date-time"}, "isChildOrganizedSport": {"type": "boolean"}, "lastWorkDate": {"type": "string", "format": "date-time"}, "lossDateTime": {"type": "string", "format": "date-time"}, "lossItems": {"type": "array", "items": {"type": "string"}}, "lossType": {"type": "string"}, "offsets": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementOffsetEntity"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}, "overrideFaceValueAmountTL": {"$ref": "#/components/schemas/Money"}, "relationshipToInsuredCd": {"type": "string"}}}, "CapAccidentalDismembermentSettlementOffsetEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "isPrePostTax": {"type": "boolean"}, "offsetTerm": {"$ref": "#/components/schemas/Term"}, "offsetType": {"type": "string"}, "proratingRate": {"type": "string"}}}, "CapAccidentalDismembermentSettlementPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accidentalDismembermentCertInfo": {"$ref": "#/components/schemas/CapAccidentalDismembermentCertInfoEntity"}, "accidentalDismembermentMasterInfo": {"$ref": "#/components/schemas/CapAccidentalDismembermentMasterInfoEntity"}, "ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isAgeReductionApplied": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string"}}}, "CapAccidentalDismembermentSettlementResultEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/components/schemas/Money"}, "autoAdjudicatedDuration": {"type": "number"}, "benefitAmountPerUnit": {"$ref": "#/components/schemas/Money"}, "benefitCd": {"type": "string"}, "burnDegree": {"type": "string"}, "claimCoverageName": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "dependentRegistryId": {"type": "string"}, "eligibilityEvaluationCd": {"type": "string"}, "grossAmountMode": {"type": "string"}, "grossBenefitAmount": {"$ref": "#/components/schemas/BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time"}, "isAutoAdjudicated": {"type": "boolean"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}, "numberOfUnits": {"type": "integer", "format": "int64"}, "paymentDetailInfo": {"$ref": "#/components/schemas/PaymentDetailInfoEntity"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}, "reductionAmountType": {"type": "string"}}}, "CapAccidentalDismembermentSettlementRulesInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/components/schemas/AccumulatorRemainingsEntity"}}, "claimCoverageName": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "coverageConfiguration": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementCoverageConfigEntity"}, "currentDateTime": {"type": "string", "format": "date-time"}, "details": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "lifeIntake": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementLifeIntakeInfoEntity"}, "loss": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementLossInfoEntity"}, "policy": {"$ref": "#/components/schemas/CapAccidentalDismembermentSettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccidentalDismembermentRelatedSettlmentEntity"}}, "settlement": {"$ref": "#/components/schemas/EntityLink"}, "wrapperInfo": {"$ref": "#/components/schemas/CapSettlementWrapperInfoEntity"}}}, "CapAccidentalDismembermentSettlementTaxEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "jurisdictionType": {"type": "string"}, "taxType": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}}}, "CapAgeReductionDetailsEntity": {"type": "object", "properties": {"ageCd": {"type": "integer", "format": "int32"}, "reducedToCd": {"type": "number"}}}, "CapBurnInfoEntity": {"type": "object", "properties": {"burnAmount": {"$ref": "#/components/schemas/Money"}, "burnDegreeTypeCd": {"type": "string"}}}, "CapClaimFaceValueCalculationResultEntity": {"type": "object", "properties": {"result": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimFaceValueResultEntity"}}}}, "CapClaimFaceValueResultEntity": {"type": "object", "properties": {"calculatedFaceAmount": {"$ref": "#/components/schemas/Money"}, "claimCoveragePrefix": {"type": "string"}, "isAgeReductionUsed": {"type": "boolean"}, "originalFaceAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimWrapperCoverageInfoEntity": {"type": "object", "properties": {"ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "benefitStructures": {"type": "array", "items": {"$ref": "#/components/schemas/CapCoverageBenefitStructuresEntity"}}, "coverageCd": {"type": "string"}, "isAgeReductionApplied": {"type": "boolean"}, "specificBenefitInfo": {"$ref": "#/components/schemas/CapSpecificBenefitInfoEntity"}}}, "CapClaimWrapperFaceValueCalculationInput": {"type": "object", "properties": {"addBasicFaceValueAmount": {"$ref": "#/components/schemas/Money"}, "addVoluntaryFaceValueAmount": {"$ref": "#/components/schemas/Money"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimWrapperCoverageInfoEntity"}}, "isChildOrganizedSport": {"type": "boolean"}, "isMasterPolicy": {"type": "boolean"}, "memberAge": {"type": "integer", "format": "int32"}, "relationshipToInsuredCd": {"type": "string"}, "subjectOfClaimAge": {"type": "integer", "format": "int32"}}}, "CapCoverageBenefitStructuresEntity": {"type": "object", "properties": {"annualEarningsAmount": {"$ref": "#/components/schemas/Money"}, "approvedAmount": {"$ref": "#/components/schemas/Money"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitTypeCd": {"type": "string"}, "employeeAmtpct": {"type": "number"}, "roundingMethod": {"type": "string"}, "salaryMultiple": {"type": "number"}, "typeOfBenefitStructure": {"type": "string"}}}, "CapInsuredInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "isMain": {"type": "boolean"}, "registryTypeId": {"type": "string"}}}, "CapLifeLossPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapReductionAmountInfoEntity": {"type": "object", "properties": {"reductionAmount": {"$ref": "#/components/schemas/Money"}, "reductionAmountTypeCd": {"type": "string"}}}, "CapSettlementWrapperInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "claimSubjectId": {"type": "string"}, "claimWrapperIdentification": {"$ref": "#/components/schemas/EntityLink"}, "memberRegistryTypeId": {"type": "string"}}}, "CapSpecificBenefitInfoEntity": {"type": "object", "properties": {"benefitPct": {"type": "number"}, "childBenefitAmount": {"$ref": "#/components/schemas/Money"}, "indBenefitAmount": {"$ref": "#/components/schemas/Money"}, "isChildOrganizedSportApplied": {"type": "boolean"}, "spouseBenefitAmount": {"$ref": "#/components/schemas/Money"}}}, "EntityKey": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string"}}}, "EntityLink": {"type": "object", "properties": {"_uri": {"type": "string"}}}, "JAXRSErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["USER_ERROR", "RULES_RUNTIME", "COMPILATION", "SYSTEM", "BAD_REQUEST", "VALIDATION"]}, "message": {"type": "string"}}}, "JAXRSUserErrorResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "MessageType": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "source": {"type": "string"}}}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}}}, "PaymentDetailInfoEntity": {"type": "object", "properties": {"initiatePayment": {"type": "boolean"}}}, "Period": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}}, "Term": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"BasicAuth": {"description": "Genesis Basic credentials", "scheme": "basic", "type": "http"}, "SSOToken": {"description": "Genesis Authorization header. Value format: `[Token {token}]`", "in": "header", "name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}