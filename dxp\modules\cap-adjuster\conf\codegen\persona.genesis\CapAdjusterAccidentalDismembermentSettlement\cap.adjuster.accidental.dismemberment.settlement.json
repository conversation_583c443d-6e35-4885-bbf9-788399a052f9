{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.accidental.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Accidental Dismemberment Settlements API", "version": "1", "title": "CAP Adjuster: Accidental Dismemberment Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-accidental-dismemberment-settlements", "description": "CAP Adjuster: Accidental Dismemberment Settlements API"}], "paths": {"/losses-accidental-dismemberment-settlements/{rootId}/{revisionNo}": {"get": {"summary": "Get accidental dismemberment settlement by rootId and revisionNo", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}, "/losses-accidental-dismemberment-settlements/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for accidental dismemberment settlement", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}, "/losses-accidental-dismemberment-settlements/draft": {"post": {"summary": "Init accidental dismemberment settlement", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/initSettlement", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}, "/losses-accidental-dismemberment-settlements/adjudicate": {"post": {"summary": "Adjudicate accidental dismemberment settlement", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/adjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}, "put": {"summary": "Readjudicate accidental dismemberment settlement", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}, "/losses-accidental-dismemberment-settlements/approve": {"post": {"summary": "Approve accidental dismemberment settlement", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/approveSettlement", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}, "/losses-accidental-dismemberment-settlements/disapprove": {"post": {"summary": "Disapprove accidental dismemberment settlement", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/command/disapproveSettlement", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}, "/losses-accidental-dismemberment-settlements/adjudication-input": {"post": {"summary": "Accidental dismemberment settlement adjudication input", "x-dxp-path": "/api/capsettlement/AccidentalDismembermentSettlement/v1/transformation/CapAccidentalDismembermentSettlementAdjudicationInput", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment-settlements"]}}}}