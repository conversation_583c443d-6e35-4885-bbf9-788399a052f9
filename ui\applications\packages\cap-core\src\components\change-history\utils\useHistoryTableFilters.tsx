/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {useEffect, useState} from 'react'
import {uniq} from 'lodash'
import {historyTableFilter} from './filter'
import {HistoryTableRow, OptionsListeProps} from './types'
import {extractHistoryTableRows} from './rows'

interface useHistoryTableFiltersProps {
    data: ReturnType<typeof extractHistoryTableRows>
}

export function useHistoryTableFilters({data}: useHistoryTableFiltersProps) {
    const [tableData, setTableData] = useState<HistoryTableRow[]>([])
    const [optionsList, setOptionsList] = useState<OptionsListeProps>({
        component: [],
        adjustedBy: [],
        attribute: [],
        entity: [],
        reference: []
    })
    const [tableFilters, setTableFilters] = useState({})
    useEffect(() => {
        const componentOptions = [] as string[]
        const adjustedByOptions = [] as string[]
        const attributeOptions = [] as string[]
        const entityOptions = [] as string[]
        const referenceOptions = [] as string[]
        data.forEach(v => {
            componentOptions.push(v.component)
            adjustedByOptions.push(v.adjustedBy)
            attributeOptions.push(v.attribute)
            entityOptions.push(v.entity)
            referenceOptions.push(v.reference)
        })
        setOptionsList({
            component: uniq(componentOptions),
            adjustedBy: uniq(adjustedByOptions),
            attribute: uniq(attributeOptions),
            entity: uniq(entityOptions),
            reference: uniq(referenceOptions)
        })
        setTableData(data)
    }, [data])
    const handleSearch = (selectedKeys: (string | Date)[], confirm: () => void, dataIndex: string) => {
        confirm()
        const newTableData = historyTableFilter(data, {
            ...tableFilters,
            [dataIndex]: selectedKeys
        })
        setTableData(newTableData)
        setTableFilters({
            ...tableFilters,
            [dataIndex]: selectedKeys
        })
    }
    const handleReset = (clearFilters: () => void, dataIndex: string) => {
        const newTableData = historyTableFilter(data, {
            ...tableFilters,
            [dataIndex]: undefined
        })
        setTableData(newTableData)
        setTableFilters({
            ...tableFilters,
            [dataIndex]: undefined
        })
        clearFilters()
    }

    return {tableData, handleReset, optionsList, handleSearch, tableFilters}
}
