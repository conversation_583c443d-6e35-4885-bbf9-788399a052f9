/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapBalance} from '@eisgroup/cap-financial-models'
import {dateUtils} from '@eisgroup/cap-services'
import {opt} from '@eisgroup/common-types'
import {useTranslate} from '@eisgroup/i18n'
import {Col, Row} from '@eisgroup/ui-kit'
import {EisDateTime} from '@eisgroup/ui-temporals'
import {sumBy} from 'lodash'
import {runInAction} from 'mobx'
import {observer} from 'mobx-react'
import React, {useEffect, useState} from 'react'
import {EntityLink, getTranslatedCoverageName, moneyByLocale, Settlements} from '../..'
import {BalanceStore} from '../../common/store/BalanceStore'
import {refactorDateFormat} from '../payments/RenderUtils'
import {BalanceListCommonExpandRow} from './BalanceListCommonExpandRow'
import {getSortedScheduledAllocation} from './utils'
import CapBalanceItemActualAllocationEntity = CapBalance.CapBalanceItemActualAllocationEntity
import CapBalanceItemScheduledAllocationEntity = CapBalance.CapBalanceItemScheduledAllocationEntity
import Period = CapBalance.Period

interface BalanceListLifeExpandRowProps {
    allocation: CapBalanceItemActualAllocationEntity
    balanceStore: BalanceStore
    allAssociatedSettlements: Settlements[]
}

export const BalanceListLifeExpandRow: React.FC<BalanceListLifeExpandRowProps> = observer(props => {
    const [claimType, setClaimType] = useState<string>()
    const [actualCoverageDate, setActualCoverageDate] = useState<string>()
    const [scheduledCoverageDate, setScheduledCoverageDate] = useState<string>()
    const [claimCoverageName, setClaimCoverageName] = useState<string>('')
    const [claimCoveragePrefix, setClaimCoveragePrefix] = useState<string>('')
    const {t} = useTranslate()
    useEffect(() => {
        findClaimType()

        setClaimCoverageName(props.allocation.allocationPayableItem?.coverageCd || '')
        const composedActualCoverageDate = composeCoverageDate(
            props.allocation.allocationPayableItem?.benefitDate,
            props.allocation.allocationPayableItem?.benefitPeriod
        )
        setActualCoverageDate(composedActualCoverageDate)
        const composedScheduledCoverageDate = composeCoverageDate(
            props.allocation.scheduledAllocations?.[0]?.allocationPayableItem?.benefitDate,
            props.allocation.scheduledAllocations?.[0]?.allocationPayableItem?.benefitPeriod
        )
        setScheduledCoverageDate(composedScheduledCoverageDate)

        composeCoverageLabel()
    }, [])

    const findClaimType = () => {
        const rootId = EntityLink.from(props.allocation?.allocationSource?._uri || '').rootId
        props.allAssociatedSettlements?.forEach(settlement => {
            if (rootId.includes(settlement._key.rootId)) {
                setClaimType(settlement.settlementLossInfo?.claimType)
            }
        })
    }

    const composeCoverageDate = (benefitDate?: EisDateTime | Date, benefitPeriod?: Period) => {
        const formattedBenefitDate = dateUtils(benefitDate).render
        const formattedBenefitPeriod = benefitPeriod
            ? opt(benefitPeriod && refactorDateFormat(benefitPeriod).toString()).orElse(t('cap-core:not_available'))
            : t('cap-core:not_available')
        return benefitDate ? formattedBenefitDate : formattedBenefitPeriod
    }

    const composeCoverageLabel = () => {
        const allocation = EntityLink.from(props.allocation.allocationSource!._uri)
        props.balanceStore
            .loadSettlementByType(allocation.rootId, allocation.revisionNo, allocation.modelName)
            .subscribe(either => {
                either.map(r => {
                    runInAction(() => {
                        setClaimCoveragePrefix(r?.claimCoveragePrefix)
                    })
                })
            })
    }

    const scheduledAllocations = getSortedScheduledAllocation(props.allocation)
    return (
        <Row>
            <Col span={4}>
                <h4>{claimType}</h4>
            </Col>
            <Col span={9}>
                <div>
                    <label>
                        {getTranslatedCoverageName(claimCoverageName, claimCoveragePrefix)}({actualCoverageDate})
                    </label>
                    {moneyByLocale(props.allocation?.allocationGrossAmount?.amount || 0)}
                </div>
                <BalanceListCommonExpandRow allocation={props.allocation} />
            </Col>
            <Col span={9}>
                <div>
                    <label>
                        {getTranslatedCoverageName(claimCoverageName, claimCoveragePrefix)}(
                        {props.allocation?.scheduledAllocations ? scheduledCoverageDate : actualCoverageDate})
                    </label>
                    {moneyByLocale(
                        sumBy(
                            props.allocation?.scheduledAllocations,
                            (schedule: CapBalanceItemScheduledAllocationEntity) =>
                                schedule?.allocationGrossAmount!.amount
                        ) || 0
                    )}
                </div>
                {scheduledAllocations &&
                    scheduledAllocations.map(schedule => {
                        return <BalanceListCommonExpandRow allocation={schedule} />
                    })}
            </Col>
        </Row>
    )
})
