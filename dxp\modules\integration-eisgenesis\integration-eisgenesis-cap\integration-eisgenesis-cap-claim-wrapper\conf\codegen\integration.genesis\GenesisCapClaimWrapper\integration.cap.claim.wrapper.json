{"swagger": "2.0", "info": {"description": "API for ClaimWrapper", "version": "1", "title": "ClaimWrapper model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/ClaimWrapper/v1/command/adjudicateLoss": {"post": {"description": "The command that instantiates adjudication process for claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/closeLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/initLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapClaimWrapperInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/reopenLoss": {"post": {"description": "The command that reopens claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/submitLoss": {"post": {"description": "The command that performs validation of the provided claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/updateLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapClaimWrapperUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapClaimWrapperUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/command/updateLossImmediate": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapClaimWrapperUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapClaimWrapperUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ClaimWrapperLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ClaimWrapperLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/ClaimWrapperKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/CapClaimWrapperEligibilityInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapClaimWrapperEligibilityInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/capResolveCloseClaimReason": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/claimCoverageToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimCoverageToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/claimWrapperCoverages": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperAvailableCoverageEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/claimWrapperToLossSettlementInputs": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapperToLossSettlementInputsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/claimWrapperToSettlementRefreshInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ClaimWrapperToSettlementRefreshInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/ClaimWrapper/v1/transformation/lifeClaimClosureToOpenItems": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeClaimClosureToOpenItemsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapClaimWrapperEligibilityInputOutputs": {"properties": {"output": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEligibilityInput"}}, "title": "CapClaimWrapperEligibilityInputOutputs"}, "CapClaimWrapperEligibilityInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapClaimWrapperEligibilityInputOutputs"}}, "title": "CapClaimWrapperEligibilityInputOutputsSuccess"}, "CapClaimWrapperEligibilityInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapClaimWrapperEligibilityInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapClaimWrapperEligibilityInputOutputsSuccessBody"}, "CapClaimWrapperInitInput": {"required": ["entity"], "properties": {"claimType": {"type": "string"}, "entity": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "memberRegistryTypeId": {"type": "string"}, "policyId": {"type": "string"}}, "title": "CapClaimWrapperInitInput"}, "CapClaimWrapperInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapClaimWrapperInitInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapClaimWrapperInitInputBody"}, "CapClaimWrapperUpdateInput": {"required": ["_key", "claimWrapperUpdateUIAction", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimWrapperUpdateUIAction": {"type": "string"}, "entity": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperDetailEntity"}, "policyId": {"type": "string"}}, "title": "CapClaimWrapperUpdateInput"}, "CapClaimWrapperUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapClaimWrapperUpdateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapClaimWrapperUpdateInputBody"}, "CapResolveCloseClaimReasonOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapResolveCloseClaimReasonOutputs"}, "CapResolveCloseClaimReasonOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputs"}}, "title": "CapResolveCloseClaimReasonOutputsSuccess"}, "CapResolveCloseClaimReasonOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapResolveCloseClaimReasonOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapResolveCloseClaimReasonOutputsSuccessBody"}, "ClaimCoverageToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ClaimCoverageToAccumulatorTxOutputs"}, "ClaimCoverageToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimCoverageToAccumulatorTxOutputs"}}, "title": "ClaimCoverageToAccumulatorTxOutputsSuccess"}, "ClaimCoverageToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimCoverageToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimCoverageToAccumulatorTxOutputsSuccessBody"}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossCloseInputBody"}, "ClaimLossReopenInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossReopenInputBody"}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "lossSubStatusCd": {"type": "string"}}, "title": "ClaimLossSubStatusInput"}, "ClaimLossSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossSubStatusInputBody"}, "ClaimWrapperKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "ClaimWrapperKrakenDeprecatedBundleRequest"}, "ClaimWrapperKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimWrapperKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimWrapperKrakenDeprecatedBundleRequestBody"}, "ClaimWrapperLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntity"}}}, "title": "ClaimWrapperLoadHistoryResult"}, "ClaimWrapperLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimWrapperLoadHistoryResult"}}, "title": "ClaimWrapperLoadHistoryResultSuccess"}, "ClaimWrapperLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimWrapperLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimWrapperLoadHistoryResultSuccessBody"}, "ClaimWrapperToLossSettlementInputsOutputs": {"properties": {"settlementInputs": {"type": "array", "items": {"type": "object"}}}, "title": "ClaimWrapperToLossSettlementInputsOutputs"}, "ClaimWrapperToLossSettlementInputsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimWrapperToLossSettlementInputsOutputs"}}, "title": "ClaimWrapperToLossSettlementInputsOutputsSuccess"}, "ClaimWrapperToLossSettlementInputsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimWrapperToLossSettlementInputsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimWrapperToLossSettlementInputsOutputsSuccessBody"}, "ClaimWrapperToSettlementRefreshInputOutputs": {"properties": {"output": {"type": "object"}}, "title": "ClaimWrapperToSettlementRefreshInputOutputs"}, "ClaimWrapperToSettlementRefreshInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimWrapperToSettlementRefreshInputOutputs"}}, "title": "ClaimWrapperToSettlementRefreshInputOutputsSuccess"}, "ClaimWrapperToSettlementRefreshInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimWrapperToSettlementRefreshInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimWrapperToSettlementRefreshInputOutputsSuccessBody"}, "ClaimWrapper_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "ClaimWrapper AccessTrackInfo"}, "ClaimWrapper_BaseDamageLossWrapper": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseDamageLossWrapper"}, "damageType": {"type": "string", "description": "The associated loss type to ClaimWrapper"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "ClaimWrapper BaseDamageLossWrapper"}, "ClaimWrapper_CapAgeReductionDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAgeReductionDetailsEntity"}, "ageCd": {"type": "integer", "format": "int64", "description": "Age"}, "reducedToCd": {"type": "number", "description": "Reduced To Percentage"}}, "title": "ClaimWrapper CapAgeReductionDetailsEntity"}, "ClaimWrapper_CapBeneficiaryDesignationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBeneficiaryDesignationEntity"}, "beneficiaryPercentage": {"type": "number", "description": "Percentage of Benefit Coverage Designation must be within range of 0-100 and cannot exceed 2 decimal places."}, "coverageId": {"type": "string", "description": "Defines unique coverage(settlement) ID."}}, "title": "ClaimWrapper CapBeneficiaryDesignationEntity"}, "ClaimWrapper_CapBeneficiaryRole": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBeneficiaryRole"}, "beneficiaryDesignations": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapBeneficiaryDesignationEntity"}}, "beneficiaryPaymentMethodId": {"type": "string", "description": "Beneficiary Preferred Payment method Id."}, "beneficiaryType": {"type": "string", "description": "Type of beneficiary, Primary and Contingent."}, "checkAddressId": {"type": "string", "description": "Address for beneficiary chech payment method."}, "hasWillReceiveBenefit": {"type": "boolean", "description": "If the beneficiary is willing to receive the benefit."}, "isQualifiedToReceiveBenefit": {"type": "boolean", "description": "If the beneficiary is qualified to receive the benefit."}, "isRetainedAssetApproved": {"type": "boolean", "description": "If Retained Asset Account is approved to use by beneficairy."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "The date of proof of Loss is received."}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}, "representativeRegistryId": {"type": "string", "description": "uri to guardian."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "ClaimWrapper CapBeneficiaryRole", "description": "Entity for beneficiary role"}, "ClaimWrapper_CapClaimSubjectInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimSubjectInfoEntity"}, "age": {"type": "integer", "format": "int64", "description": "Age of this subject"}, "claimSubjectId": {"type": "string", "description": "Unique identifier for subject of claim"}, "employerId": {"type": "string", "description": "Unique identifier for employer role"}, "registryId": {"type": "string", "description": "Unique identifier for subject of claim"}, "relationshipToInsuredCd": {"type": "string", "description": "Code value could be Self, Spouse and Child."}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "ClaimWrapper CapClaimSubjectInfoEntity", "description": "Claim Party Role"}, "ClaimWrapper_CapClaimWrapperAvailableCoverageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperAvailableCoverageEntity"}, "availableCoverages": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapSelectedCoveragesInfoInput"}}}, "title": "ClaimWrapper CapClaimWrapperAvailableCoverageEntity"}, "ClaimWrapper_CapClaimWrapperAvailableCoverageEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperAvailableCoverageEntity"}}, "title": "ClaimWrapper_CapClaimWrapperAvailableCoverageEntitySuccess"}, "ClaimWrapper_CapClaimWrapperAvailableCoverageEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperAvailableCoverageEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimWrapper_CapClaimWrapperAvailableCoverageEntitySuccessBody"}, "ClaimWrapper_CapClaimWrapperCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperCoverageInfoEntity"}, "ageReductionDetails": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapAgeReductionDetailsEntity"}}, "benefitStructures": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapCoverageBenefitStructuresEntity"}}, "coverageCd": {"type": "string", "description": "The value presents the value of policy coverage code"}, "eligibility": {"$ref": "#/definitions/ClaimWrapper_CapEligibilityEntity"}, "specificBenefitInfo": {"$ref": "#/definitions/ClaimWrapper_CapSpecificBenefitInfoEntity"}}, "title": "ClaimWrapper CapClaimWrapperCoverageInfoEntity"}, "ClaimWrapper_CapClaimWrapperDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "ClaimWrapper"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapClaimWrapperDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "beneficiaryRole": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapBeneficiaryRole"}}, "claimLossDate": {"type": "string", "format": "date-time", "description": "Date of Loss for a Claim under the same policy."}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperDiagnosisInformationEntity"}}, "earnings": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEarningsEntity"}}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string", "description": "The attribute represents the eligibility rules decision override reason."}, "lastSelectedCoveragesInput": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapSelectedCoveragesInfoInput"}}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "selectedPlanId": {"type": "string", "description": "Selected policy plan id"}}, "title": "ClaimWrapper CapClaimWrapperDetailEntity", "description": "Defines what loss it is and what happened."}, "ClaimWrapper_CapClaimWrapperDiagnosisInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperDiagnosisInformationEntity"}, "date": {"type": "string", "format": "date", "description": "Defines date of diagnosis."}, "icdCode": {"type": "string", "description": "Defines code of diagnosis."}, "primaryCode": {"type": "boolean", "description": "Defines if diagnosis code is primary."}}, "title": "ClaimWrapper CapClaimWrapperDiagnosisInformationEntity", "description": "Entity for diagnosis information."}, "ClaimWrapper_CapClaimWrapperEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperEarningsEntity"}, "ageReductionAppliedPct": {"type": "number", "description": "Age Reduction Applied Percentage."}, "appliedCoverageGroup": {"type": "string", "description": "Indicator for which the face value applied to, currently this field only used for TL claim type"}, "calculatedFaceAmount": {"$ref": "#/definitions/Money"}, "coveredEarning": {"$ref": "#/definitions/Money"}, "faceValueAmount": {"$ref": "#/definitions/Money"}, "isAgeReductionUsed": {"type": "boolean", "description": "It means firstly policy defined age reduction applicable, and the age from the Subject of Claim meets trigger condition"}, "isFaceValueOverrided": {"type": "boolean", "description": "Indicator if face value is overrided"}, "multiplier": {"type": "number", "description": "The multiplier from a policy"}, "overrideFaceValueAmount": {"$ref": "#/definitions/Money"}}, "title": "ClaimWrapper CapClaimWrapperEarningsEntity"}, "ClaimWrapper_CapClaimWrapperEligibilityInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperEligibilityInput"}, "availableCoverages": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapSelectedCoveragesInfoInput"}}, "className": {"type": "string"}, "dateOfHire": {"type": "string", "format": "date"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "lossInfo": {"$ref": "#/definitions/ClaimWrapper_CapDamageLossInfoEntity"}, "policy": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperPolicyInfoEntity"}, "subjectOfClaim": {"$ref": "#/definitions/ClaimWrapper_CapClaimSubjectInfoEntity"}}, "title": "ClaimWrapper CapClaimWrapperEligibilityInput"}, "ClaimWrapper_CapClaimWrapperEligibilityResult": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperEligibilityResult"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_MessageType"}}}, "title": "ClaimWrapper CapClaimWrapperEligibilityResult"}, "ClaimWrapper_CapClaimWrapperEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "ClaimWrapper"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapClaimWrapperEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/ClaimWrapper_AccessTrackInfo"}, "claimType": {"type": "string", "description": "Specific the Claim type based on the type of Policy to which it is integrated. For example, a Supplemental Benefits Claim against a Critical Illness Policy should reflect/display that it's a Critical Illness Claim."}, "claimWrapperUpdateUIAction": {"type": "string", "description": "Action indicator for claim wrapper update from UI"}, "damageLosses": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_BaseDamageLossWrapper"}}, "eligibilityResult": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEligibilityResult"}, "eventCaseInfo": {"$ref": "#/definitions/ClaimWrapper_EventCaseInfoEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "isGenerated": {"type": "boolean", "description": "Indicates if claim is auto generated."}, "lossDetail": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperDetailEntity"}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "lossType": {"type": "string", "description": "Indicate multipe types of losses happened in this claim application or from single event."}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID."}, "policy": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperPolicyInfoEntity"}, "policyId": {"type": "string"}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "state": {"type": "string", "description": "Current status of the Loss. Updated each time a new status is gained through state machine."}, "subjectOfClaim": {"$ref": "#/definitions/ClaimWrapper_CapClaimSubjectInfoEntity"}}, "title": "ClaimWrapper CapClaimWrapperEntity", "description": "Main object for the CAP Loss Domain."}, "ClaimWrapper_CapClaimWrapperEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntity"}}, "title": "ClaimWrapper_CapClaimWrapperEntitySuccess"}, "ClaimWrapper_CapClaimWrapperEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimWrapper_CapClaimWrapperEntitySuccessBody"}, "ClaimWrapper_CapClaimWrapperPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimWrapperPolicyInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapClaimWrapperCoverageInfoEntity"}}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "hasERISA": {"type": "boolean", "description": "The Emplyee Retirement Income Security Act of 1974"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "masterAvailablePlans": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapMasterPolicyPlanDetailsEntity"}}, "masterPolicyId": {"type": "string", "description": "master policy id"}, "masterPolicyNumber": {"type": "string", "description": "master policy number"}, "masterPolicyProductCd": {"type": "string", "description": "master Policy Product Code"}, "orgCustomerNumber": {"type": "string", "description": "Organization Customer Number"}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyPackageCd": {"type": "string", "description": "Package Code"}, "policyPlanCd": {"type": "string", "description": "Plan Code"}, "policyPlanId": {"type": "string", "description": "Plan Id"}, "policyPlanName": {"type": "string", "description": "Plan Name"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/ClaimWrapper_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "ClaimWrapper CapClaimWrapperPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "ClaimWrapper_CapCoverageBenefitStructuresEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCoverageBenefitStructuresEntity"}, "annualEarningsAmount": {"$ref": "#/definitions/Money"}, "approvedAmount": {"$ref": "#/definitions/Money"}, "benefitAmount": {"$ref": "#/definitions/Money"}, "benefitTypeCd": {"type": "string", "description": "Benefit Type Code"}, "employeeAmtpct": {"type": "number", "description": "Percentage of employee amount"}, "incrementAmount": {"$ref": "#/definitions/Money"}, "individualAmtPct": {"type": "number", "description": "Percentage of individual amount"}, "maxBenefitAmount": {"$ref": "#/definitions/Money"}, "minBenefitAmount": {"$ref": "#/definitions/Money"}, "multipleValues": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_MultipleValues"}}, "roundingMethod": {"type": "string", "description": "Rounding Method in policy's salaryMultiple"}, "salaryMultiple": {"type": "number", "description": "Salary Multiple in policy's salaryMultiple"}, "typeOfBenefitStructure": {"type": "string", "description": "type of benefit structure."}}, "title": "ClaimWrapper CapCoverageBenefitStructuresEntity"}, "ClaimWrapper_CapDamageLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDamageLossInfoEntity"}, "accidentDate": {"type": "string", "format": "date-time"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "claimLossDate": {"type": "string", "format": "date-time"}, "dateOfCIDiagnosis": {"type": "string", "format": "date-time"}, "dateOfDiagnosis": {"type": "string", "format": "date"}, "hiServiceDate": {"type": "string", "format": "date-time"}, "officialDeathDate": {"type": "string", "format": "date-time"}, "primaryDiagnosisDate": {"type": "string", "format": "date"}}, "title": "ClaimWrapper CapDamageLossInfoEntity"}, "ClaimWrapper_CapEligibilityEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEligibilityEntity"}, "eligibilityTypeCd": {"type": "string", "description": "Eligibility type code"}, "waitingPeriodAmount": {"type": "integer", "format": "int64", "description": "Waiting period amount"}, "waitingPeriodDefCd": {"type": "string", "description": "Waiting Period Def Code"}, "waitingPeriodModeCd": {"type": "string", "description": "Waiting period mode code"}}, "title": "ClaimWrapper CapEligibilityEntity"}, "ClaimWrapper_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "ClaimWrapper CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "ClaimWrapper_CapLifeClaimAPOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLifeClaimAPOpenItemInfo"}, "apPeriod": {"$ref": "#/definitions/ClaimWrapper_Period"}, "apStatus": {"type": "string", "description": "The decision made upon each approval period whether it's accepted or not"}, "claimNumber": {"type": "string", "description": "Claim Number"}}, "title": "ClaimWrapper CapLifeClaimAPOpenItemInfo"}, "ClaimWrapper_CapLifeClaimBalanceOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLifeClaimBalanceOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "ClaimWrapper CapLifeClaimBalanceOpenItemInfo", "description": "Entity for balance open item info"}, "ClaimWrapper_CapLifeClaimClosureOpenItemsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLifeClaimClosureOpenItemsOutput"}, "actualPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimPaymentDefinitionOpenItemInfo"}}, "apInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimAPOpenItemInfo"}}, "balanceInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimBalanceOpenItemInfo"}}, "coverageInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimCoverageOpenItemInfo"}}, "issuedPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimPaymentDefinitionOpenItemInfo"}}, "scheduledPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimScheduledPaymentOpenItemInfo"}}}, "title": "ClaimWrapper CapLifeClaimClosureOpenItemsOutput"}, "ClaimWrapper_CapLifeClaimCoverageOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLifeClaimCoverageOpenItemInfo"}, "amountType": {"type": "string"}, "apInfos": {"type": "array", "items": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimAPOpenItemInfo"}}, "coverageName": {"type": "string"}, "dateRange": {"$ref": "#/definitions/ClaimWrapper_Period"}, "incidentDate": {"type": "string", "format": "date-time"}, "totalGBAorDuration": {"type": "number"}, "unpaidGBAorDuration": {"type": "number"}}, "title": "ClaimWrapper CapLifeClaimCoverageOpenItemInfo"}, "ClaimWrapper_CapLifeClaimPaymentDefinitionOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLifeClaimPaymentDefinitionOpenItemInfo"}, "payeeDisplay": {"type": "string"}, "paymentDate": {"type": "string", "format": "date-time"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string"}, "paymentState": {"type": "string"}}, "title": "ClaimWrapper CapLifeClaimPaymentDefinitionOpenItemInfo"}, "ClaimWrapper_CapLifeClaimScheduledPaymentOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLifeClaimScheduledPaymentOpenItemInfo"}, "allocationPeriod": {"$ref": "#/definitions/ClaimWrapper_Period"}, "countOfUnposted": {"type": "integer", "format": "int64"}, "coverageName": {"type": "string"}, "frequencyType": {"type": "string"}, "payeeDisplay": {"type": "string"}, "paymentDate": {"type": "string", "format": "date-time"}, "paymentScheduleNumber": {"type": "string"}}, "title": "ClaimWrapper CapLifeClaimScheduledPaymentOpenItemInfo"}, "ClaimWrapper_CapMasterPolicyPlanDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMasterPolicyPlanDetailsEntity"}, "planCd": {"type": "string", "description": "Plan Code"}, "planId": {"type": "string", "description": "Plan Id"}, "planName": {"type": "string", "description": "Plan Name"}}, "title": "ClaimWrapper CapMasterPolicyPlanDetailsEntity"}, "ClaimWrapper_CapSelectedCoveragesInfoInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSelectedCoveragesInfoInput"}, "benefitCategory": {"type": "string"}, "claimCoverageLabel": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "icdOrCptCode": {"type": "string", "description": "Configured icd/cpt code"}, "icdOrCptId": {"type": "string", "description": "Id of icd/cpt entity which used to bind settlement with icd/cpt."}, "isAutomaticCoverage": {"type": "boolean"}, "lossType": {"type": "array", "items": {"type": "string"}}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}}, "title": "ClaimWrapper CapSelectedCoveragesInfoInput"}, "ClaimWrapper_CapSpecificBenefitInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSpecificBenefitInfoEntity"}, "benefitPct": {"type": "number"}, "childBenefitAmount": {"$ref": "#/definitions/Money"}, "indBenefitAmount": {"$ref": "#/definitions/Money"}, "isChildOrganizedSportApplied": {"type": "boolean"}, "spouseBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "ClaimWrapper CapSpecificBenefitInfoEntity"}, "ClaimWrapper_EventCaseInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "EventCaseInfoEntity"}, "caseNumber": {"type": "string"}, "isSicknessInjury": {"type": "string", "description": "Injury vs Sickness."}}, "title": "ClaimWrapper EventCaseInfoEntity"}, "ClaimWrapper_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "ClaimWrapper MessageType", "description": "Holds information of message type."}, "ClaimWrapper_MultipleValues": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MultipleValues"}, "specifiedAmount": {"type": "number", "description": "Specified Amount"}}, "title": "ClaimWrapper MultipleValues"}, "ClaimWrapper_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "ClaimWrapper Period"}, "ClaimWrapper_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "ClaimWrapper Term"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "LifeClaimClosureToOpenItemsOutputs": {"properties": {"output": {"$ref": "#/definitions/ClaimWrapper_CapLifeClaimClosureOpenItemsOutput"}}, "title": "LifeClaimClosureToOpenItemsOutputs"}, "LifeClaimClosureToOpenItemsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/LifeClaimClosureToOpenItemsOutputs"}}, "title": "LifeClaimClosureToOpenItemsOutputsSuccess"}, "LifeClaimClosureToOpenItemsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/LifeClaimClosureToOpenItemsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LifeClaimClosureToOpenItemsOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}