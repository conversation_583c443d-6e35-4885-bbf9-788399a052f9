import React, {FC} from 'react'
import {t} from '@eisgroup/i18n'
import {observer} from 'mobx-react'
import {CaseSystemService, CSClaimWrapperService} from '@eisgroup/cap-services'
import {ActionFormBase} from './ActionFormBase'
import addExternalOverPaymentConfig from '../../../../builder/BalanceAddExternalOverPayment.builder'
import {DrawerFormStateType} from '../../../form-drawer'
import {BALANCE_ACTION_ADD_EXTERNAL_OVERPAYMENT_FORM_ID, BALANCE_ACTIONS_MAP} from '../../../../common/constants'
import {BalanceActionDrawerProps} from '../BalanceActionDrawer'
import {ICaseSystem} from '../../../../common/Types'

type AddExternalOverpaymentActionFormProps = BalanceActionDrawerProps<
    ICaseSystem,
    CaseSystemService | CSClaimWrapperService<ICaseSystem>
>

export const AddExternalOverpaymentActionForm: FC<AddExternalOverpaymentActionFormProps> = observer(props => {
    const {actionKey: key, payeeLink, balanceStore, closeDrawer, getBalanceChangeLog, getLoadPayments} = props
    const isLoading = balanceStore.actionsStore.isRunning(BALANCE_ACTIONS_MAP.ADD_EXTERNAL_OVERPAYMENT)

    const onFormConfirm = values => {
        const params = {
            payee: {_uri: payeeLink},
            originSource: {_uri: balanceStore.balanceSource}
        }
        const saveParams = {
            ...params,
            externalBalanceAmount: values.externalBalanceAmount,
            externalBalanceReason: values.comment
        }
        const nextBalanceCount = balanceStore.balanceChangeLogCount + 1

        balanceStore.addExternalOverpayment(saveParams).subscribe(() => {
            getBalanceChangeLog(nextBalanceCount)
            getLoadPayments()
            closeDrawer()
        })
    }
    return (
        <ActionFormBase
            uiEngineProps={{
                formId: BALANCE_ACTION_ADD_EXTERNAL_OVERPAYMENT_FORM_ID,
                config: addExternalOverPaymentConfig,
                initialValues: {
                    action: key
                },
                onNext: onFormConfirm
            }}
            drawerActionProps={{
                drawerFormState: DrawerFormStateType.Create,
                handleFormCancel: closeDrawer,
                isLoading,
                labels: {
                    createButtonLabel: t('cap-core:balance_actions_drawer_external_overpayment_add_button')
                }
            }}
        />
    )
})
