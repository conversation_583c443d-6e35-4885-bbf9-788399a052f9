Common.projectSettings

lazy val integrationEisGenesisCap = project.in(file("integration-eisgenesis-cap"))
lazy val integrationEisGenesisCapOpenl = project.in(file("integration-eisgenesis-cap-openl"))

lazy val integrationEisGenesis = project.in(file("."))
  .enablePlugins(PlayMinimalJava)
  .aggregate(
    integrationEisGenesisCap,
    integrationEisGenesisCapOpenl
  )
  .dependsOn(
    integrationEisGenesisCap,
    integrationEisGenesisCapOpenl
  )

publish / skip := true