/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {UIEngine} from '@eisgroup/builder'
import {
    ClaimDisabilityLossSettlement,
    dateUtils,
    IndividualCustomer,
    OrganizationCustomer
} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {FormApi} from '@eisgroup/form'
import {cloneDeep} from 'lodash'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import React from 'react'
import {OptionValue, PropsCustomizerMap} from '../../common/Types'
import {
    ComponentWithFormSpy,
    DISABILITY_LOSS_TYPES,
    editorConfig,
    KrakenFormEvaluation,
    READJUDICATE_SETTLEMENT
} from '../../index'
import {KrakenEvaluationContext, RulesEvaluationProps} from '../../kraken'
import {collectAndSortPayee, getCurrentUsername} from '../../utils'
import {DrawerActions, DrawerFormStateType} from '../form-drawer'
import config from './builder/ApprovalPeriodsDrawerForm.builder'
import {ApprovalPeriodsDrawerProps, ClaimDisabilityApprovalPeriodEntity, getAbsencePeriodRange} from './types'

export const APPROVAL_PERIODS_FORM_ID = 'approvalPeriodsFormId'
export const PAYEE_INPUT_ID = 'approval-periods-drawer-payee-input-id'
export const FREQUENCY_TYPE_INPUT_ID = 'approval-periods-drawer-frequency-type-input-id'

export const ApprovalPeriodsDrawer: React.FC<ApprovalPeriodsDrawerProps> = observer(props => {
    const {krakenRulesEvaluationContext, viewStore} = props
    const {settlement, getApprovalPeriodFactory} = viewStore
    const onFormConfirm = (form: FormApi, kraken?: any, rulesEvaluationProps?: RulesEvaluationProps): void => {
        kraken.evaluate({
            ...rulesEvaluationProps?.payload,
            onSuccess: () => {
                if (form.getState().hasValidationErrors) {
                    return
                }
                const data = form.getState().values
                const formApprovalPeriods = data.settlement.settlementDetail?.approvalPeriods || []
                const approvalPeriods = settlement?.settlementDetail?.approvalPeriods || []
                const formedPayee = formApprovalPeriods?.[0]?.payee
                    ? {_uri: formApprovalPeriods?.[0]?.payee}
                    : undefined

                let resultApprovalPeriods: ClaimDisabilityApprovalPeriodEntity[] = []
                const commonAttributes = {
                    approverPerson: getCurrentUsername(),
                    dateOfStatusChange: new Date(Date.now()),
                    partialDisability: {
                        ...formApprovalPeriods?.[0]?.partialDisability,
                        currentEarningsAmount: formApprovalPeriods?.[0]?.partialDisability?.isPartialDisability
                            ? formApprovalPeriods?.[0].partialDisability.currentEarningsAmount
                            : null
                    },
                    payee: formedPayee
                }
                if (props.drawerFormStateType === DrawerFormStateType.Edit) {
                    resultApprovalPeriods = approvalPeriods.map(v => {
                        if (formApprovalPeriods?.[0]._key?.id === v._key?.id) {
                            v = {
                                ...formApprovalPeriods?.[0],
                                ...commonAttributes,
                                proofOfLossReceivedDate: formApprovalPeriods?.[0]?.proofOfLossReceivedDate ?? null,
                                cancelReason: formApprovalPeriods?.[0]?.cancelReason ?? null
                            }
                        }
                        return v
                    })
                } else {
                    resultApprovalPeriods = [
                        ...approvalPeriods,
                        {
                            ...getApprovalPeriodFactory(),
                            ...formApprovalPeriods?.[0],
                            ...commonAttributes
                        }
                    ]
                }

                viewStore.reAdjudicateSettlement({
                    ...settlement,
                    settlementDetail: {
                        ...settlement?.settlementDetail,
                        approvalPeriods: resultApprovalPeriods
                    }
                })

                viewStore.formDrawerStore.closeDrawer()
            }
        })
    }

    const addToBeginning = (
        period: ClaimDisabilityApprovalPeriodEntity,
        periods?: ClaimDisabilityApprovalPeriodEntity[]
    ): ClaimDisabilityApprovalPeriodEntity[] => {
        const approvals = cloneAndSortPeriods(toJS(periods))
        approvals.unshift(period)
        return approvals
    }

    const moveToBeginning = (
        index: number,
        periods?: ClaimDisabilityApprovalPeriodEntity[]
    ): ClaimDisabilityApprovalPeriodEntity[] => {
        const approvals = cloneAndSortPeriods(toJS(periods))
        const approvalByIndex = approvals[index]
        approvals.splice(index, 1)
        approvals.unshift(approvalByIndex)
        return approvals
    }

    const cloneAndSortPeriods = (
        periods?: ClaimDisabilityApprovalPeriodEntity[]
    ): ClaimDisabilityApprovalPeriodEntity[] => {
        return cloneDeep(periods || []).sort((a, b) =>
            dateUtils(b?.approvalPeriod?.startDate).toMoment.diff(dateUtils(a?.approvalPeriod?.startDate).toMoment)
        )
    }

    const adjustFrequencyType = (formValues, form, basePath: string): void => {
        const startDate = formValues.settlement.settlementDetail.approvalPeriods[0].approvalPeriod.startDate
        const endDate = formValues.settlement.settlementDetail.approvalPeriods[0].approvalPeriod.endDate
        const {lossType} = viewStore.loss || {}

        const calculateFrequency = (unit: 'week' | 'month', frequencyType: 'WEEKLY' | 'MONTHLY'): void => {
            const diff = dateUtils(endDate).toMoment.diff(dateUtils(startDate).toMoment, unit)
            form.change(`${basePath}.frequencyType`, diff >= 1 ? frequencyType : 'SINGLEOCCURRENCE')
        }

        switch (lossType) {
            case DISABILITY_LOSS_TYPES.STD:
                calculateFrequency('week', 'WEEKLY')
                break
            case DISABILITY_LOSS_TYPES.LTD:
                calculateFrequency('month', 'MONTHLY')
                break
            default:
                form.change(`${basePath}.frequencyType`, 'SINGLEOCCURRENCE')
        }
    }

    const adjustConditionalValues = (name, basePath: string, value, form): void => {
        if (name === `${basePath}.partialDisability.isPartialDisability` && value) {
            form.change(`${basePath}.frequencyType`, 'SINGLEOCCURRENCE')
        }

        if (name === `${basePath}.partialDisability.isPartialDisability` && !value) {
            form.change(`${basePath}.partialDisability.currentEarningsAmount`, undefined)
        }

        if (name === `${basePath}.approvalStatus` && value !== 'Cancelled') {
            form.change(`${basePath}.cancelReason`, undefined)
        }
    }

    const initApprovalPeriods =
        props.drawerFormStateType === DrawerFormStateType.Edit
            ? moveToBeginning(props.approvalPeriodIndex, settlement?.settlementDetail?.approvalPeriods)
            : addToBeginning(getApprovalPeriodFactory(), settlement?.settlementDetail?.approvalPeriods)

    const getPayeeOptions = (): OptionValue[] => {
        const memberRegistryTypeId = settlement?.settlementLossInfo?.memberRegistryTypeId
        const payeeList = [...viewStore.partyInformationStore.claimPartyStore.parties].map(v => v.customer) as (
            | IndividualCustomer
            | OrganizationCustomer
        )[]
        return collectAndSortPayee(payeeList, memberRegistryTypeId) as OptionValue[]
    }

    const getFrequencyTypeOptions = (): string[] => {
        switch (viewStore.loss?.lossType) {
            case 'LTD':
                return ['SINGLEOCCURRENCE', 'MONTHLY']
            case 'STD':
                return ['SINGLEOCCURRENCE', 'WEEKLY']
            default:
                return ['SINGLEOCCURRENCE']
        }
    }

    const propsCustomizerMap: PropsCustomizerMap = {
        [PAYEE_INPUT_ID]: inputProps => ({
            ...inputProps,
            options: getPayeeOptions()
        }),
        [FREQUENCY_TYPE_INPUT_ID]: inputProps => ({
            ...inputProps,
            body: {code: getFrequencyTypeOptions()}
        })
    }

    const eliminationPeriodOverrideEndDate = settlement?.settlementDetail?.eliminationPeriodOverrideEndDate
        ? dateUtils(settlement?.settlementDetail?.eliminationPeriodOverrideEndDate).render
        : ''
    const eliminationPeriodThroughDate = settlement?.settlementResult?.eliminationPeriodThroughDate
        ? dateUtils(settlement?.settlementResult?.eliminationPeriodThroughDate).render
        : ''
    const initialValues = {
        lossType: viewStore.loss?.lossType,
        isPWOnly: 'false',
        eliminationPeriodThroughDate: eliminationPeriodOverrideEndDate || eliminationPeriodThroughDate,
        absencePeriodRange: getAbsencePeriodRange(settlement?.settlementAbsenceInfo?.absencePeriods || []),
        settlement: {
            ...settlement,
            settlementDetail: {
                ...settlement?.settlementDetail,
                approvalPeriods: initApprovalPeriods.map(period => ({
                    ...period,
                    payee: period?.payee?._uri
                }))
            }
        } as ClaimDisabilityLossSettlement
    }

    const handleChange = (name, value, form) => {
        const basePath = 'settlement.settlementDetail.approvalPeriods[0]'

        if (name === `${basePath}.approvalPeriod.startDate` || name === `${basePath}.approvalPeriod.endDate`) {
            const formValues = form.getState().values

            const isPartial =
                formValues.settlement.settlementDetail.approvalPeriods[0].partialDisability?.isPartialDisability

            if (!isPartial || isPartial === 'false') {
                adjustFrequencyType(formValues, form, basePath)
            }
        }
        adjustConditionalValues(name, basePath, value, form)
    }

    return (
        <KrakenEvaluationContext.Provider value={krakenRulesEvaluationContext}>
            <UIEngine
                {...editorConfig}
                formId={APPROVAL_PERIODS_FORM_ID}
                config={config}
                initialValues={initialValues}
                propsCustomizerMap={propsCustomizerMap}
                validationRuleEngineConfiguration={{
                    serviceDeclarations: krakenRulesEvaluationContext.serviceDeclarations || [],
                    transformResultsDeclarations: []
                }}
                formInfo={{skipDisabledFieldsValidation: true}}
            >
                <ComponentWithFormSpy subscription={{modified: true}} onFormChange={handleChange} />
                <KrakenFormEvaluation />
                <DrawerActions
                    handleFormCancel={() => {
                        viewStore.formDrawerStore.closeDrawer()
                    }}
                    handleFormConfirm={onFormConfirm}
                    drawerFormState={props.drawerFormStateType}
                    isKrakenValidation
                    isLoading={viewStore.actionsStore.isRunning(READJUDICATE_SETTLEMENT)}
                    labels={{
                        editButtonLabel: t('cap-core:save'),
                        createButtonLabel: t('cap-core:save')
                    }}
                />
            </UIEngine>
        </KrakenEvaluationContext.Provider>
    )
})
