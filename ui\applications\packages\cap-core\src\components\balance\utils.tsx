/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {CapBalance} from '@eisgroup/cap-financial-models'
import * as MAPI from '@eisgroup/models-api'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {ClaimParty} from '@eisgroup/cap-services'
import {getPaymentMethodIdAndCheckAddressId, validatePaymentMethod} from '../payments/Utils'
import CapBaseBalanceItemAllocationReduction = CapBalance.CapBalanceItemAllocationReduction
import CapBaseBalanceItemAllocationTax = CapBalance.CapBalanceItemAllocationTax
import CapBaseBalanceItemAllocationAddition = CapBalance.CapBalanceItemAllocationAddition
import CapFinancialAdjustmentWithholdingEntity = CapEventCase.CapFinancialAdjustmentWithholdingEntity

type SortAllocationType =
    | CapBaseBalanceItemAllocationTax[]
    | CapBaseBalanceItemAllocationAddition[]
    | CapBaseBalanceItemAllocationReduction[]

export const sortBalanceAllocation = (typeOrder: string[], allocationArray: SortAllocationType, typeName: string) => {
    return allocationArray.sort((a, b) => {
        const indexA = typeOrder.indexOf(a[typeName])
        const indexB = typeOrder.indexOf(b[typeName])

        if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB
        }
        if (indexA !== -1) {
            return -1
        }
        if (indexB !== -1) {
            return 1
        }
        return 0
    })
}

export const getSortedScheduledAllocation = allocation => {
    const allocationReductionTypeOrder = allocation.allocationReductions?.map(item => item.reductionSubType) || []
    const allocationTaxTypeOrder = allocation.allocationTaxes?.map(item => item.reductionSubType) || []
    const allocationAdditionTypeOrder = allocation.allocationAdditions?.map(item => item.reductionSubType) || []
    return (
        allocation.scheduledAllocations?.map(v => {
            return {
                ...v,
                allocationReductions: sortBalanceAllocation(
                    allocationReductionTypeOrder,
                    v.allocationReductions || [],
                    'reductionSubType'
                ),
                allocationTaxes: sortBalanceAllocation(allocationTaxTypeOrder, v.allocationTaxes || [], 'taxSubType'),
                allocationAdditions: sortBalanceAllocation(
                    allocationAdditionTypeOrder,
                    v.allocationAdditions || [],
                    'additionSubType'
                )
            }
        }) || []
    )
}

export const getWithholdingLossSources = (withholdings: CapFinancialAdjustmentWithholdingEntity[]) => {
    let lossSources = [] as MAPI.ExternalLink[]
    withholdings?.forEach(v => {
        lossSources = lossSources.concat(v.lossSources)
    })
    return lossSources
}

export const getPaymentMethodIdAndCheckAddressIdValidationMsg = (
    payeeLink: string,
    source: string,
    customerList: ClaimParty[],
    store
) => {
    const {paymentMethodId, checkAddressId, isPayeeMember} = getPaymentMethodIdAndCheckAddressId(
        store,
        source,
        payeeLink
    )
    const claimParty = customerList.find(party => payeeLink.includes(party?.customer?._key?.rootId ?? ''))
    return validatePaymentMethod(paymentMethodId, checkAddressId, isPayeeMember, claimParty)
}
