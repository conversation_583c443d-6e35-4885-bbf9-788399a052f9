Common.projectSettings

libraryDependencies += "com.eisgroup.dxp" % "integrationeisgenesiscemcustomersorganization" % Common.dxpCoreVersion
lazy val integrationEisGenesisCapRef = LocalProject("integrationEisGenesisCap")
lazy val integrationEisGenesisCapOpenlRef = LocalProject("integrationEisGenesisCapOpenl")

lazy val capAdjuster = project.in(file("."))
  .enablePlugins(PlayMinimalJava, DxpCodegenPlugin)
  .dependsOn(
    integrationEisGenesisCapRef,
    integrationEisGenesisCapOpenlRef
  )