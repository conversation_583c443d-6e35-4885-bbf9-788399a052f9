/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.eventcase;

import java.util.List;
import java.util.concurrent.CompletionStage;

import com.google.inject.ImplementedBy;

import cap.adjuster.services.eventcase.dto.CapGenericLoss;
import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import cap.adjuster.services.eventcase.impl.CapAdjusterEventCasesServiceImpl;

/**
 * Service that provides methods for CAP Event Case
 */
@ImplementedBy(CapAdjusterEventCasesServiceImpl.class)
public interface CapAdjusterEventCasesService {

    /**
     * Get absence losses related to event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     *
     * @return list of absence losses related to event case
     */
    CompletionStage<List<CapGenericLoss>> getAbsenceLosses(String rootId, Integer revisionNo);

    /**
     * Get life claims related to event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     *
     * @return list of life claims related to event case
     */
    CompletionStage<List<CapGenericLoss>> getLifeClaims(String rootId, Integer revisionNo);

    /**
     * Get life losses related to event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     *
     * @return list of life losses related to event case
     */
    CompletionStage<List<CapGenericLoss>> getLifeLosses(String rootId, Integer revisionNo);

    /**
     * Get life settlements related to event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     *
     * @return list of life settlements related to event case
     */
    CompletionStage<List<CapGenericSettlement>> getLifeSettlements(String rootId, Integer revisionNo);

    /**
     * Get settlements related to event case
     *
     * @param rootId     event case identifier
     * @param revisionNo event case revision number
     *
     * @return list of settlements related to event case
     */
    CompletionStage<List<CapGenericSettlement>> getSettlements(String rootId, Integer revisionNo);
}
