/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {ModelDefinition} from '@eisgroup/models-api'

import {ChangeHistoryModelNames} from './types'

/**
 * Updates models to include references and external links as attributes.
 * This simplifies attribute search in history table.
 */
export function patchModels<T extends Record<ChangeHistoryModelNames, ModelDefinition.DomainModel>>(models: T): T {
    return Object.fromEntries(Object.entries(models).map(([key, entry]) => [key, patchModel(entry)])) as T
}

function patchModel(model: ModelDefinition.DomainModel): ModelDefinition.DomainModel {
    return {
        ...model,
        types: Object.fromEntries(
            Object.entries(model.types).map(([key, entry]) => [
                key,
                {
                    ...entry,
                    attributes: {
                        ...entry.attributes,
                        ...Object.fromEntries(
                            Object.entries(entry.extLinks).map(([linkKey, linkEntry]) => [
                                linkKey,
                                convertExternalLinkToAttribute(linkEntry)
                            ])
                        ),
                        ...Object.fromEntries(
                            Object.entries(entry.references).map(([referenceKey, referenceEntry]) => [
                                referenceKey,
                                convertEntityReferenceToAttribute(referenceEntry)
                            ])
                        )
                    }
                }
            ])
        )
    }
}

function convertExternalLinkToAttribute(extLink: ModelDefinition.ExternalLink): ModelDefinition.Attribute {
    return {
        ...extLink,
        type: {
            type: 'STRING'
        }
    }
}

function convertEntityReferenceToAttribute(
    reference: ModelDefinition.ModeledEntityReference
): ModelDefinition.Attribute {
    return {
        ...reference,
        type: {
            type: reference.type
        }
    }
}
