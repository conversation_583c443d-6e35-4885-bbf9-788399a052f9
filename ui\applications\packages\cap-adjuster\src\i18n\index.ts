/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

/**
 * NOTE: This file has been automatically generated by @eisgroup/infra-scripts.
 * Avoid manual edits as changes may be overwritten during the next generation process.
 */

import {Localization} from '@eisgroup/i18n'

import {enMT} from './cap-adjuster-i18n.en_MT'
import {enUS} from './cap-adjuster-i18n.en'

export const resources: Localization.ResourceBundle[] = [enMT, enUS]
