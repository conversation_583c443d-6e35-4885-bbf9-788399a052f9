{"name": "@testing-library/user-event", "version": "10.4.1", "description": "Fire events the same way the user does", "main": "dist/index.js", "typings": "typings/index.d.ts", "keywords": ["react-testing-library", "dom-testing-library", "react", "testing"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=10", "npm": ">=6"}, "repository": {"type": "git", "url": "https://github.com/testing-library/user-event"}, "bugs": {"url": "https://github.com/testing-library/user-event/issues"}, "homepage": "https://github.com/testing-library/user-event#readme", "files": ["dist", "typings"], "scripts": {"build": "kcd-scripts build", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate"}, "dependencies": {"@babel/runtime": "^7.10.2"}, "devDependencies": {"@testing-library/dom": "^7.8.0", "@testing-library/jest-dom": "^5.9.0", "@testing-library/react": "^10.0.5", "kcd-scripts": "^6.2.0", "react": "^16.13.1", "react-dom": "^16.13.1"}, "peerDependencies": {"@testing-library/dom": ">=5"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js", "rules": {"jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/tabindex-no-positive": "off", "no-return-assign": "off"}}, "eslintIgnore": ["node_modules", "coverage", "dist"]}