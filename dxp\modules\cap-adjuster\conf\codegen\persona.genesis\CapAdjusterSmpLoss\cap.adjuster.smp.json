{"swagger": "2.0", "x-dxp-spec": {"imports": {"smp": {"schema": "integration.cap.smp.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: SMP Loss API", "version": "1", "title": "CAP Adjuster: SMP Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-smp", "description": "CAP Adjuster: SMP Loss API"}], "paths": {"/losses-smp/{rootId}/{revisionNo}": {"get": {"summary": "Search SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/caploss/CapSmp/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/rules/bundle": {"post": {"summary": "Rules bundle for SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/draft": {"post": {"summary": "Init SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp": {"post": {"summary": "Create SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-smp"]}, "put": {"summary": "Update SMP loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapSmp/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/closeLoss": {"post": {"summary": "Close SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/command/closeLoss", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/reopenLoss": {"post": {"summary": "Reopen SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/command/reopenLoss", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/submitLoss": {"post": {"summary": "Submit SMP loss", "x-dxp-path": "/api/caploss/CapSmp/v1/command/submitLoss", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/setLossSubStatus": {"post": {"summary": "Set SMP loss sub-status", "x-dxp-path": "/api/caploss/CapSmp/v1/command/setLossSubStatus", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/updateReturnToWork": {"post": {"summary": "Updates Return To Work Entity", "x-dxp-path": "/api/caploss/CapSmp/v1/command/updateReturnToWork", "tags": ["/cap-adjuster/v1/losses-smp"]}}, "/losses-smp/retrieve-closure-open-items": {"post": {"summary": "Retrieve SMP loss closure open items", "x-dxp-path": "/api/caploss/CapSmp/v1/transformation/absenceClaimClosureToOpenItems", "tags": ["/cap-adjuster/v1/losses-smp"]}}}}