/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {LookupLabel} from '@eisgroup/react-components'
import {useTranslate} from '@eisgroup/i18n'
import {observer} from 'mobx-react'
import {CapBalance} from '@eisgroup/cap-financial-models'
import {MoneyFormat, BALANCE_EXPAND_LOOKUP_LABEL, ReductionType} from '../..'

import CapBalanceItemActualAllocationEntity = CapBalance.CapBalanceItemActualAllocationEntity
import CapBalanceItemScheduledAllocationEntity = CapBalance.CapBalanceItemScheduledAllocationEntity

export interface BalanceListCommonExpandRowProps {
    allocation: CapBalanceItemActualAllocationEntity | CapBalanceItemScheduledAllocationEntity
}

export const BalanceListCommonExpandRow: React.FC<BalanceListCommonExpandRowProps> = observer(props => {
    const {t} = useTranslate()
    const formatNegativeAmount = (amount: number) => {
        if (amount === 0) {
            return 0
        }
        return -amount
    }

    const resolveReductionSubType = (reductionType: string, reductionSubType?: string) => {
        if (reductionType === ReductionType.OFFSET) {
            return <LookupLabel lookup='OffsetType' code={reductionSubType} />
        }
        if (reductionType === ReductionType.DEDUCTION) {
            return <LookupLabel lookup='DeductionType' code={reductionSubType} />
        }

        return reductionSubType
    }

    return (
        <>
            {props.allocation.allocationReductions &&
                props.allocation.allocationReductions.map(reduction => {
                    const reductionSubType = resolveReductionSubType(
                        reduction.reductionType,
                        reduction.reductionSubType
                    )
                    return (
                        <div>
                            <label>
                                {reductionSubType} {t(`cap-core:reduction_type_${reduction.reductionType}`)}
                            </label>
                            <MoneyFormat value={formatNegativeAmount(reduction?.appliedAmount?.amount || 0)} />
                        </div>
                    )
                })}
            {props.allocation.allocationTaxes &&
                props.allocation.allocationTaxes.map(tax => {
                    const taxState = <LookupLabel lookup='TaxState' code={tax.taxState} emptyLabel='' />
                    return (
                        <div>
                            <span className={BALANCE_EXPAND_LOOKUP_LABEL}>
                                {taxState}{' '}
                                <LookupLabel
                                    lookup={tax.taxType === 'FICA' ? 'FicaType' : 'TaxType'}
                                    code={tax.taxType === 'FICA' ? tax.taxSubType : tax.taxType}
                                />
                            </span>
                            <MoneyFormat value={formatNegativeAmount(tax?.appliedAmount?.amount || 0)} />
                        </div>
                    )
                })}
            {props.allocation.allocationAdditions &&
                props.allocation.allocationAdditions.map(addition => {
                    return (
                        <div>
                            <label>
                                {addition.additionSubType} {t(`cap-core:addition_type_${addition.additionType}`)}
                            </label>
                            <MoneyFormat value={addition?.appliedAmount?.amount || 0} />
                        </div>
                    )
                })}
        </>
    )
})
