/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.leaveloss;

import cap.adjuster.services.financial.dto.CapPayment;
import cap.adjuster.services.leaveloss.dto.CapLeaveSettlement;
import cap.adjuster.services.leaveloss.impl.CapAdjusterLeaveLossServiceImpl;
import com.google.inject.ImplementedBy;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * Service that provides methods for CAP LEAVE
 */
@ImplementedBy(CapAdjusterLeaveLossServiceImpl.class)
public interface CapAdjusterLeaveLossService {

    /**
     * Get payments associated with leave loss
     *
     * @param rootId leave loss identifier
     * @param revisionNo leave loss revision number
     * @return list of payments related to absence loss
     */
    CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo);

    /**
     * Get settlements associated with leave loss
     *
     * @param rootId     leave loss identifier
     * @param revisionNo leave loss revision number
     * @return leave loss settlements
     */
    CompletionStage<List<CapLeaveSettlement>> getSettlements(String rootId, Integer revisionNo);
}
