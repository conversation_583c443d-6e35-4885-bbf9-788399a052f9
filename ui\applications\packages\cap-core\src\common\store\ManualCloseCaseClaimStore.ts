/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {Observable} from 'rxjs'
import {Either, Right} from '@eisgroup/data.either'
import {action, observable, runInAction, toJS} from 'mobx'
import {errorToRxResult} from '@eisgroup/common'
import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import moment, {Moment} from 'moment'
import type {BusinessTypes, CapEventCase} from '@eisgroup/cap-event-case-models'
import {CapSpecialHandling} from '@eisgroup/cap-models'
import {
    CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo,
    CapAdjusterEventCaseCapEventCaseCapEventCaseClosureOpenItemsOutput,
    CapAdjusterEventCaseCapEventCaseCapEventCaseCoverageOpenItemInfo
} from '@eisgroup/cap-gateway-client'
import {
    LossParamsWithModelName,
    backofficeWorkService,
    claimLeaveService,
    claimLtdService,
    claimSmpService,
    claimStdService,
    claimWrapperService,
    eventCaseService,
    specialHandlingService,
    CapGenericLoss,
    dateUtils,
    dateComparison,
    WorkQueueDetails,
    OrgPerson,
    backOfficeWorkflowService,
    WorkflowCase,
    WorkflowTask
} from '@eisgroup/cap-services'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'
import {EntityLink} from '../../utils/EntityLink'
import {CapAdjusterLossIdentifierRequest, ClosureToOpenItemsOutputsType, DefaultQuery, ManagersInfo} from '../Types'
import {ClaimLossTypeToModelName, DisabilityClaimTypesMap, MANUAL_CLOSE_FROM} from '../constants'
import CapSpecialHandlingEntity = CapSpecialHandling.CapSpecialHandlingEntity

export const LOAD_ACTIVE_TASK_LIST = 'loadActiveTaskList'
export const LOAD_CLOSURE_OPEN_ITEMS = 'loadClosureOpenItems'

export interface ManualCloseCaseClaimStore extends BaseRootStore {
    /**
     * Manual close case claim data list
     */
    activeTaskList: WorkflowTask[]
    managersInfo: ManagersInfo
    allClaimList: BusinessTypes.CapLoss[]
    specialHandlingsInfo: DefaultQuery
    claimClosureOpenItemsOutputs: ClosureToOpenItemsOutputsType
    /**
     * Action to search manual close case claim data list.
     */
    loadActiveTaskList: (entityUris: string[]) => void
    setAllClaimList: (claims: BusinessTypes.CapLoss[]) => void
    retrieveClosureOpenItems: (params: CapAdjusterLossIdentifierRequest, fromComponent: string) => void
    getClaimManager: (claims: CapGenericLoss[]) => void
    getScheduledPaymentInfos: (
        scheduledPaymentInfos: CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[],
        fromComponent: string
    ) => CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
    getClosestPayment: (
        payments: CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[],
        today: Moment
    ) => CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
    actionsAfterLoadClosureOpenItems: (response: ClosureToOpenItemsOutputsType, fromComponent: string) => void
    getClaimManagerUserInformation: (userId: string) => RxResult<OrgPerson[]>
    getClaimManagerQueueInformation: (queueCd: string[]) => RxResult<WorkQueueDetails[]>
    loadSpecialHandling: (lossParams: LossParamsWithModelName) => Promise<any>
    getSpecialHandling: (claimList: CapEventCase.CapEventCaseClaimOpenItemInfo[]) => void
}

export class ManualCloseCaseClaimStoreImpl extends BaseRootStoreImpl implements ManualCloseCaseClaimStore {
    constructor(rootStore?: BaseRootStore) {
        super()
        if (rootStore) {
            this.actionsStore = rootStore.actionsStore
        }
    }

    @observable activeTaskList: WorkflowTask[] = []

    @observable managersInfo: ManagersInfo

    @observable allClaimList: BusinessTypes.CapLoss[] = []

    @observable specialHandlingsInfo: DefaultQuery

    @observable claimClosureOpenItemsOutputs: ClosureToOpenItemsOutputsType

    @action
    setAllClaimList = (claims: BusinessTypes.CapLoss[]) => {
        this.allClaimList = claims
    }

    @action
    loadActiveTaskList = (entityUris: string[]) => {
        this.callService(
            backOfficeWorkflowService.getActiveTasks(entityUris),
            response => {
                runInAction(() => {
                    this.activeTaskList = response.sort((a, b) =>
                        (a.entityRefNo || '').localeCompare(b.entityRefNo || '')
                    )
                })
            },
            LOAD_ACTIVE_TASK_LIST
        )
    }

    getClosestPayment(
        payments: CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[],
        today: Moment
    ) {
        if (!payments.length) {
            return [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
        }
        const payees = [] as string[]
        const closestPayments = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]

        payments.forEach(v => {
            if (v.payeeDisplay && !payees.includes(v.payeeDisplay)) {
                payees.push(v.payeeDisplay)
            }
        })
        for (let i = 0; i < payees.length; i++) {
            const filterPayment = payments.filter(v => v.payeeDisplay === payees[i])
            const closestPayment = filterPayment.reduce((prev, curr) => {
                const currentDiff = moment(dateUtils(curr.paymentDate).toMoment).utc().diff(today, 'days')
                const prevDiff = moment(dateUtils(prev.paymentDate).toMoment).utc().diff(today, 'days')
                if (Math.abs(currentDiff) < Math.abs(prevDiff)) {
                    return curr
                }
                return prev
            }, filterPayment[0])
            closestPayments.push(closestPayment)
        }

        return closestPayments
    }

    getScheduledPaymentInfos(
        scheduledPaymentInfos: CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[],
        fromComponent: string
    ) {
        switch (fromComponent) {
            case MANUAL_CLOSE_FROM.LEAVE:
            case MANUAL_CLOSE_FROM.LTD:
            case MANUAL_CLOSE_FROM.SMP:
            case MANUAL_CLOSE_FROM.STD:
                return this.getClosestPayment(scheduledPaymentInfos, moment.utc())
            case MANUAL_CLOSE_FROM.LIFE_CLAIM:
                return (
                    scheduledPaymentInfos?.sort((a, b) =>
                        dateComparison(a.paymentDate).isBefore(b.paymentDate) ? 1 : -1
                    ) ?? []
                )
            case MANUAL_CLOSE_FROM.CASE: {
                let smpPayments = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
                let ltdPayments = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
                let stdPayments = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
                let leavePayments = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
                const lifePayments = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
                let result = [] as CapAdjusterEventCaseCapEventCaseCapEventCaseScheduledPaymentOpenItemInfo[]
                scheduledPaymentInfos?.forEach(v => {
                    const claimNo = v.claimNumber || ''
                    if (claimNo.indexOf(DisabilityClaimTypesMap.Leave) > -1) {
                        leavePayments.push(v)
                    }
                    if (claimNo.indexOf(DisabilityClaimTypesMap.LTD) > -1) {
                        ltdPayments.push(v)
                    }
                    if (claimNo.indexOf(DisabilityClaimTypesMap.STD) > -1) {
                        stdPayments.push(v)
                    }
                    if (claimNo.indexOf('SMD') > -1) {
                        smpPayments.push(v)
                    }
                    if (
                        claimNo.indexOf(DisabilityClaimTypesMap.Leave) === -1 &&
                        claimNo.indexOf(DisabilityClaimTypesMap.LTD) === -1 &&
                        claimNo.indexOf(DisabilityClaimTypesMap.STD) === -1 &&
                        claimNo.indexOf('SMD') === -1
                    ) {
                        lifePayments.push(v)
                    }

                    ltdPayments = this.getClosestPayment(ltdPayments, moment.utc())
                    leavePayments = this.getClosestPayment(leavePayments, moment.utc())
                    stdPayments = this.getClosestPayment(stdPayments, moment.utc())
                    smpPayments = this.getClosestPayment(smpPayments, moment.utc())
                    result = [...lifePayments, ...ltdPayments, ...leavePayments, ...stdPayments, ...smpPayments]
                })
                return result
            }
            default:
                return scheduledPaymentInfos
        }
    }

    actionsAfterLoadClosureOpenItems = (response: ClosureToOpenItemsOutputsType, fromComponent: string) => {
        const claimInfos =
            (response.output as CapAdjusterEventCaseCapEventCaseCapEventCaseClosureOpenItemsOutput)?.claimInfos?.filter(
                v => v.claimLink?.indexOf('PaymentDefinition') === -1
            ) || []

        this.claimClosureOpenItemsOutputs = {
            ...response,
            output: {
                ...response.output,
                claimInfos: claimInfos.sort((a, b) => (a.claimNumber || '').localeCompare(b.claimNumber || '')) || [],
                coverageInfos:
                    response.output?.coverageInfos?.sort((a, b) => {
                        if (fromComponent === MANUAL_CLOSE_FROM.CASE) {
                            const coverageA =
                                (a as CapAdjusterEventCaseCapEventCaseCapEventCaseCoverageOpenItemInfo).claimNumber ||
                                ''
                            const coverageB =
                                (a as CapAdjusterEventCaseCapEventCaseCapEventCaseCoverageOpenItemInfo).claimNumber ||
                                ''
                            return coverageA.localeCompare(coverageB)
                        }
                        return (a.coverageName || '').localeCompare(b.coverageName || '')
                    }) || [],
                actualPaymentInfos:
                    response.output?.actualPaymentInfos?.sort((a, b) =>
                        dateComparison(a.paymentDate).isBefore(b.paymentDate) ? 1 : -1
                    ) || [],
                scheduledPaymentInfos: this.getScheduledPaymentInfos(
                    response.output?.scheduledPaymentInfos ?? [],
                    fromComponent
                ),
                balanceInfos:
                    response.output?.balanceInfos?.sort((a, b) =>
                        (a.payeeDisplay || '').localeCompare(b.payeeDisplay || '')
                    ) || [],
                apInfos:
                    (
                        response.output as CapAdjusterEventCaseCapEventCaseCapEventCaseClosureOpenItemsOutput
                    )?.apInfos?.sort((a, b) => {
                        if (fromComponent === MANUAL_CLOSE_FROM.CASE) {
                            return (a.claimNumber || '').localeCompare(b.claimNumber || '')
                        }
                        return dateComparison(a.apPeriod?.startDate).isBefore(b.apPeriod?.startDate) ? 1 : -1
                    }) || []
            }
        } as ClosureToOpenItemsOutputsType
        this.getClaimManager(claimInfos)
        this.getSpecialHandling(claimInfos as CapEventCase.CapEventCaseClaimOpenItemInfo[])
    }

    @action
    retrieveClosureOpenItems = (params: CapAdjusterLossIdentifierRequest, fromComponent: string) => {
        switch (fromComponent) {
            case MANUAL_CLOSE_FROM.LIFE_CLAIM:
                this.callService(
                    claimWrapperService.retrieveClosureOpenItems(params),
                    response => {
                        runInAction(() => {
                            this.actionsAfterLoadClosureOpenItems(response, fromComponent)
                        })
                    },
                    LOAD_CLOSURE_OPEN_ITEMS
                )
                break
            case MANUAL_CLOSE_FROM.CASE:
                this.callService(
                    eventCaseService.retrieveClosureOpenItems(params),
                    response => {
                        runInAction(() => {
                            this.actionsAfterLoadClosureOpenItems(response, fromComponent)
                        })
                    },
                    LOAD_CLOSURE_OPEN_ITEMS
                )
                break
            case MANUAL_CLOSE_FROM.STD:
                this.callService(
                    claimStdService.retrieveClosureOpenItems(params),
                    response => {
                        runInAction(() => {
                            this.actionsAfterLoadClosureOpenItems(response, fromComponent)
                        })
                    },
                    LOAD_CLOSURE_OPEN_ITEMS
                )
                break
            case MANUAL_CLOSE_FROM.LTD:
                this.callService(
                    claimLtdService.retrieveClosureOpenItems(params),
                    response => {
                        runInAction(() => {
                            this.actionsAfterLoadClosureOpenItems(response, fromComponent)
                        })
                    },
                    LOAD_CLOSURE_OPEN_ITEMS
                )
                break
            case MANUAL_CLOSE_FROM.SMP:
                this.callService(
                    claimSmpService.retrieveClosureOpenItems(params),
                    response => {
                        runInAction(() => {
                            this.actionsAfterLoadClosureOpenItems(response, fromComponent)
                        })
                    },
                    LOAD_CLOSURE_OPEN_ITEMS
                )
                break
            case MANUAL_CLOSE_FROM.LEAVE:
                this.callService(
                    claimLeaveService.retrieveClosureOpenItems(params),
                    response => {
                        runInAction(() => {
                            this.actionsAfterLoadClosureOpenItems(response, fromComponent)
                        })
                    },
                    LOAD_CLOSURE_OPEN_ITEMS
                )
                break
            default:
                break
        }
    }

    getClaimManagerUserInformation = (userId: string) => {
        return this.call(() => backofficeWorkService.searchOrganizationalUserByUserID(userId)).flatMap(r =>
            r.fold(errorToRxResult, payload => {
                return Observable.of(Right(payload))
            })
        )
    }

    getClaimManagerQueueInformation = (queueCd: string[]): RxResult<WorkQueueDetails[]> => {
        return backOfficeWorkflowService.loadWorkQueues(queueCd)
    }

    @action
    getClaimManager = (claimList: any[]) => {
        const entityURIs = [] as string[]
        if (!claimList.length) {
            return
        }
        claimList.forEach(claim => {
            const claimId = EntityLink.from(claim.claimLink)?.rootId
            entityURIs.push(`gentity://CapLoss/${ClaimLossTypeToModelName[claim.claimType]}//${claimId}/1`)
            this.managersInfo = {...this.managersInfo, [claimId]: {}}
        })

        this.callService<WorkflowCase[]>(backOfficeWorkflowService.searchCaseByEntityURIs(entityURIs), response => {
            const caseInfos = toJS(response)
            caseInfos.forEach(async caseInfo => {
                const claimRootId = caseInfo.entityKey?.rootId as string
                if (caseInfo?.assignmentInfo?.userId) {
                    const either = await this.getClaimManagerUserInformation(caseInfo.assignmentInfo.userId).toPromise()
                    if (either.isRight) {
                        const data = either.get()
                        // TODO When userId is not empty but the result of the search API is undefined, userId is temporarily assembled into UserInfo and returned.
                        const userInfo = (
                            data[0]
                                ? toJS(data[0])
                                : {
                                      securityIdentity: caseInfo?.assignmentInfo?.userId
                                  }
                        ) as OrgPerson
                        runInAction(() => {
                            this.managersInfo = {
                                ...this.managersInfo,
                                [claimRootId]: {
                                    ...this.managersInfo[claimRootId],
                                    userInfo
                                }
                            }
                        })
                    }
                } else if (caseInfo?.assignmentInfo?.queueCd) {
                    const result = this.getClaimManagerQueueInformation([caseInfo.assignmentInfo.queueCd])
                    if (result) {
                        const queueInfo = (result[0] ? toJS(result[0]) : {}) as WorkQueueDetails
                        runInAction(() => {
                            this.managersInfo = {
                                ...this.managersInfo,
                                [claimRootId]: {
                                    ...this.managersInfo[claimRootId],
                                    queueInfo
                                }
                            }
                        })
                    }
                }
            })
        })
    }

    loadSpecialHandling = (lossParams: LossParamsWithModelName) => {
        return specialHandlingService
            .getSpecialHandling(lossParams)
            .map((result: Either<ErrorMessage, CapSpecialHandlingEntity>) => {
                if (result.isRight) {
                    return result.get()
                }
                return undefined
            })
            .toPromise()
    }

    @action
    getSpecialHandling = (claimList: CapEventCase.CapEventCaseClaimOpenItemInfo[]) => {
        const promiseList = [] as Promise<any>[]
        if (!claimList.length) {
            return
        }
        claimList.forEach(claim => {
            const claimRoot = EntityLink.from(claim.claimLink || '')
            promiseList.push(
                this.loadSpecialHandling({
                    rootId: claimRoot?.rootId,
                    revisionNo: Number(claimRoot?.revisionNo || 1),
                    modelName: ClaimLossTypeToModelName[claim.claimType || '']
                })
            )
            if (claim._key?.rootId) {
                this.specialHandlingsInfo = {...this.specialHandlingsInfo, [claim._key.rootId]: []}
            }
        })
        Promise.all(promiseList).then(response => {
            response.forEach(v => {
                const claimRootId = v && v.length ? EntityLink.from(v?.[0]?.claimLossIdentification?._uri)?.rootId : ''
                if (claimRootId) {
                    runInAction(() => {
                        this.specialHandlingsInfo = {
                            ...this.specialHandlingsInfo,
                            [claimRootId]: Object.keys(v?.[0]).filter(s => v?.[0][s] && typeof v?.[0][s] === 'boolean')
                        }
                    })
                }
            })
        })
    }
}

export const manualCloseCaseClaimStore = new ManualCloseCaseClaimStoreImpl()
