/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {observable, action, runInAction} from 'mobx'
import {claimLossService} from '@eisgroup/cap-services'
import {BaseRootStoreImpl, storeBindingFactory} from '@eisgroup/cap-core'
import {opt} from '@eisgroup/common-types'
import {CapAdjusterLossSearchResultCapLossSearchEntityResponseV3} from '@eisgroup/cap-gateway-client'

export const LOAD_INITIAL_DATA = 'loadInitialData'

export interface ClaimLossListStore {
    /**
     * Claim loss list
     */
    claimLossList: any[]
    /**
     * Initiates store with needed data
     */
    initLossListStore: () => void
    /**
     * Searches claim losses with provided search criteria
     */
    searchClaimLoss: (lossNumber: string) => void
}

export class ClaimLossListStore extends BaseRootStoreImpl implements ClaimLossListStore {
    @observable claimLossList: any[] = []

    private filterLifeLoss = (list: any[]): any[] => {
        const lossModelNameList = [
            'PremiumWaiverLoss',
            'DeathLoss',
            'AcceleratedLoss',
            'CILoss',
            'HILoss',
            'AccidentalDismembermentLoss'
        ]
        return list.filter(v => !lossModelNameList.includes(v._modelName))
    }

    @action
    initLossListStore = (): void => {
        this.callService<CapAdjusterLossSearchResultCapLossSearchEntityResponseV3>(
            claimLossService.searchClaimLoss(),
            response =>
                runInAction(() => {
                    const result = opt(response.result).orElse([])
                    this.claimLossList = this.filterLifeLoss(result).slice(0, 10)
                }),
            LOAD_INITIAL_DATA
        )
    }

    @action
    searchClaimLoss = (lossNumber: string): void => {
        this.callService<CapAdjusterLossSearchResultCapLossSearchEntityResponseV3>(
            claimLossService.searchClaimLoss(lossNumber),
            response =>
                runInAction(() => {
                    const result = opt(response.result).orElse([])
                    this.claimLossList = this.filterLifeLoss(result).slice(0, 10)
                })
        )
    }
}

export const {createViewLoader, connectToStore} = storeBindingFactory<ClaimLossListStore>(
    () => new ClaimLossListStore()
)
