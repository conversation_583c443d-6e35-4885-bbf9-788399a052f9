/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.impl;

import static core.dataproviders.impl.ContextPreservingCompletionStageFactory.completedFuture;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import javax.inject.Inject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.eisgroup.dxp.dataproviders.genesiscapeventcase.GenesisCapEventCaseDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.GenesisCapGenericSearchDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.CapDynamicSearchRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.CapPolicySearchRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.CapPolicySearchRequestDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.ClaimCapDynamicSearchResponseSuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.Claim_policy_SearchQueryTypeDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.HeaderProjectionModel_HeaderProjectionDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.SearchValueMatcherDTO;
import com.eisgroup.dxp.dataproviders.genesiscemcustomersorganization.GenesisCemCustomersOrganizationDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscemcustomersorganization.dto.ExtendedSearchOrganizationCustomerSearchQueryDTO;
import com.eisgroup.dxp.dataproviders.genesiscemcustomersorganization.dto.ExtendedSearchOrganizationCustomerSearchEntityRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscemcustomersorganization.dto.ExtendedSearchOrganizationCustomerSearchEntityRequestDTO;
import com.eisgroup.dxp.dataproviders.genesiscemcustomersorganization.dto.ExtendedSearchOrganizationCustomerSearchValueMatcherDTO;
import com.eisgroup.dxp.services.capadjusterclaimsearch.converters.CapAdjusterClaimSearchCapDynamicSearchRequestBodyApiConverter;
import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequest;
import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequestBody;
import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchClaim_SearchQueryType;
import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchSearchValueMatcher;
import com.google.common.collect.Lists;

import cap.adjuster.services.claim.CapAdjusterClaimsService;
import cap.adjuster.services.claim.converters.CapAdjusterClaimIndexResponseConverter;
import cap.adjuster.services.claim.converters.CapAdjusterClaimSLSIndexResponseConverter;
import cap.adjuster.services.claim.converters.CapAdjusterPolicyProjectionHeaderConverter;
import cap.adjuster.services.claim.dto.CapAdjusterClaimIndex;
import cap.adjuster.services.claim.dto.CapAdjusterClaimIndexResponse;
import cap.adjuster.services.claim.dto.CapAdjusterPolicyProjectionHeader;
import core.services.pagination.PageData;
import core.utils.AsyncUtils;
import dataproviders.CapClaimSLSDataProvider;
import dataproviders.common.dto.GenesisLinkDTO;
import dataproviders.common.dto.GenesisSearchResponseDTO;
import dataproviders.common.utils.GenesisPaginationUtils;
import dataproviders.common.utils.GenesisReferenceUtils;
import dataproviders.dto.CapLossDTO;

public class CapAdjusterClaimsServiceImpl implements CapAdjusterClaimsService {

    private static final String EVENT_CASE_MODEL_NAME = "CapEventCase";
    private static final String CUSTOMER_NUMBER_FIELD_NAME = "customerNumber";
    private static final Integer LIMIT_PER_PAGE = 100;

    private GenesisCapGenericSearchDataProvider claimSearchDataProvider;
    private GenesisCemCustomersOrganizationDataProvider genesisCemCustomersOrganizationDataProvider;
    private GenesisCapEventCaseDataProvider eventCaseDataProvider;
    private CapClaimSLSDataProvider<CapLossDTO> claimSLSDataProvider;
    private CapAdjusterClaimSearchCapDynamicSearchRequestBodyApiConverter<CapDynamicSearchRequestBodyDTO, CapAdjusterClaimSearchCapDynamicSearchRequestBody> requestBodyApiConverter;
    private CapAdjusterPolicyProjectionHeaderConverter<HeaderProjectionModel_HeaderProjectionDTO, CapAdjusterPolicyProjectionHeader> policyProjectionHeaderConverter;
    private CapAdjusterClaimIndexResponseConverter<ClaimCapDynamicSearchResponseSuccessBodyDTO, CapAdjusterClaimIndexResponse> claimIndexResponseConverter;
    private CapAdjusterClaimSLSIndexResponseConverter<GenesisSearchResponseDTO, CapAdjusterClaimIndexResponse> claimSLSIndexResponseConverter;
    private GenesisPaginationUtils paginationUtils;

    @Override
    public CompletionStage<CapAdjusterClaimIndexResponse> searchClaims(CapAdjusterClaimSearchCapDynamicSearchRequestBody body, String fields, PageData pageData) {
        CapDynamicSearchRequestBodyDTO requestBodyDTO = requestBodyApiConverter.convertToInternalDTO(body);
        return claimSearchDataProvider.apiCommonSearchV1ClaimPost(requestBodyDTO, fields, pageData.getLimit(), pageData.getOffset())
            .thenApply(claimIndexResponseConverter::convertToApiDTO)
            .thenCompose(response -> enrichAdditionalData(response));
    }

    /**
     * Enrich Additional Data
     *
     * @param response
     * @return
     */
    private CompletionStage<CapAdjusterClaimIndexResponse> enrichAdditionalData(CapAdjusterClaimIndexResponse response) {
        return AsyncUtils.sequence(response.result.stream()
            .filter(claimIndex -> CollectionUtils.isNotEmpty(claimIndex.capPolicyId) && !StringUtils.equals(EVENT_CASE_MODEL_NAME, claimIndex.lossModelName))
            .map(claimIndex -> searchPolicyProjectionHeaderByPolicyId(claimIndex.capPolicyId.get(0), false)
                .thenApply(policyProjectionHeader -> {
                    claimIndex.policyProjectionHeader = policyProjectionHeader;
                    return claimIndex;
                }).thenCompose(claimWithPolicy -> resolveSubjectOfClaimLink(claimWithPolicy.caseSystemId)
                    .thenApply(subjectOfClaimLink -> {
                        claimWithPolicy.subjectOfClaimLink = subjectOfClaimLink;
                        return claimWithPolicy;
                    })
                )
            )
        ).thenApply(claims -> response);

    }

    /**
     * Search Policy Projection Header By PolicyId
     *
     * @param capPolicyId
     * @param versionInd
     * @return
     */
    private CompletionStage<CapAdjusterPolicyProjectionHeader> searchPolicyProjectionHeaderByPolicyId(String capPolicyId, boolean versionInd) {
        CapPolicySearchRequestBodyDTO request = createPolicySearchRequest(capPolicyId, versionInd);
        return claimSearchDataProvider.apiCommonSearchV1ClaimPolicyProjectionHeaderPost(request,null,1,null)
            .thenApply(response -> policyProjectionHeaderConverter.convertToApiDTOs(response.body.success.result))
            .thenCompose(policyProjectionHeaders -> {
                if (CollectionUtils.isEmpty(policyProjectionHeaders)) {
                    return completedFuture(null);
                }
                CapAdjusterPolicyProjectionHeader policyProjectionHeader = policyProjectionHeaders.get(0);
                if (StringUtils.isNotEmpty(policyProjectionHeader.masterPolicyId)) {
                     return searchPolicyProjectionHeaderByPolicyId(policyProjectionHeader.masterPolicyId, true)
                        .thenCompose(masterPolicyHeader -> {
                            if (masterPolicyHeader == null) {
                                return completedFuture(policyProjectionHeader);
                            }
                            policyProjectionHeader.masterPolicyProductCd = masterPolicyHeader.productCd;
                            policyProjectionHeader.masterPolicyNumber = masterPolicyHeader.policyNumber;
                            return resolveCustomerNumber(masterPolicyHeader.insuredRegistryTypeIds.get(0))
                                .thenApply(orgCustomerNumber -> {
                                    policyProjectionHeader.orgCustomerNumber = orgCustomerNumber;
                                    return policyProjectionHeader;
                                });
                        });
                }
                return resolveCustomerNumber(policyProjectionHeader.insuredRegistryTypeIds.get(0))
                    .thenApply(orgCustomerNumber -> {
                        policyProjectionHeader.orgCustomerNumber = orgCustomerNumber;
                        return policyProjectionHeader;
                    });
            });

    }

    /**
     * Resolve Subject Of Claim Link from Event Case
     *
     * @param eventCaseLink
     * @return
     */
    private CompletionStage<String> resolveSubjectOfClaimLink(String eventCaseLink) {
        GenesisLinkDTO linkDTO = new GenesisLinkDTO(eventCaseLink);
        String rootId = GenesisReferenceUtils.getTargetId(linkDTO);
        Integer version = GenesisReferenceUtils.getVersion(linkDTO) != null ? GenesisReferenceUtils.getVersion(linkDTO) : 1;
        return eventCaseDataProvider.apiCaplossCapEventCaseV1EntitiesRootIdRevisionNoGet(UUID.fromString(rootId), version, null, null)
            .thenApply(eventCase -> StringUtils.isEmpty(eventCase.body.success.lossDetail.subjectOfCaseLink) ? eventCase.body.success.lossDetail.subjectOfCaseRole.registryId : eventCase.body.success.lossDetail.subjectOfCaseLink);
    }

    /**
     * Resolve Customer Number
     *
     * @param registryTypeId
     * @return
     */
    private CompletionStage<String> resolveCustomerNumber(String registryTypeId) {
        ExtendedSearchOrganizationCustomerSearchEntityRequestBodyDTO request = createOrgCustomerSearchRequest(registryTypeId);
        return genesisCemCustomersOrganizationDataProvider.apiCustomerORGANIZATIONCUSTOMERV1SearchPost(request, null,false, CUSTOMER_NUMBER_FIELD_NAME, 1, null, null)
            .thenApply(response -> response.body.success.result)
            .thenApply(result -> { if (CollectionUtils.isNotEmpty(result)) {
                return result.get(0).customerNumber;
            }
            return null;
        });
    }

    /**
     * Create organization customer search request
     *
     * @param registryTypeId
     * @return
     */
    private ExtendedSearchOrganizationCustomerSearchEntityRequestBodyDTO createOrgCustomerSearchRequest(String registryTypeId) {
        ExtendedSearchOrganizationCustomerSearchEntityRequestBodyDTO request = new ExtendedSearchOrganizationCustomerSearchEntityRequestBodyDTO();
        request.body = new ExtendedSearchOrganizationCustomerSearchEntityRequestDTO();
        request.body.query = new ExtendedSearchOrganizationCustomerSearchQueryDTO();
        request.body.query.registryTypeId = new ExtendedSearchOrganizationCustomerSearchValueMatcherDTO();
        request.body.query.registryTypeId.matches = Lists.newArrayList(registryTypeId);
        return request;
    }

    /**
     * Create policy search request
     *
     * @param capPolicyId
     * @param versionInd
     * @return
     */
    private CapPolicySearchRequestBodyDTO createPolicySearchRequest(String capPolicyId, boolean versionInd) {
        CapPolicySearchRequestBodyDTO request = new CapPolicySearchRequestBodyDTO();
        request.body = new CapPolicySearchRequestDTO();
        request.body.query = new Claim_policy_SearchQueryTypeDTO();
        if (versionInd) {
            request.body.query.capPolicyVersionId = new SearchValueMatcherDTO();
            request.body.query.capPolicyVersionId.matches = Lists.newArrayList(capPolicyId);
        } else {
            request.body.query.capPolicyId = new SearchValueMatcherDTO();
            request.body.query.capPolicyId.matches = Lists.newArrayList(capPolicyId);
        }

        return request;
    }

    /**
     * @param text
     * @param claimSearchRequestBody
     * @param fields
     * @param pageData
     * @return
     *
     * @deprecated since 24.1, unneeded
     */
    @Deprecated(since = "24.1", forRemoval = true)
    @Override
    public CompletionStage<CapAdjusterClaimIndexResponse> advancedSearchClaims(String text,
                                                                               CapAdjusterClaimSearchCapDynamicSearchRequestBody claimSearchRequestBody,
                                                                                String fields, PageData pageData) {
        return singleLineSearchAllClaims(text, fields)
            .thenCompose(claimSLSResponse ->
                searchAllClaims(claimSearchRequestBody, fields)
                    .thenCompose(claimResponse -> combineClaims(claimSLSResponse, claimResponse, pageData))
            ).thenCompose(response -> enrichAdditionalData(response));
    }

    /**
     * Combine SLS result with claim result
     *
     * @param claimSLSResponse
     * @param claimResponse
     * @param pageData
     * @return
     */
    private CompletionStage<CapAdjusterClaimIndexResponse> combineClaims(CapAdjusterClaimIndexResponse claimSLSResponse, CapAdjusterClaimIndexResponse claimResponse, PageData pageData) {
        CapAdjusterClaimIndexResponse response = new CapAdjusterClaimIndexResponse();
        if (claimResponse == null) {
            claimResponse = new CapAdjusterClaimIndexResponse();
            claimResponse.count = Long.valueOf("0");
            claimResponse.result = Lists.newArrayList();
        }
        if (claimSLSResponse == null) {     //No full text search
            response.count = claimResponse.count;
            response.result = paginationUtils.propagatePaginationMetadata(pageData, claimResponse.result.stream().collect(Collectors.toList()));
            return enrichAdditionalData(response);
        } else {
            if (claimResponse == null && CollectionUtils.isNotEmpty(claimSLSResponse.result)) {    //Only full text search
                List<String> slsRootIds = claimSLSResponse.result.stream()
                    .map(claimSLS -> claimSLS.rootId)
                    .collect(Collectors.toList());

                CapAdjusterClaimSearchCapDynamicSearchRequestBody requestBody = new CapAdjusterClaimSearchCapDynamicSearchRequestBody();
                requestBody.body = new CapAdjusterClaimSearchCapDynamicSearchRequest();
                requestBody.body.query = new CapAdjusterClaimSearchClaim_SearchQueryType();
                requestBody.body.query.rootId =new CapAdjusterClaimSearchSearchValueMatcher();
                requestBody.body.query.rootId.matches = slsRootIds;

                return searchClaims(requestBody, null, pageData);
            } else {    //Both full text and parameter search
                List<CapAdjusterClaimIndex> combineResult = claimResponse.result.stream()
                    .filter(claim -> getClaimSLSIndexByRootId(claimSLSResponse, claim.rootId) != null)
                    .collect(Collectors.toList());

                response.count = Long.valueOf(combineResult.size());
                response.result = paginationUtils.propagatePaginationMetadata(pageData, combineResult.stream().collect(Collectors.toList()));
                return enrichAdditionalData(response);
            }
        }
    }

    /**
     * Get Claim SLS Index by Root id
     *
     * @param claimSLSResponse
     * @param rootId
     * @return
     */
    private CapAdjusterClaimIndex getClaimSLSIndexByRootId(CapAdjusterClaimIndexResponse claimSLSResponse, String rootId) {
        return claimSLSResponse.result.stream()
            .filter(claimSLS -> StringUtils.equals(claimSLS.rootId, rootId))
            .findFirst().orElse(null);
    }

    /**
     * Single line search all claims
     *
     * @param text
     * @return
     */
    private CompletionStage<CapAdjusterClaimIndexResponse> singleLineSearchAllClaims(String text, String fields) {
        if (StringUtils.isEmpty(text)) {
            return completedFuture(null);
        }
        return claimSLSDataProvider.singleLineSearchClaim(text, fields, new PageData(0, LIMIT_PER_PAGE, null), CapLossDTO.class)
            .thenApply(claimSLSIndexResponseConverter::convertToApiDTO)
            .thenCompose(response -> {
                if (response == null || CollectionUtils.isEmpty(response.result) || response.count <= response.result.size()) {
                    return completedFuture(response);
                }

                Integer pageCount = Math.toIntExact(response.count / response.result.size());
                if (Math.toIntExact(response.count % response.result.size()) > 0) {
                    pageCount++;
                }

                List<CompletableFuture<CapAdjusterClaimIndexResponse>> futuresList = Lists.newArrayList();
                for (int i = 1; i < pageCount; i++) {
                    futuresList.add(claimSLSDataProvider.singleLineSearchClaim(text, fields, new PageData(i * LIMIT_PER_PAGE, LIMIT_PER_PAGE, null), CapLossDTO.class)
                        .thenApply(claimSLSIndexResponseConverter::convertToApiDTO).toCompletableFuture());
                }
                return CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[futuresList.size()]))
                    .thenApply(v -> futuresList.stream().map(future -> future.join()).collect(Collectors.toList()))
                    .thenApply(resultList -> {
                        response.result.addAll(resultList.stream()
                            .flatMap(result -> result.result.stream())
                            .collect(Collectors.toList()));
                        return response;
                    });
            });
    }

    /**
     * Search all claims
     *
     * @param body
     * @param fields
     * @return
     */
    private CompletionStage<CapAdjusterClaimIndexResponse> searchAllClaims(CapAdjusterClaimSearchCapDynamicSearchRequestBody body, String fields) {
        if (body.body.query == null) {
            return completedFuture(null);
        }
        CapDynamicSearchRequestBodyDTO requestBodyDTO = requestBodyApiConverter.convertToInternalDTO(body);
        return claimSearchDataProvider.apiCommonSearchV1ClaimPost(requestBodyDTO, fields, LIMIT_PER_PAGE, null)
            .thenApply(claimIndexResponseConverter::convertToApiDTO)
            .thenCompose(response -> {
                if (response == null || CollectionUtils.isEmpty(response.result) || response.count <= response.result.size()) {
                    return completedFuture(response);
                }

                Integer pageCount = Math.toIntExact(response.count / response.result.size());
                if (Math.toIntExact(response.count % response.result.size()) > 0) {
                    pageCount++;
                }

                List<CompletableFuture<CapAdjusterClaimIndexResponse>> futuresList = Lists.newArrayList();
                for (int i = 1; i < pageCount; i++) {
                    futuresList.add(claimSearchDataProvider.apiCommonSearchV1ClaimPost(requestBodyDTO, fields, LIMIT_PER_PAGE, i * LIMIT_PER_PAGE)
                        .thenApply(claimIndexResponseConverter::convertToApiDTO).toCompletableFuture());
                }
                return CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[futuresList.size()]))
                    .thenApply(v -> futuresList.stream().map(future -> future.join()).collect(Collectors.toList()))
                    .thenApply(resultList -> {
                        response.result.addAll(resultList.stream()
                            .flatMap(result -> result.result.stream())
                            .collect(Collectors.toList()));
                        return response;
                    });
            });
    }

    @Inject
    public void setClaimSearchDataProvider(
        GenesisCapGenericSearchDataProvider claimSearchDataProvider) {
        this.claimSearchDataProvider = claimSearchDataProvider;
    }

    @Inject
    public void setRequestBodyApiConverter(
        CapAdjusterClaimSearchCapDynamicSearchRequestBodyApiConverter<CapDynamicSearchRequestBodyDTO, CapAdjusterClaimSearchCapDynamicSearchRequestBody> requestBodyApiConverter) {
        this.requestBodyApiConverter = requestBodyApiConverter;
    }

    @Inject
    public void setPolicyProjectionHeaderConverter(
        CapAdjusterPolicyProjectionHeaderConverter policyProjectionHeaderConverter) {
        this.policyProjectionHeaderConverter = policyProjectionHeaderConverter;
    }

    @Inject
    public void setGenesisCemCustomersOrganizationDataProvider(
        GenesisCemCustomersOrganizationDataProvider genesisCemCustomersOrganizationDataProvider) {
        this.genesisCemCustomersOrganizationDataProvider = genesisCemCustomersOrganizationDataProvider;
    }

    @Inject
    public void setClaimIndexResponseConverter(
        CapAdjusterClaimIndexResponseConverter<ClaimCapDynamicSearchResponseSuccessBodyDTO, CapAdjusterClaimIndexResponse> claimIndexResponseConverter) {
        this.claimIndexResponseConverter = claimIndexResponseConverter;
    }

    @Inject
    public void setEventCaseDataProvider(
        GenesisCapEventCaseDataProvider eventCaseDataProvider) {
        this.eventCaseDataProvider = eventCaseDataProvider;
    }

    @Inject
    public void setClaimSLSDataProvider(CapClaimSLSDataProvider claimSLSDataProvider) {
        this.claimSLSDataProvider = claimSLSDataProvider;
    }

    @Inject
    public void setClaimSLSIndexResponseConverter(
        CapAdjusterClaimSLSIndexResponseConverter<GenesisSearchResponseDTO, CapAdjusterClaimIndexResponse> claimSLSIndexResponseConverter) {
        this.claimSLSIndexResponseConverter = claimSLSIndexResponseConverter;
    }

    @Inject
    public void setPaginationUtils(GenesisPaginationUtils paginationUtils) {
        this.paginationUtils = paginationUtils;
    }
}
