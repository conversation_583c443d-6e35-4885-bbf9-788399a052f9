"use strict";
/*
 * Copyright © 2019 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.enUS = void 0;
exports.enUS = {
    locale: { country: 'US', language: 'en' },
    ns: 'cap-adjuster',
    resources: {}
};
//# sourceMappingURL=cap-adjuster-i18n.en.js.map