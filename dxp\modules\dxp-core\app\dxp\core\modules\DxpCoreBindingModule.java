/* Copyright © 2019 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dxp.core.modules;

import com.typesafe.config.Config;
import core.services.version.VersionService;
import dxp.core.services.DxpCoreVersionService;
import genesis.core.modules.GenesisCoreBindingModule;
import play.Environment;

public class DxpCoreBindingModule extends GenesisCoreBindingModule {

    public DxpCoreBindingModule(Environment environment, Config configuration) {
        super(environment, configuration);
    }

    @Override
    protected void configure() {
        super.configure();

        // Version service
        bind(VersionService.class).to(DxpCoreVersionService.class);
    }
}
