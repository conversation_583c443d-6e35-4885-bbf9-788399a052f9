/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementResultEntity,
    CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementRulesInput,
    CapAdjusterCiAdjudicationOpenlCapCISettlementResultEntity,
    CapAdjusterCiAdjudicationOpenlCapCISettlementRulesInput,
    CapAdjusterClaimWrapperCapClaimWrapperUpdateInput,
    CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity,
    CapAdjusterHiAdjudicationOpenlCapHISettlementResultEntity,
    CapAdjusterHiAdjudicationOpenlCapHISettlementRulesInput
} from '@eisgroup/cap-gateway-client'
import {
    CapClaimDefaultSettlementWithEditingStatus,
    claimAcceleratedSettlementService,
    claimAccidentalDismembermentSettlementService,
    claimCISettlementService,
    claimDeathSettlementService,
    claimHISettlementService,
    claimPremiumWaiverSettlementService,
    claimWrapperService
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import {Right} from '@eisgroup/data.either'
import {action, observable} from 'mobx'
import {Observable} from 'rxjs/Observable'
import {flatMap} from 'rxjs/operators'
import {INIT_SETTLEMENT, LOAD_SETTLEMENT} from '../CommonActionNames'
import {CoverageStore} from '../CoverageStore'
import {setDateRange, setInitSettlementRequestBody, setServiceLoadApi, newPeriodBySeveralType} from '../../utils'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {ActionsStore, ActionsStoreImpl} from '../ActionsStore'
import {FormDrawerStore, FormDrawerStoreImpl} from '../../../components/form-drawer'
import {SettlementModelName} from '../../constants'

export class CoverageStoreImpl extends BaseRootStoreImpl implements CoverageStore {
    actionsStore: ActionsStore

    formDrawerStore: FormDrawerStore

    @observable coverages: CapClaimDefaultSettlementWithEditingStatus[] = []

    constructor() {
        super()
        this.actionsStore = new ActionsStoreImpl()
        this.formDrawerStore = new FormDrawerStoreImpl()
    }

    loadBySettlementType(coverage: any, serviceLoadApi: any, isEdit?: boolean) {
        return this.call<any>(() => serviceLoadApi.loadSettlement(coverage._key), LOAD_SETTLEMENT)
    }

    @action
    loadCurrentSettlement = (coverage: any, isEdit?: boolean) => {
        switch (coverage._modelName) {
            case SettlementModelName.PREMIUMWAIVER_SETTLEMENT:
                return this.loadBySettlementType(coverage, claimPremiumWaiverSettlementService, isEdit)
            case SettlementModelName.DEATH_SETTLEMENT:
                return this.loadBySettlementType(coverage, claimDeathSettlementService, isEdit)
            case SettlementModelName.ACCELERATED_SETTLEMENT:
                return this.loadBySettlementType(coverage, claimAcceleratedSettlementService, isEdit)
            case SettlementModelName.ACCIDENTALDISMEMBERMENT_SETTLEMENT:
                return this.loadBySettlementType(coverage, claimAccidentalDismembermentSettlementService, isEdit)
            case SettlementModelName.CI_SETTLEMENT:
                return this.loadBySettlementType(coverage, claimCISettlementService, isEdit)
            case SettlementModelName.HI_SETTLEMENT:
            default:
                return this.loadBySettlementType(coverage, claimHISettlementService, isEdit)
        }
    }

    @action
    updateCurrentSettlementTimestamp = (coverage: any): RxResult<any> => {
        const serviceLoadApi = setServiceLoadApi(coverage)
        return this.call<any>(() => serviceLoadApi.loadSettlement(coverage._key), LOAD_SETTLEMENT)
    }

    @action
    initSettlements = (
        body: CapAdjusterClaimWrapperCapClaimWrapperUpdateInput
    ): RxResult<CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity> => {
        return this.call(
            () =>
                claimWrapperService.startClaimUpdatingFlow(body).pipe(
                    flatMap(either =>
                        either.fold(errorToRxResult, payload => {
                            return Observable.of(Right(payload))
                        })
                    )
                ),
            INIT_SETTLEMENT
        )
    }

    callServiceBySettlementType(coverage: CapClaimDefaultSettlementWithEditingStatus, serviceApi: any) {
        const setErrorMsg = e => {
            let errorMsg = e.message
            if (e.errors && e.errors.length) {
                errorMsg = e.errors?.[0]?.message
            }
            return errorMsg
        }
        const requestBody = setInitSettlementRequestBody(coverage)
        return this.call(
            () =>
                serviceApi.readjudicateSettlement(requestBody).pipe(
                    flatMap(r =>
                        (r as any).fold(
                            e =>
                                errorToRxResult({
                                    code: String(e.errors ? e.errors?.[0]?.code : e.code),
                                    message: setErrorMsg(e)
                                } as ErrorMessage),
                            payload => {
                                return Observable.of(Right(payload))
                            }
                        )
                    )
                ),
            INIT_SETTLEMENT
        )
    }

    @action
    callServiceToUpdateCoverages = (coverage: CapClaimDefaultSettlementWithEditingStatus) => {
        const period = newPeriodBySeveralType(coverage._type)
        const requiredCoverage = {
            ...coverage,
            settlementDetail: {
                ...coverage.settlementDetail,
                dateRange: coverage.settlementDetail.dateRange
                    ? setDateRange(coverage.settlementDetail.dateRange, period)
                    : null
            }
        }
        switch (coverage._modelName) {
            case SettlementModelName.PREMIUMWAIVER_SETTLEMENT:
                return this.callServiceBySettlementType(requiredCoverage, claimPremiumWaiverSettlementService)
            case SettlementModelName.DEATH_SETTLEMENT:
                return this.callServiceBySettlementType(requiredCoverage, claimDeathSettlementService)
            case SettlementModelName.ACCELERATED_SETTLEMENT:
                return this.callServiceBySettlementType(requiredCoverage, claimAcceleratedSettlementService)
            case SettlementModelName.ACCIDENTALDISMEMBERMENT_SETTLEMENT:
                return this.callServiceBySettlementType(requiredCoverage, claimAccidentalDismembermentSettlementService)
            case SettlementModelName.CI_SETTLEMENT:
                return this.callServiceBySettlementType(requiredCoverage, claimCISettlementService)
            case SettlementModelName.HI_SETTLEMENT:
            default:
                return this.callServiceBySettlementType(requiredCoverage, claimHISettlementService)
        }
    }

    @action
    updateCoverages = (coverages: any[]) => {
        this.coverages = [...coverages]
    }

    @action
    grossAmountHICalculation = (
        body: CapAdjusterHiAdjudicationOpenlCapHISettlementRulesInput
    ): RxResult<CapAdjusterHiAdjudicationOpenlCapHISettlementResultEntity> => {
        return this.call(
            () =>
                claimWrapperService.grossAmountCalculation(body).pipe(
                    flatMap(either =>
                        either.fold(errorToRxResult, payload => {
                            return Observable.of(Right(payload))
                        })
                    )
                ),
            INIT_SETTLEMENT
        )
    }

    @action
    grossAmountCICalculation = (
        body: CapAdjusterCiAdjudicationOpenlCapCISettlementRulesInput
    ): RxResult<CapAdjusterCiAdjudicationOpenlCapCISettlementResultEntity> => {
        return this.call(
            () =>
                claimWrapperService.grossAmountCICalculation(body).pipe(
                    flatMap(either =>
                        either.fold(errorToRxResult, payload => {
                            return Observable.of(Right(payload))
                        })
                    )
                ),
            INIT_SETTLEMENT
        )
    }

    @action
    grossAmountACCCalculation = (
        body: CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementRulesInput
    ): RxResult<CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementResultEntity> => {
        return this.call(() =>
            claimWrapperService.grossAmountACCCalculation(body).pipe(
                flatMap(either =>
                    either.fold(errorToRxResult, payload => {
                        return Observable.of(Right(payload))
                    })
                )
            )
        )
    }
}
