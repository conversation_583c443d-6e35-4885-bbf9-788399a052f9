/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package dataproviders.dto;

import java.math.BigDecimal;
import java.util.List;

import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.EntityKeyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.MoneyDTO;
import com.fasterxml.jackson.annotation.JsonProperty;

public class CapClaimSettlementResultDTO extends core.dataproviders.dto.PartialUpdateInternalDTO implements core.dataproviders.dto.InternalDTO {

    @JsonProperty(value = "isAutoAdjudicated")
    public Boolean isAutoAdjudicated;

    @JsonProperty(value = "remainingAmount")
    public MoneyDTO remainingAmount;

    @JsonProperty(value = "autoAdjudicatedDuration")
    public Long autoAdjudicatedDuration;

    @JsonProperty(value = "autoAdjudicatedAmount")
    public MoneyDTO autoAdjudicatedAmount;

    @JsonProperty(value = "reserve")
    public BigDecimal reserve;

    @JsonProperty(value = "_type")
    public String _type;

    @JsonProperty(value = "messages")
    public List<Object> messages;

    @JsonProperty(value = "_key")
    public EntityKeyDTO _key;

    @JsonProperty(value = "grossBenefitAmount")
    public Object grossBenefitAmount;

    @JsonProperty(value = "paidAmount")
    public MoneyDTO paidAmount;

    @JsonProperty(value = "eligibilityEvaluationCd")
    public String eligibilityEvaluationCd;

    @JsonProperty(value = "benefitAmountPerUnit")
    public MoneyDTO benefitAmountPerUnit;

    @JsonProperty(value = "formulaCalculationDetails")
    public List<Object> formulaCalculationDetails;

    @JsonProperty(value = "maxBenefitDuration")
    public String maxBenefitDuration;

    @JsonProperty(value = "planCd")
    public String planCd;

    @JsonProperty(value = "planName")
    public String planName;

    @JsonProperty(value = "coverageCd")
    public String coverageCd;

    @JsonProperty(value = "dependentRegistryId")
    public String dependentRegistryId;

    @JsonProperty(value = "grossAmountMode")
    public String grossAmountMode;

    @JsonProperty(value = "accumulatorDetails")
    public List<Object> accumulatorDetails;

    @JsonProperty(value = "paymentDetailInfo")
    public Object paymentDetailInfo;
}
