/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import core.services.ApiDTO;

public class Genesis<PERSON>ink implements ApiDTO {

    @JsonProperty("_uri")
    public String uri;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        return uri.equals(((GenesisLink) o).uri);
    }

    @Override
    public int hashCode() {
        return uri.hashCode();
    }
}
