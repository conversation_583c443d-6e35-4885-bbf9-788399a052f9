/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial;

import java.util.List;
import java.util.concurrent.CompletionStage;

import com.google.inject.ImplementedBy;

import cap.adjuster.services.financial.dto.CapFinancialPayment;
import cap.adjuster.services.financial.impl.CapAdjusterFinancialServiceImpl;

/**
 * Service that provides methods for CAP Financial
 */
@ImplementedBy(CapAdjusterFinancialServiceImpl.class)
public interface CapAdjusterFinancialService {

    /**
     * Get scheduled and actual payments by indicated case/claim
     *
     * @param rootId case/claim identifier
     * @param revisionNo case/claim revision
     * @param modelName case/claim model name
     *
     * @return list of scheduled and actual payments related to indicated case/claim
     */
    CompletionStage<List<CapFinancialPayment>> getPaymentsWithScheduled(String rootId, Integer revisionNo, String modelName);
}
