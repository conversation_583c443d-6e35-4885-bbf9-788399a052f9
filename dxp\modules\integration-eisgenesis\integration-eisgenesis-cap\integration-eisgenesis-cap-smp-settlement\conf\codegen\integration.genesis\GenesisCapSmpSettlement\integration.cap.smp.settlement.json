{"swagger": "2.0", "info": {"description": "API for CapSmpSettlement", "version": "1", "title": "CapSmpSettlement model API facade"}, "basePath": "/", "schemes": ["http"], "paths": {"/api/capsettlement/CapSmpSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/command/approveSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapApproveSettlementRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/command/closeSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/command/initSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRequestSMPSettlementAdjudicationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapSmpSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapSmpSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapSmpSettlementKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/transformation/CapSMPSettlementAdjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSMPSettlementAdjudicationInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/transformation/adjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/transformation/approveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/transformation/disapproveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/transformation/initDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapSmpSettlement/v1/transformation/readjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AdjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputs"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "CapApproveSettlementRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "userId": {"type": "string"}}, "title": "CapApproveSettlementRequest"}, "CapApproveSettlementRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapApproveSettlementRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapApproveSettlementRequestBody"}, "CapRequestSMPSettlementAdjudicationInput": {"required": ["entity"], "properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementDetailEntity"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapRequestSMPSettlementAdjudicationInput"}, "CapRequestSMPSettlementAdjudicationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRequestSMPSettlementAdjudicationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRequestSMPSettlementAdjudicationInputBody"}, "CapSMPSettlementAdjudicationInputOutputs": {"properties": {"output": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementRulesInput"}}, "title": "CapSMPSettlementAdjudicationInputOutputs"}, "CapSMPSettlementAdjudicationInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapSMPSettlementAdjudicationInputOutputs"}}, "title": "CapSMPSettlementAdjudicationInputOutputsSuccess"}, "CapSMPSettlementAdjudicationInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapSMPSettlementAdjudicationInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSMPSettlementAdjudicationInputOutputsSuccessBody"}, "CapSettlementReadjudicateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementDetailEntity"}}, "title": "CapSettlementReadjudicateInput"}, "CapSettlementReadjudicateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementReadjudicateInputBody"}, "CapSmpSettlementKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapSmpSettlementKrakenDeprecatedBundleRequest"}, "CapSmpSettlementKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSmpSettlementKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSmpSettlementKrakenDeprecatedBundleRequestBody"}, "CapSmpSettlementLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntity"}}}, "title": "CapSmpSettlementLoadHistoryResult"}, "CapSmpSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapSmpSettlementLoadHistoryResult"}}, "title": "CapSmpSettlementLoadHistoryResultSuccess"}, "CapSmpSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapSmpSettlementLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSmpSettlementLoadHistoryResultSuccessBody"}, "CapSmpSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapSmpSettlement AccessTrackInfo"}, "CapSmpSettlement_BenefitDurationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BenefitDurationEntity"}, "benefitDuration": {"type": "integer", "format": "int64", "description": "Benefit Duration defined in SMP Policy"}, "benefitDurationTypeCd": {"type": "string", "description": "Benefit Duration Type Cd defined in SMP Policy"}, "benefitDurationUnitsCd": {"type": "string", "description": "Benefit Duration Units Cd in SMP Policy"}}, "title": "CapSmpSettlement BenefitDurationEntity", "description": "Benefit Durations defined in SMP Policy."}, "CapSmpSettlement_CapApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapApprovalPeriodEntity"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapSmpSettlement CapApprovalPeriodEntity"}, "CapSmpSettlement_CapBaseDisabilityAccumulatorExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityAccumulatorExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Accumulator Unit code."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapSmpSettlement_Term"}}, "title": "CapSmpSettlement CapBaseDisabilityAccumulatorExtension", "description": "Entity for Accumulator extension for rules."}, "CapSmpSettlement_CapBaseDisabilityFormulaCalculationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaCalculationDetails"}, "calculatedResult": {"$ref": "#/definitions/Money"}, "formulaAppliedAttribute": {"type": "string", "description": "Attribute name which formula calculation applied"}, "formulaContent": {"type": "string", "description": "Formula Content."}, "formulaId": {"type": "string", "description": "Formula Id."}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityFormulaParameter"}}, "policyRoundingAmount": {"type": "string", "description": "Policy Rounding Amount"}, "policyRoundingFactorCd": {"type": "string", "description": "Policy Rounding Factor Cd"}, "roundingResult": {"$ref": "#/definitions/Money"}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}}, "title": "CapSmpSettlement CapBaseDisabilityFormulaCalculationDetails", "description": "An entity for formula calculation details."}, "CapSmpSettlement_CapBaseDisabilityFormulaParameter": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaParameter"}, "paramExtension": {"type": "object", "description": "Extension of parameter. E.g currency"}, "paramName": {"type": "string", "description": "Parameter name."}, "paramValue": {"type": "number", "description": "Value of parameter."}}, "title": "CapSmpSettlement CapBaseDisabilityFormulaParameter", "description": "An entity for formula parameter."}, "CapSmpSettlement_CapBaseDisabilityPaymentAdditionDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAdditionDetails"}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type"}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentAdditionDetailsColaEntity"}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentAdditionDetails", "description": "Entity for the payment addition information."}, "CapSmpSettlement_CapBaseDisabilityPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAdditionDetailsColaEntity"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "COLA anniversary date"}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapSmpSettlement_CapBaseDisabilityPaymentAllocationBalanceAdjustment": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationBalanceAdjustment"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentAllocationBalanceAdjustment", "description": "Entity for the payment allocation information."}, "CapSmpSettlement_CapBaseDisabilityPaymentAllocationDeductionDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationDeductionDetails"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentAllocationDeductionDetails", "description": "Details of 3rd party deduction payment allocation."}, "CapSmpSettlement_CapBaseDisabilityPaymentAllocationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationDetails"}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentAllocationBalanceAdjustment"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentAllocationDeductionDetails"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationPayableItem": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentAllocationPayableItem"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentAllocationDetails", "description": "Entity for the payment allocation information."}, "CapSmpSettlement_CapBaseDisabilityPaymentAllocationPayableItem": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationPayableItem"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapSmpSettlement_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentAllocationPayableItem", "description": "Stores details for what it is paid."}, "CapSmpSettlement_CapBaseDisabilityPaymentDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentDetails"}, "payeeRoleDetails": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentPayeeRoleDetails"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentAdditionDetails"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentAllocationDetails"}}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentDetails", "description": "Payment transaction details."}, "CapSmpSettlement_CapBaseDisabilityPaymentPayeeRoleDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentPayeeRoleDetails"}, "registryId": {"$ref": "#/definitions/EntityLink"}}, "title": "CapSmpSettlement CapBaseDisabilityPaymentPayeeRoleDetails", "description": "Payment Payee Role details."}, "CapSmpSettlement_CapBaseDisabilitySettlementResultAccumulatorExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilitySettlementResultAccumulatorExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Accumulator Unit code."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapSmpSettlement_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapSmpSettlement CapBaseDisabilitySettlementResultAccumulatorExtension", "description": "Entity for Accumulat extension details."}, "CapSmpSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapSmpSettlement CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapSmpSettlement_CapSMPClaimDetailSelectedCoverageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPClaimDetailSelectedCoverageEntity"}, "coverageName": {"type": "string"}, "planName": {"type": "string"}}, "title": "CapSmpSettlement CapSMPClaimDetailSelectedCoverageEntity"}, "CapSmpSettlement_CapSMPGrossBenefitAmountEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPGrossBenefitAmountEntity"}, "buyUpCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "coreCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "totalApprovedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapSmpSettlement CapSMPGrossBenefitAmountEntity", "description": "Entity for SMP Settlement Gross Benefit Amount calculation."}, "CapSmpSettlement_CapSMPSettlementAbsenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementAbsenceInfoEntity"}, "absencePeriods": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementAbsencePeriodInfoEntity"}}, "absenceReason": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementAbsenceReasonInfoEntity"}, "finalPtoDate": {"type": "string", "format": "date-time"}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes. Inherited from SMP Claim."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax. Inherited from SMP Claim."}, "isOtherIncome": {"type": "boolean"}, "jobClassificationCd": {"type": "string"}, "lastWorkDates": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementAbsenceInfoLastWorkDateEntity"}}, "returnToWorkDate": {"type": "string", "format": "date-time", "description": "Return to Work date"}, "taxes": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementFinancialAdjustmentTax"}}, "typicalWorkWeek": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementLossInfoTypicalWorkWeekEntity"}, "wasDaysUsedAfterLdw": {"type": "boolean"}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}}, "title": "CapSmpSettlement CapSMPSettlementAbsenceInfoEntity", "description": "The object that includes information taken from Absence case."}, "CapSmpSettlement_CapSMPSettlementAbsenceInfoLastWorkDateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementAbsenceInfoLastWorkDateEntity"}, "lastWorkDate": {"type": "string", "format": "date-time"}}, "title": "CapSmpSettlement CapSMPSettlementAbsenceInfoLastWorkDateEntity"}, "CapSmpSettlement_CapSMPSettlementAbsencePeriodInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementAbsencePeriodInfoEntity"}, "absenceTypeCd": {"type": "string", "description": "This attribute describes the type of absence in time perspective."}, "actualRtwDate": {"type": "string", "format": "date-time"}, "estimatedRtwDate": {"type": "string", "format": "date-time"}, "intermittentPeriodDetails": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementIntermittentPeriodDetailsEntity"}, "period": {"$ref": "#/definitions/CapSmpSettlement_Period"}, "reducedPeriodDetails": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementReducedPeriodDetailsEntity"}}, "title": "CapSmpSettlement CapSMPSettlementAbsencePeriodInfoEntity", "description": "Entity that contains Absence periods information"}, "CapSmpSettlement_CapSMPSettlementAbsenceReasonInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementAbsenceReasonInfoEntity"}, "absenceReasons": {"type": "array", "items": {"type": "string", "description": "Absence reasons."}}, "pregnancies": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementPregInfoEntity"}}}, "title": "CapSmpSettlement CapSMPSettlementAbsenceReasonInfoEntity", "description": "Entity that contains Absence reason information."}, "CapSmpSettlement_CapSMPSettlementAccumulatorDetailsRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementAccumulatorDetailsRulesInput"}, "amountType": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "customerURI": {"type": "string", "description": "Primary Insured."}, "extension": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityAccumulatorExtension"}, "limitAmount": {"type": "number", "description": "Accumulator limit amount."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string", "description": "Policy of accumulator."}, "remainingAmount": {"type": "number", "description": "Accumulator remaining amount."}, "reservedAmount": {"type": "number", "description": "Accumulator reserved amount."}, "resource": {"$ref": "#/definitions/EntityLink"}, "term": {"$ref": "#/definitions/CapSmpSettlement_Term"}, "type": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}, "usedAmount": {"type": "number", "description": "Accumulator used amount."}}, "title": "CapSmpSettlement CapSMPSettlementAccumulatorDetailsRulesInput", "description": "Accumulator details for rules."}, "CapSmpSettlement_CapSMPSettlementApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementApprovalPeriodEntity"}, "approvalPeriod": {"$ref": "#/definitions/CapSmpSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string", "description": "Period approval status."}, "approverPerson": {"type": "string", "description": "User that approved the period."}, "cancelReason": {"type": "string", "description": "Additional note why the period is canceled."}, "dateOfStatusChange": {"type": "string", "format": "date-time", "description": "The date when the status was changed."}, "frequencyType": {"type": "string", "description": "Defines the frequency of payments during the approved period."}, "payee": {"$ref": "#/definitions/EntityLink"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}}, "title": "CapSmpSettlement CapSMPSettlementApprovalPeriodEntity", "description": "Entity for the settlement periods that are subject for approval."}, "CapSmpSettlement_CapSMPSettlementBenefitWeeklySalaryPct": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementBenefitWeeklySalaryPct"}, "benefitPct": {"type": "number", "description": "Percentage of weekly earnings covered by policy."}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapSmpSettlement CapSMPSettlementBenefitWeeklySalaryPct", "description": "An entity that stores details of Percentage benefit type."}, "CapSmpSettlement_CapSMPSettlementCoverageInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementCoverageInfo"}, "applicableClassNames": {"type": "array", "items": {"type": "string"}}, "benefitDurations": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_BenefitDurationEntity"}}, "benefitPct": {"type": "number", "description": "Percentage of weekly earnings covered by policy."}, "benefitTypeCd": {"type": "string", "description": "Defines type of which formula to apply to calculate gross benefit amount."}, "coverageCd": {"type": "string"}, "earningsDefinitionCd": {"type": "string", "description": "Earning definition Cd defined in policy"}, "eliminationPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementEliminationPeriods"}}, "employmentStatus": {"type": "string", "description": "Employment Status in Policy"}, "employmentTerminiationDate": {"type": "string", "format": "date", "description": "Employment Terminiation Date in Policy"}, "individualRecordTypeCd": {"type": "string", "description": "Employment Status in SMP Policy"}, "isSelfBill": {"type": "boolean", "description": "Is Self Bill defined in SMP policy"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "partyTypeCd": {"type": "string", "description": "Party Type Cd defined in Policy"}, "payrollFrequencyCd": {"type": "string", "description": "Payroll Frequency Cd in SMP Policy"}, "planCd": {"type": "string", "description": "Describes planCd defined in SMP policy"}, "planName": {"type": "string", "description": "Describes planName defined in SMP policy"}, "riskStateCd": {"type": "string", "description": "Risk State Cd defined in SMP Policy"}, "roundingAmount": {"type": "integer", "format": "int64", "description": "Rounding Amount defined in SMP policy"}, "roundingFactorCd": {"type": "string", "description": "Rounding Factor Cd defined in SMP policy"}, "taxabilityCd": {"type": "string", "description": "For selected coverage defines if weekly benefits are taxable or tax-free."}, "tieredWeeklySalaryPct": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementTieredWeeklySalaryPctEntity"}, "weeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "weeklyPctDetails": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementBenefitWeeklySalaryPct"}, "workweekTypeCd": {"type": "integer", "format": "int64", "description": "Member work week type defined in Policy"}}, "title": "CapSmpSettlement CapSMPSettlementCoverageInfo", "description": "An entity for coverage information."}, "CapSmpSettlement_CapSMPSettlementCptEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementCptEntity"}, "cptCode": {"type": "string"}}, "title": "CapSmpSettlement CapSMPSettlementCptEntity"}, "CapSmpSettlement_CapSMPSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapSmpSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapSMPSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementApprovalPeriodEntity"}}, "approvedAmountOverride": {"$ref": "#/definitions/Money"}, "approvedAmountOverrideReason": {"type": "string"}, "benefitOverridePctReason": {"type": "string"}, "benefitOverridePercent": {"type": "number"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string", "description": "The attribute represents the eligibility rules decision override reason."}, "eliminationPeriodOverrideEndDate": {"type": "string", "format": "date-time", "description": "The attribute that represents the overridden end date of elimination period."}, "eliminationPeriodOverrideReason": {"type": "string", "description": "The attribute that represents the reason of elimination period end date override."}, "lastWorkDateOverride": {"type": "string", "format": "date-time"}, "maxBenefitOverridePeriod": {"$ref": "#/definitions/CapSmpSettlement_Period"}, "maxBenefitOverrideReason": {"type": "string", "description": "The attribute that represents the reason of why maximum benefit period was set manually."}, "maxWeeklyBenefitOverrideAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitOverrideAmountReason": {"type": "string"}, "paidToDate": {"type": "string", "format": "date", "description": "Paid to date"}, "taxablePercentageOverride": {"type": "number", "description": "The taxable percentage override defined by user manually that will be taxable."}, "weeklyEarningsOverrideAmount": {"$ref": "#/definitions/Money"}, "weeklyEarningsOverrideReason": {"type": "string"}}, "title": "CapSmpSettlement CapSMPSettlementDetailEntity", "description": "Entity that encompasses state-mandated product settlement details."}, "CapSmpSettlement_CapSMPSettlementDiagnosisInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementDiagnosisInfoEntity"}, "icdCode": {"type": "string"}, "primaryCode": {"type": "boolean"}}, "title": "CapSmpSettlement CapSMPSettlementDiagnosisInfoEntity"}, "CapSmpSettlement_CapSMPSettlementEarningEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementEarningEntity"}, "weekEarningAmount": {"$ref": "#/definitions/Money"}, "weekNo": {"type": "integer", "format": "int64"}}, "title": "CapSmpSettlement CapSMPSettlementEarningEntity"}, "CapSmpSettlement_CapSMPSettlementEliminationPeriods": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementEliminationPeriods"}, "eliminationPeriodCd": {"type": "integer", "format": "int64", "description": "Defines elimination period of time (in calendar days) between the injury and receiving benefits payments from an insurer."}, "eliminationPeriodTypeCd": {"type": "string", "description": "Defines elimination period type"}}, "title": "CapSmpSettlement CapSMPSettlementEliminationPeriods", "description": "An entity for elimination period of time (in calendar days) between the injury and receiving benefits payments from an insurer."}, "CapSmpSettlement_CapSMPSettlementFinancialAdjustmentOffset": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementFinancialAdjustmentOffset"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapSmpSettlement_Term"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the claim."}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapSmpSettlement CapSMPSettlementFinancialAdjustmentOffset", "description": "Business entity that describes the Offsets of the claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapSmpSettlement_CapSMPSettlementFinancialAdjustmentTax": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementFinancialAdjustmentTax"}, "financialAdjustmentTaxFederal": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementFinancialAdjustmentTaxFederal"}, "jurisdictionType": {"type": "string", "description": "Defines jurisdiction type."}, "taxType": {"type": "string", "description": "Defines the type of a tax."}, "term": {"$ref": "#/definitions/CapSmpSettlement_Term"}}, "title": "CapSmpSettlement CapSMPSettlementFinancialAdjustmentTax", "description": "This business entity describes the withholding taxes, and the amounts that will be applied to the Claim payments."}, "CapSmpSettlement_CapSMPSettlementFinancialAdjustmentTaxFederal": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementFinancialAdjustmentTaxFederal"}, "federalTaxAmount": {"$ref": "#/definitions/Money"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions. Value is inherited from Absence Case."}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation. Value is inherited from Absence Case."}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage. Value is inherited from Absence Case."}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation. Value is inherited from Absence Case."}, "federalTaxTerm": {"$ref": "#/definitions/CapSmpSettlement_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type. Value is inherited from Absence Case."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapSmpSettlement CapSMPSettlementFinancialAdjustmentTaxFederal", "description": "Defines Federal tax details."}, "CapSmpSettlement_CapSMPSettlementIntermittentActualAbsenceEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementIntermittentActualAbsenceEntity"}, "absenceDate": {"type": "string", "format": "date-time", "description": "Absence date."}, "absenceSeconds": {"type": "integer", "format": "int64", "description": "Absence in seconds."}}, "title": "CapSmpSettlement CapSMPSettlementIntermittentActualAbsenceEntity", "description": "Actual absences."}, "CapSmpSettlement_CapSMPSettlementIntermittentPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementIntermittentPeriodDetailsEntity"}, "actualAbsences": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementIntermittentActualAbsenceEntity"}}}, "title": "CapSmpSettlement CapSMPSettlementIntermittentPeriodDetailsEntity", "description": "Intermittent Absence period details"}, "CapSmpSettlement_CapSMPSettlementLossInfoClaimPayeeDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementLossInfoClaimPayeeDetails"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapSmpSettlement CapSMPSettlementLossInfoClaimPayeeDetails"}, "CapSmpSettlement_CapSMPSettlementLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementLossInfoEntity"}, "absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations."}, "claimPayeeDetails": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementLossInfoClaimPayeeDetails"}, "coverageType": {"type": "string", "description": "Type of coverage"}, "disabilityReasonCd": {"type": "string", "description": "Defines disability reason"}, "eligibilityVerifiedCd": {"type": "string"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossNumber": {"type": "string"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Type of loss. Inherited from claim level."}, "memberRegistryTypeId": {"type": "string"}, "offsets": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementFinancialAdjustmentOffset"}}, "selectedCoverage": {"$ref": "#/definitions/CapSmpSettlement_CapSMPClaimDetailSelectedCoverageEntity"}, "smpPriorEarning": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementEarningEntity"}}, "smpPriorQuarterEarnings": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementPriorQuarterEarningsEntity"}}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable. Inherited from Claim."}}, "title": "CapSmpSettlement CapSMPSettlementLossInfoEntity", "description": "Business entity that houses the information from loss to use in settlement."}, "CapSmpSettlement_CapSMPSettlementLossInfoTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementLossInfoTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapSmpSettlement CapSMPSettlementLossInfoTypicalWorkWeekEntity"}, "CapSmpSettlement_CapSMPSettlementMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementCoverageInfo"}}}, "title": "CapSmpSettlement CapSMPSettlementMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "CapSmpSettlement_CapSMPSettlementPaymentsRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementPaymentsRulesInput"}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "paymentDetails": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityPaymentDetails"}, "paymentNumber": {"type": "string", "description": "Unique payment ID."}, "paymentSubType": {"type": "string", "description": "Payment transaction subtype."}, "state": {"type": "string", "description": "Payment transaction state."}}, "title": "CapSmpSettlement CapSMPSettlementPaymentsRulesInput", "description": "Payment detail for rules."}, "CapSmpSettlement_CapSMPSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementPolicyInfoEntity"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "smpMasterInfo": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementMasterInfoEntity"}, "term": {"$ref": "#/definitions/CapSmpSettlement_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapSmpSettlement CapSMPSettlementPolicyInfoEntity", "description": "Entity for SMP Settlement Policy Information."}, "CapSmpSettlement_CapSMPSettlementPregInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementPregInfoEntity"}, "cptCodes": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementCptEntity"}}, "deliveryDate": {"type": "string", "format": "date", "description": "Date of birth."}, "deliveryTypeCd": {"type": "string", "description": "Type of Birth."}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementDiagnosisInfoEntity"}}, "dueDate": {"type": "string", "format": "date-time"}, "numberOfBirths": {"type": "integer", "format": "int64"}}, "title": "CapSmpSettlement CapSMPSettlementPregInfoEntity", "description": "Entity that contains Pregnancy details."}, "CapSmpSettlement_CapSMPSettlementPriorQuarterEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementPriorQuarterEarningsEntity"}, "quarterEarningsAmount": {"$ref": "#/definitions/Money"}, "quarterNo": {"type": "integer", "format": "int64"}}, "title": "CapSmpSettlement CapSMPSettlementPriorQuarterEarningsEntity", "description": "Entity that encompasses the prior quarterly earnings."}, "CapSmpSettlement_CapSMPSettlementReducedPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementReducedPeriodDetailsEntity"}, "secondsFri": {"type": "integer", "format": "int64", "description": "Seconds Friday"}, "secondsMon": {"type": "integer", "format": "int64", "description": "Seconds Monday"}, "secondsSat": {"type": "integer", "format": "int64", "description": "Seconds Saturday"}, "secondsSun": {"type": "integer", "format": "int64", "description": "Seconds Sunday"}, "secondsThu": {"type": "integer", "format": "int64", "description": "Seconds Thursday"}, "secondsTue": {"type": "integer", "format": "int64", "description": "Seconds Tuesday"}, "secondsWed": {"type": "integer", "format": "int64", "description": "Seconds Wednesday"}}, "title": "CapSmpSettlement CapSMPSettlementReducedPeriodDetailsEntity", "description": "Reduced Absence period details"}, "CapSmpSettlement_CapSMPSettlementResultAccumulatorDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementResultAccumulatorDetailEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilitySettlementResultAccumulatorExtension"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapSmpSettlement CapSMPSettlementResultAccumulatorDetailEntity", "description": "Accumulator details entity."}, "CapSmpSettlement_CapSMPSettlementResultApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementResultApprovalPeriodEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementResultAccumulatorDetailEntity"}}, "approvalPeriod": {"$ref": "#/definitions/CapSmpSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string", "description": "Period approval status."}, "approverPerson": {"type": "string", "description": "User that approved the period."}, "benefitDuration": {"type": "number", "description": "Benefit duration for specific approval period."}, "cancelReason": {"type": "string", "description": "Additional note why the period is canceled."}, "dateOfStatusChange": {"type": "string", "format": "date-time", "description": "The date when the status was changed."}, "frequencyType": {"type": "string", "description": "Defines the frequency of payments during the approved period."}, "payee": {"$ref": "#/definitions/EntityLink"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}}, "title": "CapSmpSettlement CapSMPSettlementResultApprovalPeriodEntity", "description": "Entity of the processed periods."}, "CapSmpSettlement_CapSMPSettlementResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementResultEntity"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementResultApprovalPeriodEntity"}}, "autoAdjudicatedDuration": {"type": "integer", "format": "int64", "description": "Indicates the duration of Auto Adjudication Case Duration Guideline value in days."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "calculatedLastWorkDate": {"type": "string", "format": "date-time"}, "coverageCd": {"type": "string"}, "coverageName": {"type": "string"}, "coverageWeeklyLimitAmount": {"$ref": "#/definitions/Money"}, "coveredWeeklyEarningsAmount": {"$ref": "#/definitions/Money"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "eliminationPeriod": {"type": "integer", "format": "int64", "description": "This attribute defines elimination period. Elimination period is time (in calendar days) between the beginning of date of loss and receiving benefits payments from an insurer. No benefits are payable during the elimination period.."}, "eliminationPeriodThroughDate": {"type": "string", "format": "date-time", "description": "This attribute defines end date of elimination period. Elimination period is time (starting from date of loss) an Insured must be disabled before qualifying for SMP benefits. No benefits are payable during the elimination period."}, "employmentStatus": {"type": "string"}, "employmentTerminiationDate": {"type": "string", "format": "date-time"}, "formulaCalculationDetails": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapBaseDisabilityFormulaCalculationDetails"}}, "grossBenefitAmount": {"$ref": "#/definitions/CapSmpSettlement_CapSMPGrossBenefitAmountEntity"}, "individualRecordTypeCd": {"type": "string"}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if this Claim is the subject to Auto Adjudication."}, "isEligibilityEvaluationCdOverriden": {"type": "boolean", "description": "This attribute describes if Eligibility evaluation Cd is overriden"}, "isMaxBenefitPeriodOverriden": {"type": "boolean"}, "isSelfBill": {"type": "boolean"}, "maxBenefitDuration": {"type": "string", "description": "Attribute defines Maximum Benefit Duration in hours. The example of this period would be according to ISO standards and the example value of this attribute of 21 days would be: PT504H."}, "maxBenefitEndDate": {"type": "string", "format": "date-time", "description": "This attribute describes the Maximum End date for Benefit period. It is defined adding the Benefit Period duration to Benefit start date."}, "maxBenefitStartDate": {"type": "string", "format": "date-time", "description": "The attribute described the Maximum Benefit Start Date and determined by Elimination Period End Date. The latter is taken from the Policy."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_MessageType"}}, "partyTypeCd": {"type": "string"}, "planCd": {"type": "string"}, "planName": {"type": "string"}, "proratingRate": {"type": "string", "description": "Prorating rate, used to prorate AGBA, deductions and taxes. The values of the prorating rate can be 1/5 or 1/7."}, "reserve": {"type": "number"}, "roundingAmount": {"type": "integer", "format": "int64"}, "roundingFactorCd": {"type": "string"}, "taxablePercentage": {"type": "number", "description": "The calculated percentage of gross benefit amount that will be taxable."}, "totalBenefitDuration": {"type": "number", "description": "The total benefit duration for a claim."}}, "title": "CapSmpSettlement CapSMPSettlementResultEntity", "description": "Business entity defines SMP settlement result."}, "CapSmpSettlement_CapSMPSettlementRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementRulesInput"}, "absence": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementAbsenceInfoEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementAccumulatorDetailsRulesInput"}}, "currentDateTime": {"type": "string", "format": "date-time", "description": "Current system date and time."}, "details": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementLossInfoEntity"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementPaymentsRulesInput"}}, "policy": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementPolicyInfoEntity"}, "settlement": {"$ref": "#/definitions/EntityLink"}}, "title": "CapSmpSettlement CapSMPSettlementRulesInput"}, "CapSmpSettlement_CapSMPSettlementTieredWeeklySalaryPctEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSMPSettlementTieredWeeklySalaryPctEntity"}, "benefitPctOnPortionOfWeeklySalaryAboveHalfOfSAWW": {"type": "number", "description": "Benefit Pct On Portion Of Weekly Salary Above Half Of SAWW defined in SMP Policy"}, "benefitPctOnPortionOfWeeklySalaryUpToHalfOfSAWW": {"type": "number", "description": "Benefit Pct On Portion Of Weekly Salary Up To Half Of SAWW defined in SMP Policy"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "stateAverageWeeklyWageAmount": {"$ref": "#/definitions/Money"}}, "title": "CapSmpSettlement CapSMPSettlementTieredWeeklySalaryPctEntity", "description": "Cap SMP Settlement Tiered Weekly Salary Pct Entity defined in SMP Policy."}, "CapSmpSettlement_CapSmpSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapSmpSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapSmpSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapSmpSettlement_AccessTrackInfo"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "policy": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "settlementAbsenceInfo": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementAbsenceInfoEntity"}, "settlementDetail": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementLossInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/CapSmpSettlement_CapSMPSettlementResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}}, "title": "CapSmpSettlement CapSmpSettlementEntity", "description": "The object that encompasses attributes set for SMP Settlement."}, "CapSmpSettlement_CapSmpSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntity"}}, "title": "CapSmpSettlement_CapSmpSettlementEntitySuccess"}, "CapSmpSettlement_CapSmpSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapSmpSettlement_CapSmpSettlementEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSmpSettlement_CapSmpSettlementEntitySuccessBody"}, "CapSmpSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapSmpSettlement MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapSmpSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapSmpSettlement Period"}, "CapSmpSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapSmpSettlement Term"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputs"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputs"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}