/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.eventcase.impl;

import cap.adjuster.services.eventcase.CapAdjusterEventCasesService;
import cap.adjuster.services.eventcase.converters.CapAdjusterGenericLossConverter;
import cap.adjuster.services.eventcase.converters.CapAdjusterGenericSettlementConverter;
import cap.adjuster.services.eventcase.dto.CapGenericLoss;
import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.GenesisCapAccumulatorContainerDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.dto.AccumulatorContainerLoadRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.dto.AccumulatorContainerLoadRequestDTO;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.dto.CapAccumulatorContainer_CapAccumulatorDTO;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.GenesisCapEventCaseDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapeventcase.dto.CapEventCase_CapEventCaseEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.GenesisCapLossSearchDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.MoneyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapSettlementSearchEntityResponseV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapSettlementSearchEntityResponseV3SuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapSettlementSearchEntityRequestV3BodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapSettlementSearchEntityRequestV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapSettlementSearchQueryDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapSettlementSearchValueMatcherDTO;
import com.eisgroup.dxp.services.capadjusteraccumulatorcontainer.converters.CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulatorApiConverter;
import com.eisgroup.dxp.services.capadjusteraccumulatorcontainer.dto.CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator;
import com.google.common.collect.Lists;
import core.services.pagination.SortParam.Direction;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.dto.CapLossDTO;
import dataproviders.dto.CapSettlementDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;
import genesis.core.utils.GenesisJsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static core.dataproviders.impl.ContextPreservingCompletionStageFactory.completedFuture;
import static java.lang.String.format;

public class CapAdjusterEventCasesServiceImpl implements CapAdjusterEventCasesService {

    private static final String CAP_LOSS = "CapLoss";
    private static final String CAP_SETTLEMENT = "CapSettlement";
    private static final String BASED_ON = "based_on";
    private static final String CLAIM_CASE = "claim_case";
    private static final String LOSS_CASE = "loss_case";
    private static final String SETTLEMENT_CASE = "settlement_case";
    private static final String EVENT_CASE_URI = "gentity://CapLoss/CapEventCase//%s/%s";
    private static final Integer SETTLEMENTS_LIMIT_PER_PAGE = 100;
    private static final String SETTLEMENT_URI = "gentity://CapSettlement/%s//%s/%s";
    private static final String DEFAULT_CURRENCY_CD = "USD";
    private static final String BENEFIT_TERMBOUND_PERRESOURCE = "Benefit_TermBound_PerResource";
    private static final String ACCUMULATOR_UNIT_CODE_FIELD = "accumulatorUnitCd";
    private static final String ACCUMULATOR_UNIT_CODE = "Money";

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private GenesisCapLossSearchDataProvider capLossSearchDataProvider;
    private GenesisJsonUtils genesisJsonUtils;
    private GenesisCapEventCaseDataProvider capEventCaseDataProvider;

    private CapAdjusterGenericLossConverter<CapLossDTO, CapGenericLoss> genericLossConverter;
    private CapAdjusterGenericSettlementConverter<CapSettlementDTO, CapGenericSettlement> genericSettlementConverter;

    private GenesisCapAccumulatorContainerDataProvider capAccumulatorContainerDataProvider;
    private CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulatorApiConverter<CapAccumulatorContainer_CapAccumulatorDTO, CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator> capAccumulatorApiConverter;

    /**
     * {@inheritDoc}
     */
    @Override
    public CompletionStage<List<CapGenericLoss>> getAbsenceLosses(String rootId, Integer revisionNo) {
        return getLossesAssociatedWithEventCase(rootId, revisionNo, BASED_ON)
                .thenApply(capDisabilityLossDTOs -> capDisabilityLossDTOs.stream()
                        .map(genericLossConverter::convertToApiDTO)
                        .filter(loss -> StringUtils.isNotEmpty(loss.lossType))
                        .collect(Collectors.toList()));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CompletionStage<List<CapGenericLoss>> getLifeClaims(String rootId, Integer revisionNo) {
        return getLossesAssociatedWithEventCase(rootId, revisionNo, CLAIM_CASE)
            .thenApply(claimWrappers -> claimWrappers.stream()
                    .map(genericLossConverter::convertToApiDTO)
                    .sorted(Comparator.comparing(claim -> ((CapGenericLoss)claim).claimType, Comparator.naturalOrder())
                            .thenComparing(claim -> ((CapGenericLoss)claim).lossNumber, Comparator.naturalOrder()))
                    .collect(Collectors.toList()));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CompletionStage<List<CapGenericLoss>> getLifeLosses(String rootId, Integer revisionNo) {
        return getLossesAssociatedWithEventCase(rootId, revisionNo, LOSS_CASE)
            .thenApply(claimWrappers -> claimWrappers.stream()
                .map(genericLossConverter::convertToApiDTO)
                .collect(Collectors.toList()));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CompletionStage<List<CapGenericSettlement>> getLifeSettlements(String rootId, Integer revisionNo) {
        return getLifeSettlementsAssociatedWithEventCase(rootId, revisionNo)
            .thenCombine(getAccumulators(rootId, revisionNo), (capEventCaseRelationshipSettlements, accumulators) -> prefillPaidAmount((List<CapSettlementDTO>) capEventCaseRelationshipSettlements, accumulators))
            .thenApply(settlements -> settlements.stream()
                .sorted(Comparator.comparing(settlement -> settlement.timestamp,Comparator.reverseOrder()))
                .collect(Collectors.toList()));
    }

    /**
     * {@inheritDoc}
     *
     * @return
     */
    @Override
    public CompletionStage<List<CapGenericSettlement>> getSettlements(String rootId, Integer revisionNo) {
        String eventCaseLink = format(EVENT_CASE_URI, rootId, revisionNo);
        SearchCapSettlementSearchEntityRequestV3BodyDTO request = prepareSettlementSearchRequest(eventCaseLink);
        return searchAllSettlements(request)
            .thenApply(response -> {
                if (response == null || CollectionUtils.isEmpty(response.result)) {
                    return Lists.newArrayList();
                }
                return response.result.stream()
                    .map(result -> genericSettlementConverter.convertToApiDTO(genesisJsonUtils.convertObjectToDto(result, CapSettlementDTO.class)))
                    .collect(Collectors.toList());
            });

    }

    /**
     * Prepare settlement index search request
     *
     * @param eventCaseLink
     * @return
     */
    private SearchCapSettlementSearchEntityRequestV3BodyDTO prepareSettlementSearchRequest(String eventCaseLink) {
        SearchCapSettlementSearchEntityRequestV3BodyDTO request = new SearchCapSettlementSearchEntityRequestV3BodyDTO();

        request.body = new SearchCapSettlementSearchEntityRequestV3DTO();
        request.body.query = new SearchCapSettlementSearchQueryDTO();
        request.body.query.eventCaseLink = new SearchCapSettlementSearchValueMatcherDTO();
        request.body.query.eventCaseLink.matches = new ArrayList<>();
        request.body.query.eventCaseLink.matches.add(eventCaseLink);
        request.body.resolveEntity = true;

        Map<String, Object> settlementNumberSort = new HashMap<>();
        settlementNumberSort.put("settlementNumber", Direction.DESC.name());
        request.body.sorting = Lists.newArrayList(settlementNumberSort);
        return request;
    }

    /**
     * Search all indexed settlements without pagination
     *
     * @param request
     * @return
     */
    private CompletionStage<ResultCapSettlementSearchEntityResponseV3DTO> searchAllSettlements(SearchCapSettlementSearchEntityRequestV3BodyDTO request) {
        return capLossSearchDataProvider.apiCommonSearchV3CapSettlementPost(request, null, null, SETTLEMENTS_LIMIT_PER_PAGE, null)
            .thenCompose(result -> {
                if (result == null || result.body == null) {
                    return completedFuture(null);
                } else if (result.body.success.count == 0 || result.body.success.count <= result.body.success.result.size()) {
                    return completedFuture(result.body.success);
                }
                Integer pageCount = Math.toIntExact(result.body.success.count / result.body.success.result.size());
                if (Math.toIntExact(result.body.success.count % result.body.success.result.size()) > 0) {
                    pageCount++;
                }

                List<CompletableFuture<ResultCapSettlementSearchEntityResponseV3SuccessBodyDTO>> futuresList = Lists.newArrayList();
                for (int i = 1; i < pageCount; i++) {
                    futuresList.add(capLossSearchDataProvider.apiCommonSearchV3CapSettlementPost(request, null, null, SETTLEMENTS_LIMIT_PER_PAGE, i * SETTLEMENTS_LIMIT_PER_PAGE).toCompletableFuture());
                }
                return CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[futuresList.size()]))
                    .thenApply(v -> futuresList.stream().map(future -> future.join()).collect(Collectors.toList()))
                    .thenApply(resultList -> {
                        result.body.success.result.addAll(resultList.stream()
                            .flatMap(response -> response.body.success.result.stream())
                            .collect(Collectors.toList()));
                        return result.body.success;
                    });
            });
    }

    /**
     * Get losses associated with event case
     *
     * @param rootId event case identifier
     * @param revisionNo event case revision number
     * @param predicate predicate of relationship
     * @return list of losses related to event case
     */
    private CompletionStage<List<? extends CapLossDTO>> getLossesAssociatedWithEventCase(String rootId, Integer revisionNo, String predicate) {
        String eventCaseURI = format(EVENT_CASE_URI, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(CAP_LOSS, predicate, eventCaseURI);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapLossDTO.class);
    }

    private CompletionStage<List<? extends CapSettlementDTO>> getLifeSettlementsAssociatedWithEventCase(String rootId, Integer revisionNo) {
        String lossUri = format(EVENT_CASE_URI, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(CAP_SETTLEMENT, SETTLEMENT_CASE, lossUri);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapSettlementDTO.class);
    }

    /**
     * Get accumulators associated with event case
     *
     * @param rootId  Event Case rootId
     * @param revisionNo  Event Case revision number
     * @return list of accumulator
     */
    private CompletionStage<List<CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator>> getAccumulators(String rootId, Integer revisionNo) {
        return capEventCaseDataProvider.apiCaplossCapEventCaseV1EntitiesRootIdRevisionNoGet(UUID.fromString(rootId), revisionNo, null, null)
            .thenCompose(capEventCaseSuccessBody -> {
                CapEventCase_CapEventCaseEntityDTO eventCase = capEventCaseSuccessBody.body.success;
                AccumulatorContainerLoadRequestBodyDTO internalDto = new AccumulatorContainerLoadRequestBodyDTO();
                internalDto.body = new AccumulatorContainerLoadRequestDTO();
                internalDto.body.customerURI = eventCase.memberRegistryTypeId;
                return completedFuture(eventCase.policies.stream()
                    .map(policy->policy.capPolicyId)
                    .distinct()
                    .flatMap(capPolicyId -> {
                    internalDto.body.policyURI = capPolicyId;
                    return capAccumulatorContainerDataProvider.apiCapaccumulatorcontainerCapAccumulatorContainerV1AccumulatorLoadAccumulatorsPost(internalDto)
                        .thenApply(accumulatorSuccessBody -> accumulatorSuccessBody.body.success
                            .stream()
                            .flatMap(accumulatorContainer -> accumulatorContainer.accumulators.stream())
                            .map(accumulatorDTO -> capAccumulatorApiConverter.convertToApiDTO(accumulatorDTO))
                        )
                        .toCompletableFuture()
                        .join();
                }).collect(Collectors.toList()));
            });
    }

    /**
     * Prefill Paid Amount for each settlement
     *
     * @return
     */
    private List<CapGenericSettlement> prefillPaidAmount(List<CapSettlementDTO> capSettlementDTO, List<CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator> accumulators) {
        List<CapGenericSettlement> settlementAPIDtos = genericSettlementConverter.convertToApiDTOs(capSettlementDTO);
        if (CollectionUtils.isEmpty(settlementAPIDtos)) {
            return settlementAPIDtos;
        }

        return settlementAPIDtos
            .stream()
            .map(settlement -> calculatePaidAmount(settlement, accumulators))
            .collect(Collectors.toList());
    }

    /**
     * Calculate total paid amount for each settlement
     *
     * @param settlement
     * @param accumulators
     * @return CapGenericSettlement
     */
    private CapGenericSettlement calculatePaidAmount(CapGenericSettlement settlement, List<CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator> accumulators) {

        List<CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator> termBoundAccumulatorsOfMoney = accumulators.stream()
            .filter(accumulator -> StringUtils.equals(BENEFIT_TERMBOUND_PERRESOURCE, accumulator.type) &&
                StringUtils.equals(ACCUMULATOR_UNIT_CODE, Optional.ofNullable(genesisJsonUtils.convertObjectToMap(accumulator.extension).get(ACCUMULATOR_UNIT_CODE_FIELD))
                    .map(Object::toString)
                    .orElse(null)))
            .filter(accumulator -> StringUtils.equals(format(SETTLEMENT_URI, settlement.modelName, settlement.key.rootId, settlement.modelVersion), accumulator.resource._uri))
            .collect(Collectors.toList());

        BigDecimal termBoundTotalPaidAmount;
        if (CollectionUtils.isEmpty(termBoundAccumulatorsOfMoney) && settlement.settlementResult != null && settlement.settlementResult.benefitAmountPerUnit != null) {
            termBoundTotalPaidAmount = accumulators.stream()
                .filter(accumulator -> StringUtils.equals(BENEFIT_TERMBOUND_PERRESOURCE, accumulator.type))
                .filter(accumulator -> StringUtils.equals(format(SETTLEMENT_URI, settlement.modelName, settlement.key.rootId, settlement.modelVersion), accumulator.resource._uri))
                .map(accumulator -> accumulator.usedAmount.multiply(settlement.settlementResult.benefitAmountPerUnit.amount))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            termBoundTotalPaidAmount = termBoundAccumulatorsOfMoney.stream()
                .map(accumulator -> accumulator.usedAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        return setPaidAmount(settlement, termBoundTotalPaidAmount, DEFAULT_CURRENCY_CD);
    }

    /**
     * Set value of paid amount into settlement
     *
     * @param settlement
     * @param termBoundTotalPaidAmount
     * @param currencyCd
     * @return
     */
    private CapGenericSettlement setPaidAmount(CapGenericSettlement settlement, BigDecimal termBoundTotalPaidAmount, String currencyCd) {
        MoneyDTO paidAmountDTO = new MoneyDTO();
        paidAmountDTO.amount = termBoundTotalPaidAmount;
        paidAmountDTO.currency = currencyCd;
        if (settlement.settlementResult != null) {
            settlement.settlementResult.paidAmount = paidAmountDTO;
        }
        return settlement;
    }

    @Inject
    public void setSearchRelationshipsDataProvider(
        GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    public void setGenericLossConverter(
        CapAdjusterGenericLossConverter<CapLossDTO, CapGenericLoss> genericLossConverter) {
        this.genericLossConverter = genericLossConverter;
    }

    @Inject
    public void setGenericSettlementConverter(
        CapAdjusterGenericSettlementConverter<CapSettlementDTO, CapGenericSettlement> genericSettlementConverter) {
        this.genericSettlementConverter = genericSettlementConverter;
    }

    @Inject
    public void setCapEventCaseDataProvider(GenesisCapEventCaseDataProvider capEventCaseDataProvider) {
        this.capEventCaseDataProvider = capEventCaseDataProvider;
    }

    @Inject
    public void setCapAccumulatorContainerDataProvider(GenesisCapAccumulatorContainerDataProvider capAccumulatorContainerDataProvider) {
        this.capAccumulatorContainerDataProvider = capAccumulatorContainerDataProvider;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setCapAccumulatorApiConverter(CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulatorApiConverter capAccumulatorApiConverter) {
        this.capAccumulatorApiConverter = capAccumulatorApiConverter;
    }
    @Inject
    public void setCapLossSearchDataProvider(
        GenesisCapLossSearchDataProvider capLossSearchDataProvider) {
        this.capLossSearchDataProvider = capLossSearchDataProvider;
    }

    @Inject
    public void setGenesisJsonUtils(GenesisJsonUtils genesisJsonUtils) {
        this.genesisJsonUtils = genesisJsonUtils;
    }
}
