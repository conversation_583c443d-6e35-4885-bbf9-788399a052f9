{"swagger": "2.0", "x-dxp-spec": {"imports": {"death": {"schema": "integration.cap.death.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Death Loss API", "version": "1", "title": "CAP Adjuster: Death Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-death", "description": "CAP Adjuster: Death Loss API"}], "paths": {"/losses-death/{rootId}/{revisionNo}": {"get": {"summary": "Get death loss by rootId and revisionNo", "x-dxp-path": "/api/caploss/DeathLoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-death"]}}, "/losses-death/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for death loss", "x-dxp-path": "/api/caploss/DeathLoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-death"]}}, "/losses-death": {"post": {"summary": "Create death loss", "x-dxp-path": "/api/caploss/DeathLoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-death"]}, "put": {"summary": "Update death loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/DeathLoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-death"]}}, "/losses-death/draft": {"post": {"summary": "Init death loss", "x-dxp-path": "/api/caploss/DeathLoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-death"]}}}}