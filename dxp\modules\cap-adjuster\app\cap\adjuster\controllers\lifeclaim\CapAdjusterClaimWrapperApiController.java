/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.lifeclaim;

import cap.adjuster.services.lifeclaim.CapAdjusterClaimWrapperService;
import cap.adjuster.services.lifeclaim.dto.CapLifeClaimSettlement;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterClaimWrapperApiController.TAG_API_CAP_ADJUSTER_CLAIM_WRAPPER,
                description = "CAP Adjuster: Claim Wrapper API")})
@Api(value = CapAdjusterClaimWrapperApiController.TAG_API_CAP_ADJUSTER_CLAIM_WRAPPER,
        tags = CapAdjusterClaimWrapperApiController.TAG_API_CAP_ADJUSTER_CLAIM_WRAPPER)
public class CapAdjusterClaimWrapperApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_CLAIM_WRAPPER = "/cap-adjuster/v1/claim-wrappers";

    private CapAdjusterClaimWrapperService claimWrapperService;

    /**
     * Get claimWrapper' settlements
     *
     * @param rootId Claim Wrapper identifier
     * @param revisionNo Claim Wrapper revision number
     * @return list of settlements related to Claim Wrapper
     */
    @ApiOperation(value = "Get loss's settlements associated with claim wrapper",
            response = CapLifeClaimSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Claim Wrapper identifier", required = true) String rootId,
                                                  @ApiParam(value = "Claim Wrapper Revision number", required = true) Integer revisionNo) {
        return completeOk(claimWrapperService.getSettlements(rootId, revisionNo));
    }

    @Inject
    public void setClaimWrapperService(CapAdjusterClaimWrapperService claimWrapperService) {
        this.claimWrapperService = claimWrapperService;
    }
}
