/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.lifeloss;

import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import cap.adjuster.services.lifeloss.impl.CapAdjusterLifeLossServiceImpl;

import com.google.inject.ImplementedBy;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * Service that provides methods for CAP Life losses
 */
@ImplementedBy(CapAdjusterLifeLossServiceImpl.class)
public interface CapAdjusterLifeLossService {

    /**
     * Get settlements associated with loss
     *
     * @param rootId     loss rootId
     * @param revisionNo loss revision number
     * @param modelName  loss model name
     *
     * @return list of settlements associated with loss
     */
    CompletionStage<List<CapGenericSettlement>> getLossSettlements(String rootId, Integer revisionNo, String modelName);

}
