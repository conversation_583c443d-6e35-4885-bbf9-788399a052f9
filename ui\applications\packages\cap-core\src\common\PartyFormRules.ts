/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {EisDate} from '@eisgroup/ui-temporals'
import moment from 'moment'
import {isNotEmpty} from './utils'
import {PreferredContactTypes} from '../components/preferred-contact-info/PreferredContactInfo'

export const validateBirthDate = (value?: Date | EisDate): string | undefined => {
    return opt(value)
        .map(v => {
            const to = moment()
            const from = moment(to).subtract(120, 'years')
            return dateUtils(v).toMoment.isBetween(from, to, undefined, '[]')
        })
        .orElse(true)
        ? undefined
        : t('cap-core:customer_birthDate_validation')
}

export const isPhoneValid = (errorMessage: string) => (value, allValues) => {
    const reg10Digit = /\d\d\d-\d\d\d-\d\d\d\d/
    const reg11Digit = /\d-\d\d\d-\d\d\d-\d\d\d\d/
    const reg10DigitNumber = /^\d{10}$/
    const reg11DigitNumber = /^\d{11}$/
    const isValid =
        reg10Digit.test(value) || reg11Digit.test(value) || reg10DigitNumber.test(value) || reg11DigitNumber.test(value)
    const customer = allValues.party?.customer || allValues.employee || allValues.subject?.customer
    return customer?.communicationInfo?.preferredContactMethod === PreferredContactTypes.PHONE && !isValid
        ? t(errorMessage)
        : undefined
}

export const validEmail = (value: string): boolean => {
    const emailReg1 = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/
    return emailReg1.test(value)
}

export const isEmailValid = (errorMessage: string) => (value, allValues) => {
    const customer = allValues.party?.customer || allValues.employee || allValues.subject?.customer
    const emailValue = value === '_@_._' ? null : value
    return (customer?.communicationInfo?.preferredContactMethod === PreferredContactTypes.EMAIL &&
        !isNotEmpty(emailValue)) ||
        (isNotEmpty(emailValue) && !validEmail(emailValue))
        ? t(errorMessage)
        : undefined
}

export const validateLegalId = (value, allValues) => {
    const legalIdMaskRegex = /^\d{2}-\d{7}$/
    return value && !legalIdMaskRegex.test(value) ? t('cap-core:customer_legal_id_is_not_valid') : undefined
}

export const isSSNValid = (errorMessage: string) => (value, allValues) => {
    const re = /\d\d\d-\d\d-\d\d\d\d/
    const isValid = re.test(value)
    return !isValid ? t(errorMessage) : undefined
}
