/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import {LocalizationFormats, t} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {Button, ColumnProps, DatePicker, Select} from '@eisgroup/ui-kit'
import {ActionFilterSmall} from '@eisgroup/ui-kit-icons'
import {isArray} from 'lodash'
import * as React from 'react'
import {Link} from 'react-router'
import {
    CHANGE_HISTORY_TABLE_HEADER_FILTER,
    CHANGE_HISTORY_TABLE_HEADER_FILTER_RANGEPICKER,
    CHANGE_HISTORY_TABLE_HEADER_FILTER_SELECT,
    isURI,
    tryTranslateValue
} from '../../..'
import {ChangeHistoryURI} from '../ChangeHistoryURI'
import {ChangeHistoryClaimModelNames, HistoryTableRow, OptionsListeProps, tableFiltersProps} from './types'

export const getColumnSelectSearchProps = ({
    dataIndex,
    tableFilters,
    optionsList,
    handleSearch,
    handleReset
}: {
    dataIndex: string
    tableFilters: tableFiltersProps
    optionsList: OptionsListeProps
    handleSearch: (selectedKeys: (string | Date)[], confirm: () => void, dataIndex: string) => void
    handleReset: (clearFilters: () => void, dataIndex: string) => void
}) => ({
    filterDropdown: ({confirm, clearFilters}) => {
        let selectedKeys = tableFilters[dataIndex]
        return (
            <div className={CHANGE_HISTORY_TABLE_HEADER_FILTER}>
                <Select
                    value={tableFilters[dataIndex]}
                    mode='multiple'
                    onChange={value => {
                        selectedKeys = value
                    }}
                    className={CHANGE_HISTORY_TABLE_HEADER_FILTER_SELECT}
                >
                    {optionsList[dataIndex]?.map(v => (
                        <Select.Option value={v}>{v}</Select.Option>
                    ))}
                </Select>
                <Button
                    type='primary'
                    onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    icon='search'
                    size='small'
                >
                    {t('cap-core:history_table_search_apply')}
                </Button>
                <Button onClick={() => handleReset(clearFilters, dataIndex)} size='small'>
                    {t('cap-core:history_table_search_clear')}
                </Button>
            </div>
        )
    },
    filterIcon: <ActionFilterSmall />,
    onFilter: (value: string[], record: HistoryTableRow) => value.includes(record[dataIndex]),
    sorter: (a: HistoryTableRow, b: HistoryTableRow) => {
        return a[dataIndex]?.localeCompare(b[dataIndex])
    }
})

const getColumnDateSearchProps = ({
    dataIndex,
    tableFilters,
    handleSearch,
    handleReset
}: {
    dataIndex: string
    tableFilters: any
    handleSearch: (selectedKeys: (string | Date)[], confirm: () => void, dataIndex: string) => void
    handleReset: (clearFilters: () => void, dataIndex: string) => void
}) => ({
    filterDropdown: ({confirm, clearFilters}) => {
        let selectedKeys = tableFilters[dataIndex]
        return (
            <div className={CHANGE_HISTORY_TABLE_HEADER_FILTER}>
                <DatePicker.RangePicker
                    format={LocalizationFormats.DateLocaleFormats.DATE}
                    className={CHANGE_HISTORY_TABLE_HEADER_FILTER_RANGEPICKER}
                    defaultValue={tableFilters[dataIndex]}
                    onChange={value => {
                        selectedKeys = value
                    }}
                />
                <Button
                    type='primary'
                    onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    icon='search'
                    size='small'
                >
                    {t('cap-core:history_table_search_apply')}
                </Button>
                <Button onClick={() => handleReset(clearFilters, dataIndex)} size='small'>
                    {t('cap-core:history_table_search_clear')}
                </Button>
            </div>
        )
    },
    filterIcon: <ActionFilterSmall />,
    onFilter: (value: string, record: HistoryTableRow) => value.includes(record[dataIndex]),
    sorter: (a: HistoryTableRow, b: HistoryTableRow) => {
        if (dateUtils(a.currentDate).toMoment.isBefore(dateUtils(b.currentDate).toMoment)) {
            return 1
        }
        return -1
    }
})

export function getHistoryTableColumns(props): ColumnProps<HistoryTableRow>[] {
    return [
        {
            key: 'date',
            dataIndex: 'date',
            title: t('cap-core:history_table_date_and_time'),
            width: '10%',
            render: (text, record: HistoryTableRow) => {
                return (
                    <span>
                        {record.date
                            ? dateUtils(record.date).format(t('cap-core:history_table_date_column_format'), true)
                            : t('cap-core:not_available')}
                    </span>
                )
            },
            ...getColumnDateSearchProps({
                dataIndex: 'date',
                ...props
            })
        },
        {
            key: 'component',
            dataIndex: 'component',
            title: t('cap-core:history_table_component'),
            width: '5%',
            ...getColumnSelectSearchProps({
                dataIndex: 'component',
                ...props
            })
        },
        {
            key: 'reference',
            dataIndex: 'reference',
            title: t('cap-core:history_table_reference'),
            width: '5%',
            render: renderReferenceLink,
            ...getColumnSelectSearchProps({
                dataIndex: 'reference',
                ...props
            })
        },
        {
            key: 'entity',
            dataIndex: 'entity',
            title: t('cap-core:history_table_entity'),
            width: '10%',
            ...getColumnSelectSearchProps({
                dataIndex: 'entity',
                ...props
            })
        },
        {
            key: 'attribute',
            dataIndex: 'attribute',
            title: t('cap-core:history_table_attribute'),
            width: '20%',
            ...getColumnSelectSearchProps({
                dataIndex: 'attribute',
                ...props
            })
        },
        {
            key: 'original',
            dataIndex: 'original',
            title: t('cap-core:history_table_original'),
            width: '20%',
            className: 'history-table-original',
            render: renderHistoryValue
        },
        {
            key: 'new',
            dataIndex: 'new',
            title: t('cap-core:history_table_new'),
            width: '20%',
            className: 'history-table-new',
            render: renderHistoryValue
        },
        {
            key: 'adjustedBy',
            dataIndex: 'adjustedBy',
            title: t('cap-core:history_table_adjusted_by'),
            width: '10%',
            ...getColumnSelectSearchProps({
                dataIndex: 'adjustedBy',
                ...props
            })
        }
    ]
}

function renderHistoryValue(value: string | string[], record: HistoryTableRow): React.ReactNode | string {
    const {lookupName} = record.metadata

    if (lookupName && value.length) {
        return <LookupLabel lookup={lookupName} code={value} emptyLabel={isArray(value) ? value.join(', ') : value} />
    }

    if (isURI(value)) {
        return <ChangeHistoryURI uri={isArray(value) ? value : [value]} />
    }

    const displayValue = getHistoryDisplayValue(value)
    return <span title={displayValue}>{displayValue}</span>
}

function getHistoryDisplayValue(value: string | string[]): string {
    return isArray(value) ? value.map(tryTranslateValue).join(', ') : tryTranslateValue(value)
}

export function renderReferenceLink(value: string, record: HistoryTableRow): React.ReactNode | string {
    const {modelName, rootId, revisionNo} = record.metadata?.lossIdentification || {}

    if (!modelName || !rootId || !revisionNo) {
        return value
    }
    const link = `${
        modelName === ChangeHistoryClaimModelNames.ClaimWrapper ? 'claim/claim-overview' : modelName
    }/${rootId}/${revisionNo}`

    if (record.attribute === 'Description' && record.component === 'Payment') {
        return <Link to={record.eventCaseInformation.link}>{record.eventCaseInformation.number}</Link>
    }

    if (record.metadata.refExtension) {
        return (
            <p>
                <Link to={link}>{value}</Link>
                {record.metadata.refExtension}
            </p>
        )
    }
    return <Link to={link}>{value}</Link>
}
