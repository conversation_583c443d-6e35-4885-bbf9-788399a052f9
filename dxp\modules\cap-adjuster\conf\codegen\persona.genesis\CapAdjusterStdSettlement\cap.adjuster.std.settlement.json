{"swagger": "2.0", "x-dxp-spec": {"imports": {"std.settlement": {"schema": "integration.cap.std.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: STD Settlement API", "version": "1", "title": "CAP Adjuster: STD Settlement API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-std", "description": "CAP Adjuster: STD Loss API"}], "paths": {"/losses-std/settlements/readjudicate": {"patch": {"summary": "Readjudicate STD settlement", "x-dxp-path": "/api/capsettlement/CapStdSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/settlements/rules/bundle": {"post": {"summary": "Bundle STD settlement", "x-dxp-path": "/api/capsettlement/CapStdSettlement/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-std"]}}, "/losses-std/settlements/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/capsettlement/CapStdSettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-std"]}}}}