/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import path from 'node:path'
import type {StorybookConfig} from '@storybook/react-webpack5'

import {getAliases} from '@eisgroup/infra-scripts'

function getAbsolutePath(value: string) {
    return path.dirname(require.resolve(path.join(value, 'package.json')))
}

const config: StorybookConfig = {
    stories: [path.resolve(__dirname, '../libs/claims-core/src/**/*.stories.(js|jsx|ts|tsx)')],
    addons: [
        getAbsolutePath('@storybook/addon-essentials'),
        getAbsolutePath('@storybook/addon-a11y'),
        getAbsolutePath('@storybook/addon-webpack5-compiler-swc')
    ],
    docs: {
        defaultName: 'Code'
    },
    webpackFinal: async config => {
        // explanation: https://github.com/TypeStrong/ts-loader#loader-options
        config.ignoreWarnings = [/export .* was not found in/, /Should not import the named export \'version\'/]

        if (config.resolve?.alias) {
            config.resolve.alias = {
                ...config.resolve?.alias,
                ...getAliases(path.resolve(__dirname, '../'))
            }
        } else {
            config.resolve = {
                ...config.resolve,
                alias: getAliases(path.resolve(__dirname, '../'))
            }
        }

        config.module?.rules?.push({
            test: /.*\.less$/,
            use: [
                'style-loader',
                'css-loader',
                {
                    loader: 'less-loader',
                    options: {
                        lessOptions: {
                            javascriptEnabled: true,
                            math: 'always'
                        }
                    }
                }
            ]
        })
        return config
    },
    framework: '@storybook/react-webpack5',
    swc: (config, options) => {
        return {
            jsc: {
                parser: {
                    syntax: 'typescript',
                    decorators: true
                }
            }
        }
    },
    typescript: {
        check: false,
        reactDocgen: 'react-docgen-typescript',
        reactDocgenTypescriptOptions: {
            shouldRemoveUndefinedFromOptional: true,
            // Makes union prop types like variant and size appear as select controls
            shouldExtractLiteralValuesFromEnum: true,

            // The authors of "react-docgen-typescript-plugin" recommend the following `compilerOptions`
            // to speed up the build. However, this causes inherited props to be excluded from the stories unless
            // they are explicitly specified in the story args (https://github.com/storybookjs/storybook/issues/23084).
            // Explicitly specifying props in the args requires manually writing descriptions and other details,
            // which are not automatically retrieved from the code.
            //
            // compilerOptions: {
            //     allowSyntheticDefaultImports: false,
            //     esModuleInterop: false
            // },

            // Excludes third-party props from node_modules, except for specific packages.
            propFilter: prop => (prop.parent ? !/node_modules\/(?!@eisgroup|antd)/.test(prop.parent.fileName) : true)
        }
    }
}

export default config
