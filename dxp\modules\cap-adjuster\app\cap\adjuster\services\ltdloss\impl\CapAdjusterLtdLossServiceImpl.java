/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.ltdloss.impl;

import cap.adjuster.services.financial.converters.CapAdjusterPaymentConverter;
import cap.adjuster.services.financial.dto.CapPayment;
import cap.adjuster.services.ltdloss.CapAdjusterLtdLossService;
import cap.adjuster.services.ltdloss.converters.CapAdjusterLtdSettlementConverter;
import cap.adjuster.services.ltdloss.dto.CapLtdSettlement;
import core.utils.AsyncUtils;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.dto.CapLtdSettlementDTO;
import dataproviders.dto.CapPaymentDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static java.lang.String.format;

public class CapAdjusterLtdLossServiceImpl implements CapAdjusterLtdLossService {

    private static final String SETTLEMENT_BASED_ON_LOSS = "based_on";
    private static final String PAYMENT_USES_RESERVES_SETTLEMENT = "uses/reserves";
    private static final String CAP_SETTLEMENT = "CapSettlement";
    private static final String CAP_PAYMENT = "CapPayment";
    private static final String CAP_PAYMENT_ENTITY = "CapPaymentEntity";
    private static final String LTD_LOSS_URI = "gentity://CapLoss/CapLtd//%s/%s";
    private static final String SETTLEMENT_URI = "gentity://CapSettlement/CapLtdSettlement//%s/%s";

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private CapAdjusterPaymentConverter<CapPaymentDTO, CapPayment> paymentConverter;
    private CapAdjusterLtdSettlementConverter<CapLtdSettlementDTO, CapLtdSettlement> settlementConverter;

    @Override
    public CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo)
                .thenCompose(capLtdSettlementDTOs -> AsyncUtils.sequence(
                        capLtdSettlementDTOs.stream()
                                .map(this::getPaymentsAssociatedWithSettlement)
                                .collect(Collectors.toList())
                ))
                .thenApply(capLtdLossDTOs -> capLtdLossDTOs.stream()
                        .flatMap(List::stream)
                        .filter(this::isPaymentTypeAppropriate)
                        .map(paymentConverter::convertToApiDTO)
                        .collect(Collectors.toList())
                );
    }

    @Override
    public CompletionStage<List<CapLtdSettlement>> getSettlements(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo)
                .thenApply(capLtdSettlementDTOs -> capLtdSettlementDTOs.stream()
                        .map(settlementConverter::convertToApiDTO)
                        .collect(Collectors.toList()));
    }

    private boolean isPaymentTypeAppropriate(CapPaymentDTO paymentDTO) {
        return StringUtils.equals(paymentDTO.gentityType, CAP_PAYMENT_ENTITY);
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapLtdSettlementDTO>> getSettlementsAssociatedWithLoss(String rootId, Integer revisionNo) {
        String lossUri = format(LTD_LOSS_URI, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(lossUri, SETTLEMENT_BASED_ON_LOSS, CAP_SETTLEMENT);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapLtdSettlementDTO.class);
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapPaymentDTO>> getPaymentsAssociatedWithSettlement(CapLtdSettlementDTO ltdSettlementDTO) {
        String settlementUri = format(SETTLEMENT_URI, ltdSettlementDTO.key.rootId, ltdSettlementDTO.key.revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(CAP_PAYMENT, PAYMENT_USES_RESERVES_SETTLEMENT, settlementUri);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapPaymentDTO.class);
    }

    @Inject
    public void setSearchRelationshipsDataProvider(
            GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setPaymentConverter(CapAdjusterPaymentConverter paymentConverter) {
        this.paymentConverter = paymentConverter;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setSettlementConverter(CapAdjusterLtdSettlementConverter settlementConverter) {
        this.settlementConverter = settlementConverter;
    }
}
