/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim;

import java.util.concurrent.CompletionStage;

import com.eisgroup.dxp.services.capadjusterclaimsearch.dto.CapAdjusterClaimSearchCapDynamicSearchRequestBody;
import com.google.inject.ImplementedBy;

import cap.adjuster.services.claim.dto.CapAdjusterClaimIndexResponse;
import cap.adjuster.services.claim.impl.CapAdjusterClaimsServiceImpl;
import core.services.pagination.PageData;

/**
 * Service that provides methods for claim
 */
@ImplementedBy(CapAdjusterClaimsServiceImpl.class)
public interface CapAdjusterClaimsService {

    /**
     * Search Claim indexes with additional data
     *
     * @param body
     * @param fields
     * @param pageData
     * @return
     */
    CompletionStage<CapAdjusterClaimIndexResponse> searchClaims(
        CapAdjusterClaimSearchCapDynamicSearchRequestBody body, String fields, PageData pageData);

    /**
     * Advanced Search Claim indexes with additional data
     *
     * @param slsSearchContent
     * @param claimSearchRequestBody
     * @param fields
     * @param pageData
     * @return
     *
     * @deprecated since 24.1, unneeded
     */
    @Deprecated(since = "24.1", forRemoval = true)
    CompletionStage<CapAdjusterClaimIndexResponse> advancedSearchClaims(String slsSearchContent,
        CapAdjusterClaimSearchCapDynamicSearchRequestBody claimSearchRequestBody, String fields, PageData pageData);
}
