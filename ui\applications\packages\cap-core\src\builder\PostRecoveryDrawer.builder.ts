import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "itemCount": 2
      },
      "components": [
        {
          "type": "CUSTOM_SELECT_INPUT",
          "id": "b771d415-1b44-43c6-bf77-55fae45ec14f",
          "props": {
            "md": 12,
            "label": "cap-core:payments_post_recovery_drawer_party",
            "name": "payee",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Party"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          }
        },
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 12,
            "name": "recoveryAmount",
            "allowDecimal": true,
            "label": "cap-core:payments_post_recovery_drawer_total_recovery_amount",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Total Recovery Amount"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                },
                {
                  "skipOnEmpty": true,
                  "type": "amount-gt0"
                }
              ]
            }
          },
          "id": "85d081b6-edf0-4ca2-a930-98884925713a"
        }
      ],
      "id": "74a7968a-2882-4d7d-a65b-2656f855f70c"
    }
  ],
  "version": 11
}

export default config;
