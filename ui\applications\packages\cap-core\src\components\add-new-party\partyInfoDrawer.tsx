/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import * as React from 'react'
import {CustomerType} from '@eisgroup/cap-services'
import {toJS} from 'mobx'
import {useState} from 'react'
import {Spin} from '@eisgroup/ui-kit'
import {FormApi} from '@eisgroup/form-core'
import {t} from '@eisgroup/i18n'
import {DrawerWidth} from '../../common/constants'
import {DrawerFormStateType, FormDrawer} from '../form-drawer'
import {filterOutEmptyCommunicationInfoValues} from '../../utils/CustomerUtils'
import {PartyInfoComponent} from './partyInfoComponent'
import {PartyStoreImpl} from './partyStore'
import {OptionValue} from '../../common/Types'
import {PreferredContactTypes} from '../preferred-contact-info/PreferredContactInfo'
import {getPrimaryAddressIndex} from '../../utils'

export interface PartyInfoDrawerProps {
    /**
     * individual customer or origization customer
     */
    customerType: CustomerType
    /**
     * customer
     */
    customer: any
    /**
     * drawer visible
     */
    visible: boolean
    /**
     * render type
     */
    drawerFormState: DrawerFormStateType
    associatedWithOptions?: OptionValue[]
    hideRelationshipToParticipant?: boolean
    showDisabledSelfRelationship?: boolean
    preferredContactMethod: PreferredContactTypes
    /**
     * save customer success callback
     */
    afterSuccess: (customer) => void
    /**
     * close drawer
     */
    onDrawerClose: () => void
    isSubject?: boolean
}

const partyStore = new PartyStoreImpl()

export const PartyInfoDrawer = (props: PartyInfoDrawerProps) => {
    const {
        customerType,
        customer,
        visible,
        drawerFormState,
        afterSuccess,
        onDrawerClose,
        associatedWithOptions,
        hideRelationshipToParticipant,
        showDisabledSelfRelationship,
        preferredContactMethod,
        isSubject
    } = props
    const [loading, setLoading] = useState(false)
    const onClose = () => {
        onDrawerClose()
    }
    const onsubmitParty = (form: FormApi) => {
        setLoading(true)
        const formErrors = form.getState().errors
        if (JSON.stringify(formErrors) !== JSON.stringify({})) {
            return
        }
        const formValues = form.getState().values
        const customerValue = formValues.party.customer
        const newCustomer = {
            ...customer,
            ...customerValue
        }
        if (drawerFormState === DrawerFormStateType.Create) {
            partyStore.createCustomer(filterOutEmptyCommunicationInfoValues(newCustomer)).then(customerResult => {
                afterSuccess(customerResult)
                setLoading(false)
            })
        } else {
            partyStore.updateCustomer(filterOutEmptyCommunicationInfoValues(newCustomer)).then(customerResult => {
                afterSuccess(customerResult)
                setLoading(false)
            })
        }
    }
    const initialValues = {
        party: {
            customer: toJS(customer)
        }
    }
    const formTitle = () => {
        if (drawerFormState === DrawerFormStateType.Create) {
            return customerType === CustomerType.Individual
                ? t('cap-core:claim_party_detail_add_new_individual_party_form_title')
                : t('cap-core:claim_party_detail_add_new_organization_party_form_title')
        }
        return t('cap-core:claim_party_addtional_action_edit_contact_info')
    }
    const currentAddressIdx = getPrimaryAddressIndex(customer?.communicationInfo?.addresses)
    return visible ? (
        <Spin spinning={loading}>
            <FormDrawer
                formTitle={formTitle()}
                className='form-drawer-action-wrapper'
                onFormCancel={onClose}
                shouldRemovePopupCancel={false}
                drawerWidth={DrawerWidth.SMALL}
                formToRender={
                    <PartyInfoComponent
                        currentAddressIdx={currentAddressIdx > 0 ? currentAddressIdx : 0}
                        onDrawerClose={onDrawerClose}
                        onsubmitParty={onsubmitParty}
                        initialValues={initialValues}
                        associatedWithOptions={associatedWithOptions}
                        hideRelationshipToParticipant={hideRelationshipToParticipant}
                        showDisabledSelfRelationship={showDisabledSelfRelationship}
                        drawerFormState={drawerFormState}
                        preferredContactMethod={preferredContactMethod}
                        customerType={customerType}
                        isSubject={isSubject}
                    />
                }
            />
        </Spin>
    ) : null
}
