import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "MULTI_SELECT",
          "props": {
            "md": 12,
            "name": "lossSources",
            "label": "cap-core:deductions_table_lossTypes",
            "pathToValue": "_uri",
            "options": [],
            "events": [
              {
                "id": "c9aabaa6-a9dd-458b-9fb9-6072c60f655c",
                "dispatchEventProperty": "onChange",
                "eventName": "onDeductionLossSourcesChange"
              }
            ],
            "field": {
              "validations": [
                {
                  "skipOnEmpty": true,
                  "type": "required"
                }
              ]
            },
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "isClaimLevel"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "693e4e76-825e-4107-943a-e434b1b1e03a"
        }
      ],
      "id": "2bb7af8d-62ea-4190-a973-3b021482a6fe"
    }
  ],
  "version": 26,
  "globalEvents": {},
  "actionChains": {
    "onDeductionLossSourcesChange": {
      "type": "API",
      "apiSettings": {
        "method": "onDeductionLossSourcesChange"
      }
    }
  }
}

export default config;
