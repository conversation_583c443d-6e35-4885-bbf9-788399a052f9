/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import React from 'react'
import {observer} from 'mobx-react'
import classNames from 'classnames'
import {Collapse, ColumnProps, Table} from '@eisgroup/ui-kit'
import {opt} from '@eisgroup/common-types'
import {useTranslate} from '@eisgroup/i18n'
import {CapBalance} from '@eisgroup/cap-financial-models'
import {sumBy, uniq} from 'lodash'
import {BalanceStore} from '../../common/store/BalanceStore'
import {LOAD_BALANCE} from '../../common/store/impl/BalanceStoreImpl'
import {
    BALANCE_TABLE_COLUMN_TAG,
    BALANCE_TABLE_RECALCULATION_PAYMENTS,
    BALANCE_TABLE_RECALCULATION_PAYMENTS_TOP,
    RECALCULATION_PAYMENTS_AMOUNT_BOX,
    RECALCULATION_PAYMENTS_SECTION,
    FINANCIAL_INFORMATION_BALANCE_SUB_TABLE,
    Settlements,
    moneyByLocale,
    MoneyFormat,
    BALANCE_TABLE_RECALCULATION_PAYMENTS_WRAPPER,
    SettlementModelNamesForDisability
} from '../..'
import {BalanceListExpand} from './BalanceListExpand'
import {EntityLink} from '../../utils/EntityLink'
import CapBalanceItemEntity = CapBalance.CapBalanceItemEntity

const {Panel} = Collapse
export interface RecalculationPaymentsProps {
    balanceStore: BalanceStore
    allAssociatedSettlements: Settlements[]
}

export const RecalculationPayments: React.FC<RecalculationPaymentsProps> = observer(props => {
    const {balanceStore, allAssociatedSettlements} = props
    const {t} = useTranslate()
    const getColumns = (): ColumnProps<any>[] => {
        return [
            {
                key: 'postDate',
                title: t('cap-core:balance_table_post_date_label'),
                render: (text: string, record: CapBalanceItemEntity) => dateUtils(record.paymentDate).render
            },
            {
                key: 'ID',
                title: t('cap-core:balance_table_id_label'),
                render: (text: string, record: CapBalanceItemEntity) => {
                    return <span>{opt(record.paymentNumber).orElse(t('cap-core:not_available'))}</span>
                }
            },
            {
                key: 'entities',
                title: t('cap-core:balance_table_entities_label'),
                render: (text: string, record: CapBalanceItemEntity) => {
                    const rootIds = record.actualAllocations
                        .flatMap(allocation => allocation?.allocationSource?._uri)
                        .filter(Boolean)
                        .map(item => EntityLink.from(item || '').rootId)
                    const claimTypes: string[] = []
                    allAssociatedSettlements.forEach(settlement => {
                        if (rootIds.includes(settlement._key.rootId)) {
                            const isDisabilitySettlement = Object.keys(SettlementModelNamesForDisability).includes(
                                settlement._modelName || ''
                            )
                            if (isDisabilitySettlement) {
                                claimTypes.push(settlement.settlementLossInfo?.lossType!)
                            } else {
                                claimTypes.push(settlement.settlementLossInfo?.claimType)
                            }
                        }
                    })
                    return <span>{uniq(claimTypes).toString()}</span>
                }
            },
            {
                key: 'paymentAmount',
                title: t('cap-core:balance_table_payment_amount_label'),
                align: 'right',
                render: (text: string, record: CapBalanceItemEntity) => {
                    return <span>{moneyByLocale(record?.actualNetAmount?.amount || 0)}</span>
                }
            },
            {
                key: 'shouldHavePaid',
                title: t('cap-core:balance_table_should_have_paid_label'),
                align: 'right',
                render: (text: string, record: CapBalanceItemEntity) => {
                    return <span>{moneyByLocale(record?.scheduledNetAmount?.amount || 0)}</span>
                }
            },
            {
                key: 'balanceAdjustment',
                title: t('cap-core:balance_table_balance_adjustment'),
                align: 'right',
                render: (text: string, record: CapBalanceItemEntity) => {
                    const {balancedNetAmount, actualNetAmount} = record
                    const tempActualNetAmount = actualNetAmount ? actualNetAmount.amount : Number(0)
                    const tempBalancedNetAmount = balancedNetAmount ? balancedNetAmount.amount : Number(0)
                    const balanceAdjustment = tempBalancedNetAmount - tempActualNetAmount
                    let balancePoint = balanceAdjustment < 0 ? t('cap-core:incoming') : t('cap-core:outgoing')
                    if (balanceAdjustment === 0) {
                        balancePoint = ''
                    }
                    return (
                        <span>
                            {moneyByLocale(balanceAdjustment)}
                            <p className={BALANCE_TABLE_COLUMN_TAG}>{balancePoint}</p>
                        </span>
                    )
                }
            },
            {
                key: 'balance',
                title: t('cap-core:balance_table_balance_label'),
                align: 'right',
                render: (text: string, record: CapBalanceItemEntity) => {
                    const {scheduledNetAmount, actualNetAmount} = record
                    const tempScheduledNetAmount = scheduledNetAmount ? scheduledNetAmount.amount : Number(0)
                    const tempActualNetAmount = actualNetAmount ? actualNetAmount.amount : Number(0)
                    return <MoneyFormat value={tempScheduledNetAmount - tempActualNetAmount} />
                }
            }
        ]
    }

    const renderPaymentAndShouldPaidAmountBox = () => {
        const paymentAmount = sumBy(balanceStore.balance?.balanceItems, balanceItem =>
            balanceItem.actualNetAmount?.amount ? balanceItem.actualNetAmount.amount : 0
        )
        const shouldPaid = sumBy(balanceStore.balance?.balanceItems, balanceItem =>
            balanceItem.scheduledNetAmount?.amount ? balanceItem.scheduledNetAmount.amount : 0
        )
        const NA = t('cap-core:not_available')

        return (
            <div className={RECALCULATION_PAYMENTS_SECTION}>
                <div className={RECALCULATION_PAYMENTS_AMOUNT_BOX}>
                    <div>{t('cap-core:balance_table_payment_amount_label')}</div>
                    <p>{paymentAmount !== undefined ? <MoneyFormat value={paymentAmount} /> : NA}</p>
                </div>
                <div className={RECALCULATION_PAYMENTS_AMOUNT_BOX}>
                    <div>{t('cap-core:balance_table_amount_box_should_have_paid')}</div>
                    <p>{shouldPaid !== undefined ? <MoneyFormat value={shouldPaid} /> : NA}</p>
                </div>
            </div>
        )
    }

    const renderExpandContent = (record: any): React.ReactNode => {
        return (
            <BalanceListExpand
                balanceItem={record}
                balanceStore={balanceStore}
                allAssociatedSettlements={allAssociatedSettlements}
            />
        )
    }

    return (
        <div
            className={classNames(
                FINANCIAL_INFORMATION_BALANCE_SUB_TABLE,
                BALANCE_TABLE_RECALCULATION_PAYMENTS_WRAPPER
            )}
        >
            <Collapse
                defaultActiveKey='recalculationPayments'
                isWhite
                accordion
                bordered={false}
                className={BALANCE_TABLE_RECALCULATION_PAYMENTS}
            >
                <Panel
                    key='recalculationPayments'
                    header={
                        <div className={BALANCE_TABLE_RECALCULATION_PAYMENTS_TOP}>
                            <div>{t('cap-core:balance_table_label')}</div>
                            {renderPaymentAndShouldPaidAmountBox()}
                        </div>
                    }
                >
                    <div>
                        <Table
                            pagination={false}
                            dataSource={balanceStore?.balance?.balanceItems}
                            columns={getColumns()}
                            expandedRowRender={(record: any) => renderExpandContent(record)}
                            rowKey={(row, idx) => row._key?.id?.toString() || idx.toString()}
                            loading={balanceStore.actionsStore.isRunning(LOAD_BALANCE)}
                        />
                    </div>
                </Panel>
            </Collapse>
        </div>
    )
})
