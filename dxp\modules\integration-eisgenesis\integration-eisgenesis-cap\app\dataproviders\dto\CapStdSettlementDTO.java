/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.dto;

import com.eisgroup.dxp.dataproviders.genesiscapstdsettlement.dto.CapStdSettlement_CapSTDSettlementPolicyInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapstdsettlement.dto.CapStdSettlement_CapStdSettlementAbsenceInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapstdsettlement.dto.CapStdSettlement_CapStdSettlementDetailEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapstdsettlement.dto.CapStdSettlement_CapStdSettlementLossInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapstdsettlement.dto.CapStdSettlement_CapStdSettlementResultEntityDTO;
import dataproviders.common.dto.GenesisRootDTO;

public class CapStdSettlementDTO extends GenesisRootDTO {

    public String settlementType;
    public CapStdSettlement_CapStdSettlementResultEntityDTO settlementResult;
    public String settlementNumber;
    public CapStdSettlement_CapStdSettlementDetailEntityDTO settlementDetail;
    public String policyId;
    public String state;
    public CapStdSettlement_CapStdSettlementAbsenceInfoEntityDTO settlementAbsenceInfo;
    public CapStdSettlement_CapSTDSettlementPolicyInfoEntityDTO policy;
    public CapStdSettlement_CapStdSettlementLossInfoEntityDTO settlementLossInfo;
}
