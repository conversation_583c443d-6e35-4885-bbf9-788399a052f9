/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.relationships.impl;

import dataproviders.common.GenesisDataProvider;
import dataproviders.common.dto.GenesisRootDTO;
import dataproviders.common.dto.GenesisSearchRequestDTO;
import dataproviders.common.dto.GenesisSearchResponseDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.relationships.dto.GenesisTripletSearchRequestDTO;

import java.util.List;
import java.util.concurrent.CompletionStage;

public class GenesisSearchRelationshipsDataProviderImpl<T extends GenesisRootDTO> extends GenesisDataProvider
        implements GenesisSearchRelationshipsDataProvider<T> {

    private static final String GENERIC_URL_SEARCH_RELATIONSHIPS = "/api/common/search/v1/relationships";

    @Override
    protected String getBaseUrl() {
        return configuration.getString("genesis.platform.baseurl");
    }

    @Override
    protected <T extends GenesisRootDTO> CompletionStage<GenesisSearchResponseDTO<T>> search(String searchPath,
                                                                                             GenesisSearchRequestDTO searchRequest,
                                                                                             Class<T> resultType) {
        addDefaultSort(searchRequest);
        return super.search(searchPath, searchRequest, resultType);
    }

    @Override
    protected void addDefaultSort(GenesisSearchRequestDTO searchRequestDTO) {
        // no default sort for relationships search
    }

    @Override
    public CompletionStage<List<T>> getRelationships(GenesisTripletDTO tripletDTO, Class<T> tClass) {
        return search(GENERIC_URL_SEARCH_RELATIONSHIPS, new GenesisTripletSearchRequestDTO(tripletDTO), tClass)
                .thenApply(searchResponse -> searchResponse.result);
    }
}
