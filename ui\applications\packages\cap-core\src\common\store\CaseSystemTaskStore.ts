import {Org<PERSON>erson, WorkQueueDetails, WorkflowCase} from '@eisgroup/cap-services'
import type {BusinessTypes} from '@eisgroup/cap-event-case-models'
import {BaseRootStore} from './BaseRootStore'
import {ICaseSystem} from '../Types'

export interface CaseSystemTaskStore<CS extends ICaseSystem> extends BaseRootStore {
    associatedLosses?: BusinessTypes.CapLoss[]
    workCases: WorkflowCase[]
    userProfiles: OrgPerson[]
    workQueues: WorkQueueDetails[]

    loadWorkCases: (loss?: BusinessTypes.CapLoss, caseSystem?: CS) => void
    loadWorkUserProfile: () => void
    loadWorkQueues: () => void
}
