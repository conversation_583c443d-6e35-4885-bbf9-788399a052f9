warning package.json: No license field
$ vitest run

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/tempClaim/ui-claim/ui/applications/packages/cap-core[39m

 [32m✓[39m test/components/case-search/stores/CaseTableStore.test.ts [2m([22m[2m20 tests[22m[2m)[22m[90m 21[2mms[22m[39m
 [32m✓[39m test/components/case-search/stores/CaseSearchRootStore.test.ts [2m([22m[2m17 tests[22m[2m)[22m[90m 36[2mms[22m[39m
(node:76324) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:44496) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:29480) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:67988) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:99276) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/change-history/utils/columns.test.ts [2m([22m[2m17 tests[22m[2m)[22m[90m 32[2mms[22m[39m
[90mstdout[2m | test/commom/utils.test.tsx[2m > [22m[2mutils test[2m > [22m[2mshould return available payment method types
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/commom/utils.test.tsx [2m([22m[2m25 tests[22m[2m)[22m[90m 35[2mms[22m[39m
 [32m✓[39m test/components/coverage-table-attr/Utils.test.tsx [2m([22m[2m12 tests[22m[2m)[22m[90m 50[2mms[22m[39m
 [32m✓[39m test/components/case-search/services/SearchService.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 14[2mms[22m[39m
(node:7228) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstderr[2m | test/components/search/AutocompletableSearch.test.tsx[2m > [22m[2mAutocompletableSearch Component[2m > [22m[2mhandles view all click
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.
    in Animate (created by Popup)
    in div (created by Popup)
    in Popup (created by Trigger)
    in Portal (created by Trigger)
    in Trigger (created by SelectTrigger)
    in SelectTrigger (created by Select)
    in Select (created by Context.Consumer)
    in Select (created by Context.Consumer)
    in AutoComplete (created by AutoCompleteElement)
    in AutoCompleteElement
    in Unknown
    in Unknown (at AutocompletableSearch.tsx:382)
    in AutocompletableSearch (at AutocompletableSearch.test.tsx:95)

 [32m✓[39m test/components/search/AutocompletableSearch.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[33m 968[2mms[22m[39m
   [33m[2m✓[22m[39m AutocompletableSearch Component[2m > [22mrenders correctly [33m330[2mms[22m[39m
(node:88424) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:21760) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/commom/store/ManualCloseCaseClaimStore.test.ts[2m > [22m[2mManualCloseCaseClaimStore[2m > [22m[2mshould get closest scheduled payment
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/commom/store/ManualCloseCaseClaimStore.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 39[2mms[22m[39m
 [32m✓[39m test/components/approval-periods/ApprovalPeriodsDrawer.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 10[2mms[22m[39m
[90mstdout[2m | test/components/coverages-info/Utils.test.tsx[2m > [22m[2mdetermineAnniversaryDateType[2m > [22m[2mshould return "actual" when latestBenefitEndDatePlusOneDay is on or after minAnniversaryDate
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/coverages-info/Utils.test.tsx [2m([22m[2m29 tests[22m[2m)[22m[90m 47[2mms[22m[39m
(node:81420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:3616) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/utils/PaymentsUtils.test.tsx[2m > [22m[2mformatDuration[2m > [22m[2mreturns correct duration
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/utils/PaymentsUtils.test.tsx [2m([22m[2m11 tests[22m[2m)[22m[90m 30[2mms[22m[39m
(node:38540) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:31276) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:23408) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:28044) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:87972) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/payments/RenderUtils.test.tsx[2m > [22m[2mcalculateInterest[2m > [22m[2mshould create tax title for STATE
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/payments/RenderUtils.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 82[2mms[22m[39m
[90mstdout[2m | test/components/related-case/RelatedCaseTable.test.tsx[2m > [22m[2mRelatedCaseTable[2m > [22m[2mshould render the component correctly
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/related-case/RelatedCaseTable.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 1002[2mms[22m[39m
   [33m[2m✓[22m[39m RelatedCaseTable[2m > [22mshould handle remove action [33m312[2mms[22m[39m
[90mstderr[2m | test/components/related-case/RelatedCaseTable.test.tsx
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.
    in Animate (created by Popup)
    in div (created by Popup)
    in Popup (created by Trigger)
    in Portal (created by Trigger)
    in Trigger (created by Tooltip)
    in Tooltip (created by Context.Consumer)
    in Tooltip (created by Context.Consumer)
    in Popconfirm (created by Popconfirm)
    in Popconfirm (at ListActions.tsx:76)
    in div (at ListActions.tsx:131)
    in ListActions (at RelatedCaseTable.tsx:157)
    in td (created by TableCell)
    in TableCell (created by TableRow)
    in tr (created by BodyRow)
    in BodyRow (created by TableRow)
    in TableRow (created by Connect(TableRow))
    in Connect(TableRow) (created by ExpandableRow)
    in ExpandableRow (created by Connect(ExpandableRow))
    in Connect(ExpandableRow) (created by BaseTable)
    in tbody (created by BaseTable)
    in table (created by BaseTable)
    in BaseTable (created by Connect(BaseTable))
    in Connect(BaseTable) (created by BodyTable)
    in div (created by BodyTable)
    in BodyTable (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in ExpandableTable (created by Connect(ExpandableTable))
    in Connect(ExpandableTable) (created by Table)
    in Provider (created by Table)
    in Table (created by LocaleReceiver)
    in LocaleReceiver (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Table (created by ForwardRef(TableComponent))
    in ForwardRef(TableComponent) (at RelatedCaseTable.tsx:187)
    in div (created by PanelContent)
    in div (created by PanelContent)
    in PanelContent (created by CollapsePanel)
    in AnimateChild (created by Animate)
    in Animate (created by CollapsePanel)
    in div (created by CollapsePanel)
    in CollapsePanel (created by Context.Consumer)
    in CollapsePanel (created by Panel)
    in Panel (at RelatedCaseTable.tsx:183)
    in div (created by Collapse)
    in Collapse (created by Context.Consumer)
    in Collapse (created by Collapse)
    in Collapse (at RelatedCaseTable.tsx:182)
    in div (at RelatedCaseTable.tsx:181)
    in Unknown (at RelatedCaseTable.test.tsx:206)

 [32m✓[39m test/commom/store/CustomerStore.test.ts [2m([22m[2m13 tests[22m[2m)[22m[90m 40[2mms[22m[39m
 [32m✓[39m test/commom/CaseRelationshipStore.test.ts [2m([22m[2m10 tests[22m[2m)[22m[90m 37[2mms[22m[39m
[90mstdout[2m | test/utils/CaseSystemPaymentStoreUtils.test.ts[2m > [22m[2mCaseSystemPaymentStoreUtils[2m > [22m[2mshould filter allocation condition
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/utils/CaseSystemPaymentStoreUtils.test.ts [2m([22m[2m24 tests[22m[2m)[22m[90m 64[2mms[22m[39m
[90mstderr[2m | test/components/party-details-form/PartyDetailsRole.test.tsx[2m > [22m[2mPartyDetailsRole[2m > [22m[2mrenders without crashing
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectFilter (at PartyClaimRolesInput.tsx:31)
    in div (at PartyClaimRolesInput.tsx:30)
    in PartyClaimRolesInput (at PartyDetailsRole.tsx:126)
    in div (at PartyDetailsRole.tsx:125)
    in div (created by Context.Consumer)
    in Col (created by Col)
    in Col (at PartyDetailsRole.tsx:124)
    in div (created by Context.Consumer)
    in Row (created by Row)
    in Row (at PartyDetailsRole.tsx:115)
    in PartyDetailsRole (at PartyDetailsRole.test.tsx:20)
 [32m✓[39m test/components/party-details-form/PartyDetailsRole.test.tsx [2m([22m[2m10 tests[22m[2m)[22m[33m 1279[2mms[22m[39m
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)
Warning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectCmp (created by withLookupBody(LookupSelectCmp))
    in withLookupBody(LookupSelectCmp) (created by FormBlock)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (created by FormItem)
    in FormItem (created by FormBlock)
    in RenderIf (created by FormBlock)
    in FormBlock (created by [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))
    in [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)) (created by FieldComponent)
    in FieldComponent (created by ForwardRef(Field))
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))) (created by ValuesSpy)
    in ValuesSpy (created by [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))))
    in [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))) (at PartyClaimRelationshipInput.tsx:20)
    in div (at PartyClaimRelationshipInput.tsx:19)
    in PartyClaimRelationshipInput (at PartyDetailsRole.tsx:136)
    in div (at PartyDetailsRole.tsx:135)
    in div (created by Context.Consumer)
    in Col (created by Col)
    in Col (at PartyDetailsRole.tsx:134)
    in div (created by Context.Consumer)
    in Row (created by Row)
    in Row (at PartyDetailsRole.tsx:115)
    in PartyDetailsRole (at PartyDetailsRole.test.tsx:20)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

 [32m✓[39m test/commom/store/CaseSystemPaymentStoreImpl.test.ts [2m([22m[2m39 tests[22m[2m | [22m[33m1 skipped[39m[2m)[22m[33m 3085[2mms[22m[39m
   [33m[2m✓[22m[39m CaseSystemPaymentStoreImpl[2m > [22mshould loadCurrentPayments [33m3015[2mms[22m[39m
(node:51328) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstderr[2m | test/components/claim-banner/contact-information-popover/PreferredPaymentMethodEditor.test.tsx[2m > [22m[2mPreferredPaymentMethodEditor[2m > [22m[2mrenders without crashing
[22m[39mWarning: Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?

Check the render method of `PreferredPaymentMethod`.
[90mstdout[2m | test/components/claim-banner/contact-information-popover/PreferredPaymentMethodEditor.test.tsx[2m > [22m[2mPreferredPaymentMethodEditor[2m > [22m[2mshould handle CHECK payment method type correctly
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

    in Unknown (at PreferredPaymentMethodSelect.tsx:162)
    in FieldElemWrapper (created by PreferredPaymentMethod)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
 [32m✓[39m test/components/claim-banner/contact-information-popover/PreferredPaymentMethodEditor.test.tsx [2m([22m[2m17 tests[22m[2m)[22m[33m 23276[2mms[22m[39m
   [33m[2m✓[22m[39m PreferredPaymentMethodEditor[2m > [22mrenders without crashing [33m22100[2mms[22m[39m
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (at PreferredPaymentMethodSelect.tsx:149)
    in div (created by Context.Consumer)
    in Col (created by Col)
    in Col (at PreferredPaymentMethodSelect.tsx:148)
    in div (created by Context.Consumer)
    in Row (created by Row)
    in Row (at PreferredPaymentMethodSelect.tsx:147)
    in PreferredPaymentMethod (created by Form(PreferredPaymentMethod))
    in Form(PreferredPaymentMethod) (at PreferredPaymentMethodEditor.tsx:228)
    in Unknown
    in Unknown (created by withConditionHOC(Component))
    in withConditionHOC(Component) (created by SlotEngineBlock)
    in SlotEngineBlock
    in Unknown (created by ComponentsBlock)
    in ComponentsBlock (created by ReactFinalForm)
    in form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by RootFormEngineComponent)
    in RootFormEngineComponent (created by RootEngineComponent)
    in RootEngineComponent (created by UIEngine)
    in EventServiceInitializer (created by UIEngine)
    in RenderedComponentsTreeProvider (created by UIEngine)
    in FieldValidationContextProvider (created by UIEngine)
    in GlobalStoreWrapper (created by UIEngine)
    in ReadOnlyModeProvider (created by UIEngine)
    in UIEngine (at PreferredPaymentMethodEditor.tsx:253)
    in div (created by Drawer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in DrawerChild (created by PortalWrapper)
    in Portal (created by PortalWrapper)
    in PortalWrapper (created by DrawerWrapper)
    in DrawerWrapper (created by Context.Consumer)
    in Drawer (created by Context.Consumer)
    in withConfigConsumer(Drawer) (created by Drawer)
    in Drawer (at FormDrawer.tsx:104)
    in FormDrawer (at PreferredPaymentMethodEditor.tsx:322)
    in PreferredPaymentMethodEditor (at PreferredPaymentMethodEditor.test.tsx:61)

[90mstdout[2m | test/components/payments/PaymentsAndRecoveriesTable.test.tsx[2m > [22m[2mPaymentsAndRecoveriesTable[2m > [22m[2mrenders PaymentsAndRecoveriesTable with payments and recoveries
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/payments/PaymentsAndRecoveriesTable.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[33m 456[2mms[22m[39m
(node:18868) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:19068) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:76980) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/store/IntakeStore.test.ts [2m([22m[2m13 tests[22m[2m)[22m[90m 42[2mms[22m[39m
(node:41792) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:86492) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/case-search/stores/CaseSuggestionStore.test.ts [2m([22m[2m15 tests[22m[2m)[22m[90m 22[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/ClaimBannerHeader.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 331[2mms[22m[39m
(node:88928) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:73056) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstderr[2m | test/components/claim-pre-existing-conditions/PreExistingConditionsInfoTable.test.tsx[2m > [22m[2mPreExistingConditionsInfoTable[2m > [22m[2mrenders tables and header
 [32m✓[39m test/components/claim-pre-existing-conditions/PreExistingConditionsInfoTable.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 585[2mms[22m[39m
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at PreExistingConditionsInfoTable.tsx:273)
    in div (at PreExistingConditionsInfoTable.tsx:272)
    in div (at PreExistingConditionsInfoTable.tsx:261)
    in div (created by Context.Consumer)
   [33m[2m✓[22m[39m PreExistingConditionsInfoTable[2m > [22mrenders tables and header [33m326[2mms[22m[39m
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at PreExistingConditionsInfoTable.tsx:260)
    in wrappedComponent (at PreExistingConditionsInfoTable.test.tsx:99)
Warning: componentWillReceiveProps has been renamed, and is not recommended for use. See https://fb.me/react-unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://fb.me/react-derived-state
* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 17.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.

Please update the following components: Table

[90mstderr[2m | test/components/claim-pre-existing-conditions/PreExistingConditionsInfoTable.test.tsx[2m > [22m[2mPreExistingConditionsInfoTable[2m > [22m[2mdoes not allow edit when no authority
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at PreExistingConditionsInfoTable.tsx:273)
    in div (at PreExistingConditionsInfoTable.tsx:272)
    in div (at PreExistingConditionsInfoTable.tsx:261)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at PreExistingConditionsInfoTable.tsx:260)
    in wrappedComponent (at PreExistingConditionsInfoTable.test.tsx:128)

(node:84328) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:31112) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/deductions/DeductionsTable.test.tsx[2m > [22m[2mDeductionsTable[2m > [22m[2mshould render without crashing
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/deductions/DeductionsTable.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 958[2mms[22m[39m
   [33m[2m✓[22m[39m DeductionsTable[2m > [22mshould render without crashing [33m433[2mms[22m[39m
(node:57316) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/ClaimPartyStore.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 25[2mms[22m[39m
(node:97232) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/utils/ValidationUtils.test.ts[2m > [22m[2mValidation Utils[2m > [22m[2mperiodsOverlap[2m > [22m[2mshould not overlap
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/utils/ValidationUtils.test.ts [2m([22m[2m16 tests[22m[2m)[22m[90m 30[2mms[22m[39m
 [32m✓[39m test/commom/store/ChangeHistoryStore.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 24[2mms[22m[39m
[90mstdout[2m | test/commom/ClaimPartyUtils.test.tsx[2m > [22m[2mvalidateBirthDate[2m > [22m[2mreturns undefined if value is within the valid range
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/commom/ClaimPartyUtils.test.tsx [2m([22m[2m11 tests[22m[2m)[22m[90m 22[2mms[22m[39m
(node:22824) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:49796) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/ClaimLossUtils.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 15[2mms[22m[39m
(node:48668) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/CustomerUtils.test.ts [2m([22m[2m9 tests[22m[2m)[22m[90m 12[2mms[22m[39m
 [32m✓[39m test/utils/CustomerSearchUtils.test.ts [2m([22m[2m9 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/commom/constants.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 11[2mms[22m[39m
(node:65668) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/icd-code-table/IcdCodeTable.test.tsx[2m > [22m[2mIcdCodeTable[2m > [22m[2mshould render correctly
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

[90mstderr[2m | test/components/icd-code-table/IcdCodeTable.test.tsx[2m > [22m[2mIcdCodeTable[2m > [22m[2mshould render correctly
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:49)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)
Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:49)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

[90mstderr[2m | test/components/icd-code-table/IcdCodeTable.test.tsx[2m > [22m[2mIcdCodeTable[2m > [22m[2mshould display correct validation messages when create new icd entry
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:65)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)
Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:65)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)
Warning: Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state.
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(TextInput)))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(TextInput)) (at IcdSearch.tsx:142)
    in IcdSearch (at IcdCodeTable.tsx:123)
    in div (at IcdCodeTable.tsx:122)
    in td (created by TableCell)
    in TableCell (created by TableRow)
    in tr (created by BodyRow)
    in BodyRow (created by TableRow)
    in TableRow (created by Connect(TableRow))
    in Connect(TableRow) (created by ExpandableRow)
    in ExpandableRow (created by Connect(ExpandableRow))
    in Connect(ExpandableRow) (created by BaseTable)
    in tbody (created by BaseTable)
    in table (created by BaseTable)
    in BaseTable (created by Connect(BaseTable))
    in Connect(BaseTable) (created by BodyTable)
    in div (created by BodyTable)
    in BodyTable (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in ExpandableTable (created by Connect(ExpandableTable))
    in Connect(ExpandableTable) (created by Table)
    in Provider (created by Table)
    in Table (created by LocaleReceiver)
    in LocaleReceiver (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Table (created by ForwardRef(TableComponent))
    in ForwardRef(TableComponent) (at DataTable.tsx:197)
    in div (at AddableTableWrapper.tsx:84)
    in AddableTableWrapper (at DataTable.tsx:187)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:65)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

[90mstderr[2m | test/components/icd-code-table/IcdCodeTable.test.tsx[2m > [22m[2mIcdCodeTable[2m > [22m[2mshould display correct validation messages when mutiple icd codes are selected primary
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
 [32m✓[39m test/components/icd-code-table/IcdCodeTable.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 1406[2mms[22m[39m
   [33m[2m✓[22m[39m IcdCodeTable[2m > [22mshould render correctly [33m416[2mms[22m[39m
   [33m[2m✓[22m[39m IcdCodeTable[2m > [22mshould display correct validation messages when create new icd entry [33m357[2mms[22m[39m
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
   [33m[2m✓[22m[39m IcdCodeTable[2m > [22mshould display correct validation messages when mutiple icd codes are selected primary [33m326[2mms[22m[39m
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:75)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)
Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:75)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

[90mstderr[2m | test/components/icd-code-table/IcdCodeTable.test.tsx[2m > [22m[2mIcdCodeTable[2m > [22m[2mshould display validation messge when code is not selected while edit existing
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:86)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)
Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at IcdCodeTable.tsx:80)
    in PrefixProvider (at IcdCodeTable.tsx:79)
    in div (at IcdCodeTable.tsx:78)
    in IcdCodeTable (at IcdCodeTable.test.tsx:86)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

(node:98112) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/Utils.test.ts [2m([22m[2m10 tests[22m[2m)[22m[90m 24[2mms[22m[39m
(node:95760) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/approval-periods/ApprovalPeriods.test.tsx[2m > [22m[2mApprovalPeriods[2m > [22m[2mshould render and handle drawer interactions
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/approval-periods/ApprovalPeriods.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 246[2mms[22m[39m
(node:32100) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/search-table/searchPartyStore.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 18[2mms[22m[39m
(node:39712) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:88724) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:89080) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:30136) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/change-history/utils/rows.test.ts[2m > [22m[2mRows data gathering utils[2m > [22m[2mshould display payments data
[22m[39m'event_case_link':'Event Case Link',
'test_coverage':'test coverage',
[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

[90mstdout[2m | test/components/change-history/utils/rows.test.ts[2m > [22m[2mRows data gathering utils[2m > [22m[2mshould display loss data
[22m[39m'event_case_link':'Event Case Link',
'test_coverage':'test coverage',

[90mstdout[2m | test/components/change-history/utils/rows.test.ts[2m > [22m[2mRows data gathering utils[2m > [22m[2mshould display Event Case data
[22m[39m'event_case_link':'Event Case Link',
'test_coverage':'test coverage',

 [32m✓[39m test/components/change-history/utils/rows.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 45[2mms[22m[39m
(node:63972) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:63284) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:61692) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/store/ClaimStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 22[2mms[22m[39m
(node:38612) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstderr[2m | test/components/claim-banner/claim-banner-custom-controls/ClaimBannerCustomControls.test.tsx[2m > [22m[2mClaimBannerCustomControls[2m > [22m[2mshould have follow up task available if claim is closed, ClaimWrapper
[22m[39mWarning: toBeEmpty has been deprecated and will be removed in future updates.

[90mstderr[2m | test/components/claim-banner/claim-banner-custom-controls/ClaimBannerCustomControls.test.tsx[2m > [22m[2mClaimBannerCustomControls[2m > [22m[2mshould have follow up task available for Leave Claim
[22m[39mWarning: toBeEmpty has been deprecated and will be removed in future updates.

 [32m✓[39m test/components/claim-banner/claim-banner-custom-controls/ClaimBannerCustomControls.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 605[2mms[22m[39m
[90mstderr[2m | test/components/header-popup-drawer/case-relationship/CaseRelationshipDrawer.test.tsx[2m > [22m[2mCaseRelationshipDrawer[2m > [22m[2mrenders the drawer with the correct title for Create mode
[22m[39mNeed to initialize LocalizerProvider first
Need to initialize LocalizerProvider first

[90mstderr[2m | test/components/header-popup-drawer/case-relationship/CaseRelationshipDrawer.test.tsx[2m > [22m[2mCaseRelationshipDrawer[2m > [22m[2mrenders the drawer with the correct title for Edit mode
[22m[39mNeed to initialize LocalizerProvider first
Need to initialize LocalizerProvider first

[90mstderr[2m | test/components/header-popup-drawer/case-relationship/CaseRelationshipDrawer.test.tsx[2m > [22m[2mCaseRelationshipDrawer[2m > [22m[2mcreate mode submit empty form should has error
[22m[39mNeed to initialize LocalizerProvider first
Need to initialize LocalizerProvider first

[90mstderr[2m | test/components/header-popup-drawer/case-relationship/CaseRelationshipDrawer.test.tsx[2m > [22m[2mCaseRelationshipDrawer[2m > [22m[2mcalls submitCaseRelationship with update params on form submit in Edit mode
[22m[39mNeed to initialize LocalizerProvider first
Need to initialize LocalizerProvider first

 [32m✓[39m test/components/header-popup-drawer/case-relationship/CaseRelationshipDrawer.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 15438[2mms[22m[39m
   [33m[2m✓[22m[39m CaseRelationshipDrawer[2m > [22mrenders the drawer with the correct title for Create mode [33m14857[2mms[22m[39m
(node:97516) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/DeductionsUtils.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 10[2mms[22m[39m
(node:68756) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:14448) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:71344) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/work-days/WorkDays.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 10[2mms[22m[39m
[90mstdout[2m | test/components/coverages-info/RenderUtils.test.tsx[2m > [22m[2mvalueToRender function[2m > [22m[2mshould return a formatted date when overrideInputType is DATE
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/coverages-info/RenderUtils.test.tsx [2m([22m[2m10 tests[22m[2m)[22m[90m 94[2mms[22m[39m
[90mstderr[2m | test/components/coverages-info/CoveragesInfoTable.test.tsx[2m > [22m[2mCoveragesInfoTable[2m > [22m[2mrenders without error
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
[90mstdout[2m | test/components/coverages-info/CoveragesInfoTable.test.tsx[2m > [22m[2mCoveragesInfoTable-LTD[2m > [22m[2mrenders without error
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.
    in Form (at EditableTable.tsx:67)
    in EditableTable (at CoveragesInfoTable.tsx:468)

    in div (at CoveragesInfoTable.tsx:467)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at CoveragesInfoTable.tsx:465)
    in wrappedComponent (at CoveragesInfoTable.test.tsx:52)

[90mstderr[2m | test/components/coverages-info/CoveragesInfoTable.test.tsx[2m > [22m[2mCoveragesInfoTable[2m > [22m[2mon edit click
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
 [32m✓[39m test/components/coverages-info/CoveragesInfoTable.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 1438[2mms[22m[39m
   [33m[2m✓[22m[39m CoveragesInfoTable[2m > [22mrenders without error [33m344[2mms[22m[39m
   [33m[2m✓[22m[39m CoveragesInfoTable[2m > [22mon edit click [33m332[2mms[22m[39m
   [33m[2m✓[22m[39m CoveragesInfoTable-LTD[2m > [22mrenders without colaAdditions [33m512[2mms[22m[39m
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at CoveragesInfoTable.tsx:468)
    in div (at CoveragesInfoTable.tsx:467)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at CoveragesInfoTable.tsx:465)
    in wrappedComponent (at CoveragesInfoTable.test.tsx:61)

[90mstderr[2m | test/components/coverages-info/CoveragesInfoTable.test.tsx[2m > [22m[2mCoveragesInfoTable-LTD[2m > [22m[2mrenders without error
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at CoveragesInfoTable.tsx:468)
    in div (at CoveragesInfoTable.tsx:467)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at CoveragesInfoTable.tsx:465)
    in wrappedComponent (at CoveragesInfoTable.test.tsx:75)

[90mstderr[2m | test/components/coverages-info/CoveragesInfoTable.test.tsx[2m > [22m[2mCoveragesInfoTable-LTD[2m > [22m[2mrenders without colaAdditions
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at CoveragesInfoTable.tsx:468)
    in div (at CoveragesInfoTable.tsx:467)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at CoveragesInfoTable.tsx:465)
    in wrappedComponent (at CoveragesInfoTable.test.tsx:83)

(node:35796) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/financial-info/FinancialUtils.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 28[2mms[22m[39m
(node:71080) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstderr[2m | test/components/party-details-form/PartyDetailsForm.test.tsx[2m > [22m[2mPartyDetailsForm[2m > [22m[2mrenders the component
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectCmp (created by withLookupBody(LookupSelectCmp))
    in withLookupBody(LookupSelectCmp) (created by FormBlock)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (created by FormItem)
    in FormItem (created by FormBlock)
    in RenderIf (created by FormBlock)
    in FormBlock (created by [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))
    in [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)) (created by FieldComponent)
    in FieldComponent (created by ForwardRef(Field))
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))) (created by ValuesSpy)
    in ValuesSpy (created by [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))))
    in [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))) (at PersonBaseDetails.tsx:151)
    in div (at PartyDetailsForm.tsx:81)
    in PersonBaseDetails (at PartyDetailsForm.tsx:77)
    in PrefixProvider (at PartyDetailsForm.tsx:76)
    in section (at PartyDetailsForm.tsx:142)
    in div (at PartyDetailsForm.tsx:138)
    in PartyDetailsForm (at PartyDetailsForm.test.tsx:29)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

 [32m✓[39m test/components/party-details-form/PartyDetailsForm.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 1458[2mms[22m[39m
   [33m[2m✓[22m[39m PartyDetailsForm[2m > [22mrenders the component [33m522[2mms[22m[39m
   [33m[2m✓[22m[39m PartyDetailsForm[2m > [22mrenders disabled fields when disablePartyRolesInput is true [33m347[2mms[22m[39m
 [32m✓[39m test/commom/store/CoverageStore.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 9[2mms[22m[39m
[90mstdout[2m | test/components/party-details-form/search/PartySearchContainer.test.tsx[2m > [22m[2mBasePartySearch[2m > [22m[2mshould select suggestion and call onSelect
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/party-details-form/search/PartySearchContainer.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 765[2mms[22m[39m
   [33m[2m✓[22m[39m BasePartySearch[2m > [22mshould select suggestion and call onSelect [33m438[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/address/CustomerAddressLine.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 52[2mms[22m[39m
[90mstderr[2m | test/components/icd-code-table/IcdSearch.test.tsx[2m > [22m[2mIcdSearch[2m > [22m[2mclears suggestions when input is cleared
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in IcdSearch (at IcdSearch.test.tsx:67)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

 [32m✓[39m test/components/icd-code-table/IcdSearch.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 1520[2mms[22m[39m
   [33m[2m✓[22m[39m IcdSearch[2m > [22mdisplays search suggestions when typing [33m635[2mms[22m[39m
   [33m[2m✓[22m[39m IcdSearch[2m > [22mclears suggestions when input is cleared [33m584[2mms[22m[39m
 [32m✓[39m test/components/payments/payment-drawer/steps/PaymentDetailStep.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 18[2mms[22m[39m
(node:83724) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:62256) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:16648) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:65916) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/payments/PremiumWaiverSettlementPeriodsDetails.test.tsx[2m > [22m[2mPremiumWaiverSettlementPeriodsDetail[2m > [22m[2mrenders correct benefit period data
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/payments/PremiumWaiverSettlementPeriodsDetails.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 303[2mms[22m[39m
(node:40720) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:5676) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/balance/util.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[90m 30[2mms[22m[39m
(node:40476) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/financial-info/summary-of-taxes/SummaryOfTaxesTable.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 499[2mms[22m[39m
(node:42344) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:19004) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/claim-pre-existing-conditions/TextWithPopover.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 368[2mms[22m[39m
 [32m✓[39m test/components/add-new-party/partyStore.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 7[2mms[22m[39m
 [32m✓[39m test/components/balance/PaymentWithholdingTable.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 495[2mms[22m[39m
   [33m[2m✓[22m[39m PaymentWithholdingTable[2m > [22mshould render correctly [33m391[2mms[22m[39m
(node:50752) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:44508) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/store/UserStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 11[2mms[22m[39m
 [32m✓[39m test/components/coverage-table-attr/UnpaidAmountAndRemainLimitPopover.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[90m 211[2mms[22m[39m
(node:72016) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:47532) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/utils/VendorsUtils.test.ts[2m > [22m[2mVendorsUtils[2m > [22m[2mgetDOB[2m > [22m[2mshould return formatted birth date for IndividualCustomer
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/utils/VendorsUtils.test.ts [2m([22m[2m7 tests[22m[2m)[22m[90m 20[2mms[22m[39m
[90mstdout[2m | test/components/claim-banner/BannerDetailsMainInsured.test.tsx[2m > [22m[2mBannerDetailsMainInsured[2m > [22m[2mrenders main insured details
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/claim-banner/BannerDetailsMainInsured.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 309[2mms[22m[39m
[90mstderr[2m | test/components/earnings/Earnings.test.tsx[2m > [22m[2mEarnings component[2m > [22m[2mshould trigger onSubmit callback after save clicked
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectFilter (at BaseSalaryCard.tsx:97)
    in div (at BaseSalaryCard.tsx:96)
    in div (at BaseSalaryCard.tsx:94)
    in div (at BaseSalaryCard.tsx:93)
    in BaseSalaryCard (at Earnings.tsx:189)
    in div (at Earnings.tsx:151)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at Earnings.tsx:150)
    in PrefixProvider (at Earnings.tsx:149)
    in div (at AddableTableWrapper.tsx:84)
    in AddableTableWrapper (at Earnings.tsx:142)
    in FormSpy (at Earnings.tsx:132)
    in Earnings (at Earnings.test.tsx:47)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
 [32m✓[39m test/components/earnings/Earnings.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 1028[2mms[22m[39m
   [33m[2m✓[22m[39m Earnings component[2m > [22mshould reset form to initial state after cancel clicked [33m328[2mms[22m[39m
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

 [32m✓[39m test/utils/EventCaseUtils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 8[2mms[22m[39m
[90mstderr[2m | test/components/prior-earnings/prior-earnings.test.tsx[2m > [22m[2mPriorEarnings[2m > [22m[2mrenders without error
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at DataTable.tsx:185)
    in DataTable (created by FieldArray)
    in FieldArray (created by [react-final-form]fieldArrayHoc(DataTable))
    in [react-final-form]fieldArrayHoc(DataTable) (at EditableTable.tsx:68)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
 [32m✓[39m test/components/prior-earnings/prior-earnings.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 190[2mms[22m[39m
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at EditableTable.tsx:67)
    in EditableTable (at PriorEarnings.tsx:69)
    in div (at PriorEarnings.tsx:68)
    in PriorEarnings (at prior-earnings.test.tsx:81)

(node:5352) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/case-search/stores/CaseAdvanceFilterStore.test.ts [2m([22m[2m7 tests[22m[2m)[22m[90m 11[2mms[22m[39m
 [32m✓[39m test/commom/package-class-names.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 41[2mms[22m[39m
(node:96316) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/store/IcdCodeStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 25[2mms[22m[39m
(node:84248) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:36008) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/EntityLink.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 11[2mms[22m[39m
[90mstdout[2m | test/components/claim-banner/ClaimHeader.test.tsx[2m > [22m[2mClaimHeaderPaidToDate[2m > [22m[2mrenders headerPTD without a warning icon when paidToDate is greater than lossDate
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

(node:70364) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/claim-banner/ClaimHeader.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 561[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimHeaderPaidToDate[2m > [22mrenders tooltip with "Date of Loss" message when lossType is not a disability loss type [33m323[2mms[22m[39m
[90mstderr[2m | test/components/add-new-party/partyInfoDrawer.test.tsx[2m > [22m[2mPartyInfoDrawer[2m > [22m[2mrenders without crashing
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectCmp (created by withLookupBody(LookupSelectCmp))
    in withLookupBody(LookupSelectCmp) (created by FormBlock)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (created by FormItem)
    in FormItem (created by FormBlock)
    in RenderIf (created by FormBlock)
 [32m✓[39m test/components/add-new-party/partyInfoDrawer.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 708[2mms[22m[39m
    in FormBlock (created by [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))
    in [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)) (created by FieldComponent)
    in FieldComponent (created by ForwardRef(Field))
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))) (created by ValuesSpy)
    in ValuesSpy (created by [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))))
    in [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))) (at PersonBaseDetails.tsx:151)
    in div (at PartyDetailsForm.tsx:81)
    in PersonBaseDetails (at PartyDetailsForm.tsx:77)
    in PrefixProvider (at PartyDetailsForm.tsx:76)
    in section (at PartyDetailsForm.tsx:142)
    in div (at PartyDetailsForm.tsx:138)
    in PartyDetailsForm (at partyInfoComponent.tsx:65)
    in PrefixProvider (at partyInfoComponent.tsx:64)
    in div (at partyInfoComponent.tsx:63)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at partyInfoComponent.tsx:60)
    in PartyInfoComponent (at partyInfoDrawer.tsx:122)
    in div (created by Drawer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in DrawerChild (created by PortalWrapper)
    in Portal (created by PortalWrapper)
    in PortalWrapper (created by DrawerWrapper)
    in DrawerWrapper (created by Context.Consumer)
    in Drawer (created by Context.Consumer)
    in withConfigConsumer(Drawer) (created by Drawer)
    in Drawer (at FormDrawer.tsx:104)
    in FormDrawer (at partyInfoDrawer.tsx:115)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at partyInfoDrawer.tsx:114)
   [33m[2m✓[22m[39m PartyInfoDrawer[2m > [22mrenders without crashing [33m705[2mms[22m[39m
    in PartyInfoDrawer (at partyInfoDrawer.test.tsx:73)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

(node:23900) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:42448) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:46868) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/utils/SortingUtils.test.ts[2m > [22m[2mSortingUtils[2m > [22m[2msortByDate[2m > [22m[2mshould sort objects by date in descending order
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/utils/SortingUtils.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 20[2mms[22m[39m
(node:98412) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:65604) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:38716) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/check-address-section/CheckAddressSection.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 250[2mms[22m[39m
(node:33932) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/kraken/kraken-utils.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/components/score-detail/scoreDetailDrawer.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 417[2mms[22m[39m
 [32m✓[39m test/components/approval-periods/ApprovalPeriodsDrawerBuilder.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 16922[2mms[22m[39m
   [33m[2m✓[22m[39m ApprovalPeriodsDrawer should render with UI builder[2m > [22mshould render the component [33m16920[2mms[22m[39m
 [32m✓[39m test/commom/AssignStore.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 15[2mms[22m[39m
(node:97800) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/coverage-table-attr/FormulaContent.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 101[2mms[22m[39m
(node:43780) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:82456) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/coverages-info/CoveragesInfoHeader.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 108[2mms[22m[39m
[90mstdout[2m | test/components/payments/allocation-table/AllocationTable.test.tsx[2m > [22m[2mAllocationTable[2m > [22m[2mshould render without crashing
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/payments/allocation-table/AllocationTable.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 293[2mms[22m[39m
(node:69916) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/Types.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 9[2mms[22m[39m
(node:10668) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/payments/Utils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/commom/store/paymentMethodStore.test.ts [2m([22m[2m3 tests[22m[2m)[22m[90m 11[2mms[22m[39m
(node:72556) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:11216) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/change-history/utils/useHistoryTableFilters.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 51[2mms[22m[39m
[90mstdout[2m | test/components/financial-info/taxes/TaxesTable.test.tsx[2m > [22m[2mTaxesTable Component[2m > [22m[2mshould render TaxesTable component
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

[90mstderr[2m | test/components/financial-info/taxes/TaxesTable.test.tsx[2m > [22m[2mTaxesTable Component[2m > [22m[2mshould render TaxesTable component
[22m[39mWarning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    in form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
 [32m✓[39m test/components/financial-info/taxes/TaxesTable.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 19013[2mms[22m[39m
   [33m[2m✓[22m[39m TaxesTable Component[2m > [22mshould render TaxesTable component [33m19010[2mms[22m[39m
    in BaseForm (created by RootFormEngineComponent)
    in RootFormEngineComponent (created by RootEngineComponent)
    in RootEngineComponent (created by UIEngine)
    in EventServiceInitializer (created by UIEngine)
    in RenderedComponentsTreeProvider (created by UIEngine)
    in FieldValidationContextProvider (created by UIEngine)
    in GlobalStoreWrapper (created by UIEngine)
    in ReadOnlyModeProvider (created by UIEngine)
    in UIEngine (at TaxesTable.tsx:681)
    in PrefixProvider (at TaxesTable.tsx:680)
    in form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (at TaxesTable.tsx:675)
    in div (created by Drawer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in DrawerChild (created by PortalWrapper)
    in Portal (created by PortalWrapper)
    in PortalWrapper (created by DrawerWrapper)
    in DrawerWrapper (created by Context.Consumer)
    in Drawer (created by Context.Consumer)
    in withConfigConsumer(Drawer) (created by Drawer)
    in Drawer (at FormDrawer.tsx:104)
    in FormDrawer (at TaxesTable.tsx:781)
    in div (at AddableTableWrapper.tsx:84)
    in AddableTableWrapper (at TaxesTable.tsx:763)
    in div (created by PanelContent)
    in div (created by PanelContent)
    in PanelContent (created by CollapsePanel)
    in AnimateChild (created by Animate)
    in Animate (created by CollapsePanel)
    in div (created by CollapsePanel)
    in CollapsePanel (created by Context.Consumer)
    in CollapsePanel (created by Panel)
    in Panel (at TaxesTable.tsx:743)
    in div (created by Collapse)
    in Collapse (created by Context.Consumer)
    in Collapse (created by Collapse)
    in Collapse (at TaxesTable.tsx:736)
    in div (at TaxesTable.tsx:735)
    in wrappedComponent (at TaxesTable.test.tsx:45)
Need to initialize LocalizerProvider first
Need to initialize LocalizerProvider first
Need to initialize LocalizerProvider first
Need to initialize LocalizerProvider first

[90mstderr[2m | test/components/search-table/addNewPartyBtn.test.tsx[2m > [22m[2mRenderAddNewPartyBtn[2m > [22m[2mcalls setFormParty and setSelectedResult when button is clicked
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectCmp (created by withLookupBody(LookupSelectCmp))
    in withLookupBody(LookupSelectCmp) (created by FormBlock)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (created by FormItem)
    in FormItem (created by FormBlock)
    in RenderIf (created by FormBlock)
    in FormBlock (created by [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))
    in [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)) (created by FieldComponent)
    in FieldComponent (created by ForwardRef(Field))
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))) (created by ValuesSpy)
    in ValuesSpy (created by [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))))
    in [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))) (at AddressInfo.tsx:79)
    in div (at PartyDetailsForm.tsx:218)
    in div (at PartyDetailsForm.tsx:216)
    in AddressInfo (at PartyDetailsForm.tsx:189)
 [32m✓[39m test/components/search-table/addNewPartyBtn.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 406[2mms[22m[39m
   [33m[2m✓[22m[39m RenderAddNewPartyBtn[2m > [22mcalls setFormParty and setSelectedResult when button is clicked [33m360[2mms[22m[39m
    in PrefixProvider (at PartyDetailsForm.tsx:178)
    in section (at PartyDetailsForm.tsx:177)
    in div (at PartyDetailsForm.tsx:173)
    in PartyDetailsForm (at partyInfoComponent.tsx:65)
    in PrefixProvider (at partyInfoComponent.tsx:64)
    in div (at partyInfoComponent.tsx:63)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at partyInfoComponent.tsx:60)
    in PartyInfoComponent (at partyInfoDrawer.tsx:122)
    in div (created by Drawer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in DrawerChild (created by PortalWrapper)
    in Portal (created by PortalWrapper)
    in PortalWrapper (created by DrawerWrapper)
    in DrawerWrapper (created by Context.Consumer)
    in Drawer (created by Context.Consumer)
    in withConfigConsumer(Drawer) (created by Drawer)
    in Drawer (at FormDrawer.tsx:104)
    in FormDrawer (at partyInfoDrawer.tsx:115)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Spin)
    in Spin (at partyInfoDrawer.tsx:114)
    in PartyInfoDrawer (at addNewPartyBtn.tsx:118)
    in RenderAddNewPartyBtn (at addNewPartyBtn.test.tsx:43)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

(node:42404) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/claim-banner/contact-information-popover/WorkDaysInlineEditor.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 71[2mms[22m[39m
(node:18488) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:77912) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:28016) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/overview-party-information/OverViewPartyInformation.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 330[2mms[22m[39m
[90mstderr[2m | test/components/period-time-picker/PeriodTimePicker.test.tsx
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.
    in Animate (created by Popup)
    in div (created by Popup)
    in Popup (created by Trigger)
    in Portal (created by Trigger)
    in Trigger (created by Tooltip)
 [32m✓[39m test/components/period-time-picker/PeriodTimePicker.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 1039[2mms[22m[39m
   [33m[2m✓[22m[39m PeriodTimePicker component[2m > [22msets selection className to button when value passed [33m302[2mms[22m[39m
   [33m[2m✓[22m[39m PeriodTimePicker component[2m > [22mcalls onClick with converted data to ISO8601 string [33m460[2mms[22m[39m
    in Tooltip (created by Context.Consumer)
    in Tooltip (created by Context.Consumer)
    in Popover (created by Popover)
    in Popover (at PeriodTimePicker.tsx:196)
    in div (at PeriodTimePicker.tsx:195)
    in PeriodTimePicker (at PeriodTimePicker.test.tsx:40)

(node:75412) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/earnings/EarningsCard.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 119[2mms[22m[39m
[90mstdout[2m | test/utils/CoverageUtils.test.tsx[2m > [22m[2mCoverage Utils test[2m > [22m[2mshould get pop updata filter result
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/utils/CoverageUtils.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 21[2mms[22m[39m
(node:81952) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:97244) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:71520) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:57188) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:45200) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/search-table/SearchCustomerView.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 181[2mms[22m[39m
 [32m✓[39m test/components/party-details-form/search/PartySearch.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 222[2mms[22m[39m
(node:94128) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:73708) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/store/ActionsStore.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[90m 7[2mms[22m[39m
 [32m✓[39m test/components/search-table/SearchCustomerComponent.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 245[2mms[22m[39m
[90mstdout[2m | test/components/coverage-table-attr/DateRange.test.tsx[2m > [22m[2mIncidentDate component[2m > [22m[2mrender IncidentDate in view state
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/coverage-table-attr/DateRange.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 180[2mms[22m[39m
 [32m✓[39m test/utils/ScrollableTableUtils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 7[2mms[22m[39m
(node:86156) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:53436) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:37708) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/kraken/KrakenEvaluationContext.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 8[2mms[22m[39m
[90mstdout[2m | test/components/claim-banner/contact-information-popover/EmploymentInformation.test.tsx[2m > [22m[2mEmploymentInformation[2m > [22m[2mrenders employment information correctly
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/claim-banner/contact-information-popover/EmploymentInformation.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 133[2mms[22m[39m
[90mstdout[2m | test/components/payments/payment-drawer/allocations-table/Utils.test.tsx[2m > [22m[2mallocationsTableUtils[2m > [22m[2mshould return max lossDate and paidToDate
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/payments/payment-drawer/allocations-table/Utils.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 22[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/ClaimBannerDetails.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 77[2mms[22m[39m
[90mstderr[2m | test/components/search-table/CustomerFilterSearch.test.tsx[2m > [22m[2mCustomerFilterSearch[2m > [22m[2mrenders without crashing
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectFilter (at CustomerFilterSearch.tsx:200)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (at CustomerFilterSearch.tsx:199)
    in div (created by Context.Consumer)
    in Col (created by Col)
    in Col (at CustomerFilterSearch.tsx:198)
    in div (created by Context.Consumer)
    in Row (created by Row)
    in Row (at CustomerFilterSearch.tsx:188)
    in div (at CustomerFilterSearch.tsx:150)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at CustomerFilterSearch.tsx:145)
    in div (at CustomerFilterSearch.tsx:144)
    in Observer (created by ForwardRef(ObserverForwardRef))
    in ForwardRef(ObserverForwardRef) (at CustomerFilterSearch.test.tsx:33)

 [32m✓[39m test/components/search-table/CustomerFilterSearch.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 745[2mms[22m[39m
   [33m[2m✓[22m[39m CustomerFilterSearch[2m > [22mrenders without crashing [33m311[2mms[22m[39m
 [32m✓[39m test/components/earnings/BaseSalaryCard.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 125[2mms[22m[39m
 [32m✓[39m test/utils/KrakenUtils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/components/coverages-info/EliminationPeriodOverride.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 92[2mms[22m[39m
 [32m✓[39m test/components/financial-info/summary-of-taxes/fica-exempt/FICAExempt.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 103[2mms[22m[39m
(node:83748) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:67712) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:45232) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:87992) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/coverage-table-attr/GrossAmountFormula.test.tsx[2m > [22m[2mGrossAmountFormula component[2m > [22m[2mrender GrossAmountFormula with popover
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/coverage-table-attr/GrossAmountFormula.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 486[2mms[22m[39m
   [33m[2m✓[22m[39m GrossAmountFormula component[2m > [22mrender GrossAmountFormula with popover [33m387[2mms[22m[39m
(node:75188) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:32500) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/payments/FinancialInformation.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 384[2mms[22m[39m
(node:34356) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:66364) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:21284) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:33532) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:45316) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:2952) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:3280) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:49088) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/change-history/utils/filterTag.test.tsx[2m > [22m[2mFilterTag[2m > [22m[2mrenders FilterTag component
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/change-history/utils/filterTag.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 362[2mms[22m[39m
[90mstdout[2m | test/components/payments/payment-drawer/Utils.test.ts[2m > [22m[2mpaymentDrawerUtils[2m > [22m[2misAllocationPeriodDuplicated should return true
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/payments/payment-drawer/Utils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 38[2mms[22m[39m
 [32m✓[39m test/components/check-box-cards-group/CheckBoxCardsGroup.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 155[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/claim-state/ClaimState.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 175[2mms[22m[39m
[90mstderr[2m | test/components/payments/payment-drawer/OnBehalfOfSlot.test.tsx[2m > [22m[2mOnBehalfOfSlot[2m > [22m[2mshould render without crashing
[22m[39mWarning: Each child in a list should have a unique "key" prop.

Check the render method of `SelectInput`. See https://fb.me/react-warning-keys for more information.
    in Option (created by SelectInput)
    in SelectInput (created by FormBlock)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
 [32m✓[39m test/components/payments/payment-drawer/OnBehalfOfSlot.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 345[2mms[22m[39m
    in FormItem (created by FormItem)
    in FormItem (created by FormBlock)
    in RenderIf (created by FormBlock)
    in FormBlock (created by [ant-design]formBlockHOC(SelectInput))
    in [ant-design]formBlockHOC(SelectInput) (created by FieldComponent)
    in FieldComponent (created by ForwardRef(Field))
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(SelectInput)))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(SelectInput)) (at OnBehalfOfSlot.tsx:38)
    in div (created by Context.Consumer)
    in Col (created by Col)
    in Col (at OnBehalfOfSlot.tsx:32)
    in wrappedComponent (at OnBehalfOfSlot.test.tsx:35)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

(node:24200) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/change-history/utils/models.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/components/payments/payments-table-universal/EOBRemarkPopover.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 479[2mms[22m[39m
   [33m[2m✓[22m[39m EOBRemarkPopover[2m > [22mdisplays the correct remark content [33m380[2mms[22m[39m
[90mstdout[2m | test/components/coverage-table-attr/ProofOfLossReceivedDate.test.tsx[2m > [22m[2mProofOfLossReceivedDate component[2m > [22m[2mrender ProofOfLossReceivedDate with date
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/coverage-table-attr/ProofOfLossReceivedDate.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 126[2mms[22m[39m
 [32m✓[39m test/components/face-value-card/FaceValueCard.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 67[2mms[22m[39m
[90mstderr[2m | test/components/header-popup-drawer/HeaderPopupSubStatusDrawer.test.tsx[2m > [22m[2mHeaderPopupSubStatusDrawer[2m > [22m[2mrenders the component
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in wrappedComponent (at HeaderPopupSubStatusDrawer.tsx:76)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at HeaderPopupSubStatusDrawer.tsx:67)
    in div (created by Drawer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in div (created by DrawerChild)
    in DrawerChild (created by PortalWrapper)
    in Portal (created by PortalWrapper)
    in PortalWrapper (created by DrawerWrapper)
    in DrawerWrapper (created by Context.Consumer)
    in Drawer (created by Context.Consumer)
    in withConfigConsumer(Drawer) (created by Drawer)
    in Drawer (at FormDrawer.tsx:104)
    in FormDrawer (at HeaderPopupSubStatusDrawer.tsx:87)
    in wrappedComponent (at HeaderPopupSubStatusDrawer.test.tsx:44)

 [32m✓[39m test/components/header-popup-drawer/HeaderPopupSubStatusDrawer.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[33m 627[2mms[22m[39m
   [33m[2m✓[22m[39m HeaderPopupSubStatusDrawer[2m > [22mrenders the component [33m311[2mms[22m[39m
   [33m[2m✓[22m[39m HeaderPopupSubStatusDrawer[2m > [22mcalls onFormSaveConfirm when form save is clicked [33m313[2mms[22m[39m
 [32m✓[39m test/components/icd-code-table/IcdCodeTableUtils.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 39[2mms[22m[39m
 [32m✓[39m test/components/search-table/SearchCustomerOrganizationAndIndividual.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/utils/PartyInformationUtils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 6[2mms[22m[39m
 [32m✓[39m test/components/activitiesList/ActivitiesListStore.test.ts [2m([22m[2m1 test[22m[2m)[22m[90m 8[2mms[22m[39m
(node:12508) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstderr[2m | test/components/payments/payment-drawer/wizzard/PaymentWizard.test.tsx[2m > [22m[2mPayment Wizzard[2m > [22m[2mShould render
[22m[39mNeed to initialize LocalizerProvider first
Need to initialize LocalizerProvider first
Need to initialize LocalizerProvider first
Need to initialize LocalizerProvider first

 [32m✓[39m test/components/payments/payment-drawer/wizzard/PaymentWizard.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 23385[2mms[22m[39m
   [33m[2m✓[22m[39m Payment Wizzard[2m > [22mShould render [33m23381[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/contact-information-popover/LabelValueInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 36[2mms[22m[39m
(node:1936) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/search/TestUtils.test.ts [2m([22m[2m1 test[22m[2m)[22m[90m 5[2mms[22m[39m
[90mstderr[2m | test/components/party-details-form/PartyClaimRolesInput.test.tsx[2m > [22m[2mPartyClaimRolesInput[2m > [22m[2mrenders placeholder text
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectFilter (at PartyClaimRolesInput.tsx:31)
    in div (at PartyClaimRolesInput.tsx:30)
    in PartyClaimRolesInput (at PartyClaimRolesInput.test.tsx:16)
    in div (at TestUtils.tsx:58)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at TestUtils.tsx:58)

 [32m✓[39m test/components/party-details-form/PartyClaimRolesInput.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 1315[2mms[22m[39m
   [33m[2m✓[22m[39m PartyClaimRolesInput[2m > [22mhides ReportingPartyOption when hideReportingPartyOption prop is true [33m663[2mms[22m[39m
   [33m[2m✓[22m[39m PartyClaimRolesInput[2m > [22mapplies disabledAndHideRoleOptions correctly [33m406[2mms[22m[39m
(node:68508) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/RenderUtils.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[90m 50[2mms[22m[39m
(node:32800) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:3132) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:84460) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/claim-banner/contact-information-popover/Dependents.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 132[2mms[22m[39m
(node:46436) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:83136) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:13088) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:73656) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/change-history/utils/filter.test.ts[2m > [22m[2mhistoryTableFilter[2m > [22m[2mshould filter by date range
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

(node:45568) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/change-history/utils/filter.test.ts [2m([22m[2m3 tests[22m[2m)[22m[90m 26[2mms[22m[39m
 [32m✓[39m test/utils/eobUtils.test.ts [2m([22m[2m2 tests[22m[2m)[22m[90m 8[2mms[22m[39m
(node:98904) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:49864) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:9412) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:82780) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/payments/payment-drawer/allocations-table/EOBRemarks.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 107[2mms[22m[39m
 [32m✓[39m test/components/score-detail/scoreDetailTable.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 186[2mms[22m[39m
 [32m✓[39m test/components/party-details-form/PartyClaimAssociatedWith.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 257[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/contact-info-panel/contact-info-panel.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 122[2mms[22m[39m
 [32m✓[39m test/components/balance/PaymentAppliedWithholdings.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 704[2mms[22m[39m
   [33m[2m✓[22m[39m PaymentAppliedWithholdings component[2m > [22mselects payments and updates state accordingly [33m669[2mms[22m[39m
 [32m✓[39m test/commom/store/QueueStore.test.ts [2m([22m[2m1 test[22m[2m)[22m[90m 18[2mms[22m[39m
(node:93164) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/add-new-party/partyInfoComponent.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 607[2mms[22m[39m
   [33m[2m✓[22m[39m PartyInfoComponent[2m > [22mrenders without crashing [33m603[2mms[22m[39m
[90mstderr[2m | test/components/add-new-party/partyInfoComponent.test.tsx[2m > [22m[2mPartyInfoComponent[2m > [22m[2mrenders without crashing
[22m[39mWarning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.
    in LookupSelectCmp (created by withLookupBody(LookupSelectCmp))
    in withLookupBody(LookupSelectCmp) (created by FormBlock)
    in span (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Col (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Row (created by Context.Consumer)
    in FormItem (created by FormItem)
    in FormItem (created by FormBlock)
    in RenderIf (created by FormBlock)
    in FormBlock (created by [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))
    in [ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)) (created by FieldComponent)
    in FieldComponent (created by ForwardRef(Field))
    in ForwardRef(Field) (created by [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))))
    in [react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp))) (created by ValuesSpy)
    in ValuesSpy (created by [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))))
    in [react-final-form]withRelatedValues([react-final-form]fieldHOC([ant-design]formBlockHOC(withLookupBody(LookupSelectCmp)))) (at AddressInfo.tsx:79)
    in div (at PartyDetailsForm.tsx:218)
    in div (at PartyDetailsForm.tsx:216)
    in AddressInfo (at PartyDetailsForm.tsx:189)
    in PrefixProvider (at PartyDetailsForm.tsx:178)
    in section (at PartyDetailsForm.tsx:177)
    in div (at PartyDetailsForm.tsx:173)
    in PartyDetailsForm (at partyInfoComponent.tsx:65)
    in PrefixProvider (at partyInfoComponent.tsx:64)
    in div (at partyInfoComponent.tsx:63)
    in form (created by Context.Consumer)
    in Form (created by ReactFinalForm)
    in EntityTimeZoneProvider (created by ReactFinalForm)
    in ReactFinalForm (created by BaseForm)
    in FormInfoContextProvider (created by BaseForm)
    in FormFieldsCollectorProvider (created by BaseForm)
    in BaseForm (created by Form)
    in Form (at partyInfoComponent.tsx:60)
    in PartyInfoComponent (at partyInfoComponent.test.tsx:32)

(node:19236) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/commom/store/ErrorMessagesStore.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 7[2mms[22m[39m
 [32m✓[39m test/commom/store/RoutingStore .test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 6[2mms[22m[39m
[90mstdout[2m | test/components/header-popup-drawer/manual-close-section/IncompletePaymentList.test.tsx[2m > [22m[2mIncompletePaymentList[2m > [22m[2mrenders table with correct columns and data
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/header-popup-drawer/manual-close-section/IncompletePaymentList.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 202[2mms[22m[39m
 [32m✓[39m test/components/header-popup-drawer/manual-close-section/ActiveTaskList.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 201[2mms[22m[39m
 [32m✓[39m test/components/change-history/utils.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 67[2mms[22m[39m
(node:23188) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/coverage-table-attr/IncidentDate.test.tsx[2m > [22m[2mIncidentDate component[2m > [22m[2mrender IncidentDate in view state
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/coverage-table-attr/IncidentDate.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 180[2mms[22m[39m
(node:36796) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/return-to-work/ReturnToWork.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 22719[2mms[22m[39m
   [33m[2m✓[22m[39m ReturnToWork Component[2m > [22mshould render in view mode and show edit button [33m22715[2mms[22m[39m
(node:9528) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:28304) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[90mstdout[2m | test/components/claim-reductions/ClaimReduction.test.tsx[2m > [22m[2mClaimReductionsTable[2m > [22m[2mshould render all offset reduction data table rows correctly
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/claim-reductions/ClaimReduction.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 902[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimReductionsTable[2m > [22mshould render all offset reduction data table rows correctly [33m899[2mms[22m[39m
 [32m✓[39m test/utils/ClaimPartyUtils.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 7[2mms[22m[39m
[90mstdout[2m | test/components/claim-banner/ClaimBannerContactInformation.test.tsx[2m > [22m[2mClaimBannerContactInformation test[2m > [22m[2mrenders contact information
[22m[39m[🧿] RTZ mode enabled - [33mfalse[39m
[🧭] App working in Europe/Athens timezone, and user is in Europe/Athens tz.

 [32m✓[39m test/components/claim-banner/ClaimBannerContactInformation.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 88[2mms[22m[39m
(node:39704) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:47464) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:96420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/header-popup-drawer/manual-close-section/UnprocessedBalanceList.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 223[2mms[22m[39m
(node:47088) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:30064) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:4788) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/case-search/CaseSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 261[2mms[22m[39m
 [32m✓[39m test/components/expandable-table/ExpandableTable.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 280[2mms[22m[39m
(node:89848) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/addable-table/AddableTableWrapper.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 77[2mms[22m[39m
 [32m✓[39m test/commom/CustomerUtils.test.ts [2m([22m[2m1 test[22m[2m)[22m[90m 4[2mms[22m[39m
 [32m✓[39m test/components/claim-banner/claim-dates-popover/ClaimDates.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 51[2mms[22m[39m
 [32m✓[39m test/utils/LookupUtils.test.ts [2m([22m[2m1 test[22m[2m)[22m[90m 12[2mms[22m[39m
 [32m✓[39m test/components/payments/payments-table-disability/utils.test.ts [2m([22m[2m1 test[22m[2m)[22m[90m 9[2mms[22m[39m

[2m Test Files [22m [1m[32m155 passed[39m[22m[90m (155)[39m
[2m      Tests [22m [1m[32m768 passed[39m[22m[2m | [22m[33m1 skipped[39m[90m (769)[39m
[2m   Start at [22m 14:55:08
[2m   Duration [22m 357.80s[2m (transform 7.97s, setup 306.61s, collect 4387.80s, tests 157.67s, environment 245.37s, prepare 34.71s)[22m

