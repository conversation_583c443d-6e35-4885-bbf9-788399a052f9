/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'
import {Field} from '@eisgroup/form'
import {Col, Row} from '@eisgroup/ui-kit'
import {t} from '@eisgroup/i18n'
import {
    CLAIM_REDUCE_PAYMENT_WITHHOLDING_AMOUNT,
    composeValidators,
    required,
    validateMinMaxValueForFieldName
} from '../..'

const {InputNumber} = Field

export const WithholdingPercentage: React.FC = observer(props => {
    return (
        <Row gutter={24}>
            <Col span={12} className={CLAIM_REDUCE_PAYMENT_WITHHOLDING_AMOUNT}>
                <InputNumber
                    label='cap-core:balance_actions_drawer_withholding_percentage_label'
                    name='withholdingPct'
                    placeholder={t('cap-core:percent_sign')}
                    required
                    validate={composeValidators(
                        required('cap-core:withholding_percentage_required'),
                        validateMinMaxValueForFieldName(
                            0,
                            100,
                            t('cap-core:balance_actions_drawer_withholding_percentage_label')
                        )
                    )}
                    allowDecimal
                />
            </Col>
        </Row>
    )
})
