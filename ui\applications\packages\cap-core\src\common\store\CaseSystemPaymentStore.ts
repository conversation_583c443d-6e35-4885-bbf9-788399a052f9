/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapAdjusterPaymentDefinitionCapRecoveryGenerationRequest} from '@eisgroup/cap-gateway-client'
import {BusinessTypes} from '@eisgroup/cap-event-case-models'
import {CapPaymentSchedule, CapPaymentTemplate, PaymentDefinition} from '@eisgroup/cap-financial-models'
import {
    CaseSystemService,
    ClaimLoss,
    CSClaimWrapperService,
    LossParams,
    LossParamsWithModelName,
    IndividualCustomer,
    OrganizationCustomer,
    ClaimParty
} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {StepStatus} from '@eisgroup/react-components'
import {BaseRootStore} from './BaseRootStore'
import {Allocation, ICaseSystem, Settlements} from '../Types'
import {TaxYearSummary} from '../../components/financial-info/summary-of-taxes/utils'
import {SelectOptionProps} from '../../components/select-input-wrapper/SelectInputWrapper'
import {EventCaseStore} from './EventCaseStore'
import CapLoss = BusinessTypes.CapLoss
import CapBuildPaymentScheduleInput = CapPaymentSchedule.CapBuildPaymentScheduleInput
import CapPaymentScheduleEntity = CapPaymentSchedule.CapPaymentScheduleEntity
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity
import CapPaymentEntity = PaymentDefinition.CapPaymentEntity

export enum CaseSystemPaymentWizardStepKey {
    Detail = 'detail',
    Allocations = 'Allocations'
}

export interface CaseSystemPaymentStore<
    CS extends ICaseSystem,
    CSService extends CaseSystemService | CSClaimWrapperService<CS>
> extends BaseRootStore {
    activatePaymentSchedule: ({rootId, revisionNo}: LossParams) => RxResult<CapPaymentScheduleEntity>
    activeStep: CaseSystemPaymentWizardStepKey
    allAssociatedClaims: ClaimLoss[]
    allAssociatedSettlements: Settlements[]
    associatedClaims: ClaimLoss[]
    associatedSettlements: Settlements[]
    calculatePayments: (params: CapBuildPaymentScheduleInput, payeeUri?: string) => RxResult<CapPaymentScheduleEntity>
    cancelPaymentSchedule: ({rootId, revisionNo}: LossParams) => RxResult<CapPaymentScheduleEntity>
    caseSystem?: CS
    caseStore?: EventCaseStore
    caseSystemService: CSService
    changeStep: (key: CaseSystemPaymentWizardStepKey) => void
    changeToNextStep: (key: CaseSystemPaymentWizardStepKey) => void
    checkPaymentSchedule: ({rootId, revisionNo}: LossParams, userId: string) => RxResult<CapPaymentScheduleEntity>
    closeDrawer: boolean
    createOrUpdatePayment: string
    createPayment: (params: CapBuildPaymentScheduleInput) => RxResult<CapPaymentTemplateEntity>
    currentPayee: string
    representBeneficiary: string
    currentPaymentTemplate: CapPaymentTemplateEntity
    formItemChanged: boolean
    generatePaymentSchedule: ({rootId, revisionNo}: LossParams) => RxResult<CapPaymentScheduleEntity>
    generateRecovery: (params: CapAdjusterPaymentDefinitionCapRecoveryGenerationRequest) => RxResult<CapPaymentEntity>
    hasEnterStep2: boolean
    showOnBehalfOf: boolean
    onBehalfOfOptions: SelectOptionProps[]
    loadPaymentSchedules: (lossParams: LossParamsWithModelName[]) => void
    loadAssociatedSettlements: (losses: CapLoss[]) => void
    loadCurrentPaymentTemplate: (lossParams: LossParamsWithModelName | LossParamsWithModelName[]) => void
    loadCurrentPayments: (
        {rootId, revisionNo, modelName}: LossParamsWithModelName,
        recordRootId?: string,
        entityUris?: string[],
        processDefinitionKeys?: string[]
    ) => void
    loadDataAfterPaymentScheduleCreateOrUpdate: (
        caseParamsWithModelName: LossParamsWithModelName,
        capPaymentTemplateUri: string
    ) => void
    pollLoadPaymentsAfterWorkflow: (lossParams: LossParamsWithModelName[], entityUri: string) => RxResult<any[]>
    loadPayments: (lossParams: LossParamsWithModelName[]) => RxResult<any[]>
    loadPaymentsAfterClaimUpdate: (caseParams: LossParamsWithModelName, claimParams: LossParamsWithModelName) => void
    originalPaymentTemplate: CapPaymentTemplateEntity
    originalPaymentDetailStepAllocations: Allocation[]
    payeeList: [IndividualCustomer | OrganizationCustomer]
    paymentList: any[]
    paymentSchedule: CapPaymentScheduleEntity
    paymentScheduleList: CapPaymentScheduleEntity[]
    paymentTemplate: CapPaymentTemplateEntity
    paymentTemplates: CapPaymentTemplateEntity[]
    payments: CapPaymentEntity[]
    pollPaymentsWithScheduled: (params: LossParamsWithModelName) => void
    reCalculatePayments: (params: CapBuildPaymentScheduleInput, paymentTemplate: CapPaymentTemplateEntity) => void
    resetPaymentWizard: () => void
    setCreateOrUpdatePayment: (mode: string) => void
    setFormItemChanged: (value: boolean) => void
    setHasEnterStep2: (hasEnterStep2: boolean) => void
    setShowOnBehalfOf: (showOnBehalfOf: boolean) => void
    setOnBehalfOfOptions: (onBehalfOfOptions: SelectOptionProps[]) => void
    setOriginalPaymentTemplate: (paymentTemplate: CapPaymentTemplateEntity) => void
    stepsStatuses: Record<string, StepStatus>
    suspendPaymentSchedule: ({rootId, revisionNo}: LossParams) => RxResult<CapPaymentScheduleEntity>
    taxYearSummaries: TaxYearSummary[]
    unsuspendPaymentSchedule: ({rootId, revisionNo}: LossParams) => RxResult<CapPaymentScheduleEntity>
    updateActiveStepStatus: (stepStatus: StepStatus) => void
    updateCurrentPayee: (payee?: string) => void
    updateCurrGuardian: (representBeneficiary?: string) => void
    updatePayeeInfo: (payeeList: [IndividualCustomer | OrganizationCustomer]) => void
    updatePaymentTemplate: (paymentTemplate: CapPaymentTemplateEntity) => void
    updatePayments: (payments: CapPaymentEntity[]) => void
    searchCustomerRelationship: (registryTypeId: string, beneficiaries: ClaimParty[]) => void
    setOriginalPaymentDetailStepAllocations: (allocations: Allocation[]) => void
}
