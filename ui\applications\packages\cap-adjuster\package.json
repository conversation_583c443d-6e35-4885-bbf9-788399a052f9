{"name": "@eisgroup/cap-adjuster", "version": "25.200.0-SNAPSHOT.************", "description": "Cap Adjuster View Module", "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"@eisgroup/cap-core": "~25.200.0-SNAPSHOT.************", "@eisgroup/cap-gateway-client": "~25.200.0-SNAPSHOT.************", "@eisgroup/cap-services": "~25.200.0-SNAPSHOT.************"}, "peerDependencies": {"@eisgroup/common-types": "^100.0.0", "@eisgroup/i18n": "^100.0.0", "@eisgroup/react-components": "^100.0.0", "@eisgroup/react-routing": "^100.0.0", "mobx": "~4.15.7", "react": "~16.14.0", "react-dom": "~16.14.0"}, "scripts": {"build": "tsc --project tsconfig.build.json", "postbuild": "cpx \"./src/**/*.{json,less,ico,html,gif,svg,png,ttf,woff,woff2,js,jsx}\" ./target/dist/js/src", "test": "vitest run", "test:watch": "vitest", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "eslint"}, "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "v20ModuleType": "reference implementation library", "devDependencies": {"@eisgroup/common-types": "^100.0.0", "@eisgroup/i18n": "^100.0.0"}}