/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {ClaimParty, EventCaseService, IndividualCustomer, OrganizationCustomer} from '@eisgroup/cap-services'
import {computed, IObservableArray, observable} from 'mobx'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'
import {ClaimPartyStore} from './ClaimPartyStore'
import {FormDrawerStore, FormDrawerStoreImpl} from '../../components/form-drawer'
import {CaseSystemPaymentStore} from './CaseSystemPaymentStore'
import {CapEventCaseEntity} from '../Types'
import {EventCaseStore} from '../..'

export interface PartyInformationStore extends BaseRootStore {
    eventCase: CapEventCaseEntity
    claimPartyStore: ClaimPartyStore
    eventCaseStore: EventCaseStore
    subjectOfClaim?: IndividualCustomer | OrganizationCustomer
    isClosedCase: boolean
    formDrawerStore: FormDrawerStore
    allClaimsBeneficiaries: IObservableArray<ClaimParty>
    paymentsStore: CaseSystemPaymentStore<CapEventCaseEntity, EventCaseService>
}

export class PartyInformationStoreImpl extends BaseRootStoreImpl implements PartyInformationStore {
    viewStore

    @observable subjectOfClaim?: IndividualCustomer | OrganizationCustomer

    @observable allClaimsBeneficiaries: IObservableArray<ClaimParty>

    eventCase: CapEventCaseEntity

    paymentsStore: CaseSystemPaymentStore<CapEventCaseEntity, EventCaseService>

    formDrawerStore: FormDrawerStore

    constructor(viewStore?: any) {
        super()
        this.viewStore = viewStore
        this.paymentsStore = viewStore.paymentsStore
        this.formDrawerStore = new FormDrawerStoreImpl()
    }

    @computed
    get isClosedCase(): boolean {
        return this.viewStore.eventCase.state === 'Closed'
    }

    @computed
    public get claimPartyStore(): ClaimPartyStore {
        return this.viewStore.claimPartyStore
    }

    @computed
    public get eventCaseStore(): EventCaseStore {
        return this.viewStore.eventCaseStore
    }
}
