"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectToStore = exports.createViewLoader = exports.ClaimLossListStore = exports.LOAD_INITIAL_DATA = void 0;
/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
const mobx_1 = require("mobx");
const cap_services_1 = require("@eisgroup/cap-services");
const cap_core_1 = require("@eisgroup/cap-core");
const common_types_1 = require("@eisgroup/common-types");
exports.LOAD_INITIAL_DATA = 'loadInitialData';
class ClaimLossListStore extends cap_core_1.BaseRootStoreImpl {
    constructor() {
        super(...arguments);
        this.claimLossList = [];
        this.filterLifeLoss = (list) => {
            const lossModelNameList = [
                'PremiumWaiverLoss',
                'DeathLoss',
                'AcceleratedLoss',
                'CILoss',
                'HILoss',
                'AccidentalDismembermentLoss'
            ];
            return list.filter(v => !lossModelNameList.includes(v._modelName));
        };
        this.initLossListStore = () => {
            this.callService(cap_services_1.claimLossService.searchClaimLoss(), response => mobx_1.runInAction(() => {
                const result = common_types_1.opt(response.result).orElse([]);
                this.claimLossList = this.filterLifeLoss(result).slice(0, 10);
            }), exports.LOAD_INITIAL_DATA);
        };
        this.searchClaimLoss = (lossNumber) => {
            this.callService(cap_services_1.claimLossService.searchClaimLoss(lossNumber), response => mobx_1.runInAction(() => {
                const result = common_types_1.opt(response.result).orElse([]);
                this.claimLossList = this.filterLifeLoss(result).slice(0, 10);
            }));
        };
    }
}
__decorate([
    mobx_1.observable,
    __metadata("design:type", Array)
], ClaimLossListStore.prototype, "claimLossList", void 0);
__decorate([
    mobx_1.action,
    __metadata("design:type", Object)
], ClaimLossListStore.prototype, "initLossListStore", void 0);
__decorate([
    mobx_1.action,
    __metadata("design:type", Object)
], ClaimLossListStore.prototype, "searchClaimLoss", void 0);
exports.ClaimLossListStore = ClaimLossListStore;
_a = cap_core_1.storeBindingFactory(() => new ClaimLossListStore()), exports.createViewLoader = _a.createViewLoader, exports.connectToStore = _a.connectToStore;
//# sourceMappingURL=ClaimLossListStore.js.map