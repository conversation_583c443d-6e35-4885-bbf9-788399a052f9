/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.converters;

import javax.inject.Inject;

import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.ClaimCapDynamicSearchResponseSuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.ClaimClaimIndexEntityDTO;

import cap.adjuster.services.claim.dto.CapAdjusterClaimIndex;
import cap.adjuster.services.claim.dto.CapAdjusterClaimIndexResponse;
import core.services.converters.CommonDTOConverter;

public class CapAdjusterClaimIndexResponseConverter<I extends ClaimCapDynamicSearchResponseSuccessBodyDTO, A extends CapAdjusterClaimIndexResponse>
        extends CommonDTOConverter<I, A> {

    private CapAdjusterClaimIndexEntityConverter<ClaimClaimIndexEntityDTO, CapAdjusterClaimIndex> claimIndexEntityConverter;

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        apiDTO.count = intDTO.body.success.count;
        apiDTO.result = claimIndexEntityConverter.convertToApiDTOs(intDTO.body.success.result);
        return apiDTO;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setClaimIndexEntityConverter(
        CapAdjusterClaimIndexEntityConverter<ClaimClaimIndexEntityDTO, CapAdjusterClaimIndex> claimIndexEntityConverter) {
        this.claimIndexEntityConverter = claimIndexEntityConverter;
    }
}
