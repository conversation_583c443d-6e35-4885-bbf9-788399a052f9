{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap.ci.adjudication": {"schema": "integration.cap.accele.adjudication.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Openl for Accelerated", "version": "1", "title": "CAP Adjuster: Openl for Accelerated"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/openl-accelerated", "description": "CAP Adjuster: Openl for Accelerated"}], "paths": {"/openl-accelerated/face-value-pl-calculation": {"post": {"x-dxp-path": "/_api_permanent_life_face_value_calculation", "tags": ["/cap-adjuster/v1/openl-accelerated"]}}}}