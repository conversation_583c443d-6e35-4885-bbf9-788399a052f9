{"swagger": "2.0", "info": {"description": "API for CapStdSettlement", "version": "1", "title": "CapStdSettlement model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capsettlement/CapStdSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/command/approveSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapApproveSettlementRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/command/closeSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/command/initSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRequestStdSettlementAdjudicationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapStdSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapStdSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapStdSettlementKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/CapSTDSettlementAdjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapSTDSettlementAdjudicationInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/adjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/approveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/disapproveDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/EntityLinkBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/initDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/initStdSettlementBenefitToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/InitStdSettlementBenefitToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapStdSettlement/v1/transformation/readjudicateDisabilitySettlementToAccumulatorTx": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"AdjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "AdjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "ApproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputs"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ApproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ApproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "CapApproveSettlementRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "userId": {"type": "string"}}, "title": "CapApproveSettlementRequest"}, "CapApproveSettlementRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapApproveSettlementRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapApproveSettlementRequestBody"}, "CapRequestStdSettlementAdjudicationInput": {"required": ["entity"], "properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDetailEntity"}, "policyId": {"type": "string"}, "settlementType": {"type": "string"}}, "title": "CapRequestStdSettlementAdjudicationInput"}, "CapRequestStdSettlementAdjudicationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapRequestStdSettlementAdjudicationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapRequestStdSettlementAdjudicationInputBody"}, "CapSTDSettlementAdjudicationInputOutputs": {"properties": {"output": {"$ref": "#/definitions/CapSTDSettlementRulesInput"}}, "title": "CapSTDSettlementAdjudicationInputOutputs"}, "CapSTDSettlementAdjudicationInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapSTDSettlementAdjudicationInputOutputs"}}, "title": "CapSTDSettlementAdjudicationInputOutputsSuccess"}, "CapSTDSettlementAdjudicationInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapSTDSettlementAdjudicationInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSTDSettlementAdjudicationInputOutputsSuccessBody"}, "CapSTDSettlementRulesInput": {"properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementRulesInput"}, "absence": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementAbsenceInfoEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementAccumulatorDetailsRulesInput"}}, "benefitApprovalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementApprovalPeriodEntity"}}, "benefitConfiguration": {"$ref": "#/definitions/CapStdSettlement_BaseAbsenceSettlementBenefitConfiguration"}, "coreBenefitState": {"type": "string"}, "currentDateTime": {"type": "string", "format": "date-time"}, "details": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDetailEntity"}, "isBenefitSettlement": {"type": "boolean"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementLossInfoEntity"}, "modelFactory": {"$ref": "#/definitions/ModelFactory"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementPaymentsRulesInput"}}, "policy": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementPolicyInfoEntity"}, "settlement": {"$ref": "#/definitions/EntityLink"}}, "title": "CapSTDSettlementRulesInput"}, "CapSettlementReadjudicateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDetailEntity"}}, "title": "CapSettlementReadjudicateInput"}, "CapSettlementReadjudicateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapSettlementReadjudicateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapSettlementReadjudicateInputBody"}, "CapStdSettlementKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapStdSettlementKrakenDeprecatedBundleRequest"}, "CapStdSettlementKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapStdSettlementKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStdSettlementKrakenDeprecatedBundleRequestBody"}, "CapStdSettlementLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntity"}}}, "title": "CapStdSettlementLoadHistoryResult"}, "CapStdSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStdSettlementLoadHistoryResult"}}, "title": "CapStdSettlementLoadHistoryResultSuccess"}, "CapStdSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStdSettlementLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStdSettlementLoadHistoryResultSuccessBody"}, "CapStdSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapStdSettlement AccessTrackInfo"}, "CapStdSettlement_BaseAbsencePolicyBenefitLimitLevel": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseAbsencePolicyBenefitLimitLevel"}, "limitLevelType": {"type": "string", "description": "Type of limit level"}, "timePeriodCd": {"type": "string", "description": "Time period Code"}}, "title": "CapStdSettlement BaseAbsencePolicyBenefitLimitLevel"}, "CapStdSettlement_BaseAbsenceSettlementAttrOptions": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseAbsenceSettlementAttrOptions"}, "attrName": {"type": "string", "description": "Attribute Name"}, "options": {"type": "array", "items": {"type": "string", "description": "Options of attribute, e.g. Mandatory"}}}, "title": "CapStdSettlement BaseAbsenceSettlementAttrOptions"}, "CapStdSettlement_BaseAbsenceSettlementBenefitConfiguration": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "BaseAbsenceSettlementBenefitConfiguration"}, "accumulationCategoryGroup": {"type": "string", "description": "Category group to indicate the accumulating group a benefit is belonged to in settlement amount calculation."}, "attrOptions": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_BaseAbsenceSettlementAttrOptions"}}, "benefitCategory": {"type": "string", "description": "Benefit Category"}, "benefitLevelMapping": {"type": "array", "items": {"type": "string", "description": "Claim fields mapping to policy fields configuration."}}, "calculationFormulaId": {"type": "string", "description": "Formula Id to define the formulas that are used in settlement calculation for different benefits."}, "coverageType": {"type": "string", "description": "Coverage Type"}, "groupAccumulatorLimitLevel": {"type": "string", "description": "Defines the limitLevel for the group accumulator"}, "groupUnit": {"type": "string", "description": "Indicate the amount type for group accumulator limit level amount"}, "limitLevels": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_BaseAbsencePolicyBenefitLimitLevel"}}, "unit": {"type": "string", "description": "Indicate the amount type for accumulator limit level amount"}}, "title": "CapStdSettlement BaseAbsenceSettlementBenefitConfiguration"}, "CapStdSettlement_CapApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapApprovalPeriodEntity"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapStdSettlement CapApprovalPeriodEntity"}, "CapStdSettlement_CapBaseDisabilityAccumulatorExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityAccumulatorExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Accumulator Unit code."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapStdSettlement_Term"}}, "title": "CapStdSettlement CapBaseDisabilityAccumulatorExtension", "description": "Entity for Accumulator extension for rules."}, "CapStdSettlement_CapBaseDisabilityFormulaCalculationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaCalculationDetails"}, "calculatedResult": {"$ref": "#/definitions/Money"}, "formulaAppliedAttribute": {"type": "string", "description": "Attribute name which formula calculation applied"}, "formulaContent": {"type": "string", "description": "Formula Content."}, "formulaId": {"type": "string", "description": "Formula Id."}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityFormulaParameter"}}, "policyRoundingAmount": {"type": "string", "description": "Policy Rounding Amount"}, "policyRoundingFactorCd": {"type": "string", "description": "Policy Rounding Factor Cd"}, "roundingResult": {"$ref": "#/definitions/Money"}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}}, "title": "CapStdSettlement CapBaseDisabilityFormulaCalculationDetails", "description": "An entity for formula calculation details."}, "CapStdSettlement_CapBaseDisabilityFormulaParameter": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityFormulaParameter"}, "paramExtension": {"type": "object", "description": "Extension of parameter. E.g currency"}, "paramName": {"type": "string", "description": "Parameter name."}, "paramValue": {"type": "number", "description": "Value of parameter."}}, "title": "CapStdSettlement CapBaseDisabilityFormulaParameter", "description": "An entity for formula parameter."}, "CapStdSettlement_CapBaseDisabilityPaymentAdditionDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAdditionDetails"}, "additionSource": {"$ref": "#/definitions/EntityLink"}, "additionType": {"type": "string", "description": "Addition type"}, "paymentAdditionDetailsCola": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentAdditionDetailsColaEntity"}}, "title": "CapStdSettlement CapBaseDisabilityPaymentAdditionDetails", "description": "Entity for the payment addition information."}, "CapStdSettlement_CapBaseDisabilityPaymentAdditionDetailsColaEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAdditionDetailsColaEntity"}, "anniversaryDate": {"type": "string", "format": "date-time", "description": "COLA anniversary date"}}, "title": "CapStdSettlement CapBaseDisabilityPaymentAdditionDetailsColaEntity", "description": "Cola details are described in this entity."}, "CapStdSettlement_CapBaseDisabilityPaymentAllocationBalanceAdjustment": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationBalanceAdjustment"}, "adjustingDuration": {"type": "number", "description": "Stores the amount in days that is additionally used or recovered with adjustment payment."}, "adjustingNumberOfUnits": {"type": "number", "description": "Stores the amount in trips, visits, and sessions that is additionally used or recovered with adjustment payment."}, "paymentNumber": {"type": "string", "description": "Unique number of payment the adjustment is created for."}}, "title": "CapStdSettlement CapBaseDisabilityPaymentAllocationBalanceAdjustment", "description": "Entity for the payment allocation information."}, "CapStdSettlement_CapBaseDisabilityPaymentAllocationDeductionDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationDeductionDetails"}, "deductionType": {"type": "string", "description": "Deduction type."}, "masterAllocationPayee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapStdSettlement CapBaseDisabilityPaymentAllocationDeductionDetails", "description": "Details of 3rd party deduction payment allocation."}, "CapStdSettlement_CapBaseDisabilityPaymentAllocationDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationDetails"}, "allocationBalanceAdjustmentDetails": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentAllocationBalanceAdjustment"}, "allocationDeductionDetails": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentAllocationDeductionDetails"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business Code."}, "allocationPayableItem": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentAllocationPayableItem"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationType": {"type": "string", "description": "Defines the purpose of the allocation. A payment, payment adjustment or balanse suspense."}, "expenseNumber": {"type": "string", "description": "Generated expense number."}, "isInterestOnly": {"type": "boolean", "description": "Defines if allocation is only to pay the interests."}, "isPartialDisability": {"type": "boolean", "description": "Defines if allocation was specified as partial disability"}, "reserveType": {"type": "string", "description": "Defines the reserve type of allocation."}}, "title": "CapStdSettlement CapBaseDisabilityPaymentAllocationDetails", "description": "Entity for the payment allocation information."}, "CapStdSettlement_CapBaseDisabilityPaymentAllocationPayableItem": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentAllocationPayableItem"}, "benefitCd": {"type": "string", "description": "Benefit code."}, "benefitDate": {"type": "string", "format": "date-time", "description": "Date for which is being paid for."}, "benefitPeriod": {"$ref": "#/definitions/CapStdSettlement_Period"}, "coverageCd": {"type": "string", "description": "Policy coverage code."}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "policySource": {"type": "string", "description": "Link to related Policy."}}, "title": "CapStdSettlement CapBaseDisabilityPaymentAllocationPayableItem", "description": "Stores details for what it is paid."}, "CapStdSettlement_CapBaseDisabilityPaymentDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentDetails"}, "payeeRoleDetails": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentPayeeRoleDetails"}, "paymentAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentAdditionDetails"}}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentAllocationDetails"}}}, "title": "CapStdSettlement CapBaseDisabilityPaymentDetails", "description": "Payment transaction details."}, "CapStdSettlement_CapBaseDisabilityPaymentPayeeRoleDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilityPaymentPayeeRoleDetails"}, "registryId": {"$ref": "#/definitions/EntityLink"}}, "title": "CapStdSettlement CapBaseDisabilityPaymentPayeeRoleDetails", "description": "Payment Payee Role details."}, "CapStdSettlement_CapBaseDisabilitySettlementResultAccumulatorExtension": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBaseDisabilitySettlementResultAccumulatorExtension"}, "accumulatorUnitCd": {"type": "string", "description": "Accumulator Unit code."}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit Duration Unit."}, "term": {"$ref": "#/definitions/CapStdSettlement_Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time", "description": "Accumulator Transaction Effective Date."}}, "title": "CapStdSettlement CapBaseDisabilitySettlementResultAccumulatorExtension", "description": "Entity for Accumulat extension details."}, "CapStdSettlement_CapCoverageInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCoverageInfoEntity"}, "annualEarningsAmount": {"$ref": "#/definitions/Money"}, "approvedAmount": {"$ref": "#/definitions/Money"}, "benefitDuration": {"type": "integer", "format": "int64", "description": "Defines maximum benefit duration in weeks if no limitations/exclusions are applied"}, "benefitTiers": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDBenefitTiersInfo"}}, "benefitTypeCd": {"type": "string", "description": "Defines type of which formula to apply to calculate gross benefit amount"}, "coverageCd": {"type": "string"}, "currentEarningsReducedPct": {"type": "number"}, "earningsDefinitionCd": {"type": "string", "description": "Earning definition Cd defined in policy"}, "eliminationPeriodInjury": {"type": "integer", "format": "int64", "description": "The period of time (in calendar days) between the injury and receiving benefits payments from an insurer."}, "eliminationPeriodSickness": {"type": "integer", "format": "int64", "description": "The period of time (in calendar days) between the beginning of a sickness and receiving benefits payments from an insurer."}, "employmentStatus": {"type": "string", "description": "Employment Status in Policy"}, "employmentTerminiationDate": {"type": "string", "format": "date", "description": "Employment Terminiation Date in Policy"}, "firstDayHospitalizationCd": {"type": "string", "description": "Defines if first day hospitalization option is included. If yes elimination period end date should be equal to hospitalization date."}, "guaranteedIssueAmount": {"$ref": "#/definitions/Money"}, "individualRecordTypeCd": {"type": "string", "description": "Employment Status in STD Policy"}, "isProgressiveIllnessBenefitApplicable": {"type": "boolean", "description": "Defines if policy has progressive illness benefit"}, "isSelfBill": {"type": "boolean", "description": "Is Self Bill defined in STD policy"}, "lookBackPeriodCd": {"type": "string", "description": "Pre-Existing Condition: Look Back Period"}, "maxPartialEarningsPct": {"type": "number"}, "minPartialEarningsPct": {"type": "number"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "minWeeklyBenefitPct": {"type": "number", "description": "Min Weekly Benefit Pct defined in STD policy"}, "minWeeklyBenefitTypeCd": {"type": "string", "description": "Min Weekly Benefit Type Cd defined in STD policy"}, "outPatientCd": {"type": "string", "description": "Defines if Out Patient option is included"}, "partialDisabilityType": {"type": "string", "description": "Defines partial disability type"}, "partyTypeCd": {"type": "string", "description": "Party Type Cd defined in Policy"}, "payrollFrequencyCd": {"type": "string", "description": "Payroll Frequency Cd in STD Policy"}, "planCd": {"type": "string", "description": "Describes planCd defined in STD policy"}, "planName": {"type": "string", "description": "Describes planName defined in STD policy"}, "rehabIncentiveBenefitCd": {"type": "string", "description": "Defines if a member is applicable for Rehabilitation Incentive Benefit, possible values: Included or NotIncluded"}, "rehabIncentiveBenefitPct": {"type": "number", "description": "A percentage of benefit for disabled employees who agree to participate in rehabilitation programs"}, "riskStateCd": {"type": "string", "description": "Risk State Cd defined in STD Policy"}, "roundingAmount": {"type": "integer", "format": "int64", "description": "Rounding Amount defined in STD policy"}, "roundingFactorCd": {"type": "string", "description": "Rounding Factor Cd defined in STD policy"}, "salaryChangeCd": {"type": "string", "description": "Salary Change Cd defined in STD policy"}, "taxabilityCd": {"type": "string", "description": "For selected coverage defines if weekly benefits are taxable or tax-fr"}, "weeklyAmtDetails": {"$ref": "#/definitions/CapStdSettlement_CapSTDBenefitWeeklyAmt"}, "weeklyPctDetails": {"$ref": "#/definitions/CapStdSettlement_CapSTDBenefitWeeklySalaryPct"}, "workIncentiveBenefit": {"type": "string", "description": "Defines work incentive benefit"}, "workweekTypeCd": {"type": "integer", "format": "int64", "description": "Member work week type defined in Policy"}}, "title": "CapStdSettlement CapCoverageInfoEntity", "description": "An entity for coverage information."}, "CapStdSettlement_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapStdSettlement CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapStdSettlement_CapSTDBenefitTiersInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDBenefitTiersInfo"}, "maxTierWeeklyAmount": {"$ref": "#/definitions/Money"}, "tierAmount": {"$ref": "#/definitions/Money"}, "tierDuration": {"type": "integer", "format": "int64", "description": "Tier Duration."}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}, "tierPct": {"type": "number", "description": "Tier Percentage."}, "tierTypeCd": {"type": "string", "description": "Tier Type Code."}}, "title": "CapStdSettlement CapSTDBenefitTiersInfo", "description": "An entity that stores details of 'Tiered' benefit type."}, "CapStdSettlement_CapSTDBenefitTiersOverrideEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDBenefitTiersOverrideEntity"}, "approvedTierAmountOverride": {"$ref": "#/definitions/Money"}, "approvedTierAmountOverrideReason": {"type": "string"}, "maxTierWeeklyOverrideAmount": {"$ref": "#/definitions/Money"}, "maxTierWeeklyOverrideAmountReason": {"type": "string"}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}}, "title": "CapStdSettlement CapSTDBenefitTiersOverrideEntity"}, "CapStdSettlement_CapSTDBenefitWeeklyAmt": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDBenefitWeeklyAmt"}, "maxWeeklyBenefitPct": {"type": "number", "description": "Maximum percentage of weekly earnings covered by policy"}, "weeklyBenefitAmount": {"$ref": "#/definitions/Money"}}, "title": "CapStdSettlement CapSTDBenefitWeeklyAmt", "description": "Defines details of 'Weekly Amount' benefit type"}, "CapStdSettlement_CapSTDBenefitWeeklySalaryPct": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDBenefitWeeklySalaryPct"}, "benefitPct": {"type": "number", "description": "Percentage of weekly earnings covered by policy."}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "weeklyBenefitCalcAmount": {"$ref": "#/definitions/Money"}}, "title": "CapStdSettlement CapSTDBenefitWeeklySalaryPct", "description": "An entity that stores details of 'Percentage' benefit type."}, "CapStdSettlement_CapSTDCalculatedBenefitTiers": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDCalculatedBenefitTiers"}, "approvedTierAmount": {"$ref": "#/definitions/Money"}, "maxTierWeeklyAmount": {"$ref": "#/definitions/Money"}, "tierAmount": {"$ref": "#/definitions/Money"}, "tierDuration": {"type": "integer", "format": "int64", "description": "Tier Duration."}, "tierLevel": {"type": "integer", "format": "int64", "description": "Tier Level."}, "tierPct": {"type": "number", "description": "Tier Percentage."}, "tierTypeCd": {"type": "string", "description": "Tier Type Code."}}, "title": "CapStdSettlement CapSTDCalculatedBenefitTiers", "description": "Entity for STD Settlement Tiered benefit calculation"}, "CapStdSettlement_CapSTDCaseApplicabilityResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDCaseApplicabilityResultEntity"}, "applicableTypes": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDCaseApplicableTypeEntity"}}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_MessageType"}}, "paymentLevelCd": {"type": "string", "description": "Defines payments processing level. Payments can be processed per claim or per case."}}, "title": "CapStdSettlement CapSTDCaseApplicabilityResultEntity", "description": "Business entity defines a collection of Applicability results"}, "CapStdSettlement_CapSTDCaseApplicableTypeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDCaseApplicableTypeEntity"}, "absenceReasonCd": {"type": "string", "description": "Defines reason of absence"}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations."}, "applicabilityEvaluationCd": {"type": "string"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem"}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored in CAP subsystem."}, "claimTypeCd": {"type": "string"}, "coverageTypeCd": {"type": "string", "description": "This attribute describes the coverage type."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDate": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossTypeCd": {"type": "string", "description": "Type of loss to identify Life/CI/HI loss type "}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when lifeIntake was reported."}}, "title": "CapStdSettlement CapSTDCaseApplicableTypeEntity", "description": "Business entity that defines applicability result."}, "CapStdSettlement_CapSTDCertInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDCertInfoEntity"}, "capBenefitInfo": {"type": "object", "description": "An entity for benefit information."}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapCoverageInfoEntity"}}}, "title": "CapStdSettlement CapSTDCertInfoEntity", "description": "An entity for policy information when policy type is individual (certificate) policy."}, "CapStdSettlement_CapSTDClaimDetailSelectedCoverageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDClaimDetailSelectedCoverageEntity"}, "coverageName": {"type": "string"}, "planName": {"type": "string"}}, "title": "CapStdSettlement CapSTDClaimDetailSelectedCoverageEntity"}, "CapStdSettlement_CapSTDGrossBenefitAmount": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDGrossBenefitAmount"}, "buyUpCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "coreCoverageApprovedAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "minWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "progressiveMaxWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "progressiveMinWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "progressiveWeeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "totalApprovedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapStdSettlement CapSTDGrossBenefitAmount", "description": "Entity for STD Settlement Gross Benefit Amount calculation"}, "CapStdSettlement_CapSTDMasterInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDMasterInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapCoverageInfoEntity"}}}, "title": "CapStdSettlement CapSTDMasterInfoEntity", "description": "An entity for policy information when policy type is master policy."}, "CapStdSettlement_CapSTDSettlementAbsencePeriodInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementAbsencePeriodInfoEntity"}, "absenceTypeCd": {"type": "string", "description": "This attribute describes the type of absence in time perspective."}, "actualRtwDate": {"type": "string", "format": "date-time"}, "estimatedRtwDate": {"type": "string", "format": "date-time"}, "intermittentPeriodDetails": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementIntermittentPeriodDetailsEntity"}, "period": {"$ref": "#/definitions/CapStdSettlement_Period"}, "reducedPeriodDetails": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementReducedPeriodDetailsEntity"}}, "title": "CapStdSettlement CapSTDSettlementAbsencePeriodInfoEntity", "description": "Entity that contains Absence periods information"}, "CapStdSettlement_CapSTDSettlementAbsenceReasonInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementAbsenceReasonInfoEntity"}, "absenceReasons": {"type": "array", "items": {"type": "string", "description": "Absence reasons"}}, "pregnancies": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementPregInfoEntity"}}}, "title": "CapStdSettlement CapSTDSettlementAbsenceReasonInfoEntity", "description": "Entity that contains Absence reason information"}, "CapStdSettlement_CapSTDSettlementAccumulatorDetailsRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementAccumulatorDetailsRulesInput"}, "amountType": {"type": "string", "description": "Defines accumulator amount unit, for example money or days"}, "customerURI": {"type": "string", "description": "Primary Insured."}, "extension": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityAccumulatorExtension"}, "limitAmount": {"type": "number", "description": "Accumulator limit amount."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string", "description": "Policy of accumulator."}, "remainingAmount": {"type": "number", "description": "Accumulator remaining amount."}, "reservedAmount": {"type": "number", "description": "Accumulator reserved amount."}, "resource": {"$ref": "#/definitions/EntityLink"}, "term": {"$ref": "#/definitions/CapStdSettlement_Term"}, "type": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}, "usedAmount": {"type": "number", "description": "Accumulator used amount."}}, "title": "CapStdSettlement CapSTDSettlementAccumulatorDetailsRulesInput", "description": "Accumulator details entity for rules."}, "CapStdSettlement_CapSTDSettlementApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementApprovalPeriodEntity"}, "approvalPeriod": {"$ref": "#/definitions/CapStdSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string", "description": "Period approval status."}, "approverPerson": {"type": "string", "description": "User that approved the period."}, "cancelReason": {"type": "string", "description": "Additional note why the period is canceled."}, "dateOfStatusChange": {"type": "string", "format": "date-time", "description": "The date when the status was changed."}, "frequencyType": {"type": "string", "description": "Defines the frequency of payments during the approved period."}, "partialDisability": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementApprovalPeriodPartialDisability"}, "payee": {"$ref": "#/definitions/EntityLink"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}}, "title": "CapStdSettlement CapSTDSettlementApprovalPeriodEntity", "description": "Entity for the settlement periods that are subject for approval."}, "CapStdSettlement_CapSTDSettlementApprovalPeriodPartialDisability": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementApprovalPeriodPartialDisability"}, "currentEarningsAmount": {"$ref": "#/definitions/Money"}, "isPartialDisability": {"type": "boolean", "description": "Defines if Insured was for partial disabled during the approved period."}}, "title": "CapStdSettlement CapSTDSettlementApprovalPeriodPartialDisability", "description": "Partial disability information."}, "CapStdSettlement_CapSTDSettlementBenefitOptionsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementBenefitOptionsEntity"}, "rehabIncentiveBenefitCd": {"type": "string"}, "rehabIncentiveBenefitPct": {"type": "number"}}, "title": "CapStdSettlement CapSTDSettlementBenefitOptionsEntity"}, "CapStdSettlement_CapSTDSettlementFinancialAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementFinancialAdditionEntity"}, "ancillaryActivityName": {"type": "string", "description": "The attribute describes AncillaryActivityName taken from the Absence Case"}, "financialAdditionRehabilitation": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementFinancialAdditionRehabilitationEntity"}}}, "title": "CapStdSettlement CapSTDSettlementFinancialAdditionEntity", "description": "Defines financial additions details"}, "CapStdSettlement_CapSTDSettlementFinancialAdditionRehabilitationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementFinancialAdditionRehabilitationEntity"}, "rehabilitationTerm": {"$ref": "#/definitions/CapStdSettlement_Term"}}, "title": "CapStdSettlement CapSTDSettlementFinancialAdditionRehabilitationEntity", "description": "Defines financial rehabilitation addition details"}, "CapStdSettlement_CapSTDSettlementFinancialAdjustmentOffsetEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementFinancialAdjustmentOffsetEntity"}, "amount": {"$ref": "#/definitions/Money"}, "isPrePostTax": {"type": "boolean", "description": "This attribute defines if the tax should be applied pre taxes. If the value is set to 'FALSE', the taxes are applied post taxes."}, "offsetProratingRate": {"type": "string", "description": "This attribute describes the prorating value."}, "offsetTerm": {"$ref": "#/definitions/CapStdSettlement_Term"}, "offsetType": {"type": "string", "description": "This attribute describes the type of offset that is applicable to the claim."}, "offsetWeeklyAmount": {"$ref": "#/definitions/Money"}, "proratingRate": {"type": "string", "description": "This attribute describes the prorating value."}}, "title": "CapStdSettlement CapSTDSettlementFinancialAdjustmentOffsetEntity", "description": "Business entity that describes the Offsets of the claim. These Offsets are received from outside sources or manual input and directly reduce the amount of Benefits paid by the carrier."}, "CapStdSettlement_CapSTDSettlementFinancialAdjustmentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementFinancialAdjustmentTaxEntity"}, "financialAdjustmentTaxFederal": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementFinancialAdjustmentTaxFederalEntity"}, "jurisdictionType": {"type": "string", "description": "Defines jurisdiction type."}, "taxType": {"type": "string", "description": "Defines the type of a tax."}, "term": {"$ref": "#/definitions/CapStdSettlement_Term"}}, "title": "CapStdSettlement CapSTDSettlementFinancialAdjustmentTaxEntity", "description": "This business entity describes the withholding taxes, and the amounts that will be applied to the Claim payments."}, "CapStdSettlement_CapSTDSettlementFinancialAdjustmentTaxFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementFinancialAdjustmentTaxFederalEntity"}, "federalTaxAmount": {"$ref": "#/definitions/Money"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions. Value is inherited from Absence Case."}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation. Value is inherited from Absence Case."}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage. Value is inherited from Absence Case."}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation. Value is inherited from Absence Case."}, "federalTaxTerm": {"$ref": "#/definitions/CapStdSettlement_Term"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type. Value is inherited from Absence Case."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "CapStdSettlement CapSTDSettlementFinancialAdjustmentTaxFederalEntity", "description": "Defines Federal tax details."}, "CapStdSettlement_CapSTDSettlementIntermittentActualAbsenceEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementIntermittentActualAbsenceEntity"}, "absenceDate": {"type": "string", "format": "date-time", "description": "Absence date."}, "absenceSeconds": {"type": "integer", "format": "int64", "description": "Absence in seconds."}}, "title": "CapStdSettlement CapSTDSettlementIntermittentActualAbsenceEntity", "description": "Reduced Absence period details"}, "CapStdSettlement_CapSTDSettlementIntermittentPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementIntermittentPeriodDetailsEntity"}, "actualAbsences": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementIntermittentActualAbsenceEntity"}}}, "title": "CapStdSettlement CapSTDSettlementIntermittentPeriodDetailsEntity", "description": "Intermittent Absence period details"}, "CapStdSettlement_CapSTDSettlementLossInfoClaimPayeeDetails": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementLossInfoClaimPayeeDetails"}, "partyTypeCd": {"type": "string"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapStdSettlement CapSTDSettlementLossInfoClaimPayeeDetails"}, "CapStdSettlement_CapSTDSettlementLossInfoTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementLossInfoTypicalWorkWeekEntity"}, "hoursFri": {"type": "number"}, "hoursMon": {"type": "number"}, "hoursSat": {"type": "number"}, "hoursSun": {"type": "number"}, "hoursThu": {"type": "number"}, "hoursTue": {"type": "number"}, "hoursWed": {"type": "number"}}, "title": "CapStdSettlement CapSTDSettlementLossInfoTypicalWorkWeekEntity"}, "CapStdSettlement_CapSTDSettlementPaymentsRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementPaymentsRulesInput"}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "paymentDetails": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityPaymentDetails"}, "paymentNumber": {"type": "string", "description": "Unique payment ID."}, "paymentSubType": {"type": "string", "description": "Payment transaction subtype."}, "state": {"type": "string", "description": "Payment transaction state."}}, "title": "CapStdSettlement CapSTDSettlementPaymentsRulesInput", "description": "Payment detail for rules."}, "CapStdSettlement_CapSTDSettlementPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementPolicyInfoEntity"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "stdCertInfo": {"$ref": "#/definitions/CapStdSettlement_CapSTDCertInfoEntity"}, "stdMasterInfo": {"$ref": "#/definitions/CapStdSettlement_CapSTDMasterInfoEntity"}, "term": {"$ref": "#/definitions/CapStdSettlement_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapStdSettlement CapSTDSettlementPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapStdSettlement_CapSTDSettlementPregInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementPregInfoEntity"}, "cptCodes": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementCptEntity"}}, "deliveryDate": {"type": "string", "format": "date", "description": "Date of birth"}, "deliveryTypeCd": {"type": "string", "description": "Type of Birth"}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDiagnosisInfoEntity"}}, "dueDate": {"type": "string", "format": "date-time"}, "numberOfBirths": {"type": "integer", "format": "int64"}}, "title": "CapStdSettlement CapSTDSettlementPregInfoEntity", "description": "Entity that contains Pregnancy details"}, "CapStdSettlement_CapSTDSettlementReducedPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementReducedPeriodDetailsEntity"}, "secondsFri": {"type": "integer", "format": "int64", "description": "Seconds Friday"}, "secondsMon": {"type": "integer", "format": "int64", "description": "Seconds Monday"}, "secondsSat": {"type": "integer", "format": "int64", "description": "Seconds Saturday"}, "secondsSun": {"type": "integer", "format": "int64", "description": "Seconds Sunday"}, "secondsThu": {"type": "integer", "format": "int64", "description": "Seconds Thursday"}, "secondsTue": {"type": "integer", "format": "int64", "description": "Seconds Tuesday"}, "secondsWed": {"type": "integer", "format": "int64", "description": "Seconds Wednesday"}}, "title": "CapStdSettlement CapSTDSettlementReducedPeriodDetailsEntity", "description": "Actual absences."}, "CapStdSettlement_CapSTDSettlementResultAccumulatorDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementResultAccumulatorDetailEntity"}, "accumulatorAmount": {"type": "number", "description": "Accumulator amount."}, "accumulatorAmountUnit": {"type": "string", "description": "Defines accumulator amount unit, for example money or days."}, "accumulatorCustomerUri": {"type": "string", "description": "Primary Insured."}, "accumulatorExtension": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilitySettlementResultAccumulatorExtension"}, "accumulatorParty": {"$ref": "#/definitions/EntityLink"}, "accumulatorPolicyUri": {"type": "string", "description": "Policy of accumulator."}, "accumulatorResource": {"$ref": "#/definitions/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time", "description": "Date when the accumulator is consumed."}, "accumulatorType": {"type": "string", "description": "Accumulator type. Defines what kind of accumlator is consumed."}}, "title": "CapStdSettlement CapSTDSettlementResultAccumulatorDetailEntity", "description": "Entity for calculated Accumulator details"}, "CapStdSettlement_CapSTDSettlementResultApprovalPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementResultApprovalPeriodEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementResultAccumulatorDetailEntity"}}, "approvalPeriod": {"$ref": "#/definitions/CapStdSettlement_CapApprovalPeriodEntity"}, "approvalStatus": {"type": "string", "description": "Period approval status."}, "approverPerson": {"type": "string", "description": "User that approved the period."}, "benefitDuration": {"type": "number", "description": "Benefit duration for specific approval period."}, "cancelReason": {"type": "string", "description": "Additional note why the period is canceled."}, "dateOfStatusChange": {"type": "string", "format": "date-time", "description": "The date when the status was changed."}, "frequencyType": {"type": "string", "description": "Defines the frequency of payments during the approved period."}, "partialDisability": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementApprovalPeriodPartialDisability"}, "payee": {"$ref": "#/definitions/EntityLink"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date."}}, "title": "CapStdSettlement CapSTDSettlementResultApprovalPeriodEntity", "description": "Entity of the processed periods."}, "CapStdSettlement_CapSTDSettlementRulesInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSTDSettlementRulesInput"}, "absence": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementAbsenceInfoEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementAccumulatorDetailsRulesInput"}}, "benefitApprovalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementApprovalPeriodEntity"}}, "benefitConfiguration": {"$ref": "#/definitions/CapStdSettlement_BaseAbsenceSettlementBenefitConfiguration"}, "coreBenefitState": {"type": "string"}, "currentDateTime": {"type": "string", "format": "date-time", "description": "Current system date and time."}, "details": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDetailEntity"}, "isBenefitSettlement": {"type": "boolean"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "loss": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementLossInfoEntity"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementPaymentsRulesInput"}}, "policy": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementPolicyInfoEntity"}, "settlement": {"$ref": "#/definitions/EntityLink"}}, "title": "CapStdSettlement CapSTDSettlementRulesInput"}, "CapStdSettlement_CapStdCoverageOverrideEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdCoverageOverrideEntity"}, "approvedAmountOverride": {"$ref": "#/definitions/Money"}, "approvedAmountOverrideReason": {"type": "string"}, "benefitOverridePctReason": {"type": "string"}, "benefitOverridePercent": {"type": "number"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string"}, "maxWeeklyBenefitOverrideAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitOverrideAmountReason": {"type": "string"}}, "title": "CapStdSettlement CapStdCoverageOverrideEntity"}, "CapStdSettlement_CapStdSettlementAbsenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementAbsenceInfoEntity"}, "absencePeriods": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementAbsencePeriodInfoEntity"}}, "absenceReason": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementAbsenceReasonInfoEntity"}, "applicabilityResult": {"$ref": "#/definitions/CapStdSettlement_CapSTDCaseApplicabilityResultEntity"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "finalPtoDate": {"type": "string", "format": "date-time"}, "financialAdjustmentTax": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementFinancialAdjustmentTaxEntity"}}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes. Inherited from STD Claim."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax. Inherited from STD Claim."}, "isOtherIncome": {"type": "boolean"}, "jobClassificationCd": {"type": "string"}, "lastWorkDates": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementAbsenceInfoLastWorkDateEntity"}}, "lossDiagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDiagnosisInfoEntity"}}, "returnToWorkDate": {"type": "string", "format": "date-time", "description": "Return to Work date"}, "typicalWorkWeek": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementLossInfoTypicalWorkWeekEntity"}, "wasDaysUsedAfterLdw": {"type": "boolean"}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}}, "title": "CapStdSettlement CapStdSettlementAbsenceInfoEntity", "description": "The object that includes information taken from Absence case."}, "CapStdSettlement_CapStdSettlementAbsenceInfoLastWorkDateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementAbsenceInfoLastWorkDateEntity"}, "lastWorkDate": {"type": "string", "format": "date-time"}}, "title": "CapStdSettlement CapStdSettlementAbsenceInfoLastWorkDateEntity"}, "CapStdSettlement_CapStdSettlementCptEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementCptEntity"}, "cptCode": {"type": "string"}}, "title": "CapStdSettlement CapStdSettlementCptEntity"}, "CapStdSettlement_CapStdSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapStdSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapStdSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "actualCost": {"$ref": "#/definitions/Money"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementApprovalPeriodEntity"}}, "approvedAmountOverride": {"$ref": "#/definitions/Money"}, "approvedAmountOverrideReason": {"type": "string"}, "approvedRehabsAmountOverride": {"$ref": "#/definitions/Money"}, "benefitCd": {"type": "string"}, "benefitOverridePctReason": {"type": "string"}, "benefitOverridePercent": {"type": "number"}, "buyupCoverageOverrides": {"$ref": "#/definitions/CapStdSettlement_CapStdCoverageOverrideEntity"}, "claimBenefitLabel": {"type": "string"}, "coreCoverageOverrides": {"$ref": "#/definitions/CapStdSettlement_CapStdCoverageOverrideEntity"}, "coverageCd": {"type": "string"}, "eligibilityOverrideCd": {"type": "string", "description": "The attribute represents the overridden eligibility rules decision."}, "eligibilityOverrideReason": {"type": "string"}, "eliminationPeriodOverrideEndDate": {"type": "string", "format": "date-time", "description": "The attribute that represents the overridden end date of elimination period."}, "eliminationPeriodOverrideReason": {"type": "string", "description": "The attribute that represents the reason of elimination period end date override."}, "lastWorkDateOverride": {"type": "string", "format": "date-time"}, "maxBenefitOverridePeriod": {"$ref": "#/definitions/CapStdSettlement_Period"}, "maxBenefitOverrideReason": {"type": "string"}, "maxWeeklyBenefitOverrideAmount": {"$ref": "#/definitions/Money"}, "maxWeeklyBenefitOverrideAmountReason": {"type": "string"}, "overrideBenefitTiers": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDBenefitTiersOverrideEntity"}}, "overridenGrossAmount": {"$ref": "#/definitions/Money"}, "paidToDate": {"type": "string", "format": "date", "description": "Paid to date"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "Proof of Loss received date, override this date on UI"}, "taxablePercentageOverride": {"type": "number", "description": "The taxable percentage override defined by user manually that will be taxable."}, "weeklyEarningsOverrideAmount": {"$ref": "#/definitions/Money"}, "weeklyEarningsOverrideReason": {"type": "string"}}, "title": "CapStdSettlement CapStdSettlementDetailEntity", "description": "This Business entity houses the detail of the Std Settlement."}, "CapStdSettlement_CapStdSettlementDiagnosisInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementDiagnosisInfoEntity"}, "icdCode": {"type": "string"}, "primaryCode": {"type": "boolean"}}, "title": "CapStdSettlement CapStdSettlementDiagnosisInfoEntity"}, "CapStdSettlement_CapStdSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapStdSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapStdSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapStdSettlement_AccessTrackInfo"}, "assessmentResults": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementResultEntity"}}, "benefitConfiguration": {"$ref": "#/definitions/CapStdSettlement_BaseAbsenceSettlementBenefitConfiguration"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "isGenerated": {"type": "boolean", "description": "Defines if a given entity is being generated by a service generated request or not."}, "policy": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementPolicyInfoEntity"}, "policyId": {"type": "string"}, "settlementAbsenceInfo": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementAbsenceInfoEntity"}, "settlementDetail": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementLossInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}}, "title": "CapStdSettlement CapStdSettlementEntity", "description": "The object that encompasses attributes set for STD Settlement."}, "CapStdSettlement_CapStdSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntity"}}, "title": "CapStdSettlement_CapStdSettlementEntitySuccess"}, "CapStdSettlement_CapStdSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapStdSettlement_CapStdSettlementEntitySuccessBody"}, "CapStdSettlement_CapStdSettlementLossInfoEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementLossInfoEarningsEntity"}, "annualBonusAmount": {"$ref": "#/definitions/Money"}, "annualCommissionAmount": {"$ref": "#/definitions/Money"}, "baseSalaryAmount": {"$ref": "#/definitions/Money"}, "salaryMode": {"type": "string"}}, "title": "CapStdSettlement CapStdSettlementLossInfoEarningsEntity"}, "CapStdSettlement_CapStdSettlementLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementLossInfoEntity"}, "absence": {"$ref": "#/definitions/EntityLink"}, "absenceReasonsCd": {"type": "array", "items": {"type": "string"}}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations. Value is copied from STD Claim domain."}, "claimPayeeDetails": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementLossInfoClaimPayeeDetails"}, "coverageType": {"type": "string", "description": "Type of coverage"}, "disabilityReasonCd": {"type": "string", "description": "Disability reason. Value is copied from STD Claim domain."}, "earnings": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementLossInfoEarningsEntity"}, "eligibilityVerifiedCd": {"type": "string"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "financialAddition": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementFinancialAdditionEntity"}}, "financialAdjustmentOffsets": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementFinancialAdjustmentOffsetEntity"}}, "firstDayHospitalizationDate": {"type": "string", "format": "date-time", "description": "First Day of Hospitalization."}, "interruptionDaysNumber": {"type": "integer", "format": "int64"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time. Value is copied from STD Claim domain."}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date and time when the incident happened. Value is copied from STD Claim domain."}, "lossDiagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapStdSettlementDiagnosisInfoEntity"}}, "lossNumber": {"type": "string"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Type of loss."}, "memberRegistryTypeId": {"type": "string"}, "outpatientSurgeryDate": {"type": "string", "format": "date-time", "description": "Outpatient Surgery Date."}, "reportedDate": {"type": "string", "format": "date-time"}, "selectedCoverage": {"$ref": "#/definitions/CapStdSettlement_CapSTDClaimDetailSelectedCoverageEntity"}, "taxablePercentageOverride": {"type": "number", "description": "The percentage of gross benefit amount defined by user manually that will be taxable. Inherited from Claim."}}, "title": "CapStdSettlement CapStdSettlementLossInfoEntity", "description": "Business entity that houses the information from STD claim to use in settlement."}, "CapStdSettlement_CapStdSettlementResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStdSettlementResultEntity"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementResultApprovalPeriodEntity"}}, "approvedAmount": {"$ref": "#/definitions/Money"}, "approvedRehabAmount": {"type": "string", "description": "The attribute describes calculated RehabAmount"}, "approvedRehabsAmount": {"$ref": "#/definitions/Money"}, "autoAdjudicatedDuration": {"type": "integer", "format": "int64", "description": "Indicates the duration of Auto Adjudication Case Duration Guideline value in days."}, "benefitAccumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementResultAccumulatorDetailEntity"}}, "benefitBeginDate": {"type": "string", "format": "date-time"}, "benefitCd": {"type": "string", "description": "The benefit code"}, "benefitDurationUnitCd": {"type": "string", "description": "Benefit duration unit."}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "calculatedBenefitTiers": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDCalculatedBenefitTiers"}}, "calculatedLastWorkDate": {"type": "string", "format": "date-time"}, "coverageCd": {"type": "string", "description": "Coverage code"}, "coverageName": {"type": "string"}, "coverageWeeklyLimitAmount": {"$ref": "#/definitions/Money"}, "coveredEarningsAmount": {"$ref": "#/definitions/Money"}, "coveredEarningsOverrideAmount": {"$ref": "#/definitions/Money"}, "currentEarningsReducedPct": {"type": "number"}, "eligibilityEvaluationCd": {"type": "string", "description": "This attribute describes the decision of eligibility rules."}, "eliminationPeriodDuration": {"type": "integer", "format": "int64"}, "eliminationPeriodThroughDate": {"type": "string", "format": "date-time", "description": "\tThis attribute defines end date of elimination period. Elimination period is time (starting from date of loss) an Insured must be disabled before qualifying for STD benefits. No benefits are payable during the elimination period."}, "employmentStatus": {"type": "string"}, "employmentTerminiationDate": {"type": "string", "format": "date-time"}, "formulaCalculationDetails": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapBaseDisabilityFormulaCalculationDetails"}}, "grossBenefitAmount": {"$ref": "#/definitions/CapStdSettlement_CapSTDGrossBenefitAmount"}, "individualRecordTypeCd": {"type": "string"}, "isAutoAdjudicated": {"type": "boolean", "description": "Indicates if this Claim is the subject to Auto Adjudication."}, "isEligibilityEvaluationCdOverriden": {"type": "boolean", "description": "This attribute describes if Eligibility evaluation Cd is overriden"}, "isEliminationPeriodWaived": {"type": "boolean"}, "isMaxBenefitPeriodOverriden": {"type": "boolean"}, "isProgressiveIllnessBenefitApplicable": {"type": "boolean", "description": "Defines if policy has progressive illness benefit"}, "isSelfBill": {"type": "boolean"}, "maxBenefitDuration": {"type": "string"}, "maxBenefitDurationDays": {"type": "integer", "format": "int64", "description": "The maximum amount of days for the benefit."}, "maxBenefitEndDate": {"type": "string", "format": "date-time"}, "maxBenefitStartDate": {"type": "string", "format": "date-time"}, "maxPartialEarningsPct": {"type": "number"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_MessageType"}}, "minPartialEarningsPct": {"type": "number"}, "options": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementBenefitOptionsEntity"}, "partialDisabilityCd": {"type": "string"}, "partyTypeCd": {"type": "string"}, "paymentDetailInfo": {"$ref": "#/definitions/CapStdSettlement_PaymentDetailInfoEntity"}, "planCd": {"type": "string"}, "planName": {"type": "string"}, "proratingRate": {"type": "string", "description": "Prorating rate, used to prorate AGBA, deductions and taxes. The values of the prorating rate can be 1/5 or 1/7."}, "rehabbenefitCd": {"type": "string", "description": "The attribute describes rehabbenefitCd taken from policy"}, "rehabbenefitPct": {"type": "number", "description": "The attribute describes rehabbenefitPct taken from policy"}, "rehabilitationTerm": {"$ref": "#/definitions/CapStdSettlement_Term"}, "remainingBenefitDurationDays": {"type": "integer", "format": "int64", "description": "The remaining amount of days for the benefit."}, "reserve": {"type": "number"}, "roundingAmount": {"type": "integer", "format": "int64"}, "roundingFactorCd": {"type": "string"}, "taxablePercentage": {"type": "number", "description": "The calculated percentage of gross benefit amount that will be taxable."}, "totalBenefitDuration": {"type": "number", "description": "The total benefit duration for claim."}, "usedBenefitDurationDays": {"type": "integer", "format": "int64", "description": "The amount of days used by the benefit."}, "weeklyBenefitAmount": {"$ref": "#/definitions/Money"}, "workIncentiveBenefitCd": {"type": "string"}}, "title": "CapStdSettlement CapStdSettlementResultEntity", "description": "Business entity defines STD settlement result."}, "CapStdSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "categoryCd": {"type": "string"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapStdSettlement MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapStdSettlement_PaymentDetailInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PaymentDetailInfoEntity"}, "allocationPeriod": {"$ref": "#/definitions/CapStdSettlement_Period"}, "approvalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapStdSettlement_CapSTDSettlementApprovalPeriodEntity"}}, "initiatePayment": {"type": "boolean", "description": "Defines if settlement is payable."}, "payee": {"$ref": "#/definitions/EntityLink"}, "scenarioCd": {"type": "string", "description": "Defines scenario code."}, "scheduleDescription": {"type": "string", "description": "Defines schedule description"}}, "title": "CapStdSettlement PaymentDetailInfoEntity", "description": "Business entity defines payment detail info."}, "CapStdSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapStdSettlement Period"}, "CapStdSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapStdSettlement Term"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputs"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "DisapproveDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLink"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkBody"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "InitDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputs"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "InitStdSettlementBenefitToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "InitStdSettlementBenefitToAccumulatorTxOutputs"}, "InitStdSettlementBenefitToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/InitStdSettlementBenefitToAccumulatorTxOutputs"}}, "title": "InitStdSettlementBenefitToAccumulatorTxOutputsSuccess"}, "InitStdSettlementBenefitToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/InitStdSettlementBenefitToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "InitStdSettlementBenefitToAccumulatorTxOutputsSuccessBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "ModelFactory": {"properties": {"modelName": {"type": "string"}}, "title": "ModelFactory"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs": {"properties": {"output": {"type": "object"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputs"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ReadjudicateDisabilitySettlementToAccumulatorTxOutputsSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}