/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.lifeclaim;

import cap.adjuster.services.lifeclaim.dto.CapLifeClaimSettlement;
import cap.adjuster.services.lifeclaim.impl.CapAdjusterClaimWrapperServiceImpl;
import com.google.inject.ImplementedBy;

import java.util.List;
import java.util.concurrent.CompletionStage;

/**
 * Service that provides methods for CAP ClaimWrapper
 */
@ImplementedBy(CapAdjusterClaimWrapperServiceImpl.class)
public interface CapAdjusterClaimWrapperService {

    /**
     * Get settlements associated with ClaimWrapper
     *
     * @param rootId     ClaimWrapper rootId
     * @param revisionNo ClaimWrapper revision number
     *
     * @return list of settlements
     */
    CompletionStage<List<CapLifeClaimSettlement>> getSettlements(String rootId, Integer revisionNo);


}
