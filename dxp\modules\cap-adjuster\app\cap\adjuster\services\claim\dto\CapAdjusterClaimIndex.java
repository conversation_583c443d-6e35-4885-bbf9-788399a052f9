/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.dto;

import java.time.ZonedDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import core.services.ApiDTO;
import core.services.PartialUpdateApiDTO;
import core.utils.JsonUtils;

public class CapAdjusterClaimIndex extends PartialUpdateApiDTO implements ApiDTO {
    public String caseSystemId;
    public String lastName;
    public String workQueueCd;
    public String rootId;
    public List<String> city;
    public List<String> postalCode;
    public List<String> policyNumber;
    public String legalName;
    public String workUserId;
    public String state;
    public List<String> email;
    public String caseLossNumber;
    public String workStatusCd;
    public List<String> stateProvinceCd;
    public String legalId;
    public String claimSystemId;
    public String customerNumber;
    public String workCaseId;
    public String lossModelName;
    public String claimLossNumber;
    public String firstName;
    public List<String> phoneNumber;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = JsonUtils.DATE_TIME_MILLIS_FORMAT)
    public ZonedDateTime lossDate;
    public List<String> streetAddress;
    public List<String> capPolicyId;
    public String taxId;
    public CapAdjusterPolicyProjectionHeader policyProjectionHeader;
    public String subjectOfClaimLink;
}
