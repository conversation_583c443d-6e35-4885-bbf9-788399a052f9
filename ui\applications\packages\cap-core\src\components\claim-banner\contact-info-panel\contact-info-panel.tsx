/*
 * Copyright © 2016-2018 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import * as React from 'react'
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>up<PERSON><PERSON><PERSON>} from '@eisgroup/react-components'
import {Popover, Tooltip} from '@eisgroup/ui-kit'
import {opt} from '@eisgroup/common-types'
import {LocalizationUtils} from '@eisgroup/i18n'
import {
    ActionCloseMedium,
    CommunicationEnvelopeMedium,
    CommunicationPhoneMedium,
    MapPinMedium,
    ValidationMediumDisabled,
    ValidationMediumInfo
} from '@eisgroup/ui-kit-icons'
import t = LocalizationUtils.translate

const PREFIX = 'gen'
const CONTACT_TYPE_VIEW = `${PREFIX}-contact-type-view`
const CONTACT_TYPE_VIEW_INDICATORS_POPOVER_HEADER = `${CONTACT_TYPE_VIEW}-indicators-popover-header`
const CONTACT_TYPE_VIEW_INDICATORS_POPOVER_CLOSE = `${CONTACT_TYPE_VIEW}-indicators-popover-close`
const CUSTOMER_CONTACT_INFO_COMMUNICATIONS_CONTAINER = `${PREFIX}-customer-contact-info-communications-container`
const CUSTOMER_CONTACT_METHOD_ICON = `${PREFIX}-customer-contact-method-icon`
const CUSTOMER_CONTACT_INFO_ADDRESS = `${PREFIX}-customer-contact-info-address`
const CUSTOMER_CONTACT_INFO_PHONE_NUMBERS = `${PREFIX}-customer-contact-info-phone-numbers`
const CUSTOMER_CONTACT_INFO_EMAIL = `${PREFIX}-customer-contact-info-email`
const CUSTOMER_CONTACT_INFO_VALIDATION_DISABLED = `${PREFIX}-customer-contact-info-validation-disabled`
const CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT = `${PREFIX}-customer-contact-info-phone-email-chat`
const CUSTOMER_CONTACT_INFO_PHONE_EXT_KEY = `${PREFIX}-customer-contact-info-phone-ext-key`

/**
 * Labels for ContactInfoPanel component.
 */
interface ILabels {
    /**
     * 'Contact Info' label.
     */
    contactInfo?: string

    /**
     * 'Do not solicit' label.
     */
    doNotSolicit?: string
}

/**
 * Properties of ContactInfoPanel component.
 */
interface IContactInfoPanelProps extends HasLabels<ILabels> {
    /**
     * Address information.
     */
    address?: string

    /**
     * Phone (phone + extension).
     */
    phone?: string

    /**
     * Phone number only
     */
    phoneNumber?: string

    phoneExtension?: string

    /**
     * Email.
     */
    email?: string

    /**
     * A boolean map of which contact properties should be marked as "do not solicit".
     */
    doNotSolicit?: {
        address?: boolean
        phone?: boolean
        email?: boolean
    }

    /**
     * returns any communication preferences related to customer contacts
     */
    communicationPreferences?: {
        address?: string[]
        phone?: string[]
        email?: string[]
    }

    /**
     * function called when edit button is clicked
     */
    onEdit?: () => void
}

/**
 * Component Local State
 */
interface IContactInfoPanelState {
    /**
     * control address info pop-over
     */
    showAddressInfo?: boolean
    /**
     * control phone info pop-over
     */
    showPhonesInfo?: boolean
    /**
     * control email pop-over
     */
    showEmailInfo?: boolean
}

/**
 * Component for show customer's contact information.
 */
export class ContactInfoPanel extends React.Component<IContactInfoPanelProps, IContactInfoPanelState> {
    public static defaultProps: Partial<IContactInfoPanelProps> = {
        doNotSolicit: {}
    }

    constructor(props: IContactInfoPanelProps) {
        super(props)
        this.state = {
            showAddressInfo: false,
            showPhonesInfo: false,
            showEmailInfo: false
        }
    }

    /**
     * Renders <tt>ContactInfoPanel</tt> component.
     * @returns {React.ReactNode}
     */
    public render(): React.ReactNode {
        return (
            <div>
                {this.renderAddress()}
                {this.renderPhoneNumbers()}
                {this.renderEmail()}
            </div>
        )
    }

    /**
     * Returns labels of <tt>ContactInfoPanel</tt> component.
     * @returns ILabels
     */
    private labels: () => ILabels = () => ({
        contactInfo: t('cem_components:contact_info_panel_labels_contactInfo'),
        altPhone: t('cem_components:contact_info_panel_labels_altPhone'),
        doNotSolicit: t('cem_components:contact_info_panel_labels_doNotSolicit'),
        ...this.props.labels
    })

    private renderCommunicationPreferences(preferences: string[]): React.ReactNode {
        return preferences.map((v, i) => (
            <span key={v}>
                <LookupLabel lookup='CommunicationPreferences' code={v} />
                <br />
            </span>
        ))
    }

    /**
     * Renders address.
     * @returns {React.ReactNode}
     */
    private renderAddress(): React.ReactNode {
        const preferences = opt(this.props)
            .map(v => v.communicationPreferences)
            .map(v => v.address)
            .orElse([])

        return (
            <div className={CUSTOMER_CONTACT_INFO_ADDRESS}>
                <MapPinMedium className={CUSTOMER_CONTACT_METHOD_ICON} />
                <span>{this.props.address}</span>
                {this.props.doNotSolicit!.address && (
                    <Tooltip placement='top' title={this.labels().doNotSolicit}>
                        <ValidationMediumDisabled className={CUSTOMER_CONTACT_INFO_VALIDATION_DISABLED} />
                    </Tooltip>
                )}
                {preferences.length > 0 && (
                    <Popover
                        overlayClassName={CUSTOMER_CONTACT_INFO_COMMUNICATIONS_CONTAINER}
                        title={
                            <div className={CONTACT_TYPE_VIEW_INDICATORS_POPOVER_HEADER}>
                                <span>
                                    {t('cem_components:customer_change_Addresses_column_com_preferences_title')}
                                </span>
                                <ActionCloseMedium
                                    className={CONTACT_TYPE_VIEW_INDICATORS_POPOVER_CLOSE}
                                    onClick={() =>
                                        this.setState((state: IContactInfoPanelState) => {
                                            return {
                                                ...state,
                                                showAddressInfo: false
                                            }
                                        })
                                    }
                                />
                            </div>
                        }
                        content={this.renderCommunicationPreferences(preferences)}
                        onVisibleChange={(visible: boolean) => {
                            this.setState((state: IContactInfoPanelState) => {
                                return {
                                    ...state,
                                    showAddressInfo: visible
                                }
                            })
                        }}
                        visible={this.state.showAddressInfo}
                        placement='bottomLeft'
                        size='medium'
                        arrowPointAtCenter
                        trigger='click'
                    >
                        <ValidationMediumInfo style={{marginLeft: '0.5rem'}} />
                    </Popover>
                )}
            </div>
        )
    }

    /**
     * Renders phone numbers.
     * @returns {React.ReactNode}
     */
    private renderPhoneNumbers(): React.ReactNode {
        if (!this.props.phone) {
            return null
        }
        const preferences = opt(this.props)
            .map(v => v.communicationPreferences)
            .map(v => v.phone)
            .orElse([])
        return (
            <div className={CUSTOMER_CONTACT_INFO_PHONE_NUMBERS}>
                <CommunicationPhoneMedium className={CUSTOMER_CONTACT_METHOD_ICON} />
                <a className={CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT} href={`tel:${this.props.phoneNumber}`}>
                    {this.props.phoneNumber}
                </a>
                {this.props.phoneExtension && this.props.phoneExtension.length !== 0 && (
                    <div>
                        <span className={CUSTOMER_CONTACT_INFO_PHONE_EXT_KEY}>
                            {t('cem_components:contact_info_panel_labels_phoneExtension')}
                        </span>
                        <span className={CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT}>{this.props.phoneExtension}</span>
                    </div>
                )}
                {this.props.doNotSolicit!.phone && (
                    <Tooltip placement='top' title={this.labels().doNotSolicit}>
                        <ValidationMediumDisabled className={CUSTOMER_CONTACT_INFO_VALIDATION_DISABLED} />
                    </Tooltip>
                )}
                {preferences.length > 0 && (
                    <Popover
                        overlayClassName={CUSTOMER_CONTACT_INFO_COMMUNICATIONS_CONTAINER}
                        title={
                            <div className={CONTACT_TYPE_VIEW_INDICATORS_POPOVER_HEADER}>
                                <span>
                                    {t('cem_components:customer_change_Addresses_column_com_preferences_title')}
                                </span>
                                <ActionCloseMedium
                                    className={CONTACT_TYPE_VIEW_INDICATORS_POPOVER_CLOSE}
                                    onClick={() =>
                                        this.setState((state: IContactInfoPanelState) => {
                                            return {
                                                ...state,
                                                showPhonesInfo: false
                                            }
                                        })
                                    }
                                />
                            </div>
                        }
                        content={this.renderCommunicationPreferences(preferences)}
                        onVisibleChange={(visible: boolean) => {
                            this.setState((state: IContactInfoPanelState) => {
                                return {
                                    ...state,
                                    showPhonesInfo: visible
                                }
                            })
                        }}
                        visible={this.state.showPhonesInfo}
                        placement='bottomLeft'
                        size='medium'
                        arrowPointAtCenter
                        trigger='click'
                    >
                        <ValidationMediumInfo style={{marginLeft: '0.5rem'}} />
                    </Popover>
                )}
            </div>
        )
    }

    /**
     * Renders email.
     * @returns {React.ReactNode}
     */
    private renderEmail(): React.ReactNode {
        if (!this.props.email) {
            return null
        }
        const preferences = opt(this.props)
            .map(v => v.communicationPreferences)
            .map(v => v.email)
            .orElse([])
        return (
            <div className={CUSTOMER_CONTACT_INFO_EMAIL}>
                <CommunicationEnvelopeMedium className={CUSTOMER_CONTACT_METHOD_ICON} />
                <a className={CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT} href={`mailto:${this.props.email}`}>
                    {this.props.email}
                </a>
                {this.props.doNotSolicit!.email && (
                    <Tooltip placement='top' title={this.labels().doNotSolicit}>
                        <ValidationMediumDisabled className={CUSTOMER_CONTACT_INFO_VALIDATION_DISABLED} />
                    </Tooltip>
                )}
                {preferences.length > 0 && (
                    <Popover
                        overlayClassName={CUSTOMER_CONTACT_INFO_COMMUNICATIONS_CONTAINER}
                        title={
                            <div className={CONTACT_TYPE_VIEW_INDICATORS_POPOVER_HEADER}>
                                <span>
                                    {t('cem_components:customer_change_Addresses_column_com_preferences_title')}
                                </span>
                                <ActionCloseMedium
                                    className={CONTACT_TYPE_VIEW_INDICATORS_POPOVER_CLOSE}
                                    onClick={() =>
                                        this.setState((state: IContactInfoPanelState) => {
                                            return {
                                                ...state,
                                                showEmailInfo: false
                                            }
                                        })
                                    }
                                />
                            </div>
                        }
                        content={this.renderCommunicationPreferences(preferences)}
                        onVisibleChange={(visible: boolean) => {
                            this.setState((state: IContactInfoPanelState) => {
                                return {
                                    ...state,
                                    showEmailInfo: visible
                                }
                            })
                        }}
                        visible={this.state.showEmailInfo}
                        placement='bottomLeft'
                        size='medium'
                        arrowPointAtCenter
                        trigger='click'
                    >
                        <ValidationMediumInfo style={{marginLeft: '0.5rem'}} />
                    </Popover>
                )}
            </div>
        )
    }
}
