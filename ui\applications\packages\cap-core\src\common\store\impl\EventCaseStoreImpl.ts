/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    CapAdjusterEventCaseCapEventCaseSubStatusInput,
    CapAdjusterEventCaseClaimLossCloseInput,
    CapAdjusterEventCaseClaimLossReopenInput,
    CapGenericSpecialHandling
} from '@eisgroup/cap-gateway-client'
import {BusinessTypes, CapEventCase} from '@eisgroup/cap-event-case-models'
import {CapAccumulatorContainer, CapSpecialHandling} from '@eisgroup/cap-models'
import {
    BackofficeCommonLossTypesService,
    backofficeCustomerRelationshipService,
    backofficeLossTypesService as defaultBackofficeLossTypesService,
    backofficeWorkService,
    CapDisabilityLoss,
    CheckBoxCardsCodeValue,
    ClaimLossState,
    eventCaseService,
    LossParams,
    OrgPerson,
    Relationship
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {t} from '@eisgroup/i18n'
import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import {Left, Right} from '@eisgroup/data.either'
import {cloneDeep, uniqBy} from 'lodash'
import {action, IObservableArray, observable, computed, runInAction, toJS} from 'mobx'
import {Observable} from 'rxjs'
import {flatMap, share, tap} from 'rxjs/operators'
import {
    CHANGE_EVENT_CASE_SUBSTATUS,
    CLOSE_EVENT_CASES,
    LOAD_EVENT_CASE,
    POLL_LOAD_EVENT_CASE,
    REOPEN_EVENT_CASES,
    UPDATE_EVENT_CASE,
    UPDATE_EVENT_CASE_DRAFT
} from '../../constants'
import {convertEventCaseByClaimEvents} from '../../utils'
import {BaseRootStoreImpl} from '../BaseRootStore'
import {EmploymentStore} from '../EmploymentStore'
import {EventCaseStore} from '../EventCaseStore'
import {IcdCodeStore, IcdCodeStoreImpl} from '../IcdCodeStore'
import {EmploymentStoreImpl} from './EmploymentStoreImpl'
import CapLoss = BusinessTypes.CapLoss
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapSpecialHandlingEntity = CapSpecialHandling.CapSpecialHandlingEntity
import CapAccumulatorContainerEntity = CapAccumulatorContainer.CapAccumulatorContainerEntity
import {ICaseSystemLoss} from '../../Types'

export const LOAD_ACCUMULATORS_ASSOCIATED_WITH_CASE_SYSTEM = 'loadAccumulatorsAssociatedWithCaseSystem'
export const LOAD_LOSSES_ASSOCIATED_WITH_EVENT_CASE = 'loadLossesAssociatedWithEventCase'

export class EventCaseStoreImpl extends BaseRootStoreImpl implements EventCaseStore {
    employmentStore: EmploymentStore

    @observable eventCase: CapEventCaseEntity

    @observable caseLoading: boolean

    @observable lossTypeValue: CheckBoxCardsCodeValue[]

    @observable lossTypes?: CheckBoxCardsCodeValue[]

    @observable lossesRelativeToCase: IObservableArray<CapDisabilityLoss> = observable<CapDisabilityLoss>([])

    @observable absenceLossesRelativeToEventCase: IObservableArray<CapLoss> = observable<CapLoss>([])

    @observable relationships: Relationship[]

    @observable allSpecialHandlingsUnderEventCase: CapSpecialHandlingEntity[] = []

    @observable approverPersons: OrgPerson[] = []

    @observable periodAccumulators: CapAccumulatorContainerEntity[] = observable([])

    backofficeLossTypesService: BackofficeCommonLossTypesService

    icdCodeStore: IcdCodeStore

    constructor(
        parentStore?,
        {
            backofficeLossTypesService = defaultBackofficeLossTypesService
        }: {
            backofficeLossTypesService?: BackofficeCommonLossTypesService
        } = {}
    ) {
        super()
        if (parentStore) {
            this.actionsStore = parentStore.actionsStore
            this.employmentStore = new EmploymentStoreImpl(this, parentStore.customerStore)
        }
        this.backofficeLossTypesService = backofficeLossTypesService
        this.icdCodeStore = new IcdCodeStoreImpl()
    }

    @computed
    get associatedNotClosedLosses(): CapLoss[] {
        return this.absenceLossesRelativeToEventCase.filter(loss => loss.state !== ClaimLossState.Closed)
    }

    /**
     * special handling backend error message
     * @param e - error message from backend
     * */
    getErrorMessage = e => {
        let errorMsg = ''
        if (e.errors && e.errors.length) {
            errorMsg = e.errors?.[0]?.message
            if (e.errors?.[0]?.code === 'unf0001') {
                errorMsg = 'cap-core:relationship_has_exsit_error'
            }
        }
        return errorMsg
    }

    @action
    createCustomerRelationship = params => {
        const {fromUri, toUri, relationshipToInsuredCd} = params
        const relationshipRequest = {
            _modelName: 'CustomerRelationship',
            _type: 'GenesisRelationship',
            from: {
                _uri: fromUri
            },
            relationshipTypeCd: 'Personal',
            to: {
                _uri: toUri
            },
            relationshipRoleToCd: relationshipToInsuredCd
        }

        return backofficeCustomerRelationshipService()
            .createCustomerRelationship(relationshipRequest)
            .then(response => response)
            .catch(e => {
                const errorMsg = this.getErrorMessage(e)
                return this.throwError(errorMsg)
            })
    }

    @action
    updateCustomerRelationship = params => {
        const {fromUri, toUri, relationshipToInsuredCd, _key, _timestamp} = params
        const relationshipRequest = {
            _modelName: 'CustomerRelationship',
            _type: 'GenesisRelationship',
            from: {
                _uri: fromUri
            },
            relationshipTypeCd: 'Personal',
            to: {
                _uri: toUri
            },
            relationshipRoleToCd: relationshipToInsuredCd,
            _key,
            _timestamp
        }
        return this.promiseCall(() =>
            backofficeCustomerRelationshipService().updateCustomerRelationship(relationshipRequest)
        )
    }

    @action
    getCustomerRelationships = (customerRootId: string) => {
        return this.promiseCall(() => backofficeCustomerRelationshipService().getCustomerRelationships(customerRootId))
            .then(response => {
                runInAction(() => {
                    this.relationships = response
                })
                return response
            })
            .catch(Left) as any
    }

    @action
    updateEventCase = (eventCase?: CapEventCaseEntity) => {
        if (eventCase) {
            // lossTypes fake data
            this.eventCase = {...eventCase}
        }
    }

    @action
    setCaseLoading = (caseLoading: boolean) => {
        this.caseLoading = caseLoading
    }

    @action
    refreshEventCase = (skipPolling = false): RxResult<CapEventCaseEntity> => {
        if (this.eventCase?._key) {
            this.setCaseLoading(true)
            if (skipPolling) {
                return eventCaseService.loadEventCase(this.eventCase._key).pipe(
                    tap(either =>
                        runInAction(() => {
                            this.updateEventCaseObj(either.get())
                            this.setCaseLoading(false)
                        })
                    )
                )
            }
            const polling = eventCaseService.pollLoadEventCase(this.eventCase._key).pipe(share())
            polling.subscribe({
                next: either => runInAction(() => this.updateEventCaseObj(either.get())),
                error: () => this.setCaseLoading(false),
                complete: () => this.setCaseLoading(false)
            })
            return polling
        }
        return Observable.empty()
    }

    @action
    loadEventCase = (lossParams: LossParams, actionName: string = LOAD_EVENT_CASE) => {
        this.call<CapEventCaseEntity>(() => eventCaseService.loadEventCase(lossParams), actionName).subscribe(
            either => {
                if (either.isRight) {
                    runInAction(() => this.updateEventCaseObj(either.get()))
                }
            }
        )
    }

    @action
    pollLoadEventCase = (lossParams: LossParams, actionName: string = POLL_LOAD_EVENT_CASE) => {
        this.call<CapEventCaseEntity>(() => eventCaseService.pollLoadEventCase(lossParams), actionName).subscribe(
            either => {
                if (either.isRight) {
                    runInAction(() => this.updateEventCaseObj(either.get()))
                }
            }
        )
    }

    @action
    getEventCase = (lossParams: LossParams, fromIntake?: boolean) => {
        return this.call<CapEventCaseEntity>(() => eventCaseService.loadEventCase(lossParams, fromIntake))
    }

    @action
    updateEventCaseDraftByApi = (eventCase: CapEventCaseEntity): RxResult<CapEventCaseEntity> => {
        return this.call<CapEventCaseEntity>(
            () => eventCaseService.updateEventCaseDraft(toJS(eventCase)),
            UPDATE_EVENT_CASE_DRAFT
        )
    }

    @action
    // todo: should be removed after fixing GENESIS-182527
    updateEventCasesByApi = (eventCase: CapEventCaseEntity): RxResult<CapEventCaseEntity> => {
        this.setCaseLoading(true)
        return this.call(
            () =>
                eventCaseService.updateEventCase(eventCase).flatMap(r =>
                    r.fold(errorToRxResult, payload => {
                        eventCaseService.pollLoadEventCase(payload._key).subscribe({
                            next: either => runInAction(() => this.updateEventCaseObj(either.get())),
                            error: () => this.setCaseLoading(false),
                            complete: () => this.setCaseLoading(false)
                        })
                        return Observable.of(Right(payload))
                    })
                ),
            UPDATE_EVENT_CASE
        )
    }

    @action
    updateEventCaseTypeValue = () => {
        this.callService<CheckBoxCardsCodeValue[]>(
            this.backofficeLossTypesService.getLossTypes({lossTypesName: 'ClaimEventType'}),
            response =>
                runInAction(() => {
                    this.lossTypeValue = response
                })
        )
    }

    @action
    updateEventCaseByEntity = (request: CapEventCaseEntity): RxResult<CapEventCaseEntity> => {
        this.setCaseLoading(true)
        return eventCaseService.loadEventCase(this.eventCase._key, false).pipe(
            flatMap(either =>
                either.fold(
                    e => errorToRxResult(e),
                    context => {
                        return this.call(() => this.updateEventCaseCall(request), UPDATE_EVENT_CASE).pipe(
                            tap(() => this.setCaseLoading(false))
                        )
                    }
                )
            )
        )
    }

    @action
    updateEventCaseManually = (request: CapEventCaseEntity): RxResult<CapEventCaseEntity> => {
        const requestWithUpdatedTimestamp: CapEventCaseEntity = {
            ...request,
            _timestamp: this.eventCase._timestamp
        }
        return this.call(() => this.updateEventCaseCall(requestWithUpdatedTimestamp), UPDATE_EVENT_CASE)
    }

    @action
    updateEventCaseCall = (request: CapEventCaseEntity): RxResult<CapEventCaseEntity> => {
        const saveRequest = {
            ...request,
            lossDetail: {
                ...request.lossDetail
            }
        } as CapEventCaseEntity

        const removeDuplicatedMsg = (e: ErrorMessage) => {
            let errorMsgList = [] as ErrorMessage[]
            const hash = {}
            errorMsgList = e.errors
                ? e.errors?.reduce((acc: ErrorMessage[], curr) => {
                      if (!hash[curr.code]) {
                          hash[curr.code] = true
                          acc.push(curr)
                      }
                      return acc
                  }, [])
                : []
            return errorToRxResult({
                code: e.message,
                message: String(e.errors ? errorMsgList.map(x => `${t(x.message)} `) : e.message)
            } as ErrorMessage)
        }
        return (
            eventCaseService
                .updateEventCase(saveRequest)
                // TODO remove once websocket is implemented (GENESIS-182420)
                .delay(1500)
                .flatMap(r =>
                    r.fold(
                        e => removeDuplicatedMsg(e),
                        payload => {
                            eventCaseService
                                .loadEventCase(payload._key)
                                .subscribe(either => runInAction(() => this.updateEventCaseObj(either.get())))
                            return Observable.of(Right(payload))
                        }
                    )
                )
        )
    }

    @action
    loadLossesRelativeToEventCase = () => {
        this.call<any>(() => eventCaseService.getLosses(this.eventCase._key)).subscribe(either => {
            const response = either.get()
            runInAction(() => {
                this.lossesRelativeToCase = response
            })
        })

        this.call(
            () => eventCaseService.loadLossesAssociatedWithCaseSystem(this.eventCase._key),
            LOAD_LOSSES_ASSOCIATED_WITH_EVENT_CASE
        ).subscribe(either => {
            const response = either.get()
            runInAction(() => {
                this.absenceLossesRelativeToEventCase.replace(response)
            })
        })
    }

    @action
    loadAccumulators = (losses: ICaseSystemLoss[]): void => {
        this.actionsStore.startAction(LOAD_ACCUMULATORS_ASSOCIATED_WITH_CASE_SYSTEM)
        const accumulatorContainers: CapAccumulatorContainerEntity[] = []
        Observable.of(...uniqBy(losses, 'policyId'))
            .flatMap(loss => eventCaseService.loadAccumulator(loss.policyId, this.eventCase?.memberRegistryTypeId!))
            .subscribe({
                next: either => {
                    accumulatorContainers.push(...either.get())
                },
                complete: () => {
                    runInAction(() => {
                        this.periodAccumulators = observable.array(accumulatorContainers)
                    })
                    this.actionsStore.completeAction(LOAD_ACCUMULATORS_ASSOCIATED_WITH_CASE_SYSTEM)
                },
                error: () => this.actionsStore.failAction(LOAD_ACCUMULATORS_ASSOCIATED_WITH_CASE_SYSTEM)
            })
    }

    @action
    loadLossTypes = () => {
        this.callService<CheckBoxCardsCodeValue[]>(
            this.backofficeLossTypesService.getLossTypes({lossTypesName: 'DisabilityClaimType'}),
            response =>
                runInAction(() => {
                    this.lossTypes = response
                })
        )
    }

    @action
    setCaseDetailClaimEvents = (claimEvents: string[]) => {
        if (!!this.eventCase && !!this.eventCase.lossDetail?.claimEvent) {
            this.updateEventCase({
                ...this.eventCase,
                lossDetail: {
                    ...this.eventCase.lossDetail,
                    claimEvent: {
                        ...this.eventCase.lossDetail?.claimEvent,
                        claimEvents
                    }
                }
            })
        }
    }

    @action
    updateClaimEvent = (claimEvents: string[] | undefined) => {
        if (this.eventCase) {
            this.updateEventCase(convertEventCaseByClaimEvents(this.eventCase, claimEvents))
            const icdCodes = this.eventCase.lossDetail?.diagnoses?.map(d => d.icdCode || '') || []
            this.icdCodeStore.loadIcdCodeEntries(icdCodes)
        }
    }

    @action
    closeEventCase = (request: CapAdjusterEventCaseClaimLossCloseInput): RxResult<CapEventCaseEntity> => {
        return this.call(
            () =>
                eventCaseService.closeEventCase(request).flatMap(r =>
                    r.fold(
                        e => {
                            const errorMsg = this.getCloseCaseErrorMsg(e)
                            return errorToRxResult({
                                code: String(e.errors ? e.errors?.[0]?.code : e.code),
                                message: String(e.errors ? errorMsg : e.message)
                            } as ErrorMessage)
                        },
                        payload => {
                            runInAction(() => this.updateEventCaseObj(payload))
                            return Observable.of(Right(payload))
                        }
                    )
                ),
            CLOSE_EVENT_CASES
        )
    }

    @action
    reOpenEventCase = (request: CapAdjusterEventCaseClaimLossReopenInput): RxResult<CapEventCaseEntity> => {
        return this.call(
            () =>
                eventCaseService.reOpenEventCase(request).flatMap(r =>
                    r.fold(errorToRxResult, payload => {
                        runInAction(() => this.updateEventCaseObj(payload))
                        return Observable.of(Right(payload))
                    })
                ),
            REOPEN_EVENT_CASES
        )
    }

    @action
    changeCaseSubStatus = (request: CapAdjusterEventCaseCapEventCaseSubStatusInput): RxResult<CapEventCaseEntity> => {
        return this.call(
            () =>
                eventCaseService.changeCaseSubStatus(request).flatMap(r =>
                    r.fold(errorToRxResult, payload => {
                        runInAction(() => this.updateEventCaseObj(payload))
                        return Observable.of(Right(payload))
                    })
                ),
            CHANGE_EVENT_CASE_SUBSTATUS
        )
    }

    @action
    getLatestTimestamp = (params: LossParams, callback?: any) => {
        eventCaseService.loadEventCase(params).subscribe(either => {
            const eventCase = either.get()
            callback(eventCase._timestamp)
        })
    }

    @action
    loadAllSpecialHandlingsUnderEventCase = () => {
        this.call<CapGenericSpecialHandling[]>(() =>
            eventCaseService.loadAllSpecialHandingsUnderEventCase(this.eventCase?._key)
        ).subscribe(either => {
            runInAction(() => {
                this.allSpecialHandlingsUnderEventCase = either
                    .get()
                    .map(specialHandling =>
                        CapSpecialHandling.factory.fromJS<CapSpecialHandlingEntity>(specialHandling)
                    )
            })
        })
    }

    @action
    getApproverPersonsBySecurityIdentity = (securityIds?: string[]): void => {
        if (!securityIds?.length) {
            return
        }

        this.call(() => backofficeWorkService.loadUserProfiles(securityIds)).subscribe(either => {
            runInAction(() => {
                const users = either.get()
                runInAction(() => {
                    this.approverPersons = users
                })
            })
        })
    }

    getApproverPersonFullName = (securityIdentity: string): string => {
        const {firstName, lastName} =
            this.approverPersons.find(approver => approver.securityIdentity === securityIdentity)?.personInfo || {}
        return firstName && lastName ? `${firstName} ${lastName}` : ''
    }

    private getCloseCaseErrorMsg = (e: ErrorMessage): string => {
        let errorMsg = ''
        if (e.errors && e.errors.length) {
            errorMsg = e.errors?.[0]?.message
            if (e.errors?.[0]?.code === 'cecc001') {
                errorMsg = 'cap-core:manual_close_hard_stop_alert'
            }
            if (e.errors?.[0]?.code === 'cecc002') {
                errorMsg = 'cap-core:manual_close_case_existing_issued_payment_alert'
            }
            if (e.errors?.[0]?.code === 'cecc003') {
                errorMsg = 'cap-core:manual_close_case_existing_unpaid_payment_alert'
            }
        }
        return errorMsg
    }

    @action
    private updateEventCaseObj = (response?: CapEventCaseEntity) => {
        if (response) {
            // workaround, otherwise observability is lost
            this.updateEventCase(cloneDeep(response))
        }
    }
}
