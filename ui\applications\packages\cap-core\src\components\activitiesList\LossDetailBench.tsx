/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import * as React from 'react'
import {t} from '@eisgroup/i18n'
import {Button, Tabs, useSidebar, ActivityDrawer, SidebarActivity, ActivityHeader} from '@eisgroup/ui-kit'
import {ActionHistoryAlertPanelMedium} from '@eisgroup/ui-kit-icons'
import {observer} from 'mobx-react'
import {Services, ServiceProvider} from '@eisgroup/workium-ui'
import {
    LOSSDETAIL_WORKBENCH,
    LOSSDETAIL_WORKBENCH_DRAWER_LIST_HEADER,
    LOSSDETAIL_WORKBENCH_TABS
} from '../../common/package-class-names'
import {ActivitiesList} from './ActivitiesList'
import {TimelineTabs} from './types'

const {TabPane} = Tabs

const MAIN_DRAWER_WIDTH = 434

export const LossDetailBench = observer(() => {
    // TODO: Review, looks like state is not needed here
    const [, setShowNewCommunication] = React.useState(false)
    const [, setShowUpdateCommunication] = React.useState(false)
    const [currentTab, setCurrentTab] = React.useState(TimelineTabs.Activities)
    useSidebar()

    return (
        <div className={LOSSDETAIL_WORKBENCH}>
            <ActivityDrawer
                persistent
                disableGutters
                visible
                width={MAIN_DRAWER_WIDTH}
                className={LOSSDETAIL_WORKBENCH}
            >
                <ActivityHeader
                    className={LOSSDETAIL_WORKBENCH_DRAWER_LIST_HEADER}
                    activityTitle={t('cap-core:loss_detail_bench_toolbar_title')}
                >
                    {currentTab === TimelineTabs.Interactions && (
                        <Button
                            type='primary'
                            icon='action-add-large'
                            size='medium'
                            onClick={() => {
                                setShowNewCommunication(true)
                                setShowUpdateCommunication(false)
                            }}
                        />
                    )}
                </ActivityHeader>
                <Tabs
                    className={LOSSDETAIL_WORKBENCH_TABS}
                    defaultActiveKey={currentTab}
                    onChange={key => {
                        setCurrentTab(key as TimelineTabs)
                    }}
                    destroyInactiveTabPane
                >
                    <TabPane tab={t('cap-core:loss_detail_bench_tab_activities')} key={TimelineTabs.Activities}>
                        <ActivitiesList />
                    </TabPane>
                    <TabPane tab={t('cap-core:loss_detail_bench_tab_interactions')} key={TimelineTabs.Interactions} />
                </Tabs>
            </ActivityDrawer>
        </div>
    )
})

interface LossDetailActivityOptions {
    services: Services
}

export function lossDetailBenchActivity({services}: LossDetailActivityOptions): SidebarActivity {
    return {
        name: 'bam',
        icon: <ActionHistoryAlertPanelMedium />,
        title: t('cap-core:loss_detail_bench_toolbar_title'),
        element: (
            <ServiceProvider services={services}>
                <LossDetailBench />
            </ServiceProvider>
        )
    }
}
