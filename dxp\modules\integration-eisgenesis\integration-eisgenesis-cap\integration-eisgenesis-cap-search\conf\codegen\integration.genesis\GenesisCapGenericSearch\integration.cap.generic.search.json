{"swagger": "2.0", "info": {"description": "API for search", "version": "1", "title": "search model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/common/search/v1/": {"post": {"description": "Returns all policy records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPolicyLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v1/claim": {"post": {"description": "Performs search by the provided query.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapDynamicSearchRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ClaimCapDynamicSearchResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v1/claim_policy": {"post": {"description": "Performs policy search by the provided query.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPolicySearchRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/claim_policy_CapPolicySearchResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v1/claim_policy/projection/header": {"post": {"description": "Performs policy search and transforms results to the projection.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPolicySearchRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/claim_policy_header_CapPolicySearchProjectionResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v1/claim_policy_sls": {"post": {"description": "Performs policy single line search by the provided query.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPolicySingleLineSearchRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/claim_policy_sls_CapPolicySearchResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v1/claim_policy_sls/projection/header": {"post": {"description": "Performs policy single line search and transforms results to the projection.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPolicySingleLineSearchRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/claim_policy_sls_header_CapPolicySearchProjectionResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v1/claim_sls": {"post": {"description": "Performs single line search by the provided query.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/ClaimSLSRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapJsonSearchResponseSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapAccumulatorProjectionIndex": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapAccumulatorContainerSearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/claimHeader": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchClaimIndexEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapClaimHeaderSearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/claim_canonical_policy": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapPolicySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapAccumulatorProjectionIndex": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapAccumulatorContainerSearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/claimHeader": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchClaimIndexEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapClaimHeaderSearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/claim_canonical_policy": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapPolicySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapAccumulator": {"properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_type": {"type": "string", "example": "CapAccumulator"}, "amountType": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "limitAmount": {"type": "number", "description": "Maximum amount that can be spent. Probably taken directly from Policy."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "reservedAmount": {"type": "number", "description": "Amount that is reserved to be used up."}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactions": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "type": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "usedAmount": {"type": "number", "description": "Amount that is already used."}}, "title": "CapAccumulator", "description": "CapAccumulator aggregates values of a single accumulator type."}, "CapAccumulatorContainer": {"properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorContainer"}, "accumulators": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulator"}}, "lastTransaction": {"$ref": "#/definitions/EntityLink"}}, "title": "CapAccumulatorContainer", "description": "AccumulatorContainer is responsible for storing all accumulators associated with a single policy or customer."}, "CapAccumulatorContainer_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorContainer AccessTrackInfo"}, "CapAccumulatorContainer_CapAccumulator": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulator"}, "accessTrackInfo": {"$ref": "#/definitions/CapAccumulatorContainer_AccessTrackInfo"}, "amountType": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "limitAmount": {"type": "number", "description": "Maximum amount that can be spent. Probably taken directly from Policy."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/CapAccumulatorContainer_Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "reservedAmount": {"type": "number", "description": "Amount that is reserved to be used up."}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactions": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "type": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "usedAmount": {"type": "number", "description": "Amount that is already used."}}, "title": "CapAccumulatorContainer CapAccumulator", "description": "CapAccumulator aggregates values of a single accumulator type."}, "CapAccumulatorContainer_CapAccumulatorContainerEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapAccumulatorContainer"}, "_modelType": {"type": "string", "example": "CapAccumulatorContainer"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAccumulatorContainerEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapAccumulatorContainer_AccessTrackInfo"}, "accumulators": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulator"}}, "customerURI": {"type": "string"}, "lastTransaction": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string"}}, "title": "CapAccumulatorContainer CapAccumulatorContainerEntity", "description": "Responsible for storing all accumulators associated with a single policy or customer."}, "CapAccumulatorContainer_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorContainer Term"}, "CapCanonicalPolicyIndex_CapCanonicalPolicyIndexEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapCanonicalPolicyIndex"}, "_modelType": {"type": "string", "example": "ClaimCanonicalPolicyIndexDocument"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapCanonicalPolicyIndexEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "coverableItems": {"type": "array", "items": {"$ref": "#/definitions/CapCanonicalPolicyIndex_CoverableItem"}}, "currencyCd": {"type": "string", "description": "Currency Code"}, "effectiveTerm": {"$ref": "#/definitions/CapCanonicalPolicyIndex_EffectiveTermEntity"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapCanonicalPolicyIndex_MainInsuredInfoEntity"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policySystem": {"type": "string"}, "policySystemId": {"$ref": "#/definitions/EntityLink"}, "policySystemRevisionNo": {"type": "integer", "format": "int64"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "primaryURI": {"$ref": "#/definitions/EntityLink"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "risks": {"type": "array", "items": {"$ref": "#/definitions/CapCanonicalPolicyIndex_InsurableRisk"}}, "term": {"$ref": "#/definitions/CapCanonicalPolicyIndex_TermEntity"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapCanonicalPolicyIndex CapCanonicalPolicyIndexEntity", "description": "Root type of the canonical policy model."}, "CapCanonicalPolicyIndex_CompositeCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CompositeCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "items": {"type": "array", "items": {"$ref": "#/definitions/CapCanonicalPolicyIndex_CoverableItem"}}}, "title": "CapCanonicalPolicyIndex CompositeCoverableItemCondition", "description": "Describes other item conditions to support item hierarchy."}, "CapCanonicalPolicyIndex_CoverableItem": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CoverableItem"}, "code": {"type": "string", "description": "Code representing this coverable item ."}, "compositeCoverableItemCondition": {"$ref": "#/definitions/CapCanonicalPolicyIndex_CompositeCoverableItemCondition"}, "delayableCoverableItemCondition": {"$ref": "#/definitions/CapCanonicalPolicyIndex_DelayableCoverableItemCondition"}, "distanceBoundCoverableItemCondition": {"$ref": "#/definitions/CapCanonicalPolicyIndex_DistanceBoundCoverableItemCondition"}, "limitedCoverableItemCondition": {"$ref": "#/definitions/CapCanonicalPolicyIndex_LimitedCoverableItemCondition"}, "occurrableCoverableItemCondition": {"$ref": "#/definitions/CapCanonicalPolicyIndex_OccurrableCoverableItemCondition"}, "timedCoverableItemCondition": {"$ref": "#/definitions/CapCanonicalPolicyIndex_TimedCoverableItemCondition"}}, "title": "CapCanonicalPolicyIndex CoverableItem", "description": "Describes coverage-applicable policy item, which is composed of conditions extending CoverableItemCondition type."}, "CapCanonicalPolicyIndex_DelayableCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "DelayableCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "delay": {"type": "string", "description": "Delay in ISO 8601 format."}}, "title": "CapCanonicalPolicyIndex DelayableCoverableItemCondition", "description": "Condition describing some delay."}, "CapCanonicalPolicyIndex_DistanceBoundCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "DistanceBoundCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "distanceMoreThan": {"type": "number", "description": "Decimal number indicating the distance lower bound."}}, "title": "CapCanonicalPolicyIndex DistanceBoundCoverableItemCondition", "description": "Condition describing some distance limit."}, "CapCanonicalPolicyIndex_EffectiveTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "EffectiveTermEntity"}, "effTxEffectiveDate": {"type": "string", "format": "date-time", "description": "Shows actual policy's effective date in relation to other versions of the same policy."}, "effTxExpirationDate": {"type": "string", "format": "date-time", "description": "Shows actual policy's expiration date in relation to other versions of the same policy."}}, "title": "CapCanonicalPolicyIndex EffectiveTermEntity", "description": "Holds effective policy term information."}, "CapCanonicalPolicyIndex_InsurableRisk": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "InsurableRisk"}, "coverableItems": {"type": "array", "items": {"$ref": "#/definitions/CapCanonicalPolicyIndex_CoverableItem"}}, "id": {"type": "string", "description": "ID of this insurable risk."}}, "title": "CapCanonicalPolicyIndex InsurableRisk", "description": "Holds information about the subject of insurance."}, "CapCanonicalPolicyIndex_LimitedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "LimitedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "amount": {"type": "number", "description": "Limit amount."}}, "title": "CapCanonicalPolicyIndex LimitedCoverableItemCondition", "description": "Condition describing some limit amount."}, "CapCanonicalPolicyIndex_MainInsuredInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MainInsuredInfoEntity"}, "city": {"type": "array", "items": {"type": "string"}}, "customerId": {"type": "string"}, "customerNumber": {"type": "string"}, "email": {"type": "array", "items": {"type": "string"}}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "legalId": {"type": "string"}, "legalName": {"type": "string"}, "phoneNumber": {"type": "array", "items": {"type": "string"}}, "postalCode": {"type": "array", "items": {"type": "string"}}, "stateProvinceCd": {"type": "array", "items": {"type": "string"}}, "streetAddress": {"type": "array", "items": {"type": "string"}}, "taxId": {"type": "string"}}, "title": "CapCanonicalPolicyIndex MainInsuredInfoEntity"}, "CapCanonicalPolicyIndex_OccurrableCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "OccurrableCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "maxOccurrences": {"type": "integer", "format": "int64", "description": "Maximum number of occurrences."}}, "title": "CapCanonicalPolicyIndex OccurrableCoverableItemCondition", "description": "Condition describing some number of occurrences."}, "CapCanonicalPolicyIndex_TermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "TermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapCanonicalPolicyIndex TermEntity"}, "CapCanonicalPolicyIndex_TimedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "TimedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "period": {"type": "string", "description": "Period in ISO 8601 format."}}, "title": "CapCanonicalPolicyIndex TimedCoverableItemCondition", "description": "Condition describing some time period."}, "CapDynamicSearchRequest": {"properties": {"fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/Claim_SearchQueryType"}, "sort": {"$ref": "#/definitions/Claim_SearchSortType"}}, "title": "CapDynamicSearchRequest"}, "CapDynamicSearchRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapDynamicSearchRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDynamicSearchRequestBody"}, "CapHeaderModel_CapClaimHeaderCase": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimHeaderCase"}, "caseNumber": {"type": "string"}}, "title": "CapHeaderModel CapClaimHeaderCase"}, "CapHeaderModel_CapClaimHeaderPolicy": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimHeaderPolicy"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}}, "title": "CapHeaderModel CapClaimHeaderPolicy"}, "CapHeaderModel_CapHeaderEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapHeaderModel"}, "_modelType": {"type": "string", "example": "CapClaimHeader"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapHeaderEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "caseNumber": {"type": "string"}, "claimCase": {"$ref": "#/definitions/CapHeaderModel_CapClaimHeaderCase"}, "claimDate": {"type": "string", "format": "date-time"}, "claimModelName": {"type": "string"}, "claimNumber": {"type": "string"}, "claimType": {"type": "string"}, "claimant": {"$ref": "#/definitions/CapHeaderModel_ClaimantAware"}, "insured": {"$ref": "#/definitions/CapHeaderModel_InsuredAware"}, "mappingType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/CapHeaderModel_CapClaimHeaderPolicy"}}, "reportedDate": {"type": "string", "format": "date-time"}, "state": {"type": "string"}, "subjects": {"type": "array", "items": {"$ref": "#/definitions/CapHeaderModel_ClaimSubjectInfo"}}, "totalIncurred": {"$ref": "#/definitions/Money"}}, "title": "CapHeaderModel CapHeaderEntity"}, "CapHeaderModel_ClaimSubjectInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ClaimSubjectInfo"}, "subject": {"type": "string"}, "subjectId": {"type": "string"}}, "title": "CapHeaderModel ClaimSubjectInfo"}, "CapHeaderModel_ClaimantAware": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ClaimantAware"}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}}, "title": "CapHeaderModel ClaimantAware"}, "CapHeaderModel_InsuredAware": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "InsuredAware"}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}}, "title": "CapHeaderModel InsuredAware"}, "CapJsonSearchResponse": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object"}}}, "title": "CapJsonSearchResponse"}, "CapJsonSearchResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapJsonSearchResponse"}}, "title": "CapJsonSearchResponseSuccess"}, "CapJsonSearchResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapJsonSearchResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapJsonSearchResponseSuccessBody"}, "CapPolicy": {"properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_type": {"type": "string", "example": "CapPolicy"}, "risks": {"type": "array", "items": {"$ref": "#/definitions/InsurableRisk"}}}, "title": "CapPolicy", "description": "Root type of the canonical policy model."}, "CapPolicyLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "CapPolicyLinkRequest"}, "CapPolicyLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapPolicyLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPolicyLinkRequestBody"}, "CapPolicyModel_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapPolicyModel CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapPolicyModel_CapPolicyEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPolicyModel"}, "_modelType": {"type": "string", "example": "CapPolicy"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPolicyEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "coverableItems": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CoverableItem"}}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "risks": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_InsurableRisk"}}, "term": {"$ref": "#/definitions/CapPolicyModel_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapPolicyModel CapPolicyEntity", "description": "Canonical policy entity, which represents a standard policy used in claims."}, "CapPolicyModel_CompositeCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CompositeCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "items": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CoverableItem"}}}, "title": "CapPolicyModel CompositeCoverableItemCondition", "description": "Describes other item conditions to support item hierarchy."}, "CapPolicyModel_CoverableItem": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CoverableItem"}, "code": {"type": "string", "description": "Code representing this coverable item ."}, "compositeCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_CompositeCoverableItemCondition"}, "delayableCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_DelayableCoverableItemCondition"}, "distanceBoundCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_DistanceBoundCoverableItemCondition"}, "limitedCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_LimitedCoverableItemCondition"}, "occurrableCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_OccurrableCoverableItemCondition"}, "timedCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_TimedCoverableItemCondition"}}, "title": "CapPolicyModel CoverableItem", "description": "Describes coverage-applicable policy item, which is composed of conditions extending CoverableItemCondition type."}, "CapPolicyModel_DelayableCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "DelayableCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "delay": {"type": "string", "description": "Delay in ISO 8601 format."}}, "title": "CapPolicyModel DelayableCoverableItemCondition", "description": "Condition describing some delay."}, "CapPolicyModel_DistanceBoundCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "DistanceBoundCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "distanceMoreThan": {"type": "number", "description": "Decimal number indicating the distance lower bound."}}, "title": "CapPolicyModel DistanceBoundCoverableItemCondition", "description": "Condition describing some distance limit."}, "CapPolicyModel_InsurableRisk": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "InsurableRisk"}, "coverableItems": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CoverableItem"}}, "id": {"type": "string", "description": "ID of this insurable risk."}}, "title": "CapPolicyModel InsurableRisk", "description": "Holds information about the subject of insurance."}, "CapPolicyModel_LimitedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "LimitedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "amount": {"type": "number", "description": "Limit amount."}}, "title": "CapPolicyModel LimitedCoverableItemCondition", "description": "Condition describing some limit amount."}, "CapPolicyModel_OccurrableCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "OccurrableCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "maxOccurrences": {"type": "integer", "format": "int64", "description": "Maximum number of occurrences."}}, "title": "CapPolicyModel OccurrableCoverableItemCondition", "description": "Condition describing some number of occurrences."}, "CapPolicyModel_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapPolicyModel Term"}, "CapPolicyModel_TimedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "TimedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "period": {"type": "string", "description": "Period in ISO 8601 format."}}, "title": "CapPolicyModel TimedCoverableItemCondition", "description": "Condition describing some time period."}, "CapPolicySearchRequest": {"properties": {"fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/Claim_policy_SearchQueryType"}, "sort": {"$ref": "#/definitions/Claim_policy_SearchSortType"}}, "title": "CapPolicySearchRequest"}, "CapPolicySearchRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapPolicySearchRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPolicySearchRequestBody"}, "CapPolicySingleLineSearchRequest": {"properties": {"fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"type": "string"}}, "title": "CapPolicySingleLineSearchRequest"}, "CapPolicySingleLineSearchRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapPolicySingleLineSearchRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPolicySingleLineSearchRequestBody"}, "ClaimCapDynamicSearchResponse": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/ClaimClaimIndexEntity"}}}, "title": "ClaimCapDynamicSearchResponse"}, "ClaimCapDynamicSearchResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClaimCapDynamicSearchResponse"}}, "title": "ClaimCapDynamicSearchResponseSuccess"}, "ClaimCapDynamicSearchResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClaimCapDynamicSearchResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimCapDynamicSearchResponseSuccessBody"}, "ClaimClaimIndexEntity": {"properties": {"capPolicyId": {"type": "array", "items": {"type": "string"}}, "caseLossNumber": {"type": "string"}, "caseSystemId": {"type": "string"}, "city": {"type": "array", "items": {"type": "string"}}, "claimLossNumber": {"type": "string"}, "claimSystemId": {"type": "string"}, "customerNumber": {"type": "string"}, "email": {"type": "array", "items": {"type": "string"}}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "legalId": {"type": "string"}, "legalName": {"type": "string"}, "lossDate": {"type": "string", "format": "date-time"}, "lossModelName": {"type": "string"}, "phoneNumber": {"type": "array", "items": {"type": "string"}}, "policyNumber": {"type": "array", "items": {"type": "string"}}, "postalCode": {"type": "array", "items": {"type": "string"}}, "rootId": {"type": "string"}, "state": {"type": "string"}, "stateProvinceCd": {"type": "array", "items": {"type": "string"}}, "streetAddress": {"type": "array", "items": {"type": "string"}}, "taxId": {"type": "string"}, "workCaseId": {"type": "string"}, "workQueueCd": {"type": "string"}, "workStatusCd": {"type": "string"}, "workUserId": {"type": "string"}}, "title": "ClaimClaimIndexEntity"}, "ClaimSLSRequest": {"properties": {"fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"type": "string"}}, "title": "ClaimSLSRequest"}, "ClaimSLSRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimSLSRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimSLSRequestBody"}, "Claim_SearchQueryType": {"properties": {"capPolicyId": {"$ref": "#/definitions/SearchValueMatcher"}, "caseLossNumber": {"$ref": "#/definitions/SearchValueMatcher"}, "caseSystemId": {"$ref": "#/definitions/SearchValueMatcher"}, "city": {"$ref": "#/definitions/SearchValueMatcher"}, "claimLossNumber": {"$ref": "#/definitions/SearchValueMatcher"}, "claimSystemId": {"$ref": "#/definitions/SearchValueMatcher"}, "customerNumber": {"$ref": "#/definitions/SearchValueMatcher"}, "email": {"$ref": "#/definitions/SearchValueMatcher"}, "firstName": {"$ref": "#/definitions/SearchValueMatcher"}, "lastName": {"$ref": "#/definitions/SearchValueMatcher"}, "legalId": {"$ref": "#/definitions/SearchValueMatcher"}, "legalName": {"$ref": "#/definitions/SearchValueMatcher"}, "lossDate": {"$ref": "#/definitions/SearchValueMatcher"}, "lossModelName": {"$ref": "#/definitions/SearchValueMatcher"}, "phoneNumber": {"$ref": "#/definitions/SearchValueMatcher"}, "policyNumber": {"$ref": "#/definitions/SearchValueMatcher"}, "postalCode": {"$ref": "#/definitions/SearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchValueMatcher"}, "state": {"$ref": "#/definitions/SearchValueMatcher"}, "stateProvinceCd": {"$ref": "#/definitions/SearchValueMatcher"}, "streetAddress": {"$ref": "#/definitions/SearchValueMatcher"}, "taxId": {"$ref": "#/definitions/SearchValueMatcher"}, "workCaseId": {"$ref": "#/definitions/SearchValueMatcher"}, "workQueueCd": {"$ref": "#/definitions/SearchValueMatcher"}, "workStatusCd": {"$ref": "#/definitions/SearchValueMatcher"}, "workUserId": {"$ref": "#/definitions/SearchValueMatcher"}}, "title": "Claim SearchQueryType"}, "Claim_SearchSortType": {"properties": {"capPolicyId": {"type": "string"}, "caseLossNumber": {"type": "string"}, "caseSystemId": {"type": "string"}, "city": {"type": "string"}, "claimLossNumber": {"type": "string"}, "claimSystemId": {"type": "string"}, "customerNumber": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "legalId": {"type": "string"}, "legalName": {"type": "string"}, "lossDate": {"type": "string"}, "lossModelName": {"type": "string"}, "phoneNumber": {"type": "string"}, "policyNumber": {"type": "string"}, "postalCode": {"type": "string"}, "rootId": {"type": "string"}, "state": {"type": "string"}, "stateProvinceCd": {"type": "string"}, "streetAddress": {"type": "string"}, "taxId": {"type": "string"}, "workCaseId": {"type": "string"}, "workQueueCd": {"type": "string"}, "workStatusCd": {"type": "string"}, "workUserId": {"type": "string"}}, "title": "Claim SearchSortType"}, "Claim_policy_SearchQueryType": {"properties": {"capPolicyId": {"$ref": "#/definitions/SearchValueMatcher"}, "capPolicyVersionId": {"$ref": "#/definitions/SearchValueMatcher"}, "effTxEffectiveDate": {"$ref": "#/definitions/SearchValueMatcher"}, "effTxExpirationDate": {"$ref": "#/definitions/SearchValueMatcher"}, "effectiveDate": {"$ref": "#/definitions/SearchValueMatcher"}, "expirationDate": {"$ref": "#/definitions/SearchValueMatcher"}, "insuredFirstName": {"$ref": "#/definitions/SearchValueMatcher"}, "insuredLastName": {"$ref": "#/definitions/SearchValueMatcher"}, "insuredRegistryId": {"$ref": "#/definitions/SearchValueMatcher"}, "policyNumber": {"$ref": "#/definitions/SearchValueMatcher"}, "policySystem": {"$ref": "#/definitions/SearchValueMatcher"}, "policySystemRevisionNo": {"$ref": "#/definitions/SearchValueMatcher"}, "policyUri": {"$ref": "#/definitions/SearchValueMatcher"}, "productCd": {"$ref": "#/definitions/SearchValueMatcher"}, "versionOnDate": {"$ref": "#/definitions/SearchValueMatcher"}}, "title": "Claim policy SearchQueryType"}, "Claim_policy_SearchSortType": {"properties": {"capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "effTxEffectiveDate": {"type": "string"}, "effTxExpirationDate": {"type": "string"}, "effectiveDate": {"type": "string"}, "expirationDate": {"type": "string"}, "insuredFirstName": {"type": "string"}, "insuredLastName": {"type": "string"}, "insuredRegistryId": {"type": "string"}, "policyNumber": {"type": "string"}, "policySystem": {"type": "string"}, "policySystemRevisionNo": {"type": "string"}, "policyUri": {"type": "string"}, "productCd": {"type": "string"}, "versionOnDate": {"type": "string"}}, "title": "Claim policy SearchSortType"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "HeaderProjectionModel_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "HeaderProjectionModel CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "HeaderProjectionModel_HeaderProjection": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "HeaderProjectionModel"}, "_modelType": {"type": "string", "example": "RootEntity"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "HeaderProjection"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/HeaderProjectionModel_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "masterPolicyId": {"type": "string", "description": "Defines master policy version id of individual policy stored on CAP side."}, "masterPolicyNumber": {"type": "string", "description": "Indicates master policy number of individual policy."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/HeaderProjectionModel_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "HeaderProjectionModel HeaderProjection", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "HeaderProjectionModel_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "HeaderProjectionModel Term"}, "InsurableRisk": {"properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_type": {"type": "string", "example": "InsurableRisk"}, "id": {"type": "string", "description": "ID of this insurable risk."}}, "title": "InsurableRisk", "description": "Holds information about the subject of insurance."}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "PersonalAccumulatorContainer_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "PersonalAccumulatorContainer AccessTrackInfo"}, "PersonalAccumulatorContainer_CapAccumulator": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulator"}, "accessTrackInfo": {"$ref": "#/definitions/PersonalAccumulatorContainer_AccessTrackInfo"}, "amountType": {"type": "string", "description": "Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "limitAmount": {"type": "number", "description": "Maximum amount that can be spent. Probably taken directly from Policy."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/PersonalAccumulatorContainer_Term"}, "remainingAmount": {"type": "number", "description": "Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount."}, "reservedAmount": {"type": "number", "description": "Amount that is reserved to be used up."}, "resource": {"$ref": "#/definitions/EntityLink"}, "transactions": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "type": {"type": "string", "description": "Accumulator type and has to be a unique name across the system."}, "usedAmount": {"type": "number", "description": "Amount that is already used."}}, "title": "PersonalAccumulatorContainer CapAccumulator", "description": "CapAccumulator aggregates values of a single accumulator type."}, "PersonalAccumulatorContainer_CapAccumulatorContainerEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccumulatorContainerEntity"}, "accessTrackInfo": {"$ref": "#/definitions/PersonalAccumulatorContainer_AccessTrackInfo"}, "accumulators": {"type": "array", "items": {"$ref": "#/definitions/PersonalAccumulatorContainer_CapAccumulator"}}, "customerURI": {"type": "string"}, "lastTransaction": {"$ref": "#/definitions/EntityLink"}, "policyURI": {"type": "string"}}, "title": "PersonalAccumulatorContainer CapAccumulatorContainerEntity", "description": "AccumulatorContainer is responsible for storing all accumulators associated with a single policy or customer."}, "PersonalAccumulatorContainer_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "PersonalAccumulatorContainer Term"}, "ResultCapAccumulatorContainerRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "accumulatorURIs": {"type": "array", "items": {"type": "string"}}, "creationDate": {"type": "string", "format": "date-time"}, "customerNumber": {"type": "string"}, "lastUpdatedDate": {"type": "string", "format": "date-time"}, "partyNumber": {"type": "array", "items": {"type": "string"}}, "policyNumber": {"type": "string"}, "resourceNumber": {"type": "array", "items": {"type": "string"}}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}}, "title": "ResultCapAccumulatorContainerRow"}, "ResultCapAccumulatorContainerSearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "x-oneof": [{"$ref": "#/definitions/CapAccumulatorContainer_CapAccumulatorContainerEntity"}, {"$ref": "#/definitions/PersonalAccumulatorContainer_CapAccumulatorContainerEntity"}]}}}, "title": "ResultCapAccumulatorContainerSearchEntityResponseV2"}, "ResultCapAccumulatorContainerSearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapAccumulatorContainerSearchEntityResponseV2"}}, "title": "ResultCapAccumulatorContainerSearchEntityResponseV2Success"}, "ResultCapAccumulatorContainerSearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapAccumulatorContainerSearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapAccumulatorContainerSearchEntityResponseV2SuccessBody"}, "ResultCapAccumulatorContainerSearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapAccumulatorContainer, ResultCapAccumulatorContainerRow", "x-oneof": [{"$ref": "#/definitions/ResultCapAccumulatorContainerRow"}]}}}, "title": "ResultCapAccumulatorContainerSearchEntityResponseV3"}, "ResultCapAccumulatorContainerSearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapAccumulatorContainerSearchEntityResponseV3"}}, "title": "ResultCapAccumulatorContainerSearchEntityResponseV3Success"}, "ResultCapAccumulatorContainerSearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapAccumulatorContainerSearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapAccumulatorContainerSearchEntityResponseV3SuccessBody"}, "ResultCapClaimHeaderRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "capPolicyId": {"type": "array", "items": {"type": "string"}}, "caseNumber": {"type": "string"}, "caseSystemId": {"type": "string"}, "city": {"type": "array", "items": {"type": "string"}}, "claimDate": {"type": "string"}, "claimModelName": {"type": "string"}, "claimNumber": {"type": "string"}, "claimSystemId": {"type": "string"}, "claimType": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "customerNumber": {"type": "array", "items": {"type": "string"}}, "email": {"type": "array", "items": {"type": "string"}}, "firstName": {"type": "array", "items": {"type": "string"}}, "lastName": {"type": "array", "items": {"type": "string"}}, "legalId": {"type": "array", "items": {"type": "string"}}, "legalName": {"type": "array", "items": {"type": "string"}}, "phoneNumber": {"type": "array", "items": {"type": "string"}}, "policyNumber": {"type": "array", "items": {"type": "string"}}, "postalCode": {"type": "array", "items": {"type": "string"}}, "rootId": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "score": {"type": "object"}, "stateProvinceCd": {"type": "array", "items": {"type": "string"}}, "streetAddress": {"type": "array", "items": {"type": "string"}}, "subject": {"type": "array", "items": {"type": "string"}}, "subjectId": {"type": "array", "items": {"type": "string"}}, "taxId": {"type": "array", "items": {"type": "string"}}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "ResultCapClaimHeaderRow"}, "ResultCapClaimHeaderSearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapHeaderModel_CapHeaderEntity"}}}, "title": "ResultCapClaimHeaderSearchEntityResponseV2"}, "ResultCapClaimHeaderSearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapClaimHeaderSearchEntityResponseV2"}}, "title": "ResultCapClaimHeaderSearchEntityResponseV2Success"}, "ResultCapClaimHeaderSearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapClaimHeaderSearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapClaimHeaderSearchEntityResponseV2SuccessBody"}, "ResultCapClaimHeaderSearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapClaimHeader, ResultCapClaimHeaderRow", "x-oneof": [{"$ref": "#/definitions/CapHeaderModel_CapHeaderEntity"}, {"$ref": "#/definitions/ResultCapClaimHeaderRow"}]}}}, "title": "ResultCapClaimHeaderSearchEntityResponseV3"}, "ResultCapClaimHeaderSearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapClaimHeaderSearchEntityResponseV3"}}, "title": "ResultCapClaimHeaderSearchEntityResponseV3Success"}, "ResultCapClaimHeaderSearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapClaimHeaderSearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapClaimHeaderSearchEntityResponseV3SuccessBody"}, "ResultCapPolicyRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "effTxEffectiveDate": {"type": "string", "format": "date-time"}, "effTxExpirationDate": {"type": "string", "format": "date-time"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "insuredFirstName": {"type": "array", "items": {"type": "string"}}, "insuredLastName": {"type": "array", "items": {"type": "string"}}, "insuredRegistryId": {"type": "array", "items": {"type": "string"}}, "policyNumber": {"type": "string"}, "policySystem": {"type": "string"}, "policySystemRevisionNo": {"type": "integer", "format": "int64"}, "policyUri": {"type": "string"}, "productCd": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}}, "title": "ResultCapPolicyRow"}, "ResultCapPolicySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "x-oneof": [{"$ref": "#/definitions/CapPolicyModel_CapPolicyEntity"}, {"$ref": "#/definitions/CapCanonicalPolicyIndex_CapCanonicalPolicyIndexEntity"}]}}}, "title": "ResultCapPolicySearchEntityResponseV2"}, "ResultCapPolicySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapPolicySearchEntityResponseV2"}}, "title": "ResultCapPolicySearchEntityResponseV2Success"}, "ResultCapPolicySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapPolicySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapPolicySearchEntityResponseV2SuccessBody"}, "ResultCapPolicySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapPolicy, ResultCapPolicyRow", "x-oneof": [{"$ref": "#/definitions/ResultCapPolicyRow"}]}}}, "title": "ResultCapPolicySearchEntityResponseV3"}, "ResultCapPolicySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapPolicySearchEntityResponseV3"}}, "title": "ResultCapPolicySearchEntityResponseV3Success"}, "ResultCapPolicySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapPolicySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapPolicySearchEntityResponseV3SuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SearchCapAccumulatorProjectionEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchQuery"}, "sort": {"type": "object"}}, "title": "SearchCapAccumulatorProjectionEntitySearchEntityRequestV2"}, "SearchCapAccumulatorProjectionEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapAccumulatorProjectionEntitySearchEntityRequestV2Body"}, "SearchCapAccumulatorProjectionEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"type": "object"}}}, "title": "SearchCapAccumulatorProjectionEntitySearchEntityRequestV3"}, "SearchCapAccumulatorProjectionEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapAccumulatorProjectionEntitySearchEntityRequestV3Body"}, "SearchCapAccumulatorProjectionEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapAccumulatorProjectionEntitySearchMatcher"}, "SearchCapAccumulatorProjectionEntitySearchQuery": {"properties": {"accumulatorURIs": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "creationDate": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "customerNumber": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "lastUpdatedDate": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "partyNumber": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "policyNumber": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "resourceNumber": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchValueMatcher"}}, "title": "SearchCapAccumulatorProjectionEntitySearchQuery"}, "SearchCapAccumulatorProjectionEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapAccumulatorProjectionEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapAccumulatorProjectionEntitySearchValueMatcher"}, "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchQuery"}, "sort": {"type": "object"}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV2"}, "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV2Body"}, "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"type": "object"}}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV3"}, "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchEntityRequestV3Body"}, "SearchCapCanonicalPolicyIndexEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchMatcher"}, "SearchCapCanonicalPolicyIndexEntitySearchQuery": {"properties": {"capPolicyId": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "capPolicyVersionId": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "effTxEffectiveDate": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "effTxExpirationDate": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "effectiveDate": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "expirationDate": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "insuredFirstName": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "insuredLastName": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "insuredRegistryId": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "policyNumber": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "policySystem": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "policySystemRevisionNo": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "policyUri": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "productCd": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchQuery"}, "SearchCapCanonicalPolicyIndexEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapCanonicalPolicyIndexEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapCanonicalPolicyIndexEntitySearchValueMatcher"}, "SearchClaimIndexEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchClaimIndexEntitySearchQuery"}, "sort": {"type": "object"}}, "title": "SearchClaimIndexEntitySearchEntityRequestV2"}, "SearchClaimIndexEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchClaimIndexEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchClaimIndexEntitySearchEntityRequestV2Body"}, "SearchClaimIndexEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchClaimIndexEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"type": "object"}}}, "title": "SearchClaimIndexEntitySearchEntityRequestV3"}, "SearchClaimIndexEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchClaimIndexEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchClaimIndexEntitySearchEntityRequestV3Body"}, "SearchClaimIndexEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchClaimIndexEntitySearchMatcher"}, "SearchClaimIndexEntitySearchQuery": {"properties": {"capPolicyId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "caseNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "caseSystemId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "city": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimDate": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimModelName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimSystemId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimType": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.city": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.customerNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.email": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.firstName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.lastName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.legalId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.legalName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.phoneNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.postalCode": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.stateProvinceCd": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.streetAddress": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "claimant.taxId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "createdOn": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "customerNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "email": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "firstName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "lastName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "legalId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "legalName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.city": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.customerNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.email": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.firstName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.lastName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.legalId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.legalName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.phoneNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.postalCode": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.stateProvinceCd": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.streetAddress": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "mainInsured.taxId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.city": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.customerNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.email": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.firstName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.lastName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.legalId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.legalName": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.phoneNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.postalCode": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.stateProvinceCd": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.streetAddress": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "party.taxId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "phoneNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "policyNumber": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "postalCode": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "stateProvinceCd": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "streetAddress": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "subject": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "subjectId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "taxId": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}, "updatedOn": {"$ref": "#/definitions/SearchClaimIndexEntitySearchValueMatcher"}}, "title": "SearchClaimIndexEntitySearchQuery"}, "SearchClaimIndexEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchClaimIndexEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchClaimIndexEntitySearchValueMatcher"}, "SearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchValueMatcher"}, "Term": {"properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "Term"}, "claim_policy_CapPolicySearchResponse": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object"}}}, "title": "claim policy CapPolicySearchResponse"}, "claim_policy_CapPolicySearchResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/claim_policy_CapPolicySearchResponse"}}, "title": "claim_policy_CapPolicySearchResponseSuccess"}, "claim_policy_CapPolicySearchResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/claim_policy_CapPolicySearchResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "claim_policy_CapPolicySearchResponseSuccessBody"}, "claim_policy_header_CapPolicySearchProjectionResponse": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/HeaderProjectionModel_HeaderProjection"}}}, "title": "claim policy header CapPolicySearchProjectionResponse"}, "claim_policy_header_CapPolicySearchProjectionResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/claim_policy_header_CapPolicySearchProjectionResponse"}}, "title": "claim_policy_header_CapPolicySearchProjectionResponseSuccess"}, "claim_policy_header_CapPolicySearchProjectionResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/claim_policy_header_CapPolicySearchProjectionResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "claim_policy_header_CapPolicySearchProjectionResponseSuccessBody"}, "claim_policy_sls_CapPolicySearchResponse": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object"}}}, "title": "claim policy sls CapPolicySearchResponse"}, "claim_policy_sls_CapPolicySearchResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/claim_policy_sls_CapPolicySearchResponse"}}, "title": "claim_policy_sls_CapPolicySearchResponseSuccess"}, "claim_policy_sls_CapPolicySearchResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/claim_policy_sls_CapPolicySearchResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "claim_policy_sls_CapPolicySearchResponseSuccessBody"}, "claim_policy_sls_header_CapPolicySearchProjectionResponse": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/HeaderProjectionModel_HeaderProjection"}}}, "title": "claim policy sls header CapPolicySearchProjectionResponse"}, "claim_policy_sls_header_CapPolicySearchProjectionResponseSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/claim_policy_sls_header_CapPolicySearchProjectionResponse"}}, "title": "claim_policy_sls_header_CapPolicySearchProjectionResponseSuccess"}, "claim_policy_sls_header_CapPolicySearchProjectionResponseSuccessBody": {"properties": {"body": {"$ref": "#/definitions/claim_policy_sls_header_CapPolicySearchProjectionResponseSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "claim_policy_sls_header_CapPolicySearchProjectionResponseSuccessBody"}}}