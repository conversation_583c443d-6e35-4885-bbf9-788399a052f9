/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {backOfficeWorkflowService, CAP_QUEUE_SOURCE_CD, WorkQueueDetails} from '@eisgroup/cap-services'
import {action, IObservableArray, observable, runInAction} from 'mobx'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'

export interface QueueStore extends BaseRootStore {
    /**
     * Customer that was selected from queue search results.
     */
    queue?: WorkQueueDetails
    queueAllResults: IObservableArray<WorkQueueDetails>
    selectQueue: (queue?: WorkQueueDetails) => void
    /**
     * Action to search for queues.
     */
    searchQueue: (queueName: string) => void
    clearSelectQueue: () => void
    loadAllQueues: () => void
}

export class QueueStoreImpl extends BaseRootStoreImpl implements QueueStore {
    constructor() {
        super()
        this.loadAllQueues()
    }

    @observable queue?: WorkQueueDetails

    @observable queueAllResults

    @action
    selectQueue = (queue?: WorkQueueDetails) => {
        this.queue = queue
    }

    @action
    searchQueue = (queueName: string) => {
        this.callService(backOfficeWorkflowService.loadWorkQueuesName(queueName), response => {
            runInAction(() => {
                this.queueAllResults = response
            })
        })
    }

    @action
    clearSelectQueue = (): void => {
        this.queue = undefined
    }

    @action
    loadAllQueues = () => {
        this.call(() => backOfficeWorkflowService.loadAllQueues()).subscribe(either => {
            runInAction(() => {
                const response = either.get()
                this.queueAllResults = response.filter(q => q.queueCd && q.sourceCd === CAP_QUEUE_SOURCE_CD)
            })
        })
    }
}
