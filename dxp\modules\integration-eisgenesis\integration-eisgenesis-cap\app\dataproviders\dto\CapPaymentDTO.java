/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package dataproviders.dto;

import java.time.ZonedDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import core.utils.JsonUtils;
import dataproviders.common.dto.GenesisRootDTO;

public class CapPaymentDTO extends GenesisRootDTO {

    @JsonProperty("_variation")
    public String variation;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = JsonUtils.DATE_TIME_MILLIS_FORMAT)
    public ZonedDateTime creationDate;
    public String direction;
    public Map<String, Object> paymentAmount;
    public String paymentNumber;
    public String state;
    public Map<String, Object> paymentDetails;
}
