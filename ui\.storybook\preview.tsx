/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import React from 'react'
import type {Preview} from '@storybook/react'
import {Title, Stories} from '@storybook/blocks'

import {FormTranslateContext, FormTranslateFn, GenericLookupContext} from '@eisgroup/form-core'
import {LocalizationUtils} from '@eisgroup/i18n'

import './inversify.config'
import './styles.less'

const translate: FormTranslateFn = key => {
    return typeof key === 'string' ? LocalizationUtils.translate(key) : key
}

const preview: Preview = {
    tags: ['autodocs'],
    parameters: {
        layout: 'fullscreen',
        controls: {expanded: true},
        docs: {
            page: () => (
                <>
                    <Title />
                    <Stories />
                </>
            )
        },
        options: {
            storySort: (a, b) => {
                const typeOrder = ['docs', 'story']
                const ai = typeOrder.indexOf(a.type)
                const bi = typeOrder.indexOf(b.type)
                return ai !== bi ? ai - bi : a.title.localeCompare(b.title, undefined, {numeric: true})
            }
        }
    },
    globalTypes: {
        locale: {
            name: 'Locale',
            description: 'Internationalization locale',
            defaultValue: 'en',
            toolbar: {
                icon: 'globe',
                items: [
                    {value: 'en', right: '🇺🇸', title: 'English'},
                    {value: 'fr', right: '🇫🇷', title: 'Français'}
                ]
            }
        }
    },
    decorators: [
        Story => {
            return (
                <FormTranslateContext.Provider value={translate}>
                    <GenericLookupContext.Provider value={() => Promise.resolve([])}>
                        <div style={{maxWidth: '960px', margin: 'auto', marginTop: '5rem'}}>
                            <Story />
                        </div>
                    </GenericLookupContext.Provider>
                </FormTranslateContext.Provider>
            )
        }
    ]
}

export default preview
