import path from 'path'
import react from '@vitejs/plugin-react-swc'
import {defineConfig, coverageConfigDefaults} from 'vitest/config'

// Shared config for all packages
export default defineConfig({
    plugins: [react({tsDecorators: true})],
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: [path.resolve(__dirname, './vitest.setup.ts')],
        exclude: ['**/node_modules/**', '**/target/**', '**/dist/**'],
        env: {
            NODE_ENV: 'test',
            TZ: 'EET'
        },
        coverage: {
            reporter: ['html', 'lcov', 'text'],
            // To match the jest coverage directory
            reportsDirectory: './target/ts-coverage',
            include: ['**/src/**'],
            exclude: [
                '**/target/**',
                '**/models/**',
                '**/claim-ui-app/**',
                '**/i18n/**',
                '**/*.builder.{ts,tsx}',
                '**/*.stories.{ts,tsx}',
                ...coverageConfigDefaults.exclude
            ]
        }
    }
})
