/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.financial.dto;

import cap.adjuster.services.common.dto.GenesisApiModel;

/**
 * <AUTHOR>
 * @since
 */
public class CapPaymentMessage extends GenesisApiModel {

    public String severity;
    public Object allocationPeriod;
    public String code;
    public String source;
    public String message;
}
