{"openapi": "3.0.1", "info": {"description": "Auto-generated OpenAPI schema from the OpenL rules", "title": "claim-life-hi-adjudication", "version": "1.0.0"}, "servers": [{"url": "/claim-life-hi-adjudication", "variables": {}}], "security": [{"BasicAuth": [], "SSOToken": []}], "paths": {"/_api_hi_adjudication": {"post": {"description": "Rules method: org.openl.generated.beans.CapHISettlementResultEntity _api_hi_adjudication(org.openl.generated.beans.CapHISettlementRulesInput request)", "operationId": "_api_hi_adjudication", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapHISettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapHISettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapHISettlementResultEntity _api_hi_adjudication(CapHISettlementRulesInput)"}}, "/_api_hi_applicability": {"post": {"description": "Rules method: org.openl.generated.beans.CapHILossApplicabilityResult _api_hi_applicability(org.openl.generated.beans.CapHILossApplicabilityInput request)", "operationId": "_api_hi_applicability", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapHILossApplicabilityInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapHILossApplicabilityResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapHILossApplicabilityResult _api_hi_applicability(CapHILossApplicabilityInput)"}}, "/_api_hi_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapHISettlementResultEntity _api_hi_calculation(org.openl.generated.beans.CapHISettlementRulesInput request)", "operationId": "_api_hi_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapHISettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapHISettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapHISettlementResultEntity _api_hi_calculation(CapHISettlementRulesInput)"}}}, "components": {"schemas": {"AccumulatorRemainingsEntity": {"type": "object", "properties": {"accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorType": {"type": "string"}, "amountUnit": {"type": "string"}, "limitAmount": {"type": "number"}, "policyTerm": {"$ref": "#/components/schemas/Term"}, "remainingAmount": {"type": "number"}, "usedAmount": {"type": "number"}}}, "BaseLifeGrossBenefitAmount": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "totalApprovedAmount": {"$ref": "#/components/schemas/Money"}}}, "BaseLifePolicyCoverageLimitLevel": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "limitLevelType": {"type": "string"}, "timePeriodCd": {"type": "string"}}}, "BaseLifeSettlementAccumulatorDetails": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorAmount": {"type": "number"}, "accumulatorAmountUnit": {"type": "string"}, "accumulatorCustomerUri": {"type": "string"}, "accumulatorExtension": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorPolicyUri": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time"}, "accumulatorType": {"type": "string"}, "autoAdjudicatedAmount": {"type": "number"}}}, "BaseLifeSettlementAccumulatorDetailsExtension": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorUnitCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapAgeReductionDetailsEntity": {"type": "object", "properties": {"ageCd": {"type": "integer", "format": "int32"}, "reducedToCd": {"type": "number"}}}, "CapHICertInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapHISettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapHISettlementCoverageInfoEntity"}}}}, "CapHILossApplicabilityInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "eventDate": {"type": "string", "format": "date-time"}, "lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/CapLifeLossPolicyInfoEntity"}}}}, "CapHILossApplicabilityResult": {"type": "object", "properties": {"applicability": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}}}, "CapHIMasterInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapHISettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapHISettlementCoverageInfoEntity"}}}}, "CapHIRelatedSettlmentEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "admissionConfinementSeparationPeriod": {"$ref": "#/components/schemas/Period"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "hospitalConfinementLink": {"$ref": "#/components/schemas/EntityLink"}, "incidentDate": {"type": "string", "format": "date-time"}, "surgeryLink": {"$ref": "#/components/schemas/EntityLink"}}}, "CapHISettlementAttrOptionsEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "attrName": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}}}, "CapHISettlementBenefitInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "completionPeriod": {"type": "integer", "format": "int32"}, "confinementSeparationPeriod": {"type": "integer", "format": "int32"}, "distanceToHospitalMoreThanCd": {"type": "integer", "format": "int32"}, "incurralPeriod": {"type": "integer", "format": "int32"}, "maxBenefitNumber": {"type": "number"}, "maxStay": {"type": "integer", "format": "int32"}, "minStayMoreThan": {"type": "integer", "format": "int32"}, "occurWithinPeriod": {"type": "integer", "format": "int32"}, "surgeryPerformancePeriod": {"type": "integer", "format": "int32"}, "therapyCompletionPeriod": {"type": "integer", "format": "int32"}, "timePeriodCd": {"type": "string"}, "travelDistanceMoreThanCd": {"type": "integer", "format": "int32"}, "waitingPeriod": {"type": "integer", "format": "int32"}}}, "CapHISettlementCoverageConfigEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accidentInd": {"type": "boolean"}, "accumulationCategoryGroup": {"type": "string"}, "applicableLossTypes": {"type": "array", "items": {"type": "string"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/components/schemas/CapHISettlementAttrOptionsEntity"}}, "benefitLevelMapping": {"type": "array", "items": {"type": "string"}}, "calculationFormulaId": {"type": "string"}, "groupUnit": {"type": "string"}, "incurralPeriodUnit": {"type": "string"}, "limitLevels": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string"}}}, "CapHISettlementCoverageInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "benefitAmountChildPct": {"type": "number"}, "benefitAmountSpousePct": {"type": "number"}, "childOrganizedSportBenefitPct": {"type": "number"}, "coverageCd": {"type": "string"}, "isChildOrganizedSportApplied": {"type": "boolean"}}}, "CapHISettlementDeductionEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "deductionBeneficiary": {"type": "string"}, "deductionPct": {"type": "number", "format": "double"}, "deductionTerm": {"$ref": "#/components/schemas/Term"}, "deductionType": {"type": "string"}, "isPrePostTax": {"type": "boolean"}, "nonProviderPaymentType": {"type": "string"}, "stateProvided": {"type": "string"}}}, "CapHISettlementDetailEntity": {"type": "object", "properties": {"_archived": {"type": "boolean"}, "_key": {"$ref": "#/components/schemas/EntityKey"}, "_modelName": {"type": "string"}, "_modelType": {"type": "string"}, "_modelVersion": {"type": "string"}, "_timestamp": {"type": "string"}, "_type": {"type": "string"}, "_version": {"type": "string"}, "approvedAmountOverride": {"$ref": "#/components/schemas/Money"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "grossAmount": {"$ref": "#/components/schemas/Money"}, "hospitalConfinementLink": {"$ref": "#/components/schemas/EntityLink"}, "incidentDate": {"type": "string", "format": "date-time"}, "numberOfUnits": {"type": "integer", "format": "int32"}, "participantContributionPct": {"type": "number"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}, "surgeryLink": {"$ref": "#/components/schemas/EntityLink"}, "isCancelled": {"type": "boolean", "description": "Whether the settlement is cancelled"}, "cancelReason": {"type": "string", "description": "The settlement cancelled reason"}}}, "CapHISettlementLifeIntakeInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "deductions": {"type": "array", "items": {"$ref": "#/components/schemas/CapHISettlementDeductionEntity"}}, "taxes": {"type": "array", "items": {"$ref": "#/components/schemas/CapHISettlementTaxEntity"}}}}, "CapHISettlementLossInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "absence": {"$ref": "#/components/schemas/EntityLink"}, "accidentDate": {"type": "string", "format": "date-time"}, "age": {"type": "integer", "format": "int32"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "claimType": {"type": "string"}, "coverageType": {"type": "string"}, "firstTreatmentDate": {"type": "string", "format": "date-time"}, "isChildOrganizedSport": {"type": "boolean"}, "lastWorkDate": {"type": "string", "format": "date-time"}, "lossDateTime": {"type": "string", "format": "date-time"}, "lossNumber": {"type": "string"}, "lossType": {"type": "string"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}, "relationshipToInsuredCd": {"type": "string"}}}, "CapHISettlementOffsetEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "isPrePostTax": {"type": "boolean"}, "offsetTerm": {"$ref": "#/components/schemas/Term"}, "offsetType": {"type": "string"}, "proratingRate": {"type": "string"}}}, "CapHISettlementPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "hiCertInfo": {"$ref": "#/components/schemas/CapHICertInfoEntity"}, "hiMasterInfo": {"$ref": "#/components/schemas/CapHIMasterInfoEntity"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isAgeReductionApplied": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "offsets": {"$ref": "#/components/schemas/CapHISettlementOffsetEntity"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "terminationAge": {"type": "integer", "format": "int64"}, "txEffectiveDate": {"type": "string"}}}, "CapHISettlementResultEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/components/schemas/Money"}, "autoAdjudicatedDuration": {"type": "number"}, "benefitAmountPerUnit": {"$ref": "#/components/schemas/Money"}, "benefitCd": {"type": "string"}, "claimCoverageName": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityEvaluationCd": {"type": "string"}, "grossAmount": {"$ref": "#/components/schemas/Money"}, "grossBenefitAmount": {"$ref": "#/components/schemas/BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time"}, "isAutoAdjudicated": {"type": "boolean"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}, "numberOfUnits": {"type": "integer", "format": "int64"}, "paymentDetailInfo": {"$ref": "#/components/schemas/PaymentDetailInfoEntity"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}}, "CapHISettlementRulesInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/components/schemas/AccumulatorRemainingsEntity"}}, "claimCoverageName": {"type": "string"}, "coverageConfiguration": {"$ref": "#/components/schemas/CapHISettlementCoverageConfigEntity"}, "details": {"$ref": "#/components/schemas/CapHISettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "lifeIntake": {"$ref": "#/components/schemas/CapHISettlementLifeIntakeInfoEntity"}, "linkedHospitalConfinementPeriod": {"$ref": "#/components/schemas/Period"}, "linkedSettlementEligibleCd": {"type": "string"}, "loss": {"$ref": "#/components/schemas/CapHISettlementLossInfoEntity"}, "policy": {"$ref": "#/components/schemas/CapHISettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/components/schemas/CapHIRelatedSettlmentEntity"}}, "wrapperInfo": {"$ref": "#/components/schemas/CapSettlementWrapperInfoEntity"}}}, "CapHISettlementTaxEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "jurisdictionType": {"type": "string"}, "taxType": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}}}, "CapInsuredInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "isMain": {"type": "boolean"}, "registryTypeId": {"type": "string"}}}, "CapLifeLossPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapSettlementWrapperInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "claimSubjectId": {"type": "string"}, "claimWrapperIdentification": {"$ref": "#/components/schemas/EntityLink"}, "memberRegistryTypeId": {"type": "string"}}}, "EntityKey": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string"}}}, "EntityLink": {"type": "object", "properties": {"_uri": {"type": "string"}}}, "JAXRSErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["USER_ERROR", "RULES_RUNTIME", "COMPILATION", "SYSTEM", "BAD_REQUEST", "VALIDATION"]}, "message": {"type": "string"}}}, "JAXRSUserErrorResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "MessageType": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "source": {"type": "string"}}}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}}}, "PaymentDetailInfoEntity": {"type": "object", "properties": {"initiatePayment": {"type": "boolean"}}}, "Period": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}}, "Term": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"BasicAuth": {"description": "Genesis Basic credentials", "scheme": "basic", "type": "http"}, "SSOToken": {"description": "Genesis Authorization header. Value format: `[Token {token}]`", "in": "header", "name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}