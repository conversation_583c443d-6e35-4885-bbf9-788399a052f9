{"swagger": "2.0", "x-dxp-spec": {"imports": {"payment.template": {"schema": "integration.cap.payment.template.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Claim Payment Template API", "version": "1", "title": "CAP Adjuster: Claim Payment Template API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/payment-templates", "description": "CAP Adjuster: Claim Payment Template API"}, {"name": "/cap-adjuster/v1/financial", "description": "CAP Adjuster: Financial API"}], "paths": {"/payment-templates/rules/{entryPoint}": {"post": {"summary": "Retrieve set of rules for provided entry point", "x-dxp-path": "/api/cappaymenttemplate/CapPaymentTemplate/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/payment-templates"]}}, "/payment-templates/rules/bundle": {"post": {"summary": "Rules bundle for Payment Templates", "x-dxp-path": "/api/cappaymenttemplate/CapPaymentTemplate/v1/rules/bundle", "tags": ["/cap-adjuster/v1/payment-templates"]}}, "/payment-templates/{rootId}/{revisionNo}": {"get": {"summary": "Get payment template", "x-dxp-path": "/api/cappaymenttemplate/CapPaymentTemplate/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/payment-templates"]}}, "/financial/payment-template/build-flow": {"post": {"x-dxp-path": "/api/cappaymenttemplate/CapPaymentTemplate/v1/command/startBuildPaymentScheduleFlow", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-templates/load": {"post": {"x-dxp-path": "/api/cappaymenttemplate/CapPaymentTemplate/v1/entities/loadPaymentTemplates", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-template/load-by-links": {"post": {"summary": "Return all payment template records for given links", "x-dxp-path": "/api/cappaymenttemplate/CapPaymentTemplate/v1/link/", "tags": ["/cap-adjuster/v1/financial"]}}}}