/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
export const PREFIX = 'gen'

export const CLAIM_BANNER = `${PREFIX}-claim-banner`
export const CLAIM_BANNER_CELL = `${PREFIX}-claim-banner-cell`
export const CLAIM_BANNER_DETAILS = `${PREFIX}-claim-banner-details`
export const CLAIM_BANNER_DETAILS_COLUMN = `${PREFIX}-claim-banner-details-column`
export const CLAIM_BANNER_DETAILS_LABEL = `${PREFIX}-claim-banner-details-label`
export const CLAIM_BANNER_DETAILS_COLUMN_CONTENT_WRAPPER = `${PREFIX}-claim-banner-details-column-content-wrapper`
export const C<PERSON><PERSON>_BANNER_HEADER = `${PREFIX}-claim-banner-header`
export const CLAIM_BANNER_HEADER_CELL = `${PREFIX}-claim-banner-header-cell`
export const CLAIM_BANNER_HEADER_TEXT = `${PREFIX}-claim-banner-header-text`
export const CLAIM_BANNER_HEADER_LINK = `${PREFIX}-claim-banner-header-link`
export const CLAIM_BANNER_HEADER_STATUS_TEXT = `${PREFIX}-claim-banner-header-status-text`
export const CLAIM_BANNER_HEADER_DETAIL_INFO_SPECIALHANDLING = `${PREFIX}-claim-banner-header-detail-info-specialHandling`
export const CLAIM_BANNER_HEADER_DETAIL_INFO_SPECIALHANDLING_POPOVER = `${PREFIX}-claim-banner-header-detail-info-specialHandling_popover`
export const CLAIM_BANNER_HEADER_PTD_WARNING = `${PREFIX}-paidToDate-warning`
export const CLAIM_BANNER_HEADER_PTD_WRAPPER = `${PREFIX}-paidToDate-wrapper`
export const CLAIM_BANNER_CLAIM_DATES_POPOVER = `${PREFIX}-banner-claim-dates-popover`
export const CLAIM_HEADER_PAID_TO_DATE = `${PREFIX}-header-paid-to-date`
export const CLAIM_BANNER_CONTACT_INFO_INSURED_TITLE = `${PREFIX}-claim-banner-contact-info-insured-title`
export const CLAIM_BANNER_CONTACT_INFO_TITLE = `${PREFIX}-claim-banner-contact-info-title`
export const CLAIM_BANNER_POPOVER = `${PREFIX}-claim-banner-popover`
export const CLAIM_POLICY_SEARCH = `${PREFIX}-claim-policy-search`
export const CLAIM_POLICY_SEARCH_RESULT_TABLE = `${PREFIX}-claim-policy-search-result-table`
export const CLAIM_POLICY_SEARCH_INPUT = `${PREFIX}-claim-policy-search-input`
export const CLAIM_POLICY_SEARCH_INPUT_CONTAINER = `${PREFIX}-claim-policy-search-input-container`
export const CLAIM_POLICY_SEARCH_NO_DATA_MSG = `${PREFIX}-claim-policy-search-no-data-msg`
export const CLAIM_PARTIES_INFORMATION_TITLE = `${PREFIX}-claim-parties-information-title`
export const CLAIM_PARTIES_TABLE = `${PREFIX}-claim-parties-table`
export const CLAIM_PARTIES_INFORMATION_EDIT_ICON = `${PREFIX}-claim-parties-information-edit-icon`
export const CLAIM_PARTY_SEARCH_RESULT_TABLE = `${PREFIX}-claim-party-search-result-table`
export const CLAIM_PARTY_SEARCH_ERROR_MSG = `${PREFIX}-claim-policy-search-error-msg`
export const CLAIM_FORM_DIVIDER = `${PREFIX}-claim-form-divider`
export const CLAIM_APPROVAL_PERIODS = `${PREFIX}-claim-approval-periods`

// Claim Banner
export const CLAIM_HEADER_MEMBER_AVATAR_MOCK = `${PREFIX}-secondary-banner-subject-avatar-mock`
export const CLAIM_POPOVER_TEXT_LINE = `${PREFIX}-secondary-banner-popover-text-line`
export const CLAIM_POPOVER_LABEL = `${PREFIX}-secondary-banner-popover-label`
export const POPOVER_CLAIM_DATES_LABEL = `${PREFIX}-secondary-banner-popover-claim-dates-label`
export const POPOVER_CLAIM_DATES_TEXT = `${PREFIX}-secondary-banner-popover-claim-dates-text`
export const CLAIM_DATES_POPOVER_LABEL = `${PREFIX}-claim-dates-popover-label`
export const CLAIM_POPOVER_EMPLOYMENT_INFO_ROW = `${PREFIX}-secondary-banner-popover-employment-info-row`
export const CLAIM_DATES_POPOVER = `${PREFIX}-secondary-banner-claim-dates-popover`
export const CLAIM_HEADER_MEMBER_DETAIL = `${PREFIX}-claim-header-member-detail`
export const CLAIM_HEADER_MEMBER_DETAIL_INFO = `${PREFIX}-claim-header-member-detail-info`
export const CLAIM_HEADER_FLEX = `${PREFIX}-claim-header-flex`
export const CLAIM_BANNER_MEMBER_POPOVER = `${PREFIX}-claim-header-member-popover`
export const CLAIM_BANNER_DEPENDANT = `${PREFIX}-claim-header-dependant`
export const CLAIM_BANNER_MEMBER_NUMBER = `${PREFIX}-claim-header-banner-member-number`
export const CLAIM_BANNER_MEMBER_OCCUPATION_EDIT = `${PREFIX}-claim-banner-member-occupation-edit`
export const CLAIM_BANNER_PREFERRED_EDIT = `${PREFIX}-claim-banner-preferred-edit`
export const CLAIM_HEADER_HYPERLINK = `${PREFIX}-claim-header-hyperlink`
export const CLAIM_MANAGER_INFO_POPOVER = `${PREFIX}-claim-manager-info-popover`
export const CLAIM_BANNER_HYPERLINK = `${PREFIX}-banner-details-hyperlink`
export const MANUAL_CLOSE_WRAPPER = `${PREFIX}-manual-close-wrapper`
export const MANUAL_CLOSE_ERROR_RED = `${PREFIX}-manual-close-error-red`
export const MANUAL_CLOSE_HARD_STOP_ALERT = `${PREFIX}-manual-close-hard-stop-alert`

// Work days inline editor
export const WORK_DAYS_INLINE_ED_ACTIONS = `${PREFIX}-work-days-inline-editor-actions`
export const WORK_DAYS_INLINE_ED = `${PREFIX}-work-days-inline-editor`
export const WORK_DAYS_INLINE_ED_BORDER = `${PREFIX}-work-days-inline-editor-bordered`

// Steps component
export const CAP_WIZARD_STEPS_CONTAINER = `${PREFIX}-cap-wizard-steps-container`
export const STEP_SECTION_CONTROLS_SUBSECTION = `${PREFIX}-wizard-step-section-controls-subsection`
export const CAP_WIZARD_STEPS_CONTAINER_FULL_PAGE = `${PREFIX}-cap-wizard-steps-container-full-page`

// WizardButtonBar component
export const WIZARD_BUTTON_BAR_BUTTON_CONTAINER = `${PREFIX}-wizard-btn-bar-btn-container`
export const WIZARD_BUTTON_BAR_ACTION_BUTTONS = `${PREFIX}-wizard-btn-bar-action-btns`
export const WIZARD_BUTTON_BAR_NAVIGATION_BUTTONS = `${PREFIX}-wizard-btn-bar-navigation-btns`
export const WIZARD_BUTTON_BAR_LABEL = `${PREFIX}-wizard-btn-bar-label`
export const WIZARD_BUTTON_BAR_CANCEL_BUTTON = `${PREFIX}-wizard-btn-bar-cancel-btn`

// Employee component
export const AUTO_SEARCH_SUGGESTION_CONTAINER = `${PREFIX}-auto-search-suggestion-container`
export const AUTO_SEARCH_SUGGESTION_PRIMARY_CONTENT = `${PREFIX}-auto-search-suggestion-primary-content`
export const AUTO_SEARCH_SUGGESTION_SECONDARY_CONTENT = `${PREFIX}-auto-search-suggestion-secondary-content`
export const AUTO_SEARCH_SUGGESTION_SELECTED = `${PREFIX}-auto-search-suggestion-selected`
export const AUTO_SEARCH_SUGGESTION_LABEL = `${PREFIX}-auto-search-suggestion-label`
export const AUTO_SEARCH_SUGGESTION_TYPE = `${PREFIX}-auto-search-suggestion-type`
export const AUTO_SEARCH_SUGGESTION_HINT_CONTAINER = `${PREFIX}-auto-search-suggestion-hint-container`
export const AUTO_SEARCH_SUGGESTION_HINT_ICON = `${PREFIX}-auto-search-suggestion-hint-icon`
export const AUTO_SEARCH_SUGGESTION_HINT_LABEL = `${PREFIX}-auto-search-suggestion-hint-label`
export const AUTO_SEARCH_SUGGESTION_EMPTY = `${PREFIX}-auto-search-suggestion-empty`

// Form Drawer
export const DRAWER_FORM_CONTROLS_SUBSECTION = `${PREFIX}-drawer-form-controls-subsection`
export const DRAWER_FORM_HEADER_CONTAINER_FLEX = `${PREFIX}-drawer-form-header-container-flex`
export const DRAWER_FORM_HEADER_RIGHT_SIDE = `${PREFIX}-drawer-form-header-right-side`
export const DRAWER_FORM_HEADER_CLOSE_BTN = `${PREFIX}-drawer-form-header-close-btn`
export const DRAWER_FORM_HEADER_LABEL_FONT = `${PREFIX}-drawer-form-header-label-font`

// Form Drawer Actions
export const FORM_DRAWER_ACTIONS_SUBSECTION = `${PREFIX}-form-drawer-actions-subsection`

// score detail
export const SCORE_DETAIL_HEADER = `${PREFIX}-score-detail-header`
export const SCORE_DETAIL_COLLAPSE = `${PREFIX}-score-detail-collapse`
export const SCORE_DETAIL_CUSTOMER = `${PREFIX}-score-detail-customer`
export const SCORE_DETAIL_TABLE_CONTAINER = `${PREFIX}-score-detail-table-container`
export const SCORE_DETAIL_TABLE_HEADER = `${PREFIX}-score-detail-table-header`
export const SCORE_DETAIL_TABLE_HEADER_RIGHT = `${PREFIX}-score-detail-table-header-right`
export const SCORE_DETAIL_COLLAPSE_RIGHT = `${PREFIX}-score-detail-collapse-right`
export const SCORE_DETAIL_SUBJECT_OF_CLAIM = `${PREFIX}-score-details-subject-of-claim`

// Input form row grid styles
export const INPUT_FORM_ROW_1 = `${PREFIX}-input-form-row-grid-1`
export const INPUT_FORM_ROW_2X1 = `${PREFIX}-input-form-row-grid-2x1`
export const INPUT_FORM_ROW_3X1 = `${PREFIX}-input-form-row-grid-3x1`
export const INPUT_FORM_ROW_4X1 = `${PREFIX}-input-form-row-grid-4x1`
export const INPUT_FORM_ROW_8X1 = `${PREFIX}-input-form-row-grid-8x1`
export const INPUT_FORM_ROW_9X1 = `${PREFIX}-input-form-row-grid-9x1`
export const INPUT_FORM_ROW_2_1_1 = `${PREFIX}-input-form-row-grid-2-1-1`
export const INPUT_FORM_ROW_3_2_3 = `${PREFIX}-input-form-row-grid-3-2-3`
export const INPUT_FORM_ROW_2_3_3 = `${PREFIX}-input-form-row-grid-2-3-3`
export const INPUT_FORM_ROW_2_2_1 = `${PREFIX}-input-form-row-grid-2-2-1`
export const INPUT_FORM_ROW_1_2_2 = `${PREFIX}-input-form-row-grid-1-2-2`
export const INPUT_FORM_ROW_1_4 = `${PREFIX}-input-form-row-grid-1-4`
export const INPUT_FORM_ROW_3_1 = `${PREFIX}-input-form-row-grid-3-1`
export const INPUT_FORM_ROW_1_5_2 = `${PREFIX}-input-form-row-grid-1-5-2`
export const INPUT_FORM_ROW_2_1 = `${PREFIX}-input-form-row-grid-2-1`
export const INPUT_FORM_ROW_MARGIN_RIGHT_1 = `${PREFIX}-input-from-row-margin-right-1`

// ListActions component
export const LIST_ACTION_BTN = `${PREFIX}-list-action-btn`
export const LIST_ACTIONS = `${PREFIX}-list-actions`
export const FORM_ACTIONS = `${PREFIX}-form-actions`

export const BORDERLESS_BUTTON = `${PREFIX}-borderless-button`
export const ABSENCE_REASONS_SELECT = `${PREFIX}-absence-reasons-select`
export const ABSENCE_REASONS_TABLE = `${PREFIX}-absence-reasons-table`
export const ABSENCE_REASONS_TABLE_EXPANDED_ROW = `${PREFIX}-absence-reasons-table-expanded-row`
export const ABSENCE_REASONS_PARTY_BIRTH_DATE = `${PREFIX}-absence-party-birth-date`
export const ABSENCE_REASON_TABLE_ACTIONS = `${PREFIX}-absence-reasons-table-actions`

export const WORK_DAYS_ERROR = `${PREFIX}-work-days-error`
export const WORK_DAYS_SECTION = `${PREFIX}-work-days-section`
export const WORK_DAYS_WRAPPER = `${PREFIX}-work-days-wrapper`

// Validation
export const VALIDATION_MESSAGE = `${PREFIX}-validation-message`
export const CUSTOM_VALIDATION_DISPLAY = `${PREFIX}-custom-validation-display`

// AddableTableWrapper component
export const ADDABLE_TABLE_HEADER = `${PREFIX}-addable-table-header`
export const ADDABLE_TABLE_BORDER = `${PREFIX}-addable-table-border`
export const ADDABLE_TABLE_BUTTON = `${PREFIX}-addable-table-button`

// Summary of taxes table
export const SUMMARY_OF_TAXES_TABLE_COLLAPSE = `${PREFIX}-summary-of-taxes-table-collapse`

// withholding table
export const WITHHOLDING_TABLE_COLLAPSE = `${PREFIX}-withholding-table-collapse`

// Payments Information
export const PAYMENTS_NO_TOTAL_TABLE = `${PREFIX}-payments-no-total-payments-table`
export const PAYMENTS_NO_TOTAL_TABLE_HEADER = `${PREFIX}-payments-no-total-table-header`
export const PAYMENTS_TABLE_PAYMENT_DETAILS_ROW = `${PREFIX}-payments-table-payment-details-row`
export const PAYMENTS_TABLE_PAYMENT_DETAILS_CONTAINER = `${PREFIX}-payments-table-payment-details-container`
export const PAYMENTS_TABLE_PAYMENT_DETAILS_LOSS_TYPE = `${PREFIX}-payments-table-payment-details-loss-type`
export const PAYMENTS_TABLE_PAYMENT_ALLOCATIONS_GROUP = `${PREFIX}-payments-table-payment-allocations-group`

// PaymentsAndReductionsTable
export const PAYMENTS_TABLE = `${PREFIX}-payments-table`
export const PAYMENTS_TABLE_HEADER = `${PREFIX}-payments-table-header`
export const PAYMENTS_TABLE_HEADER_CARD = `${PREFIX}-payments-table-header-card`
export const PAYMENTS_TABLE_TOTAL_COLUMN = `${PREFIX}-payments-table-total-column`
export const PAYMENTS_TABLE_RECOVERY_TOTAL_COLOR = `${PREFIX}-payments-table-recovery-total-color`
export const PAYMENTS_TABLE_WRAPPER = `${PREFIX}-payments-table-wrapper`
export const PAYMENT_TABLE_COLLAPSE = `${PREFIX}-payments-table-collapse`

// Preferred Payment Method Drawer
export const PAYMENT_METHOD_DRAWER = `${PREFIX}-payment-method-drawer`
export const PAYMENT_METHOD_SELECT = `${PREFIX}-payment-method-select`
export const PAYMENT_METHOD_BUTTON = `${PREFIX}-payment-method-button`
export const PAYMENT_METHOD_SELECT_OPTGROUP_LABEL = `${PREFIX}-payment-method-select-optgroup-label`
export const PAYMENT_METHOD_SELECT_OPTGROUP_ITEM = `${PREFIX}-payment-method-select-optgroup-item`
export const PAYMENT_METHOD_PRIVILEGE_ICON = `${PREFIX}-payment-method-privilege-icon`
export const PAYMENTS_TABLE_PAYMENT_CANCEL_REASON_ICON = `${PREFIX}-case-system-payments-table-payment-cancel-reason-icon`
export const PAYMENTS_TABLE_CONFIRM_CONTENT = `${PREFIX}-case-system-payments-table-confirm-content`

export const PERSON_BASE_DETAILS = `${PREFIX}-person-base-details-section`
export const PARTY_ROLES_SELECT = `${PREFIX}-party-roles-select`
export const SELECT_DROPDOWN_MENU_ITEM = `${PREFIX}-select-dropdown-menu-item`
export const SELECT_INPUT_WRAPPER_OPTIONS = `${PREFIX}-selected-input-wrapper-options`
export const LOOKUP_COMBO_BOX_OPTION_CHECKBOX = `${PREFIX}-lookup-combo-box-option-checbox`
export const PARTY_FORM_SEARCH_INPUT_FIELD = `${PREFIX}-party-form-seach-input-field`
// ExpandableTable
export const EXPANDABLE_TABLE_ACTIONS_COLUMN = `${PREFIX}-expandable-table-actions-column`
// CareForFamilyMemberForm
export const CARE_FOR_FAMILY_MEMBER_FORM_TITLE = `${PREFIX}-care-for-family-member-form-title`
export const CARE_FOR_FAMILY_MEMBER_TABLE = `${PREFIX}-care-for-family-member-table`
// FinancialInformation
export const FINANCIAL_INFORMATION_WRAPPER = `${PREFIX}-financial-information-wrapper`
export const FINANCIAL_INFORMATION_TAB_BAR = `${PREFIX}-financial-information-tab-bar`
export const FINANCIAL_INFORMATION_TAB_TABLE = `${PREFIX}-financial-information-tab-table`
export const FINANCIAL_INFORMATION_BALANCE_TABLE = `${PREFIX}-financial-information-balance-table`
export const FINANCIAL_INFORMATION_BALANCE_SUB_TABLE = `${PREFIX}-financial-information-balance-sub-table`
export const FINANCIAL_INFORMATION_AMOUNT_VALUE = `${PREFIX}-financial-information-amount-value`
export const FINANCIAL_INFORMATION_AMOUNT_FREQUENCY = `${PREFIX}-financial-information-amount-frequency`
export const FINANCIAL_INFORMATION_FICA_EXEMPT = `${PREFIX}-financial-information-fica-exempt`
export const FINANCIAL_INFORMATION_FICA_EXEMPT_ITEM = `${PREFIX}-financial-information-fica-exempt-item`
export const FINANCIAL_INFORMATION_TAB_TABLE_PAYMENTS_UNIVERSAL = `${PREFIX}-financial-information-tab-table-payments-universal`
export const FINANCIAL_INFORMATION_FICA_EXEMPT_ITEM_ICON = `${PREFIX}-financial-information-fica-exempt-item-icon`
// Change history table
export const CHANGE_HISTORY_TABLE = `${PREFIX}-change-history-table`
export const CHANGE_HISTORY_TABLE_WRAPPER = `${PREFIX}-change-history-table-wrapper`
export const CHANGE_HISTORY_TABLE_HEADER_FILTER = `${PREFIX}-change-history-table-header-filter`
export const CHANGE_HISTORY_TABLE_HEADER_FILTER_SELECT = `${PREFIX}-change-history-table-header-filter-select`
export const CHANGE_HISTORY_TABLE_HEADER_FILTER_RANGEPICKER = `${PREFIX}-change-history-table-header-filter-rangepicker`
export const CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_NAME = `${PREFIX}-change-history-table-top-filter-component-name`
export const CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_VALUE_TAG = `${PREFIX}-change-history-table-top-filter-component-value-tag`
export const CHANGE_HISTORY_TABLE_TOP_FILTER_COMPONENT_CONTAINER = `${PREFIX}-change-history-table-top-filter-component-container`

// Claim status markers (circle)
export const CLAIM_STATUS = `${PREFIX}-claim-status`
export const CLAIM_SUBSTATUS = `${PREFIX}-claim-substatus`
export const STATUS_MARKER_INCOMPLETE = `${PREFIX}-claim-status-marker-incomplete`
export const STATUS_MARKER_PENDING = `${PREFIX}-claim-status-marker-pending`
export const STATUS_MARKER_OPEN = `${PREFIX}-claim-status-marker-open`
export const STATUS_MARKER_CLOSED = `${PREFIX}-claim-status-marker-closed`
// EditableTable
export const EDITABLE_TABLE_ACTIONS_COLUMN = `${PREFIX}-editable-table-actions-column`
export const EDITABLE_TABLE = `${PREFIX}-editable-table`
export const EDITABLE_TABLE_NAME_LABEL = `${PREFIX}-editable-table-name-label`

// Own Medical Condition
export const OMC_EDITABLE_TABLE = `${PREFIX}-omc-editable-table`
export const OMC_DIAGNOSIS_SEARCH_INPUT = `${PREFIX}-omc-diagnosis-search-input`
export const OMC_ROW_LAYOUT = `${PREFIX}-omc-row-layout`
export const OMC_EDITABLE_TABLE_ACTIVE_ROW_ERROR = `${PREFIX}-omc-editable-table-active-row-error`
export const OMC_EDITABLE_TABLE_ACTION_BUTTONS = `${PREFIX}-omc-editable-table-action-buttons`
export const OMC_DIAGNOSIS_SEARCH_OPTIONS_BODY = `${PREFIX}-omc-diagnosis-search-options-body`

// Military
export const MILITARY_INFORMATION_FORM = `${PREFIX}-military-information-form`

// Form Drawer Actions
export const FORM_DRAWER_ACTIONS = `${PREFIX}-form-drawer-actions`

// Earnings
export const EARNINGS_SECTION = `${PREFIX}-earnings-section`
export const EARNINGS_HEADER = `${PREFIX}-earnings-header`
export const EARNINGS_SECTION_ROW = `${PREFIX}-earnings-section-row`
export const EARNINGS_CARD_PRIMARY = `${PREFIX}-earnings-card-primary`
export const EARNINGS_CARD_VIEW = `${PREFIX}-earnings-card-view`
export const EARNINGS_CARD_EDIT = `${PREFIX}-earnings-card-edit`
export const EARNING_CARD_MONEY_INPUT = `${PREFIX}-earnings-card-money-input`
export const EARNINGS_CARD = `${PREFIX}-earnings-card`
export const EARNINGS_CARD_AMOUNT = `${PREFIX}-earnings-card_amount`
export const EDIT_MONEY_INPUT = `${PREFIX}-edit-money-input`
export const EARNINGS_CARD_DESCRIPTION = `${PREFIX}-earnings-card_description`
export const BASE_SALARY_CARD = `${PREFIX}-base-salary-card`
export const BASE_SALARY_CARD_EDIT = `${PREFIX}-base-salary-card-edit`
export const BASE_SALARY_EDIT_DESCRIPTION = `${PREFIX}-base-salary-edit_description`

// Prior Earnings
export const PRIOR_EARNINGS_TABLE = `${PREFIX}-prior-earnings-table`
export const PRIOR_EARNINGS_TABLE_WEEK_END_DATE = `${PREFIX}-prior-earnings-table-week-end-date`

// FaceValue
export const FACE_VALUE_CARD = `${PREFIX}-face-value-card`
export const FACE_VALUE_CARD_TITLE_WRAPPER = `${PREFIX}-face-value-card-title-wrapper`
export const FACE_VALUE_CARD_TITLE_CONTENT = `${PREFIX}-face-value-card-title-content`
export const FACE_VALUE_CARD_TITLE = `${PREFIX}-face-value-card-title`
export const FACE_VALUE_CARD_AMOUNT = `${PREFIX}-face-value-card-amount`
export const FACE_VALUE_CARD_WARNING = `${PREFIX}-face-value-card-warning`
export const FACE_VALUE_CARD_ORIGINAL_VALUE = `${PREFIX}-face-value-card-original-value`
export const FACE_VALUE_DESCS = `${PREFIX}-face-value-descs`
export const FACE_VALUE_DESC = `${PREFIX}-face-value-desc`

// Diagnosis
export const CLAIM_DIAGNOSIS_COLLAPSE = `${PREFIX}-claim-disgnosis-collapse`

// Coverages Info
export const COVERAGES_INFO = `${PREFIX}-coverages-info`
export const COVERAGES_INFO_HEADER = `${PREFIX}-coverages-info-header`
export const COVERAGES_INFO_HEADER_INFO = `${PREFIX}-coverages-info-header-info`
export const COVERAGES_INFO_HEADER_BOLD = `${PREFIX}-coverages-info-header-bold`
export const COVERAGES_INFO_HEADER_ELIMINATION_PERIOD_INFO = `${PREFIX}-coverages-info-header-elimination-period-info`
export const COVERAGES_INFO_HEADER_INFO_EDIT = `${PREFIX}-coverages-info-header-info-edit`
export const COVERAGES_INFO_HEADER_THIRD_COLUMN = `${PREFIX}-coverages-info-header-third-column`
export const COVERAGES_TABLE = `${PREFIX}-coverages-table`
export const LEAVE_COVERAGE_INFO_HEADER_CONTENT = `${PREFIX}-leave-coverages-info-header-content`
export const COVERAGES_ELIMINATION_PERIOD_THROUGH_DATE = `${PREFIX}-coverages-elimination-period-through-date`
export const COVERAGES_ELIMINATION_PERIOD_WARNING = `${PREFIX}-coverages-elimination-period-warning`
export const COVERAGES_INFO_HEADER_OTHER_DAYS = `${PREFIX}-coverages-info-other-days`

// Payment Wizard
export const PAYMENT_WIZARD_PROGRESS_BAR = `${PREFIX}-payment-wizard-progress-bar`
export const ABSENCE_PAYMENT_DRAWER = `${PREFIX}-absence-payment-drawer`
export const ADD_ALLOCATION_FORM = `${PREFIX}-add-allocation-form`
export const ADD_ALLOCATION_TABLE = `${PREFIX}-add-allocation-table`
export const ADD_ALLOCATION_FORM_HEADER = `${PREFIX}-add-allocation-form-header`
export const PAYMENT_WIZARD_MONEY_CELL = `${PREFIX}-payment-money-cell`

// Ytd Earnings
export const YTD_EARNINGS_TABLE_YEAR = `${PREFIX}-ytd-earnings-table-year-picker`
export const YTD_EARNINGS_TABLE = `${PREFIX}-ytd-earnings-table`

export const CUSTOMER_FILTER_SEARCH_WRAPPER = `${PREFIX}-customer-filter-search-wrapper`

export const PARTY_CUSTOMER_SEARCH_COMPONENT = `${PREFIX}-party-search-bar-search-component`
export const PARTY_CUSTOMER_SEARCH = `${PREFIX}-party-search-bar-search`

export const PARTY_TABLE_SEARCH_CUSTOMERS = `${PREFIX}-party-component-search-form-table`
export const PARTY_CUSTOMER_SEARCH_RESULTS_TABLE = `${PREFIX}-search-customer-results-table`
export const PARTY_CUSTOMER_SEARCH_RESULTS_TABLE_NAME = `${PREFIX}-search-customer-results-table-name`
export const PARTY_CUSTOMER_SEARCH_RESULTS_TABLE_ROW = `${PREFIX}-search-customer-results-table-row`
export const PARTY_CUSTOMER_SEARCH_RESULTS_TABLE_COL_CUSTOMER = `${PREFIX}-search-customer-results-table-col-customer`
export const PARTY_CUSTOMER_SEARCH_RESULTS_TABLE_COL_ADDRESS = `${PREFIX}-search-customer-results-table-col-address`
export const PARTY_CUSTOMER_SEARCH_RESULTS_TABLE_COL_PHONE = `${PREFIX}-search-customer-results-table-col-phone`
export const PARTY_INFORMATION_FORM = `${PREFIX}-party-information-form`

export const PARTY_INFORMATION_FORM_INFO_ITEM = `${PREFIX}-info-item`
export const PARTY_INFORMATION_FORM_INFO_LABEL = `${PREFIX}-info-label`
export const PARTY_INFORMATION_FORM_INFO_CONTENT = `${PREFIX}-info-content`

// Assign Manager
export const ASSIGN_BANNER_CONTACT_INFO_TITLE = `${PREFIX}-assign-manger-contact-info-title`
export const ASSIGN_BANNER_MEMBER_POPOVER = `${PREFIX}-assign-header-member-popover`
export const ASSIGN_CUSTOMER_CONTACT_INFO_WRAPPER = `${PREFIX}-assign-customer-contact-info-wrapper`
export const ASSIGN_CUSTOMER_CONTACT_INFO_ADDRESS = `${PREFIX}-assign-customer-contact-info-address`
export const ASSIGN_CUSTOMER_CONTACT_INFO_PHONE_NUMBERS = `${PREFIX}-assign-customer-contact-info-phone-numbers`
export const ASSIGN_CUSTOMER_CONTACT_INFO_EMAIL = `${PREFIX}-assign-customer-contact-info-email`
export const ASSIGN_CUSTOMER_CONTACT_INFO_PHONE_EMAIL_CHAT = `${PREFIX}-assign-customer-contact-info-phone-email-chat`
export const ASSIGN_CUSTOMER_CONTACT_METHOD_ICON = `${PREFIX}-assign-customer-contact-method-icon`

export const ASSIGN_USER_SEARCH_WRAPPER = `${PREFIX}-assign-user-search-wrapper`
export const ASSIGN_USER_SEARCH_INPUT_WRAPPER = `${PREFIX}-assign-user-search-input-wrapper`
export const ASSIGN_USER_SEARCH_RESULT_TABLE = `${PREFIX}-assign-user-search-result_table`
export const ASSIGN_USER_SEARCH_RESULT_TABLE_TITLE = `${PREFIX}-assign-user-search-result_table_title`
export const ASSIGN_USER_SEARCH_RESULT_TABLE_COL_NAME = `${PREFIX}-assign-user-search-table__col_name`
export const ASSIGN_USER_SEARCH_RESULT_TABLE_COL_DEPARTMENT = `${PREFIX}-assign-user-search-table__col_department`
export const ASSIGN_USER_SEARCH_RESULT_TABLE_COL_ROLE = `${PREFIX}-assign-user-search-table__col_role`
export const ASSIGN_USER_INFO_POPOVER = `${PREFIX}-assign-user-info-popover`
export const ASSIGN_MANAGER_INFO_POPOVER = `${PREFIX}-assign-manager-info-popover`

export const TABLE_FIRST_COLUMN_OFFSET = `${PREFIX}-first-column-offset`

// CustomerControls
export const CUSTOM_DROPDOWN = `${PREFIX}-claim-custom-dropdown`
export const CUSTOM_MENU = `${PREFIX}-claim-custom-menu`
export const CUSTOM_SUBMENU = `${PREFIX}-custom-submenu`
export const CUSTOM_MENU_ITEM = `${PREFIX}-custom-menu-item`
export const CUSTOM_DROPDOWN_ACTION_ARROW = `${PREFIX}-custom-dropdown-action-arrow`
export const CUSTOM_DROPDOWN_TOOLTIP = `${PREFIX}-custom-dropdown-tooltip`
export const CUSTOM_DROPDOWN_BUTTON = `${PREFIX}-claim-custom-dropdown-button`

export const RELATION_COMPONENT_CREATE_SEARCH_FORM = `${PREFIX}-relation-component-create-search-form`
// ActivityList
export const ACTIVITY_WORKBENCH_LIST = `${PREFIX}-activity-list`
export const LOSSDETAIL_WORKBENCH = `${PREFIX}-lossdetail-workbench`
export const LOSSDETAIL_WORKBENCH_DRAWER_LIST_HEADER = `${PREFIX}-lossdetail-workbench-drawer-list-header`
export const LOSSDETAIL_WORKBENCH_TABS = `${PREFIX}-lossdetail-workbench-tabs`

export const DEFAULT_COLLAPSE = `${PREFIX}-default-collapse`
export const CLAIM_BREADCRUMBS_WRAPPER = `${PREFIX}-claim-breadcrumbs-wrapper`
export const HEADER_POPUP_DRAWER = `${PREFIX}-header-popup-drawer`
export const REASON_CODE_ALIGN_UI_BUILDER_CONTENT = `${PREFIX}-reason-code-align-ui-builder-content`
export const HEADER_SUBSTATUS_DRAWER_CONTENT = `${PREFIX}-header-substatus-drawer-content`

/// / ELIGIBLE
export const CLAIM_ELIGIBILITY_STATUS_MARKER = `${PREFIX}-claim-eligibility-status-marker`
export const CLAIM_STATUS_MARKER_CONTAINER = `${PREFIX}-claim-status-marker-container`
export const CLAIM_STATUS_MARKER_ELIGIBLE = `${PREFIX}-claim-status-marker-eligible`
export const CLAIM_STATUS_MARKER_NOT_ELIGIBLE = `${PREFIX}-claim-status-marker-not_eligible`
export const CLAIM_STATUS_MARKER_OVERRIDABLE = `${PREFIX}-claim-status-marker-overridable`
export const CLAIM_STATUS_MARKER_CONTAINER_ERROR = `${PREFIX}-claim-status-marker-container-error`
export const CLAIM_ELIGIBILITY_CELL = `${PREFIX}-claim-eligibility-cell`
export const CLAIM_EDITABLE_CELL = `${PREFIX}-claim-editable-cell`
export const CLAIM_ELIGIBILITY_DISABILITY_MAIN_CONTAINER = `${PREFIX}-claim-eligibility-disability-main-container`
export const CLAIM_ELIGIBILITY_ICON_VALIDATION_ERROR = `${PREFIX}-claim-eligibility-icon-validation-error`
export const CLAIM_ELIGIBILITY_DISABILITY_CONTAINER = `${PREFIX}-claim-eligibility-disability-container`
export const CLAIM_COVERAGE_LIST_SELECT_NON_ELIGIBLE = `${PREFIX}-claim-coverages-list-select-non-eligible`
export const CLAIM_COVERAGE_EDITABLE_VALUE = `${PREFIX}-claim-coverages-editable-value`
export const CLAIM_COVERAGE_INFO_TABLE_WRAPPER = `${PREFIX}-claim-coverages-info-table-wrapper`
export const CLAIM_COVERAGE_HEADER_THROUGH_DATE = `${PREFIX}-claim-coverages-header-through-date`
export const CLAIM_COVERAGE_OVERRIDE_MARK = `${PREFIX}-claim-coverages-override-mark`
export const CLAIM_COVERAGE_CLAIM_COVERAGES_HEADER_THROUGH_DATE_OVERRIDE = `${PREFIX}-claim-coverages-header-through-date-override`

export const CLAIM_PLAN = `${PREFIX}-claim-plan`
export const CLAIM_PLAN_CONTENT = `${PREFIX}-claim-plan-content`
export const CLAIM_PLAN_DESC = `${PREFIX}-claim-plan-desc`
export const CLAIM_PLAN_ACTION_BUTTON = `${PREFIX}-claim-plan-action-button`
export const CLAIM_PLAN_RADIO_GROUP = `${PREFIX}-claim-plan-radio-group`
export const CLAIM_PLAN_CARD = `${PREFIX}-claim-plan-card`
export const CLAIM_PLAN_NAME = `${PREFIX}-claim-plan-name`

// merged from cap-case-system
// claim party
export const ADD_PARTY_INFO_DRAWER_ACTIONS = `${PREFIX}-add-party-info-Drawer-actions`
export const CLAIM_PARTY_ADD_PARTY = `${PREFIX}-claim-party-add-party`
export const CLAIM_PARTY_RELATIONSHIPS_CUSTOMER_ELLIPSIS = `${PREFIX}-claim-party-relationships-customer-ellipsis`
export const CLAIM_PARTY_RELATIONSHIPS_CUSTOMER_ELLIPSIS_A = `${PREFIX}-claim-party-relationships-customer-ellipsis-a`
export const CLAIM_PARTY_ADD_PARTY_BTN = `${PREFIX}-claim-party-add-party-btn`
export const CLAIM_PARTY_PARTY_TABLE = `${PREFIX}-claim-party-party-table`
export const CLAIM_PARTY_LIST_ACTIONS_STYLE = `${PREFIX}-claim-party-list-actions-style`
export const CLAIM_PARTY_ADD_PARTY_CUSTOMER_TYPE = `${PREFIX}-claim-party-add-party-customer-type`
export const CLAIM_PARTY_CUSTOMER_TYPE_RADIO_INPUT = `${PREFIX}-claim-party-customer-type-radio-input`
export const CLAIM_PARTY_RELATIONSHIP_CUSTOMER_TYPE_WRAPPER = `${PREFIX}-claim-party-relationship-customer-type-wrapper`
export const CLAIM_PARTY_PARTY_EXTRA_COMPONENT = `${PREFIX}-claim-party-extra-component`
export const CLAIM_PARTY_PARTY_DRAWER_ADD_NEW_PARTY_BTN = `${PREFIX}-party-drawer-add-new-party-btn`
export const CLAIM_PARTY_REMOVE_PARTY_MODAL = `${PREFIX}-claim-party-remove-party-modal`

// party

export const PARTY_CUSTOMER_SEARCH_DROPDOWN = `${PREFIX}-party-search-bar-search-dropdown`
export const PARTY_CUSTOMER_SEARCH_FILTER_POPOVER = `${PREFIX}-filter-search-popover`
export const PARTY_CUSTOMER_SEARCH_FILTER_POPOVER_VENDOR = `${PREFIX}-filter-search-popover_vendor`
export const PARTY_CUSTOMER_SEARCH_FILTER_ICON = `${PREFIX}-filter-search-icon`
export const PARTY_CUSTOMER_SEARCH_FILTER_ICON_SELECTED = `${PREFIX}-filter-search-icon_selected`

// leaves-by-plan
export const LEAVES_BY_PLAN = `${PREFIX}-leaves-by-plan`
export const LEAVES_BY_PLAN_MENU = `${PREFIX}-leaves-by-plan-menu`
export const LEAVES_BY_PLAN_ABSENCES_TIMELINE = `${PREFIX}-leaves-by-plan-absences-timeline`
export const LEAVES_BY_PLAN_EVENTS = `${PREFIX}-leaves-by-plan-events-row`
export const LEAVES_BY_PLAN_TIMELINE_ACCUMULATORS = `${PREFIX}-leaves-by-plan-timeline-accumulators`
export const LEAVES_BY_PLAN_TIMELINE_BAR = `${PREFIX}-leaves-by-plan-timeline-bar`
export const LEAVES_BY_PLAN_PERIOD_CELL = `${PREFIX}-leaves-by-plan-period-cell`
export const LEAVES_BY_PLAN_TIMELINE_BAR_ELEMENT = `${PREFIX}-leaves-by-plan-timeline-bar-element`
export const LEAVES_BY_PLAN_TIMELINE_HEADER = `${PREFIX}-leaves-by-plan-timeline-header`
export const LEAVES_BY_PLAN_DATE_LABEL = `${PREFIX}-leaves-by-plan-date-label`
export const LEAVES_BY_PLAN_TABLE_CONTROLS = `${PREFIX}-leaves-by-plan-table-controls`
export const LEAVES_BY_PLAN_ACCUMULATOR_MORE_POPOVER = `${PREFIX}-leaves-by-plan-accumulator-more-popover`
export const LEAVES_BY_PLAN_ACCUMULATOR_MORE_POPOVER_TAG = `${PREFIX}-leaves-by-plan-accumulator-more-popover-tag`
export const LEAVES_BY_PLAN_ACCUMULATOR_TAG_VALUE = `${PREFIX}-leaves-by-plan-accumulator-more-popover-tag-value`
export const LEAVES_BY_PLAN_ACCUMULATOR_TAG_NAME = `${PREFIX}-leaves-by-plan-accumulator-more-popover-tag-name`
export const LEAVES_BY_PLAN_TABLE_HEADER = `${PREFIX}-leaves-by-plan-table-header`
export const LEAVES_BY_PLAN_SECTION_TITLE = `${PREFIX}-leaves-by-plan-section-title`
export const LEAVES_BY_PLAN_ADD_BUTTON = `${PREFIX}-leaves-by-plan-add-button`
export const LEAVES_BY_PLAN_EVENT_POINTER = `${PREFIX}-leaves-by-plan-event-pointer`
export const LEAVES_BY_PLAN_LEGEND = `${PREFIX}-leaves-by-plan-legend`
export const LEAVES_BY_PLAN_LEGEND_LABEL = `${PREFIX}-leaves-by-plan-legend-label`
export const LEAVES_BY_PLAN_ABSENCE_TYPE = `${PREFIX}-leaves-by-plan-absence`
export const LEAVES_BY_PLAN_ABSENCE_TYPE_LABEL = `${PREFIX}-leaves-by-plan-absence-type-label`
export const LEAVES_BY_PLAN_NAVIGATION = `${PREFIX}-leaves-by-plan-navigation`
export const CLAIMS_TABLE_COLUMN = `${PREFIX}-claims-table-column`
export const LEAVES_BY_PLAN_EVENT_BAR_ELEMENT = `${PREFIX}-leaves-by-plan-events-period-cell`
export const LEAVES_BY_PLAN_END_PERIOD_EVENT_POINTER = `${PREFIX}-leaves-by-plan-end-period-event-pointer`
export const LEAVES_BY_PLAN_EVENT = `${PREFIX}-leaves-by-plan-event`
export const LEAVES_BY_PLAN_HOVER_EVENT = `${PREFIX}-leaves-by-plan-hover-event`
export const LEAVES_BY_PLAN_POPOVER_TITLE = `${PREFIX}-leaves-by-plan-popover-title`
export const LEAVES_BY_PLAN_POPOVER_TITLE_LABEL = `${PREFIX}-leaves-by-plan-popover-title-label`
export const LEAVES_BY_PLAN_ADD_OPTIONS = `${PREFIX}-leaves-by-plan-add-options`
export const RELATION_COMPONENT_CREATE_SEARCH_FORM_VENDORS = `${PREFIX}-relation-component-create-search-form-vendors`

export const PARTY_DRAWER_ADD_MANUALLY_BTN = `${PREFIX}-party-drawer-add-manually-btn`

// Payments Information
export const PAYMENTS_TABLE_PAYMENT_AMOUNT_NEGATIVE = `${PREFIX}-case-system-payments-table-payment-amount-negative`
export const PAYMENTS_TABLE_PAYMENT_STATE = `${PREFIX}-case-system-payments-table-payment-state`
export const CASE_SYSTEM_PAYMENTS_TABLE_PAYMENT_DETAILS_ROW = `${PREFIX}-case-system-payments-table-payment-details-row`
export const CASE_SYSTEM_PAYMENTS_TABLE_PAYMENT_DETAILS_LOSS_TYPE = `${PREFIX}-case-system-payments-table-payment-details-loss-type`
export const PAYMENTS_ACTION_POPCONFIRM_CONTENT = `${PREFIX}-case-system-payments-action-popconfirm-content`
export const PAYMENTS_SELECT_ACTION_BUTTON = `${PREFIX}-case-system-payments-select-action-button`
export const PAYMENT_HEADER_ACTION = `${PREFIX}-case-system-payment-header-action`
export const PAYMENTS_ACTION_POPCONFIRM_DISPATCH_CONTENT = `${PREFIX}-case-system-payments-action-popconfirm-dispatch-content`
export const PAYMENT_EOB_REMARKS_POPOVER = `${PREFIX}-payment-eob-remarks-popover`
export const PAYMENT_EOB_REMARKS_POPOVER_CONTENT = `${PREFIX}-payment-eob-remarks-popover-content`

// payment wizard
export const PAYMENT_DRAWER = `${PREFIX}-case-system-payment-drawer`
export const CASE_SYSTEM_PAYMENT_WIZARD_PROGRESS_BAR = `${PREFIX}-case-system-payment-wizard-progress-bar`
export const PAYMENT_DETAIL_ADD_ALLOCATION_BUTTON = `${PREFIX}-case-system-payment-detail-add-allocation-button`
export const PAYMENT_DETAIL_ADD_ERROR_CONTAINER = `${PREFIX}-case-system-payment-detail-add-error-container`
export const PAYMENT_ALLOCATIONS_SECTION = `${PREFIX}-case-system-payment-allocations-section`
export const PAYMENT_ALLOCATIONS_SECTION_TITLE = `${PREFIX}-case-system-payment-allocations-section-title`
export const PAYMENT_ALLOCATIONS_LIST = `${PREFIX}-case-system-payment-allocations-list`
export const PAYMENT_ALLOCATIONS_LIST_EXPAND_FORM = `${PREFIX}-case-system-payment-allocations-expand-form`
export const PAYMENT_ALLOCATIONS_COVERATES_DETAILS_WRAPPER = `${PREFIX}-case-system-payment-allocations-coverage-details-wrapper`
export const PAYMENT_ALLOCATIONS_EXPENDED_ROW_CONTENT = `${PREFIX}-case-system-payment-allocations-expended-row-content`
export const PAYMENT_ALLOCATIONS_RECURRING_EMPTY_ROW = `${PREFIX}-case-system-payment-allocations-recurring-empty-row`
export const PAYMENT_ALLOCATIONS_PAYMENT_PREVIEW_TABLE = `${PREFIX}-case-system-payment-allocations-payment-preview-table`
export const PAYMENT_ALLOCATIONS_DISABILITY_PAYMENT_PREVIEW_TABLE = `${PREFIX}-case-system-payment-allocations-disability-payment-preview-table`

// payment allocations payment details
export const PAYMENT_DETAILS_ROW = `${PREFIX}-payment_details_row`
export const PAYMENT_DETAILS_MONEY_CELL = `${PREFIX}-payment_details_money_cell`

// payment allocations disability
export const PAYMENT_ALLOCATIONS_DISABILITY_DETAIL_TEXT_RIGHT = `${PREFIX}-payment-allocations-disability-detail-text-right`
export const PAYMENT_ALLOCATIONS_DISABILITY_DETAIL_EXPENSE_DESCRIPTION = `${PREFIX}-payment-allocations-disability-detail-expense-description`
export const PAYMENT_ALLOCATIONS_DISABILITY_DETAIL_NEGATIVE_VALUE = `${PREFIX}-payment-allocations-disability-detail-negative-value`
export const PAYMENT_ALLOCATIONS_DISABILITY_DETAIL_DATE_RANGE = `${PREFIX}-payment-allocations-disability-detail-date-range`
export const PAYMENT_ALLOCATIONS_PREVIEW_INTEREST_ROW = `${PREFIX}-payment-allocations-preview-interest-row`
export const PAYMENT_ALLOCATIONS_PREVIEW_INTEREST_LABEL = `${PREFIX}-payment-allocations-preview-interest-label`
export const PAYMENT_ALLOCATIONS_PREVIEW_INTEREST_FIELD = `${PREFIX}-payment-allocations-preview-interest-field`
export const PAYMENT_ALLOCATIONS_STEP_CONTAINER = `${PREFIX}-payment-allocations-step-container`
export const PAYMENT_ALLOCATIONS_STEP_SUBTITLE = `${PREFIX}-payment-allocations-step-subtitle`
export const PAYMENT_EOB_REMARKS_ALLOCATION_STEP = `${PREFIX}-payment-eob-remarks-allocation-step`
export const PAYMENT_EOB_REMARKS_SUBTITLE = `${PREFIX}-payment-eob-remarks-subtitle`
export const PAYMENT_ALLOCATIONS_STEP_SUBTITLE_DIVIDER = `${PREFIX}-payment-allocations-step-subtitle-divider`
export const PAYMENT_ALLOCATIONS_STEP_NET_AMOUNT = `${PREFIX}-payment-allocations-step-net-amount`
export const PAYMENT_ALLOCATIONS_STEP_NET_AMOUNT_DIVIDER = `${PREFIX}-payment-allocations-step-net-amount-divider`
export const PAYMENT_ALLOCATIONS_STEP_POSTDATE = `${PREFIX}-payment-allocations-step-postdate`
export const PAYMENT_ALLOCATIONS_RECALCULATE = `${PREFIX}-payment-allocations-recalculate`
export const PAYMENT_ALLOCATIONS_STEP_TOTAL_HEADER = `${PREFIX}-payment-allocations-set-total-header`

// payment allocation table in overview
export const ALLOCATION_TABLE = `${PREFIX}-claim-payment-allocations-table`
export const PAYMENT_ALLOCATIONS_ALERT = `${PREFIX}-payment-allocations-alert`

// special handling
export const SPECIAL_HANDLING_SWITCH = `${PREFIX}-special-handling-switch`
export const SPECIAL_HANDLING_DRAWER_CONTENT = `${PREFIX}-special-handling-drawer-content`

// payment action message
export const PAYMENT_ACTION_SUCCESSFUL_MESSAGE = `${PREFIX}-payment-action-successful-message`
export const ACTIVATE_PAYMENT_POPUP_ERROR_ICON = `${PREFIX}-activate-payment-popup-error-icon`

// absence period rtw
export const RTW_ABSENCE_TABLE = `${PREFIX}-rtw-absence-table`
export const RTW_ABSENCE_DATE_PICKER = `${PREFIX}-rtw-absence-date-picker`
export const RTW_MULTIPLE_DATE_PICKER = `${PREFIX}-multiple-date-picker`
export const RTW_DATE_PICKER_AND_RADIO_ROW = `${PREFIX}-row`
export const RTW_RADIO_GROUP = `${PREFIX}-radio-group`
export const RTW_DATE_PICKER_FIELD = `${PREFIX}-date-picker`
export const RTW_POPOVER = `${PREFIX}-popover`
export const RTW_POPOVER_CONTENT = `${PREFIX}-popover-content`
export const RTW_LABEL = `${PREFIX}-radio-group-label`

// payment method check address
export const ADDRESS_SECTION_TITLE = `${PREFIX}-address-section-title`
export const ADD_NEW_ADDRESS = `${PREFIX}-add-new-address`
export const ADDRESS_EDIT_BTN = `${PREFIX}-address-edit-btn`
export const ADDRESS_EDIT_BTN_CONTENT = `${PREFIX}-address-edit-btn-content`
export const ADDRESS_DRAWER_CONTENT = `${PREFIX}-address-drawer-content`

// manage balance
export const BLANCE_ACTIVITIES_COLLAPSE = `${PREFIX}-balance-activities-collapse`
export const BLANCE_ACTIVITIES_TABLE = `${PREFIX}-balance-activities-table`
export const BALANCE_HEADER_LEFT_SECTION = `${PREFIX}-balance-header-left-section`
export const BALANCE_HEADER_RIGHT_SECTION = `${PREFIX}-balance-header-right-section`
export const BALANCE_HEADER_WITH_NO_ACTION_DROPDOWN = `${PREFIX}-balance-header-with-no-action-dropdown`
export const BALANCE_HEADER_RIGHT_SECTION_TOOLTIP = `${PREFIX}-balance-header-right-section-tooltip`
export const BALANCE_HEADER_AMOUNT_BOX = `${PREFIX}-balance-header-amount-box`
export const BALANCE_TABLE_TITLE = `${PREFIX}-balance-table-title`
export const BALANCE_TABLE_COLUMN_TAG = `${PREFIX}-balance-table-column-tag`
export const BALANCE_TABLE_EXPAND_SECTION = `${PREFIX}-balance-table-expand-section`
export const BALANCE_TABLE_RECALCULATION_PAYMENTS_WRAPPER = `${PREFIX}-balance-table-recalcation-payments-wrapper`
export const BALANCE_TABLE_RECALCULATION_PAYMENTS = `${PREFIX}-balance-table-recalcation-payments`
export const BALANCE_TABLE_RECALCULATION_PAYMENTS_TOP = `${PREFIX}-balance-table-recalcation-payments-top`
export const RECALCULATION_PAYMENTS_SECTION = `${PREFIX}-recalcation-payments-section`
export const RECALCULATION_PAYMENTS_AMOUNT_BOX = `${PREFIX}-recalcation-payments-amount-box`
export const PAYMENT_APPLIED_WITHHOLDINGS = `${PREFIX}-payment-applied-withholdings`
export const BALANCE_NEGATIVE_VALUE = `${PREFIX}-balance-negative-value`
export const BALANCE_DESCRIPTION_ELLIPSIS = `${PREFIX}-balance-description-ellipsis`
export const BALANCE_EXPAND_LOOKUP_LABEL = `${PREFIX}-balance_expand_section_lookup_label`
// reduce payment
export const CLAIM_REDUCE_PAYMENT_WITHHOLDING_AMOUNT = `${PREFIX}-claim-reduce-payment-withholding-amount`

// radio cards group
export const RADIO_CARDS_GROUP = `${PREFIX}-radio-cards-group`
export const RADIO_CARDS_GROUP_TITLE = `${PREFIX}-radio-cards-group__title`
export const RADIO_CARDS_GROUP_CONTENT = `${PREFIX}-radio-cards-group__content`
export const RADIO_CARDS_GROUP_ITEMS = `${PREFIX}-radio-cards-group_items`

export const DIALOG_RADIO_CARD = `${PREFIX}-dialog-radio-card`
export const DIALOG_RADIO_CARD_CONTENT = `${PREFIX}-dialog-radio-card__content`
export const DIALOG_RADIO_CARD_CHECKBOX = `${PREFIX}-dialog-radio-card__checkbox`
export const DIALOG_RADIO_CARD_NAME = `${PREFIX}-dialog-radio-card__name`
export const RADIO_CARD_DISABLED = 'disabled'
// Party Information
export const EVENT_CASE_OVER_VIEW_PARTY_INFORMATION_TITLE = `${PREFIX}-event-case-over-view-party-information-title`

// policy refresh
export const POLICY_REFRESH_MODAL = `${PREFIX}-policy-refresh-modal`
export const POLICY_REFRESH_DETAIL_VIEW_HYPERLINK = `${PREFIX}-policy-refresh-detail-view-hyperlink`

// deductions
export const DEDUCTIONS_PRIORITY_INPUT_WIDTH = `${PREFIX}-deduction-priority-input`
export const DEDUCTIONS_AMOUNT_INPUT = `${PREFIX}-deduction-amount-input`
export const DEDUCTIONS_PARTY_SELECT = `${PREFIX}-deduction-party-select`
export const DEDUCTIONS_FORM_INPUT_SECTION = `${PREFIX}-deduction-form-input-section`
export const DEDUCTIONS_TABLE_WRAPPER = `${PREFIX}-deductions-table-wrapper`
export const DEDUCTIONS_TABLE = `${PREFIX}-deductions-table`
export const DEDUCTIONS_PAID_FROM_WITH_VALIDATION = `${PREFIX}-deductions-paid-from_with_validation`

// coverages
export const COVERAGE_VALIDATION_ERROR_ICON = `${PREFIX}-coverage-validation-error-icon`
export const COVERAGE_NUMBER_OF_UNITS = `${PREFIX}-coverage-number-of-units`
export const COVERAGE_OVER_LIMIT = `${PREFIX}-coverage-over-limit`
export const COVERAGES_LIST_INPUT_OVER_LIMIT = `${PREFIX}-coverage-list-input-over-limit`
export const STATUS_MARKER_CONTAINER = `${PREFIX}-claim-status-marker-container`
// export const STATUS_MARKER_OVERRIDE_CONTAINER = `${PREFIX}-claim-status-marker-override-container`
export const STATUS_MARKER_ELIGIBLE = `${PREFIX}-claim-status-marker-eligible`
export const STATUS_MARKER_NOT_ELIGIBLE = `${PREFIX}-claim-status-marker-not_eligible`
export const STATUS_MARKER_IS_OVERRIDE = `${PREFIX}-claim-status-marker-isOverride`
export const COVERAGE_LIST_TABLE_GROSS_AMOUNT_INPUT = `${PREFIX}-coverage-list-table-gross-amount-input`
export const COVERAGES_LIST_ICON_VALIDATION_ERROR = `${PREFIX}-coverage-list-icon-validation-error`
export const COVERAGES_LIST_MARGIN_TOP = `${PREFIX}-coverage-list-margin-top`

export const COVERAGES_LIST_REMAINLIMIT_POPOVER = `${PREFIX}-coverage-list-remainLimit-popover`
export const COVERAGES_LIST_REMAINLIMIT_POPOVER_AMOUNT = `${PREFIX}-coverage-list-remainLimit-popover-amount`
export const COVERAGES_LIST_REMAINLIMIT_POPOVER_ROW = `${PREFIX}-coverage-list-remainLimit-popover-row`
export const COVERAGES_LIST_REMAINLIMIT_POPOVER_TITLE = `${PREFIX}-coverage-list-remainLimit-popover-title`
export const COVERAGES_LIST_REMAINLIMIT_POPOVER_TITLE_SUBTITLE = `${PREFIX}-coverage-list-remainLimit-popover-title-subtitle`
export const COVERAGES_LIST_ELIGIBILITY_SELECT_WRAPPER = `${PREFIX}-coverage-list-select-wrapper`
export const COVERAGES_LIST_SELECT_ELIGIBLE = `${PREFIX}-claim-coverages-list-select-eligible`
export const ELIGIBILITY_CONTAINER = `${PREFIX}-claim-eligiblity-container`
export const ELIGIBILITY_ICON_VALIDATION_ERROR = `${PREFIX}-claim-eligiblity-icon-validation-error`
export const ELIGIBILITY_SELECT_ICON_VALIDATION_ERROR = `${PREFIX}-claim-eligiblity-select-icon-validation-error`
export const COVERAGE_LABEL = `${PREFIX}-coverage-label`
export const COVERAGE_TABLE_ROW_INLINE = `${PREFIX}-coverage-table-row-inline`
export const COVERAGE_TABLE_COLA_DETAILS_POPOVER = `${PREFIX}-cola-details-popover`

// coverage forluma
export const COVERAGE_FORMULA_CONTENT_WRAPPER = `${PREFIX}-coverage-forluma-content-wrapper`
export const COVERAGE_FORMULA_CONTENT = `${PREFIX}-coverage-forluma-content`
export const COVERAGE_FORMULA_CONTENT_ROW_1 = `${PREFIX}-coverage-forluma-content-row-1`
export const COVERAGE_FORMULA_CONTENT_ROW_2 = `${PREFIX}-coverage-forluma-content-row-2`
export const ACROSS_YEAR_CALCULATION_CONTENT = `${PREFIX}-across-year-calculation-content`
export const COVERAGE_GROSS_AMOUNT_POPOVER = `${PREFIX}-coverage-grossamount-popover`
export const GROSSAMOUNT_POPOVER_ICON = `${PREFIX}-grossamount-popover-icon`

// premiumWaiverSettlementPeriodDetail
export const SETTLEMENT_DETAIL = `${PREFIX}-settlement-detail`
export const SETTLEMENT_DETAIL_BOX = `${PREFIX}-settlement-detail-box`

// elimination period through date override
export const COVERAGES_THROUGH_DATE_CONTENT = `${PREFIX}-coverages-through-date-content`
export const COVERAGES_THROUGH_DATE_DATEPICKER_INPUT = `${PREFIX}-coverages-through-date-datepicker-input`
export const COVERAGES_THROUGH_DATE_BUTTON_SECTION = `${PREFIX}-coverages-through-date-button-section`

// case search component
export const CASE_SEARCH_COMPONENT = `${PREFIX}-case-search-component`
export const CASE_SEARCH_AUTOCOMPLETE_WITH_FILTER = `${PREFIX}-case-search-component-auto-complete-with-filter`
export const CASE_SEARCH_AUTOCOMPLETE = `${PREFIX}-case-search-component-auto-complete`
export const CASE_SEARCH_COMPONENT_DROPDOWN = `${PREFIX}-case-search-component-dropdown`
export const CASE_SEARCH_FILTER_POPOVER_WRAPPER = `${PREFIX}-case-search-component-filter-search-popover-wrapper`
export const CASE_SEARCH_FILTER_POPOVER = `${PREFIX}-case-search-component-filter-search-popover`
export const CASE_SEARCH_FILTER_ICON = `${PREFIX}-case-search-component-filter-search-icon`
export const CASE_SEARCH_FILTER_ICON_SELECTED = `${PREFIX}-case-search-component-filter-search-icon_selected`
export const CASE_SEARCH_RESULT = `${PREFIX}-case-search-component-result`
export const CASE_SEARCH_RESULT_FILTER_TITLE = `${PREFIX}-case-search-component-result-filter-title`
export const CASE_SEARCH_RESULT_FILTER_TAGS = `${PREFIX}-case-search-component-result-filter-tags`
export const CASE_SEARCH_RESULT_FILTER_TAGS_ITEM = `${PREFIX}-case-search-component-result-filter-tags-item`
export const CASE_SEARCH_RESULT_FILTER_TAGS_ITEM_VALUE = `${PREFIX}-case-search-component-result-filter-tags-item-value`
export const CASE_SEARCH_RESULT_TABLE = `${PREFIX}-case-search-component-result-table`

// case relationship
export const CASE_RELATIONSHIP_TABLE_WRAPPER = `${PREFIX}-case-relationship-table-wrapper`

export const PAYMENT_TABLE_CLAIMS_LABEL_SECTION = `${PREFIX}-payment-table-claims-label-section`
export const PAYMENT_TABLE_CLAIMS_CONTENT = `${PREFIX}-payment-table-claims-content`
export const PAYMENT_TABLE_CLAIMS_TAG = `${PREFIX}-payment-table-claims-tag`

export const PRE_EXISTING_CONDITIONS_INFO_TABLE_WRAPPER = `${PREFIX}-claim-pre-existing-conditions-info-table-wrapper`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE = `${PREFIX}-claim-pre-existing-conditions-info-table`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_UPPER = `${PREFIX}-claim-pre-existing-conditions-info-table-upper`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_LOWER = `${PREFIX}-claim-pre-existing-conditions-info-table-lower`
export const PRE_EXISTING_CONDITIONS_INFO_HEADER = `${PREFIX}-claim-pre-existing-conditions-info-table-header`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_HEADER_TITLE = `${PREFIX}-claim-pre-existing-conditions-info-table-header-title`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_ROW = `${PREFIX}-claim-pre-existing-conditions-info-table-row`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_FIRST_DATA = `${PREFIX}-claim-pre-existing-conditions-info-table-first-data`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_SECONDARY = `${PREFIX}-claim-pre-existing-conditions-info-table-secondary`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_NOTES = `${PREFIX}-claim-pre-existing-conditions-info-table-notes`
export const PRE_EXISTING_CONDITIONS_HIDE = `${PREFIX}-claim-pre-existing-conditions-hide`

export const PRE_EXISTING_CONDITIONS_INFO_TABLE_LOWER_POPOVER_CONTAINER = `${PREFIX}-claim-pre-existing-conditions-info-table-lower-popover-container`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_LOWER_POPOVER_TEXT = `${PREFIX}-claim-pre-existing-conditions-info-table-lower-popover-text`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_LOWER_POPOVER_TEXT_NO_OVERFLOW = `${PREFIX}-claim-pre-existing-conditions-info-table-lower-popover-text-no-overflow`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_LOWER_POPOVER_SEE_ALL = `${PREFIX}-claim-pre-existing-conditions-info-table-lower-popover-see-all`
export const PRE_EXISTING_CONDITIONS_INFO_TABLE_LOWER_POPOVER_CONTENT = `${PREFIX}-claim-pre-existing-conditions-info-table-lower-popover-content`

export const RETURN_TO_WORK_TAB = `${PREFIX}-return-to-work-tab`
