/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.leaveloss.impl;

import cap.adjuster.services.financial.converters.CapAdjusterPaymentConverter;
import cap.adjuster.services.financial.dto.CapPayment;
import cap.adjuster.services.leaveloss.CapAdjusterLeaveLossService;
import cap.adjuster.services.leaveloss.converters.CapAdjusterLeaveSettlementConverter;
import cap.adjuster.services.leaveloss.dto.CapLeaveSettlement;
import core.utils.AsyncUtils;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.dto.CapLeaveSettlementDTO;
import dataproviders.dto.CapPaymentDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static java.lang.String.format;

public class CapAdjusterLeaveLossServiceImpl implements CapAdjusterLeaveLossService {

    private static final String SETTLEMENT_BASED_ON_LOSS = "based_on";
    private static final String PAYMENT_USES_RESERVES_SETTLEMENT = "uses/reserves";
    private static final String CAP_SETTLEMENT = "CapSettlement";
    private static final String CAP_PAYMENT = "CapPayment";
    private static final String CAP_PAYMENT_ENTITY = "CapPaymentEntity";
    private static final String LEAVE_LOSS_URI = "gentity://CapLoss/CapLeave//%s/%s";
    private static final String SETTLEMENT_URI = "gentity://CapSettlement/CapLeaveSettlement//%s/%s";

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private CapAdjusterPaymentConverter<CapPaymentDTO, CapPayment> paymentConverter;
    private CapAdjusterLeaveSettlementConverter<CapLeaveSettlementDTO, CapLeaveSettlement> settlementConverter;

    @Override
    public CompletionStage<List<CapPayment>> getPayments(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo)
                .thenCompose(capLeaveSettlementDTOs -> AsyncUtils.sequence(
                        capLeaveSettlementDTOs.stream()
                                .map(this::getPaymentsAssociatedWithSettlement)
                                .collect(Collectors.toList())
                ))
                .thenApply(capLeaveLossDTOs -> capLeaveLossDTOs.stream()
                        .flatMap(List::stream)
                        .filter(this::isPaymentTypeAppropriate)
                        .map(paymentConverter::convertToApiDTO)
                        .collect(Collectors.toList())
                );
    }

    @Override
    public CompletionStage<List<CapLeaveSettlement>> getSettlements(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo)
                .thenApply(capLeaveSettlementDTOs -> capLeaveSettlementDTOs.stream()
                        .map(settlementConverter::convertToApiDTO)
                        .collect(Collectors.toList()));
    }

    private boolean isPaymentTypeAppropriate(CapPaymentDTO paymentDTO) {
        return StringUtils.equals(paymentDTO.gentityType, CAP_PAYMENT_ENTITY);
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapLeaveSettlementDTO>> getSettlementsAssociatedWithLoss(String rootId, Integer revisionNo) {
        String lossUri = format(LEAVE_LOSS_URI, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(lossUri, SETTLEMENT_BASED_ON_LOSS, CAP_SETTLEMENT);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapLeaveSettlementDTO.class);
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapPaymentDTO>> getPaymentsAssociatedWithSettlement(CapLeaveSettlementDTO leaveSettlementDTO) {
        String settlementUri = format(SETTLEMENT_URI, leaveSettlementDTO.key.rootId, leaveSettlementDTO.key.revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(CAP_PAYMENT, PAYMENT_USES_RESERVES_SETTLEMENT, settlementUri);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapPaymentDTO.class);
    }

    @Inject
    public void setSearchRelationshipsDataProvider(
            GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setPaymentConverter(CapAdjusterPaymentConverter paymentConverter) {
        this.paymentConverter = paymentConverter;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setSettlementConverter(CapAdjusterLeaveSettlementConverter settlementConverter) {
        this.settlementConverter = settlementConverter;
    }
}
