/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.claim.converters;

import java.util.stream.Collectors;

import com.eisgroup.dxp.dataproviders.genesiscapgenericsearch.dto.HeaderProjectionModel_HeaderProjectionDTO;

import cap.adjuster.services.claim.dto.CapAdjusterPolicyProjectionHeader;
import core.services.converters.CommonDTOConverter;

public class CapAdjusterPolicyProjectionHeaderConverter<I extends HeaderProjectionModel_HeaderProjectionDTO, A extends CapAdjusterPolicyProjectionHeader>
        extends CommonDTOConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.isVerified = intDTO.isVerified;
        apiDTO.masterPolicyId = intDTO.masterPolicyId;
        apiDTO.policyNumber = intDTO.policyNumber;
        apiDTO.policyStatus = intDTO.policyStatus;
        apiDTO.riskStateCd = intDTO.riskStateCd;
        apiDTO.capPolicyId = intDTO.capPolicyId;
        apiDTO.capPolicyVersionId = intDTO.capPolicyVersionId;
        apiDTO.policyType = intDTO.policyType;
        apiDTO.masterPolicyNumber = intDTO.masterPolicyNumber;
        apiDTO.productCd = intDTO.productCd;
        apiDTO.insuredRegistryTypeIds = intDTO.insureds.stream()
            .map(insured -> insured.registryTypeId)
            .collect(Collectors.toList());
        return apiDTO;
    }
}
