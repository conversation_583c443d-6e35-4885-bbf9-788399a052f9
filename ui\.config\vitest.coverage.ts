import {coverageConfigDefaults, defineConfig} from 'vitest/config'

// Partial config for coverage report generation
export default defineConfig({
    test: {
        coverage: {
            reporter: ['html', 'lcov'],
            // To match the jest coverage directory
            reportsDirectory: './target/ts-coverage',
            include: ['**/src/**'],
            exclude: [
                '**/target/**',
                '**/models/**',
                '**/claim-ui-app/**',
                '**/i18n/**',
                '**/*.builder.{ts,tsx}',
                '**/*.stories.{ts,tsx}',
                ...coverageConfigDefaults.exclude
            ]
        }
    }
})
