/* Copyright © 2019 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dxp.core.services;

import core.services.version.VersionService;
import core.services.version.dtos.AppVersion;

public class DxpCoreVersionService implements VersionService {

    @Override
    public AppVersion getCurrentApplicationVersion() {
        // Common build info
        AppVersion appVersion = new AppVersion(
                configs.dxp.BuildInfo.version(),
                configs.dxp.BuildInfo.buildNumber(),
                configs.dxp.BuildInfo.buildTime(),
                configs.dxp.BuildInfo.revision()
        );

        // DXP Core version
        appVersion.gatewayVersion = configs.dxp.BuildInfo.dxpCoreVersion();

        return appVersion;
    }
}
