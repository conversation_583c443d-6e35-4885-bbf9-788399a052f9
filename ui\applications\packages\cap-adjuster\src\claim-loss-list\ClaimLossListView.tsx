/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import * as React from 'react'
import {ClaimLossSearch} from '@eisgroup/cap-core'
import {toJS} from 'mobx'
import {createViewLoader, LOAD_INITIAL_DATA, connectToStore} from './store/ClaimLossListStore'

interface ClaimLossListViewProps {
    /**
     * Callback for 'Search' action button.
     */
    readonly onSearch: (searchCriteria: string) => void
    /**
     * Claim loss data.
     */
    readonly data: any[]
}

export class ClaimLossListViewComponent extends React.Component<ClaimLossListViewProps> {
    public render(): React.ReactNode {
        return <ClaimLossSearch onSearch={this.props.onSearch} data={this.props.data} />
    }
}

const ClaimLossListView = connectToStore<ClaimLossListViewProps>({
    component: ClaimLossListViewComponent,
    mapStoreToProps: (store, props) => ({
        data: toJS(store.claimLossList),
        onSearch: lossNumber => store.searchClaimLoss(lossNumber)
    })
})

export const ClaimLossListViewLoader = createViewLoader({
    isLoaded: store => store.actionsStore.isCompleted(LOAD_INITIAL_DATA),
    startLoading: (store, routingData) => {
        store.initLossListStore()
        store.routingStore.setRoutingData(routingData)
    },
    component: ClaimLossListView
})
