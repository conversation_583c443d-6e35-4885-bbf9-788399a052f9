/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package dataproviders.dto;

import com.eisgroup.dxp.dataproviders.genesiscapltdsettlement.dto.CapLtdSettlement_CapLTDSettlementPolicyInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapltdsettlement.dto.CapLtdSettlement_CapLtdSettlementAbsenceInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapltdsettlement.dto.CapLtdSettlement_CapLtdSettlementDetailEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapltdsettlement.dto.CapLtdSettlement_CapLtdSettlementLossInfoEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscapltdsettlement.dto.CapLtdSettlement_CapLtdSettlementResultEntityDTO;
import dataproviders.common.dto.GenesisRootDTO;

public class CapLtdSettlementDTO extends GenesisRootDTO {

    public String settlementType;
    public CapLtdSettlement_CapLtdSettlementResultEntityDTO settlementResult;
    public String settlementNumber;
    public CapLtdSettlement_CapLtdSettlementDetailEntityDTO settlementDetail;
    public String policyId;
    public String state;
    public CapLtdSettlement_CapLtdSettlementAbsenceInfoEntityDTO settlementAbsenceInfo;
    public CapLtdSettlement_CapLTDSettlementPolicyInfoEntityDTO policy;
    public CapLtdSettlement_CapLtdSettlementLossInfoEntityDTO settlementLossInfo;
}
