/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
.@{PREFIX}-balance-header-left-section {
    margin: 0.5rem 0;

    .@{ant-prefix}-select {
        width: 12rem;
    }

    .@{ant-prefix}-select:nth-child(0) {
        margin-left: 1rem;
    }
    button {
        margin-left: 1rem;
    }
}

.@{PREFIX}-balance-header-with-no-action-dropdown {
    margin: 1rem 0;

    .@{ant-prefix}-select {
        width: 12rem;
    }

    .@{ant-prefix}-select:nth-child(0) {
        margin-left: 1rem;
    }
    button {
        margin-left: 1rem;
    }
}

.@{PREFIX}-balance-table-title {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 1.125rem;
    line-height: 26px;
    color: @eis-font-heading-h-2-color;
    width: 100%;
    padding-top: 1.5rem;
}

.@{PREFIX}-balance-header-right-section {
    .@{PREFIX}-balance-header-amount-box {
        border: 1.5px solid @eis-color-neutral-l-4;
        border-radius: 0.25rem;
        width: 13rem;
        height: 6rem;
        float: left;
        margin: 0.5rem;
        text-align: center;

        h2 {
            line-height: 3.5rem;
            padding: 0 0.5rem;
        }
    }

    .total-balance-right-section-money {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .text-balance-right-section-money-red {
        color: red;
    }
}

.@{PREFIX}-balance-header-right-section-tooltip {
    .ant-tooltip-inner {
        text-align: center;
        background-color: @white !important;
        color: @gen-color-neutral-l-2;
    }

    .ant-typography {
        color: @eis-font-heading-h-2-color !important;
    }
}

.@{PREFIX}-balance-table-column-tag {
    font-size: 12px;
    color: @gen-color-neutral-l-2;
}

.@{PREFIX}-balance-table-expand-section {
    margin: 0 17% 0 23%;

    .@{ant-prefix}-row:first-child {
        margin-top: 0px;
    }

    .@{ant-prefix}-row {
        margin-top: 24px;

        .@{ant-prefix}-col {
            h4 {
                .gen-font-other-medium-14-left-main-primary;
            }

            p {
                line-height: 24px;
                color: @gen-color-neutral-l-2;
            }

            div {
                line-height: 24px;
                padding: 0 1rem;

                label,
                .@{PREFIX}-balance_expand_section_lookup_label {
                    float: left;
                    color: @gen-color-neutral-l-2;
                }
            }
        }

        .@{ant-prefix}-col:nth-child(2),
        .@{ant-prefix}-col:nth-child(3) {
            text-align: right;
        }
    }
}

.claim_reduce_payment_drawer_calculate {
    .builder-layout-row {
        display: flex;
        align-items: center;
        .ant-btn {
            margin-top: 0.75rem;
        }
    }
}

.@{PREFIX}-claim-reduce-payment-withholding-amount {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}

.@{PREFIX}-balance-activities-collapse {
    padding-top: 1rem;
    border: 1px solid @eis-color-neutral-l-4;
    .@{PREFIX}-payments-table-collapse.@{ant-prefix}-collapse .@{ant-prefix}-collapse-item {
        margin-bottom: 0;
        border-bottom: unset;
    }

    .@{PREFIX}-balance-activities-table {
        padding-top: 0;
    }
}

.@{PREFIX}-balance-table-recalcation-payments {
    padding-top: 1rem;
    border: 1px solid @eis-color-neutral-l-4;
    .@{ant-prefix}-collapse-content-box {
        padding-top: 1.5rem;
    }
    .table-bottom-margin {
        margin-bottom: unset;
    }
}

.@{PREFIX}-recalcation-payments-table {
    margin-top: 1.5rem;
    margin-bottom: 0;
}

.@{PREFIX}-balance-table-recalcation-payments-top {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.@{PREFIX}-recalcation-payments-section {
    display: flex;
    font-size: 1rem;
}

.@{PREFIX}-recalcation-payments-amount-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: right;
    font-weight: normal;
    padding: 0 1.5rem;
    color: @medium-gray;
    font-size: 0.875rem;

    p {
        color: @dark-gray;
        font-weight: 500;
        font-size: 1.125rem;
    }
}

.@{PREFIX}-payment-applied-withholdings {
    width: 50%;
}

.@{PREFIX}-balance-negative-value {
    color: @error-red;
}
.@{PREFIX}-balance-description-ellipsis {
    max-width: 18rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    label span:last-child:not(.ant-radio-inner) {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
