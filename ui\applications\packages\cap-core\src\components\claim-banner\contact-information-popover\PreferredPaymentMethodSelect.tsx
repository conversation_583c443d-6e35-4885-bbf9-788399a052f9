/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'
import {t} from '@eisgroup/i18n'
import {Form, Select, Button, FormComponentProps, Row, Col, Tooltip} from '@eisgroup/ui-kit'
import {ValidationMediumInfo} from '@eisgroup/ui-kit-icons'
import {PaymentMethod, PaymentMethodType} from '@eisgroup/common-business-components'
import {toJS} from 'mobx'
import {
    PAYMENT_METHOD_SELECT_OPTGROUP_LABEL,
    PAYMENT_METHOD_SELECT_OPTGROUP_ITEM,
    PAYMENT_METHOD_SELECT,
    PAYMENT_METHOD_BUTTON,
    PAYMENT_METHOD_PRIVILEGE_ICON
} from '../../../common/package-class-names'
import {isInactiveDateRange} from '../../../common/utils'
import {PaymentMethodWithFormPreferred} from '../../../common/Types'
import {getPaymentMethodInfo} from './PreferredPaymentMethodEditor'
import {hasAuthorities, Privileges} from '../../../utils'

const {OptGroup, Option} = Select

export interface PreferredPaymentMethodSelectProps extends FormComponentProps {
    paymentMethods: PaymentMethodWithFormPreferred[]
    onPreferredPaymentMethodChange?: (value: string) => void
    onAddButtonClick?: () => void
    memberBeneficiaryPaymentMethodId?: string
    addNewPaymentMethodDisabled?: boolean
    savedPaymentMethodId?: string
    notRequired?: boolean
}

@observer
class PreferredPaymentMethod extends React.Component<PreferredPaymentMethodSelectProps, any> {
    preferredPaymentMethod = paymentMethods =>
        paymentMethods.find(v => v.formPreferred)?._key?.id ?? this.props.memberBeneficiaryPaymentMethodId

    handleChangePreferredPaymentMethod = value => {
        this.props.onPreferredPaymentMethodChange?.(value)
    }

    componentDidMount() {
        const {paymentMethods, savedPaymentMethodId} = this.props
        const preferredPaymentMethodValue = this.preferredPaymentMethod(paymentMethods)
        const selectedPreferredPaymentMethod = paymentMethods.find(v => v.formPreferred)?._key?.id
        if (!selectedPreferredPaymentMethod) {
            this.props.form.setFieldsValue({
                preferredPaymentMethod: savedPaymentMethodId ?? preferredPaymentMethodValue
            })
        } else {
            this.props.form.setFieldsValue({
                preferredPaymentMethod: selectedPreferredPaymentMethod
            })
        }
        this.handleChangePreferredPaymentMethod(preferredPaymentMethodValue)
    }

    componentDidUpdate(prevProps: Readonly<PreferredPaymentMethodSelectProps>): void {
        const paymentMethods = toJS(this.props.paymentMethods)
        const preferredPaymentMethodId = paymentMethods?.find(v => v.formPreferred)?._key?.id
        const prevPreferredPaymentMethodId = prevProps.paymentMethods?.find(v => v.formPreferred)?._key?.id
        if (
            preferredPaymentMethodId !== prevPreferredPaymentMethodId &&
            (preferredPaymentMethodId || prevPreferredPaymentMethodId)
        ) {
            this.props.form.setFieldsValue({
                preferredPaymentMethod: preferredPaymentMethodId
            })
            this.handleChangePreferredPaymentMethod(preferredPaymentMethodId)
        }
    }

    handleClickAddNewButton = () => {
        this.props.onAddButtonClick?.()
    }

    submitForm = () => {
        let error
        this.props.form.validateFieldsAndScroll((err, value) => {
            error = err
        })
        return error
    }

    isSelected = (paymentMethod: PaymentMethodWithFormPreferred) => {
        const {paymentMethods} = this.props
        const preferredPaymentMethodValue = this.preferredPaymentMethod(paymentMethods)
        return preferredPaymentMethodValue === paymentMethod._key?.id
    }

    getPaymentMethodExtraLabel = (paymentMethod: PaymentMethod): string => {
        const labelsArray = [] as string[]
        if (isInactiveDateRange(paymentMethod?.effectiveDate, paymentMethod?.expirationDate)) {
            labelsArray.push(`(${t('cap-core:preferred_payment_method_inactive')})`)
        }
        if (this.props.paymentMethods.find(v => v.preferred)?._key?.id === paymentMethod?._key?.id) {
            labelsArray.push(`(${t('cap-core:preferred_payment_method_preferred')})`)
        }
        return labelsArray.join(' ')
    }

    renderOptGroup = (label: string, composedPaymentMethods: PaymentMethod[]) => {
        return (
            <OptGroup key={label} label={<div className={PAYMENT_METHOD_SELECT_OPTGROUP_LABEL}>{label}</div>}>
                {composedPaymentMethods.map(paymentMethod => (
                    <Option
                        key={paymentMethod._key?.id}
                        value={paymentMethod._key?.id}
                        className={PAYMENT_METHOD_SELECT_OPTGROUP_ITEM}
                        disabled={isInactiveDateRange(paymentMethod?.effectiveDate, paymentMethod?.expirationDate)}
                    >
                        {getPaymentMethodInfo(paymentMethod)?.icon}
                        &nbsp;&nbsp;
                        {getPaymentMethodInfo(paymentMethod)?.displayValue}
                        &nbsp;&nbsp;
                        {this.getPaymentMethodExtraLabel(paymentMethod)}
                    </Option>
                ))}
            </OptGroup>
        )
    }

    render(): React.ReactNode {
        const {paymentMethods, addNewPaymentMethodDisabled} = this.props
        const savePaymentMethods = [] as PaymentMethod[]
        const oneTimePaymentMethods = [] as PaymentMethod[]
        paymentMethods
            .filter(item => ![PaymentMethodType.CASH, PaymentMethodType.CREDIT_CARD].includes(item._type))
            .forEach((paymentMethod, index) => {
                if (
                    (isInactiveDateRange(paymentMethod?.effectiveDate, paymentMethod?.expirationDate) &&
                        this.isSelected(paymentMethod)) ||
                    !isInactiveDateRange(paymentMethod?.effectiveDate, paymentMethod?.expirationDate)
                ) {
                    return [PaymentMethodType.CHECK].includes(paymentMethod._type)
                        ? oneTimePaymentMethods.push(paymentMethod)
                        : savePaymentMethods.push(paymentMethod)
                }
                return undefined
            })
        return (
            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item label={t('cap-core:preferred_payment_method_label')} required={!this.props.notRequired}>
                        {this.props.form.getFieldDecorator('preferredPaymentMethod', {
                            validateTrigger: 'onBlur',
                            initialValue: this.preferredPaymentMethod(paymentMethods),
                            rules: this.props.notRequired
                                ? []
                                : [
                                      {
                                          required: true,
                                          message: t('cap-core:preferred_payment_method_required')
                                      }
                                  ]
                        })(
                            <Select
                                className={PAYMENT_METHOD_SELECT}
                                onChange={this.handleChangePreferredPaymentMethod}
                                allowClear
                                disabled={!hasAuthorities([Privileges.CEM_CREATE_UPDATE_CUSTOMER])}
                            >
                                {savePaymentMethods?.length > 0 &&
                                    this.renderOptGroup(
                                        t('cap-core:preferred_payment_method_saved'),
                                        savePaymentMethods
                                    )}
                                {oneTimePaymentMethods?.length > 0 &&
                                    this.renderOptGroup(
                                        t('cap-core:preferred_payment_method_one_time'),
                                        oneTimePaymentMethods
                                    )}
                            </Select>
                        )}
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item className={PAYMENT_METHOD_BUTTON}>
                        {hasAuthorities([Privileges.CEM_CREATE_UPDATE_CUSTOMER]) ? (
                            <Button
                                icon='action-add-medium'
                                type='link'
                                onClick={this.handleClickAddNewButton}
                                disabled={addNewPaymentMethodDisabled}
                            >
                                {t('cap-core:preferred_payment_method_add_new_button_label')}
                            </Button>
                        ) : (
                            <div className={PAYMENT_METHOD_PRIVILEGE_ICON}>
                                <Tooltip
                                    title={t('cap-core:additional_privilege_required')}
                                    destroyTooltipOnHide
                                    placement='top'
                                    trigger='hover'
                                >
                                    <ValidationMediumInfo />
                                </Tooltip>
                            </div>
                        )}
                    </Form.Item>
                </Col>
            </Row>
        )
    }
}

export const PreferredPaymentMethodSelect = Form.create<PreferredPaymentMethodSelectProps>()(PreferredPaymentMethod)
