/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import React, {FC, useCallback} from 'react'
import {Card, Checkbox, Radio} from '@eisgroup/ui-kit'
import {CheckBoxCardsCodeValue} from '@eisgroup/cap-services'
import classNames from 'classnames'
import {
    DIALOG_RADIO_CARD,
    DIALOG_RADIO_CARD_CHECKBOX,
    DIALOG_RADIO_CARD_CONTENT,
    DIALOG_RADIO_CARD_NAME,
    RADIO_CARD_DISABLED
} from '../..'

export const CheckBoxCard: FC<{
    item: CheckBoxCardsCodeValue
    onSelect: (key: any) => void
    isSelected: boolean
    disabled?: boolean
    getCardContent: (item?: CheckBoxCardsCodeValue) => React.ReactNode
    isRadioType?: boolean
}> = ({item, onSelect, isSelected, disabled, getCardContent, isRadioType}) => {
    const onClick = useCallback(() => onSelect(item.code), [onSelect, item.code])
    const getDisableClassName = (isDisabled?: boolean): string | undefined => {
        return isDisabled ? RADIO_CARD_DISABLED : undefined
    }
    const renderCard = () => {
        return (
            <Card
                tabIndex={0}
                hoverable={!disabled}
                selected={!disabled && isSelected}
                onClick={disabled ? undefined : onClick}
                className={classNames(DIALOG_RADIO_CARD, getDisableClassName(disabled))}
            >
                <div>
                    {!isRadioType ? (
                        <Checkbox
                            className={DIALOG_RADIO_CARD_CHECKBOX}
                            checked={isSelected}
                            disabled={disabled}
                            onClick={undefined}
                        />
                    ) : (
                        <Radio
                            className={DIALOG_RADIO_CARD_CHECKBOX}
                            checked={isSelected}
                            disabled={disabled}
                            onClick={undefined}
                        />
                    )}
                </div>
                <div className={classNames(DIALOG_RADIO_CARD_CONTENT, getDisableClassName(disabled))}>
                    <span className={classNames(DIALOG_RADIO_CARD_NAME, getDisableClassName(disabled))}>
                        {getCardContent(item)}
                    </span>
                </div>
            </Card>
        )
    }
    return renderCard()
}
