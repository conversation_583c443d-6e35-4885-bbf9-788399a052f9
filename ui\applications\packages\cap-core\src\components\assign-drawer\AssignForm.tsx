/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {AssigneeType} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {Field, ValuesSpy} from '@eisgroup/form'
import {ControlsRenderingComponentProps, renderControl} from '@eisgroup/react-components'
import {Col, Form, Row} from '@eisgroup/ui-kit'
import {observer} from 'mobx-react'
import React from 'react'
import {DrawerFormStateType, hasAuthorities, INPUT_FORM_ROW_2X1, Privileges, QueueStore, UserStore} from '../..'
import {QueueSelect} from './user-queue-search/QueueSelect'
import {SearchUserWrapper} from './user-queue-search/SearchUserWrapper'

const {RadioInput, RichTextEditor} = Field

export interface AssignFormControls {
    /**
     * Assign Type control.
     */
    readonly assignType: React.ReactNode
    /**
     * User Search control.
     */
    readonly userSearch?: React.ReactNode
    /**
     * Queue Search control.
     */
    readonly queueSearch?: React.ReactNode

    /**
     * Notes control.
     */
    readonly notes: React.ReactNode
}

export interface AssignFormProps {
    drawerFormState: DrawerFormStateType
    searchUserLabel: string
    assigneeType: string
    store: any
    userStore: UserStore
    queueStore: QueueStore
}

@observer
export class AssignForm extends React.Component<
    AssignFormProps & ControlsRenderingComponentProps<AssignFormControls, AssignFormProps>
> {
    state = {
        queueCd: ''
    }

    private getRadioItems = () => {
        const hasAssignUserPrivilege = hasAuthorities([Privileges.CASE_ASSIGN_CASE_TO_USER])
        const hasAssignQueuePrivilege = hasAuthorities([Privileges.CASE_ASSIGN_CASE_TO_QUEUE])
        const assignItems = [
            {
                value: AssigneeType.User,
                label: t('cap-core:assign_form_assignee_type_user_radio_label')
            },
            {
                value: AssigneeType.Queue,
                label: t('cap-core:assign_form_assignee_type_queue_radio_label')
            }
        ]

        return assignItems
            .filter(item => hasAssignUserPrivilege && item.value === AssigneeType.User)
            .concat(assignItems.filter(item => hasAssignQueuePrivilege && item.value === AssigneeType.Queue))
    }

    private getControls = (): AssignFormControls => {
        const controlsProps = opt(this.props.controls).orElse({})
        const options = this.getRadioItems()
        return {
            assignType: renderControl(
                controlsProps.assignType,
                this.props,
                <RadioInput options={options} name='selectedAssignedType' defaultValue={this.props.assigneeType} />
            ),
            userSearch: renderControl(
                controlsProps.userSearch,
                this.props,
                <ValuesSpy relatedFields={['~selectedAssignedType']}>
                    {({relatedValues}) =>
                        relatedValues.selectedAssignedType === AssigneeType.User ? (
                            <Form.Item
                                label={this.props.searchUserLabel || t('cap-core:assign_form_search_user_input_label')}
                            >
                                <SearchUserWrapper
                                    store={this.props.userStore}
                                    isLoading={this.props.userStore?.isLoading}
                                />
                            </Form.Item>
                        ) : null
                    }
                </ValuesSpy>
            ),
            queueSearch: renderControl(
                controlsProps.userSearch,
                this.props,
                <ValuesSpy relatedFields={['~selectedAssignedType']}>
                    {({relatedValues}) =>
                        relatedValues.selectedAssignedType === AssigneeType.Queue ? (
                            <QueueSelect store={this.props.queueStore} />
                        ) : null
                    }
                </ValuesSpy>
            ),
            notes: renderControl(
                controlsProps.notes,
                this.props,
                <RichTextEditor
                    label={t('cap-core:assign_form_notes_label')}
                    name='notes'
                    placeholder={t('cap-core:assign_form_notes_placeholder')}
                />
            )
        } as AssignFormControls
    }

    render(): React.ReactNode {
        const controls = this.getControls()
        return this.props.controls && this.props.controls.render ? (
            this.props.controls.render(controls)
        ) : (
            <div>
                {controls.assignType}
                {controls.userSearch}
                <div className={INPUT_FORM_ROW_2X1}>{controls.queueSearch}</div>
                <div>
                    <Row>
                        <Col span={16}>{controls.notes}</Col>
                    </Row>
                </div>
            </div>
        )
    }
}
