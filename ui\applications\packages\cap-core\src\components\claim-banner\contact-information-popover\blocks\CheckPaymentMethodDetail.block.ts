import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */



/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "block",
  "blockId": "CheckPaymentMethodDetail",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 12,
            "label": "cap-core:preferred_payment_method_check_number",
            "name": "checkNumber"
          },
          "id": "15c403e6-a50e-40cd-9542-b24ee0cfae08"
        },
        {
          "type": "DATEPICKER_INPUT",
          "props": {
            "md": 12,
            "label": "cap-core:preferred_payment_method_check_date",
            "dateType": "date", valueType: 'DATETIME',
            "name": "checkDate"
          },
          "id": "7ef58045-7fde-49b8-a413-d3debbb0558e"
        }
      ],
      "id": "b1647e44-b4ea-406d-a2a3-69cdf8e96d0c"
    }
  ],
  "version": 11
}

export default config;
