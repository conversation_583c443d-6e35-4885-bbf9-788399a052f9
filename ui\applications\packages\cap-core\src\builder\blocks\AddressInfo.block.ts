import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "block",
  "blockId": "AddressInfo",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 24,
            "name": "addressType",
            "label": "cap-core:address_drawer_type",
            "lookupName": "AddressType",
            "lookupType": 'AddressInfoAddressType',
            "valueType": "string",
            "placeholder": "cap-core:select_placeholder",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:address_drawer_type"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
          },
          "id": "c3ccb8c2-eb67-403a-9d38-c15abb075192"
        },
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 24,
            "name": "addressLine1",
            "label": "cap-core:form_address_info_address_line_1",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:form_address_info_address_line_1"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "maxLength": 40
          },
          "id": "0c23a1a8-f18a-4655-8809-9f77b188a067"
        }
      ],
      "id": "3e47a189-e07a-4f95-8411-a01f4f53c7cb"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 12,
            "name": "addressLine2",
            "label": "cap-core:form_address_info_address_line_2",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": []
            },
            "maxLength": 40
          },
          "id": "4d0b1ff7-baba-4811-b1ab-d0a065e51b68"
        },
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 12,
            "name": "addressLine3",
            "label": "cap-core:form_address_info_address_line_3",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": []
            },
            "maxLength": 40
          },
          "id": "1b9cdd13-fdee-417a-8324-b6c962527e24"
        }
      ],
      "id": "9a51b55b-cf73-4391-9cf0-017881c19696"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 12,
            "name": "countryCd",
            "label": "cap-core:form_address_info_country",
            "lookupName": "Country",
            "valueType": "string",
            "placeholder": "cap-core:select_placeholder",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            },
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:form_address_info_country"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "32bf1736-ee04-4e96-a726-b817941e4bfb"
        }
      ],
      "id": "4edeb509-3ae4-496d-9a58-14141805fcab"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 12,
            "name": "city",
            "label": "cap-core:form_address_info_city",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:form_address_info_city"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "maxLength": 30,
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "48cd14d3-a586-4a07-bad2-c09432229f21"
        },
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 6,
            "name": "stateProvinceCd",
            "label": "cap-core:form_address_info_province",
            "lookupName": "StateProv",
            "valueType": "string",
            "placeholder": "cap-core:select_placeholder",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              },
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~countryCd"
                      },
                      "operator": "$ne",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "US"
                      }
                    }
                  ]
                }
              }
            },
            "filterBy": [
              {
                "id": "8b55f511-5d45-4d73-859d-efc43962792c",
                "fieldName": "~countryCd",
                "filterLookupKey": "countryCd"
              }
            ]
          },
          "id": "68e2e851-14ba-415d-8157-cc282eea6746"
        },
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 6,
            "name": "stateProvinceCd",
            "label": "cap-core:form_address_info_province",
            "lookupName": "StateProv",
            "valueType": "string",
            "placeholder": "cap-core:select_placeholder",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              },
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "~countryCd"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "US"
                      }
                    }
                  ]
                }
              }
            },
            "filterBy": [
              {
                "id": "8b55f511-5d45-4d73-859d-efc43962792c",
                "fieldName": "~countryCd",
                "filterLookupKey": "countryCd"
              }
            ],
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "47fcf478-c9f0-4742-844c-3dd56c89ff53"
        },
        {
          "type": "TEXT_INPUT",
          "props": {
            "md": 6,
            "name": "postalCode",
            "label": "cap-core:form_address_info_postal_code",
            "maxLength": 10,
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "cap-core:form_address_info_postal_code"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "addressInfoDisabled"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "true"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "413def08-6d62-4409-896a-3ce7dee2f1fe"
        }
      ],
      "id": "7e518e58-44b0-4670-a42d-726e5616d3f6"
    }
  ],
  "version": 60
}

export default config;
