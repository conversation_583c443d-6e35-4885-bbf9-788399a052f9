play.http.router = gateway.Routes

# Mandatory configuration
include "gateway.conf"

# ApiControllerCompositeAction executed around request (see core.controllers.actions.ApiControllerAction)
include "actions.conf"

# Security filters stack configuration
include "security.conf"

# Reference configuration for core module
include "dxp.core.module.conf"

# Swagger UI title name
core.swagger.api.info.title = "CAP DXP API"
core.swagger.api.info.description = "CAP DXP APIs"

# Property visibility configuration in Core services
core.configuration.api.properties.visible = ["core", "config", "java.version"]
core.configuration.api.properties.hidden = ["core.configuration.api.properties.hidden", "core.authenticators"]

# Request identifier header allowed to be transparently forwarded
core.controllers.request.headers.forward += "X-Request-ID"

# Request identifier header names according to the layer
core.controllers.request-id.header.name = "X-Request-ID"
core.dataproviders.request-id.header.name = "X-Gateway-Request-ID"

# Request identifier header usage configuration
core.controllers.request-id.include-in-request = true
core.controllers.request-id.include-in-response = true
core.dataproviders.request-id.include-in-request = true

# Enabling pretty print for JSON responses
core.controllers.response.serialization.formatted = true

# Deserialization settings for unknown properties
core.deserialization.onUnknownProperties = "warn" // "ignore", "warn", "fail": both controller and dataprovider layer
core.deserialization.onWrongDateFormat = "warn" // "ignore", "warn", "fail". if more special is not specified for controller/dataprovider

# Swagger UI visible API groups paths
core.swagger.group.paths = [
  {name: "All APIs", path: ".*"},
  {name: "CAP Adjuster APIs", path: "/cap-adjuster.*", client-id: "@eisgroup/cap-gateway-client"},
  {name: "DXP Core APIs", path: "/core.*"}
]
