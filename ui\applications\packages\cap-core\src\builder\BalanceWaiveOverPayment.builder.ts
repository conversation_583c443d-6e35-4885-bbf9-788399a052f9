import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 12,
            "name": "amount",
            "allowDecimal": true,
            "required": true,
            "label": "cap-core:balance_actions_drawer_amount",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Amount"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "amount-valid",
                  "fieldValue": {
                    "type": "field",
                    "value": "~totalBalance"
                  }
                }
              ]
            },
            "condition": {
              "undefined": {}
            }
          },
          "id": "234eebd4-1b06-4646-b6f6-b42677c48f5d"
        }
      ],
      "id": "60d968b6-5698-4195-877f-7cf4c540c503"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_AREA",
          "props": {
            "md": 24,
            "label": "cap-core:balance_actions_drawer_comments_label",
            "name": "comments",
            "autosize": {
              "minRows": 6,
              "maxRows": 6
            },
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Comments"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "action"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": "WAIVE_OVERPAYMENT"
                      }
                    }
                  ]
                }
              }
            }
          },
          "id": "25c46e3a-3968-4870-b560-6f3014a91a5f"
        }
      ],
      "id": "ea0e50e3-1846-4ec5-9487-9623dd3ec36f"
    }
  ],
  "version": 43
}

export default config;
