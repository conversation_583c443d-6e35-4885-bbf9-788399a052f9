{"openapi": "3.0.1", "info": {"description": "Auto-generated OpenAPI schema from the OpenL rules", "title": "claim-life-accelerated-adjudication", "version": "1.0.0"}, "servers": [{"url": "/claim-life-accelerated-adjudication", "variables": {}}], "security": [{"BasicAuth": [], "SSOToken": []}], "paths": {"/_api_accelerated_adjudication": {"post": {"description": "Rules method: org.openl.generated.beans.CapAcceleratedSettlementResultEntity _api_accelerated_adjudication(org.openl.generated.beans.CapAcceleratedSettlementRulesInput request)", "operationId": "_api_accelerated_adjudication", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAcceleratedSettlementRulesInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAcceleratedSettlementResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapAcceleratedSettlementResultEntity _api_accelerated_adjudication(CapAcceleratedSettlementRulesInput)"}}, "/_api_accelerated_applicability": {"post": {"description": "Rules method: org.openl.generated.beans.CapAcceleratedLossApplicabilityResult _api_accelerated_applicability(org.openl.generated.beans.CapAcceleratedLossApplicabilityInput request)", "operationId": "_api_accelerated_applicability", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAcceleratedLossApplicabilityInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapAcceleratedLossApplicabilityResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapAcceleratedLossApplicabilityResult _api_accelerated_applicability(CapAcceleratedLossApplicabilityInput)"}}, "/_api_permanent_life_face_value_calculation": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimFaceValueCalculationResultEntity _api_permanent_life_face_value_calculation(org.openl.generated.beans.CapClaimWrapperFaceValueCalculationInput request)", "operationId": "_api_permanent_life_face_value_calculation", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperFaceValueCalculationInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimFaceValueCalculationResultEntity"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimFaceValueCalculationResultEntity _api_permanent_life_face_value_calculation(CapClaimWrapperFaceValueCalculationI"}}}, "components": {"schemas": {"AccumulatorRemainingsEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorType": {"type": "string"}, "amountUnit": {"type": "string"}, "coverage": {"type": "string"}, "limitAmount": {"type": "number"}, "policyTerm": {"$ref": "#/components/schemas/Term"}, "remainingAmount": {"type": "number"}, "usedAmount": {"type": "number"}}}, "BaseLifeGrossBenefitAmount": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "totalApprovedAmount": {"$ref": "#/components/schemas/Money"}}}, "BaseLifePolicyCoverageLimitLevel": {"type": "object", "properties": {"limitLevelType": {"type": "string"}, "limitType": {"type": "string"}, "timePeriodCd": {"type": "string"}}}, "BaseLifeRelatedSettlmentInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "benefitCd": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "incidentDate": {"type": "string", "format": "date-time"}}}, "BaseLifeSettlementAccumulatorDetails": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorAmount": {"type": "number"}, "accumulatorAmountUnit": {"type": "string"}, "accumulatorCoverage": {"type": "string"}, "accumulatorCustomerUri": {"type": "string"}, "accumulatorExtension": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetailsExtension"}, "accumulatorParty": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorPolicyUri": {"type": "string"}, "accumulatorResource": {"$ref": "#/components/schemas/EntityLink"}, "accumulatorTransactionDate": {"type": "string", "format": "date-time"}, "accumulatorType": {"type": "string"}}}, "BaseLifeSettlementAccumulatorDetailsExtension": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time"}}}, "CapAcceleratedCertInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapAcceleratedSettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapAcceleratedSettlementCoverageInfoEntity"}}}}, "CapAcceleratedLossApplicabilityInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "eventDate": {"type": "string", "format": "date-time"}, "lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/CapLifeLossPolicyInfoEntity"}}}}, "CapAcceleratedLossApplicabilityResult": {"type": "object", "properties": {"applicability": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}}}, "CapAcceleratedMasterInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capBenefitInfo": {"$ref": "#/components/schemas/CapAcceleratedSettlementBenefitInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapAcceleratedSettlementCoverageInfoEntity"}}}}, "CapAcceleratedSettlementAttrOptionsEntity": {"type": "object", "properties": {"attrName": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}}}, "CapAcceleratedSettlementBenefitInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "assuredSum": {"$ref": "#/components/schemas/Money"}, "benefitPct": {"type": "number"}, "benefitTypeCd": {"type": "string"}, "maxBenefitAmount": {"$ref": "#/components/schemas/Money"}, "reamingAmount": {"$ref": "#/components/schemas/Money"}}}, "CapAcceleratedSettlementCoverageConfigEntity": {"type": "object", "properties": {"accidentInd": {"type": "boolean"}, "accumulationCategoryGroup": {"type": "string"}, "applicableLossTypes": {"type": "array", "items": {"type": "string"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/components/schemas/CapAcceleratedSettlementAttrOptionsEntity"}}, "calculationFormulaId": {"type": "string"}, "limitLevels": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifePolicyCoverageLimitLevel"}}, "unit": {"type": "string"}}}, "CapAcceleratedSettlementCoverageInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "applyBenefit": {"type": "string"}, "associatedItems": {"type": "string"}, "coverageCd": {"type": "string"}, "coverageLimit": {"$ref": "#/components/schemas/Money"}, "coveredLoss": {"type": "string"}, "limitLevel": {"type": "string"}}}, "CapAcceleratedSettlementDeductionEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "deductionBeneficiary": {"type": "string"}, "deductionPct": {"type": "number", "format": "double"}, "deductionTerm": {"$ref": "#/components/schemas/Term"}, "deductionType": {"type": "string"}, "isPrePostTax": {"type": "boolean"}, "nonProviderPaymentType": {"type": "string"}, "stateProvided": {"type": "string"}}}, "CapAcceleratedSettlementDetailEntity": {"type": "object", "properties": {"_archived": {"type": "boolean"}, "_key": {"$ref": "#/components/schemas/EntityKey"}, "_modelName": {"type": "string"}, "_modelType": {"type": "string"}, "_modelVersion": {"type": "string"}, "_timestamp": {"type": "string"}, "_type": {"type": "string"}, "_version": {"type": "string"}, "approvedAmountOverride": {"$ref": "#/components/schemas/Money"}, "associatedInsurableRiskOid": {"type": "string"}, "benefitCd": {"type": "string"}, "claimant": {"type": "string"}, "coverageCd": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "eliminationPeriodOverrideEndDate": {"type": "string", "format": "date-time"}, "eliminationPeriodOverrideReason": {"type": "string"}, "expenseOldAmount": {"$ref": "#/components/schemas/Money"}, "expenseReserveAmount": {"$ref": "#/components/schemas/Money"}, "expenseReserveComments": {"type": "string"}, "expenseReserveReason": {"type": "string"}, "grossAmount": {"$ref": "#/components/schemas/Money"}, "incidentDate": {"type": "string", "format": "date-time"}, "indemnityReserveAmount": {"$ref": "#/components/schemas/Money"}, "indemnityReserveComments": {"type": "string"}, "indemnityReserveOldAmount": {"$ref": "#/components/schemas/Money"}, "indemnityReserveReason": {"type": "string"}, "isGrossAmountOverrided": {"type": "boolean"}, "maxBenefitOverridePeriod": {"$ref": "#/components/schemas/Period"}, "maxBenefitOverrideReason": {"type": "string"}, "numberOfUnits": {"type": "integer", "format": "int32"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}, "recoveryOldAmount": {"$ref": "#/components/schemas/Money"}, "recoveryReserveAmount": {"$ref": "#/components/schemas/Money"}, "recoveryReserveComments": {"type": "string"}, "recoveryReserveReason": {"type": "string"}, "isCancelled": {"type": "boolean", "description": "Whether the settlement is cancelled"}, "cancelReason": {"type": "string", "description": "The settlement cancelled reason"}}}, "CapAcceleratedSettlementLifeIntakeInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "deductions": {"type": "array", "items": {"$ref": "#/components/schemas/CapAcceleratedSettlementDeductionEntity"}}, "taxes": {"type": "array", "items": {"$ref": "#/components/schemas/CapAcceleratedSettlementTaxEntity"}}}}, "CapAcceleratedSettlementLossInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "absence": {"$ref": "#/components/schemas/EntityLink"}, "claimType": {"type": "string"}, "coverageType": {"type": "string"}, "dateOfDiagnosis": {"type": "string", "format": "date-time"}, "lastWorkDate": {"type": "string", "format": "date-time"}, "lossDateTime": {"type": "string", "format": "date-time"}, "lossType": {"type": "string"}, "offsets": {"$ref": "#/components/schemas/CapAcceleratedSettlementOffsetEntity"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}}}, "CapAcceleratedSettlementOffsetEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "isPrePostTax": {"type": "boolean"}, "offsetTerm": {"$ref": "#/components/schemas/Term"}, "offsetType": {"type": "string"}, "proratingRate": {"type": "string"}}}, "CapAcceleratedSettlementPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "acceleratedCertInfo": {"$ref": "#/components/schemas/CapAcceleratedCertInfoEntity"}, "acceleratedMasterInfo": {"$ref": "#/components/schemas/CapAcceleratedMasterInfoEntity"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "terminationAge": {"type": "integer", "format": "int64"}, "txEffectiveDate": {"type": "string"}}}, "CapAcceleratedSettlementResultEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeSettlementAccumulatorDetails"}}, "autoAdjudicatedAmount": {"$ref": "#/components/schemas/Money"}, "autoAdjudicatedDuration": {"type": "number"}, "benefitCd": {"type": "string"}, "claimCoverageName": {"type": "string"}, "dateRange": {"$ref": "#/components/schemas/Period"}, "eligibilityEvaluationCd": {"type": "string"}, "grossBenefitAmount": {"$ref": "#/components/schemas/BaseLifeGrossBenefitAmount"}, "incidentDate": {"type": "string", "format": "date-time"}, "isAutoAdjudicated": {"type": "boolean"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time"}}}, "CapAcceleratedSettlementRulesInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accumulatorRemainings": {"type": "array", "items": {"$ref": "#/components/schemas/AccumulatorRemainingsEntity"}}, "cashAmountsADBInfo": {"$ref": "#/components/schemas/CapCashAmountsADBEntity"}, "claimCoverageName": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "coverageConfiguration": {"$ref": "#/components/schemas/CapAcceleratedSettlementCoverageConfigEntity"}, "currentDateTime": {"type": "string", "format": "date-time"}, "details": {"$ref": "#/components/schemas/CapAcceleratedSettlementDetailEntity"}, "isPriorAutoAdjudicated": {"type": "boolean"}, "lifeIntake": {"$ref": "#/components/schemas/CapAcceleratedSettlementLifeIntakeInfoEntity"}, "loss": {"$ref": "#/components/schemas/CapAcceleratedSettlementLossInfoEntity"}, "officialDeathDate": {"type": "string", "format": "date-time"}, "policy": {"$ref": "#/components/schemas/CapAcceleratedSettlementPolicyInfoEntity"}, "relatedSettlements": {"type": "array", "items": {"$ref": "#/components/schemas/BaseLifeRelatedSettlmentInfo"}}, "settlement": {"$ref": "#/components/schemas/EntityLink"}, "wrapperInfo": {"$ref": "#/components/schemas/CapAcceleratedSettlementWrapperInfoEntity"}}}, "CapAcceleratedSettlementTaxEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "jurisdictionType": {"type": "string"}, "taxType": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}}}, "CapAcceleratedSettlementWrapperInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "claimSubjectId": {"type": "string"}, "claimWrapperIdentification": {"$ref": "#/components/schemas/EntityLink"}, "memberRegistryTypeId": {"type": "string"}}}, "CapAgeReductionDetailsEntity": {"type": "object", "properties": {"ageCd": {"type": "integer", "format": "int32"}, "reducedToCd": {"type": "number"}}}, "CapCashAmountsADBEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "adbAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimFaceValueCalculationResultEntity": {"type": "object", "properties": {"calculatedFaceAmount": {"$ref": "#/components/schemas/Money"}, "isAgeReductionUsed": {"type": "boolean"}, "originalFaceAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimWrapperCoverageInfoEntity": {"type": "object", "properties": {"ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "benefitStructures": {"type": "array", "items": {"$ref": "#/components/schemas/CapCoverageBenefitStructuresEntity"}}, "coverageCd": {"type": "string"}}}, "CapClaimWrapperFaceValueCalculationInput": {"type": "object", "properties": {"age": {"type": "integer", "format": "int32"}, "baseFaceAmount": {"$ref": "#/components/schemas/Money"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimWrapperCoverageInfoEntity"}}, "isMasterPolicy": {"type": "boolean"}, "policyPackageCd": {"type": "string"}, "relationshipToInsuredCd": {"type": "string"}}}, "CapCoverageBenefitStructuresEntity": {"type": "object", "properties": {"annualEarningsAmount": {"$ref": "#/components/schemas/Money"}, "approvedAmount": {"$ref": "#/components/schemas/Money"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitTypeCd": {"type": "string"}, "employeeAmtpct": {"type": "number"}, "roundingMethod": {"type": "string"}, "salaryMultiple": {"type": "number"}, "typeOfBenefitStructure": {"type": "string"}}}, "CapInsuredInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "isMain": {"type": "boolean"}, "registryTypeId": {"type": "string"}}}, "CapLifeLossPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "policyNumber": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string", "format": "date-time"}}}, "EntityKey": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string"}}}, "EntityLink": {"type": "object", "properties": {"_uri": {"type": "string"}}}, "JAXRSErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["USER_ERROR", "RULES_RUNTIME", "COMPILATION", "SYSTEM", "BAD_REQUEST", "VALIDATION"]}, "message": {"type": "string"}}}, "JAXRSUserErrorResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "MessageType": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "source": {"type": "string"}}}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}}}, "Period": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}}, "Term": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"BasicAuth": {"description": "Genesis Basic credentials", "scheme": "basic", "type": "http"}, "SSOToken": {"description": "Genesis Authorization header. Value format: `[Token {token}]`", "in": "header", "name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}