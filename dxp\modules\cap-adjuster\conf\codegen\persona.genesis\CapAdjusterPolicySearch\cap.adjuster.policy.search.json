{"swagger": "2.0", "x-dxp-spec": {"imports": {"policy.search": {"schema": "integration.cap.generic.search.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Claim Policy API", "version": "1", "title": "CAP Adjuster: Claim Policy API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/policies", "description": "CAP Adjuster: Claim Policy API"}], "paths": {"/policies": {"post": {"x-dxp-path": "/api/common/search/v1/claim_policy", "tags": ["/cap-adjuster/v1/policies"]}}, "/policies/single-line-search": {"post": {"x-dxp-path": "/api/common/search/v1/claim_policy_sls", "tags": ["/cap-adjuster/v1/policies"]}}, "/policies/projection-header": {"post": {"x-dxp-path": "/api/common/search/v1/claim_policy/projection/header", "tags": ["/cap-adjuster/v1/policies"]}}}}