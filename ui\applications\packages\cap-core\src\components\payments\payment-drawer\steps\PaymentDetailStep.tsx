/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {UIEngine} from '@eisgroup/builder'
import {BusinessTypes, CapEventCase} from '@eisgroup/cap-event-case-models'
import {CapPaymentTemplate} from '@eisgroup/cap-financial-models'
import {
    CapGenericLoss,
    ClaimParty,
    dateUtils,
    IndividualCustomer,
    loadLookupOptions,
    OrganizationCustomer,
    OriginSourceType
} from '@eisgroup/cap-services'
import {errorToRxResult} from '@eisgroup/common'
import {Right} from '@eisgroup/data.either'
import {LookupValue} from '@eisgroup/form'
import {t} from '@eisgroup/i18n'
import {Alert, Button} from '@eisgroup/ui-kit'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import moment, {Moment} from 'moment'
import * as React from 'react'
import {Observable} from 'rxjs'
import {v4} from 'uuid'
import editorConfig from '../../../../builder/editor-config'
import {
    AUTO_EOB,
    CAP_EVENT_CASE,
    ClaimTypesMap,
    customerModelName,
    LifeClaimTypesMap,
    OTHER,
    ReserveType,
    SettlementModelName
} from '../../../../common/constants'
import {
    PAYMENT_DETAIL_ADD_ALLOCATION_BUTTON,
    PAYMENT_DETAIL_ADD_ERROR_CONTAINER
} from '../../../../common/package-class-names'
import {CaseSystemPaymentStore} from '../../../../common/store'
import {Allocation, OptionValue, SelectInputOptions, Settlements} from '../../../../common/Types'
import {
    collectPayee,
    formatToAllocations,
    getTranslatedCoverageName,
    hasAuthorities,
    Privileges
} from '../../../../utils'
import {EntityLink} from '../../../../utils/EntityLink'
import {RangePickerCustomProps} from '../../../range-picker/RangerPickerWrapper'
import {RemoveButtonProps} from '../../../remove-button/RemoveButton'
import {SelectCustomProps, SelectOptionProps} from '../../../select-input-wrapper/SelectInputWrapper'
import {CreateOrUpdate} from '../../Utils'
import config from '../builder/PaymentDetail.builder'
import {OnBehalfOfSlot} from '../OnBehalfOfSlot'
import CapLoss = BusinessTypes.CapLoss
import Period = BusinessTypes.Period
import CapAdditionalRole = CapEventCase.CapAdditionalRole
import CapBuildPaymentScheduleInput = CapPaymentTemplate.CapBuildPaymentScheduleInput
import CapPaymentScheduleExpensesInput = CapPaymentTemplate.CapPaymentScheduleExpensesInput
import CapPaymentScheduleSettlementInfoInput = CapPaymentTemplate.CapPaymentScheduleSettlementInfoInput
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity

export const PAYMENT_DETAIL_FORM_ID = 'PaymentDetailForm'
export const RESERVE_TYPE_LOOKUP_NAME = 'ReserveType'

interface PaymentDetailStepProps {
    store: CaseSystemPaymentStore<any, any>
    controls: React.ReactNode
    onNext: () => void
    parties: ClaimParty[]
    lifeClaims: CapGenericLoss[]
    providers: CapAdditionalRole[]
    subjectOfClaim?: IndividualCustomer | OrganizationCustomer
    employer?: OrganizationCustomer
    member?: IndividualCustomer
    beneficiaries: ClaimParty[]
    isAllocationExist: (isExist: boolean) => void
    getPaymentMethodIdAndCheckAddressId: (
        selectedClaim: string,
        allocationPayeeSource?: string
    ) => {paymentMethodId?: string; checkAddressId?: string; isPayeeMember?: boolean}
    loss: OriginSourceType
    coverageDuplicatedFlag: boolean
    periodDuplicatedFlag: boolean
    notOpenClaimFlag: boolean
    paymentMethodErrorMsg?: string
    eobRemarkValues?: LookupValue[]
}

interface PaymentDetailStepState {
    currentPayee: string | undefined
    currentBehalfOfPayee: string | undefined
    allocations: Allocation[]
    currentAllocations: Allocation[]
    buildPaymentScheduleInput: CapBuildPaymentScheduleInput
    isClaimPayment: boolean
    coverageTypesOptions: OptionValue[]
    initialBuildPaymentScheduleInputSnapshot: CapBuildPaymentScheduleInput | undefined
}

export interface PaymentDetailFormValues {
    payee: string
    representBeneficiary: string
    manualCheck: boolean
    pullCheck: boolean
    buildPaymentScheduleInput: CapBuildPaymentScheduleInput
    allocations: Allocation[]
    isClaimPayment: boolean
    isBenefitStatusChanged: boolean
}

@observer
export class PaymentDetailStep extends React.Component<PaymentDetailStepProps, PaymentDetailStepState> {
    state = {
        currentPayee: undefined,
        currentBehalfOfPayee: undefined,
        allocations: [] as Allocation[],
        currentAllocations: [] as Allocation[],
        buildPaymentScheduleInput: {} as CapBuildPaymentScheduleInput,
        isClaimPayment: false,
        coverageTypesOptions: [] as OptionValue[],
        initialBuildPaymentScheduleInputSnapshot: {} as CapBuildPaymentScheduleInput
    }

    componentDidMount(): void {
        const {
            paymentTemplate,
            originalPaymentTemplate,
            updateCurrentPayee,
            createOrUpdatePayment,
            hasEnterStep2,
            associatedSettlements,
            associatedClaims,
            representBeneficiary,
            setOriginalPaymentDetailStepAllocations
        } = this.props.store
        this.props.store.loadAssociatedSettlements([this.props.loss])
        const formPaymentTemplate = toJS(paymentTemplate)
        const originalFormPaymentTemplate = toJS(originalPaymentTemplate)

        const buildPaymentScheduleInput = formPaymentTemplate.buildPaymentScheduleInput as CapBuildPaymentScheduleInput

        const {settlements, expenses, exGratias, currentPayee} = this.getSettlementsExpensesAndExGratias(
            formPaymentTemplate,
            originalFormPaymentTemplate
        )

        const allocations = formatToAllocations(
            associatedClaims,
            associatedSettlements,
            settlements,
            expenses,
            exGratias
        )

        const payeeProvider = this.checkPayeeProvider(currentPayee)

        // allocations for current payee
        const filteredAllocations = this.loadOriginalPaymentDetailStepAllocations(currentPayee)
        const currentAllocations =
            createOrUpdatePayment === CreateOrUpdate.CREATE
                ? filteredAllocations
                : filteredAllocations.filter(v => v.representBeneficiary === representBeneficiary)
        setOriginalPaymentDetailStepAllocations(filteredAllocations)
        if (currentAllocations.length === 0) {
            currentAllocations.push({
                isInterestOnly: false,
                selectedClaim: '',
                coverageType: undefined,
                allocationPayeeSource: currentPayee,
                allocationPayeeProvider: payeeProvider,
                eobRemarkCodes: [],
                representBeneficiary,
                otherEOBMessage: '',
                key: v4()
            })
        }
        updateCurrentPayee(currentPayee)
        this.getOnBehalfOfOptions(currentPayee)
        this.loadReserveTypeLookup().subscribe()
        const isCreateAndNotEnterStep2 = createOrUpdatePayment === CreateOrUpdate.CREATE && !hasEnterStep2
        // use currentPayee in store after click previous button
        const formatCurrPayee = currentPayee
        this.setState({
            currentPayee: formatCurrPayee,
            currentBehalfOfPayee: '',
            allocations,
            currentAllocations: isCreateAndNotEnterStep2
                ? [
                      {
                          isInterestOnly: false,
                          selectedClaim: this.resetSelectedClaim(),
                          coverageType: undefined,
                          allocationPayeeSource: formatCurrPayee,
                          allocationPayeeProvider: payeeProvider,
                          key: v4(),
                          claimModelName: this.getCurrentClaimModelName(),
                          eobRemarkCodes: [],
                          representBeneficiary,
                          otherEOBMessage: ''
                      }
                  ]
                : currentAllocations,
            buildPaymentScheduleInput: {
                ...buildPaymentScheduleInput,
                description: isCreateAndNotEnterStep2 ? undefined : buildPaymentScheduleInput.description
            },
            initialBuildPaymentScheduleInputSnapshot: toJS(originalFormPaymentTemplate?.buildPaymentScheduleInput)
        })
    }

    loadOriginalPaymentDetailStepAllocations = (currentPayee: string) => {
        const {
            paymentTemplate,
            originalPaymentTemplate,
            associatedSettlements,
            hasEnterStep2,
            associatedClaims,
            representBeneficiary
        } = this.props.store
        this.props.store.loadAssociatedSettlements([this.props.loss])
        const formPaymentTemplate = toJS(paymentTemplate)
        const originalFormPaymentTemplate = toJS(originalPaymentTemplate)

        const {settlements, expenses, exGratias} = this.getSettlementsExpensesAndExGratias(
            formPaymentTemplate,
            originalFormPaymentTemplate
        )

        const allocations = formatToAllocations(
            associatedClaims,
            associatedSettlements,
            settlements,
            expenses,
            exGratias
        )

        // allocations for current payee
        return this.formatCurrentAllocations(
            allocations,
            currentPayee,
            hasEnterStep2 ? representBeneficiary : undefined
        )
    }

    loadReserveTypeLookup = () => {
        return loadLookupOptions(RESERVE_TYPE_LOOKUP_NAME).map(either =>
            either.fold(errorToRxResult, values => {
                this.setState({
                    coverageTypesOptions: [...values]
                        .sort((o1, o2) => o1.orderNo! - o2.orderNo!)
                        .map(v => v as OptionValue)
                })
                return Observable.of(Right(values))
            })
        )
    }

    getSettlementsExpensesAndExGratias = (
        formPaymentTemplate: CapPaymentTemplateEntity,
        originalFormPaymentTemplate: CapPaymentTemplateEntity
    ) => {
        const {createOrUpdatePayment, hasEnterStep2} = this.props.store
        const buildPaymentScheduleInput = formPaymentTemplate?.buildPaymentScheduleInput
        const originalBuildPaymentScheduleInput =
            originalFormPaymentTemplate?.buildPaymentScheduleInput || buildPaymentScheduleInput
        const getInitialPayee = () => {
            const initialSettlementPayee = buildPaymentScheduleInput?.settlements?.[0]?.payeeDetails?.payee?._uri
            const initialExpensePayee = buildPaymentScheduleInput?.expenses?.[0]?.payee?._uri
            const initialExGratiaPayee = buildPaymentScheduleInput?.exGratias?.[0]?.payee?._uri

            return (isInitial ? '' : initialSettlementPayee || initialExpensePayee || initialExGratiaPayee) || ''
        }

        // condition for set inital value
        const isInitial = createOrUpdatePayment === CreateOrUpdate.CREATE && !hasEnterStep2

        const currentPayee = getInitialPayee()

        const {
            settlements: curSettlements = [],
            expenses: curExpenses = [],
            exGratias: currentExGratias = []
        } = buildPaymentScheduleInput || {}
        const {
            settlements: originalSettlements = [],
            expenses: originalExpenses = [],
            exGratias: originalExGratias = []
        } = originalBuildPaymentScheduleInput || {}

        const settlements = originalSettlements
            .filter(v => v.payeeDetails?.payee?._uri !== currentPayee)
            .concat(curSettlements.filter(item => item?.payeeDetails?.payee?._uri === currentPayee))
        const expenses = originalExpenses
            .filter(v => v.payee?._uri !== currentPayee)
            .concat(curExpenses.filter(item => item?.payee?._uri === currentPayee))
        const exGratias = originalExGratias
            .filter(v => v.payee?._uri !== currentPayee)
            .concat(currentExGratias.filter(item => item?.payee?._uri === currentPayee))

        return {
            settlements,
            expenses,
            exGratias,
            currentPayee
        }
    }

    checkPayeeProvider = (value?: string) => {
        return this.props.providers?.some(uri => uri.registryId === value) ? value : undefined
    }

    formatCurrentAllocations = (allocations: Allocation[], payee: string, representBeneficiary?: string) => {
        const claimList = this.formatClaimOptions(toJS(this.props.store.associatedClaims), payee, representBeneficiary)
        const claimIds = claimList.map(claim => claim.code)

        return allocations
            .filter(v => v.allocationPayeeSource === payee && claimIds.indexOf(v.selectedClaim) > -1)
            .map(item => {
                if (
                    item.claimModelName &&
                    item.allocationPeriod &&
                    [ClaimTypesMap.LTD, ClaimTypesMap.SMP, ClaimTypesMap.STD].includes(item.claimModelName) &&
                    typeof item.allocationPeriod !== 'string'
                ) {
                    item.allocationPeriod = JSON.stringify(item.allocationPeriod)
                }
                item.eobRemarkCodes = item?.manualEOBRemarks?.length
                    ? item?.manualEOBRemarks?.filter(n => !n?.code?.includes(AUTO_EOB)).map(s => s.code || '')
                    : []
                if (item.eobRemarkCodes?.includes(OTHER)) {
                    const otherRemark = item?.manualEOBRemarks?.find(a => a.code === OTHER)
                    item.otherEOBMessage = otherRemark?.message || ''
                    item.isOtherEOBSelected = true
                }
                return {...item, key: item.coverageType === ReserveType.COVERAGE ? item.allocationSource : item._key.id}
            })
    }

    hasMatchingRepresentativeRegistryId = (value: string): boolean => {
        return (
            this.props.beneficiaries?.some(
                oneBe =>
                    oneBe?.partyRole?.representativeRegistryId === value && !oneBe?.partyRole?.relationshipToInsuredCd
            ) ?? false
        )
    }

    getOnBehalfOfOptions = (value: string) => {
        if (this.hasMatchingRepresentativeRegistryId(value)) {
            let paymentSettlements = [] as CapPaymentScheduleSettlementInfoInput[]
            let paymentExpenses = [] as CapPaymentScheduleExpensesInput[]
            const {currentPaymentTemplate, createOrUpdatePayment} = this.props.store
            if (currentPaymentTemplate) {
                paymentSettlements = currentPaymentTemplate.buildPaymentScheduleInput?.settlements || []
                paymentExpenses = currentPaymentTemplate.buildPaymentScheduleInput?.expenses || []
            }
            let currentOnBehalfOfOption
            if (createOrUpdatePayment === CreateOrUpdate.CREATE) {
                currentOnBehalfOfOption = this.filterBeneficiaries(this.props.beneficiaries, value)
            } else {
                currentOnBehalfOfOption = this.filterBeneficiaries(this.props.beneficiaries, value).filter(
                    v =>
                        paymentSettlements.findIndex(
                            template =>
                                template.payeeDetails?.representBeneficiary?._uri === v?.code &&
                                value === template.payeeDetails?.payee?._uri
                        ) > -1 ||
                        paymentExpenses.findIndex(
                            template =>
                                template.representBeneficiary?._uri === v?.code &&
                                value === template.payeeDetails?.payee?._uri
                        ) > -1
                )
            }
            this.props.store.setShowOnBehalfOf(true)
            this.props.store.setOnBehalfOfOptions(currentOnBehalfOfOption as SelectOptionProps[])
        } else {
            this.props.store.setShowOnBehalfOf(false)
        }
    }

    filterBeneficiaries = (beneficiaries: any[], choseValue: string) => {
        const choseEntityRootId = EntityLink.from(choseValue ?? '').rootId
        const seen = new Set()
        return beneficiaries
            ?.filter(
                item =>
                    item.partyRole?.representativeRegistryId === choseValue &&
                    item.customer?._key?.rootId !== choseEntityRootId
            )
            ?.map(oneInd => {
                const {registryTypeId, firstName, lastName, legalName} =
                    oneInd.customer._modelName === 'INDIVIDUALCUSTOMER'
                        ? oneInd.customer.details.person
                        : oneInd.customer.details.legalEntity
                if (seen.has(registryTypeId)) {
                    return null
                }
                seen.add(registryTypeId)
                const displayValue =
                    oneInd.customer._modelName === 'INDIVIDUALCUSTOMER' ? `${firstName} ${lastName}` : `${legalName}`
                return {
                    code: registryTypeId,
                    displayValue
                }
            })
            ?.filter(Boolean)
    }

    /**
     * @description payee change and onBehalfOf change common handle
     * @param value Beneficiary Registry ID
     * @param isGuardian
     */
    handleBeneficiaryChange = (value: string, isGuardian: boolean) => {
        const {allocations, currentPayee} = this.state
        const {createOrUpdatePayment, representBeneficiary} = this.props.store

        const filteredAllocations = this.formatCurrentAllocations(
            allocations,
            isGuardian ? currentPayee! : value,
            isGuardian ? value : ''
        )

        // store.representBeneficiary will be cleared when payee change
        // so use representBeneficiary to filter if isGuardian or not when user just change payee
        const resultAllocations = isGuardian
            ? filteredAllocations.filter(v => v.representBeneficiary === value)
            : filteredAllocations.filter(v => v.representBeneficiary === representBeneficiary)
        const tempAllocations = createOrUpdatePayment === CreateOrUpdate.CREATE ? [] : resultAllocations
        if (tempAllocations.length === 0) {
            tempAllocations.push({
                isInterestOnly: false,
                selectedClaim: this.resetSelectedClaim(),
                coverageType: undefined,
                allocationPayeeSource: currentPayee,
                allocationPayeeProvider: this.checkPayeeProvider(value),
                representBeneficiary: isGuardian ? value : '',
                key: v4(),
                claimModelName: this.getCurrentClaimModelName()
            })
        }
        return tempAllocations
    }

    payeeChange = (form: any, value: string, id?: string) => {
        const {updateCurrentPayee, updateCurrGuardian, setOriginalPaymentDetailStepAllocations} = this.props.store
        const filteredAllocations = this.loadOriginalPaymentDetailStepAllocations(value)
        setOriginalPaymentDetailStepAllocations(filteredAllocations)
        updateCurrentPayee(value)
        updateCurrGuardian('')
        this.setState(
            {
                currentPayee: value,
                currentBehalfOfPayee: undefined
            },
            () => {
                const tempAllocations = this.handleBeneficiaryChange(value, false)
                this.setState({
                    currentAllocations: tempAllocations
                })
            }
        )
        // if payee choosed value is guardian, show on behalf of dropdown
        if (id !== undefined) {
            this.getOnBehalfOfOptions(value)
        }
    }

    /**
     * On behalf of, after selecting the value, set the value of currentBehalfOfPayee as allocationPayeeSource
     * */
    onBehalfOfChange = (form: any, value: string) => {
        const {updateCurrGuardian} = this.props.store
        const tempAllocations = this.handleBeneficiaryChange(value, true)

        this.setState({
            currentAllocations: tempAllocations,
            currentBehalfOfPayee: value
        })
        updateCurrGuardian(value)
    }

    replaceIfHasGuardian = (filterPayeeList: (IndividualCustomer | OrganizationCustomer)[]) => {
        const hasRepresentativeRegistryId = this.props.beneficiaries?.some(
            item => item?.partyRole?.representativeRegistryId
        )
        if (hasRepresentativeRegistryId) {
            const matchingRepresentatives = new Set(
                this.props.beneficiaries
                    ?.filter(item => item?.partyRole?.representativeRegistryId)
                    .map(item => item.partyRole?.registryId)
            )
            return filterPayeeList?.filter(oneInd => {
                const currentRegistryTypeId = (oneInd as IndividualCustomer)?.details?.person?.registryTypeId
                const hasRepresentative = oneInd?.role?.representativeRegistryId
                return !(matchingRepresentatives.has(currentRegistryTypeId) && hasRepresentative)
            })
        }
        return filterPayeeList
    }

    getPayeeOptions = (): SelectOptionProps[] => {
        const {currentPaymentTemplate, createOrUpdatePayment, updatePayeeInfo} = this.props.store

        const payeeList = [...this.props.parties, ...this.props.beneficiaries]
            .map(v => {
                return {
                    ...v.customer,
                    role: v.partyRole
                }
            })
            .filter(Boolean) as [IndividualCustomer | OrganizationCustomer]
        if (this.props.subjectOfClaim) {
            payeeList.push(this.props.subjectOfClaim)
        }

        if (this.props.employer) {
            payeeList.push(this.props.employer)
        }

        if (this.props.member) {
            payeeList.push(this.props.member)
        }

        let paymentSettlements = [] as CapPaymentScheduleSettlementInfoInput[]
        let paymentExpenses = [] as CapPaymentScheduleExpensesInput[]
        if (currentPaymentTemplate) {
            paymentSettlements = currentPaymentTemplate.buildPaymentScheduleInput?.settlements || []
            paymentExpenses = currentPaymentTemplate.buildPaymentScheduleInput?.expenses || []
        }

        let filterPayeeList: (IndividualCustomer | OrganizationCustomer)[]
        if (createOrUpdatePayment === CreateOrUpdate.CREATE) {
            filterPayeeList = this.replaceIfHasGuardian(payeeList)
        } else {
            filterPayeeList = payeeList.filter(
                v =>
                    paymentSettlements.findIndex(
                        template =>
                            template.payeeDetails?.payee?._uri ===
                                (v as IndividualCustomer).details?.person?.registryTypeId ||
                            template.payeeDetails?.payee?._uri ===
                                (v as OrganizationCustomer).details?.legalEntity?.registryTypeId
                    ) > -1 ||
                    paymentExpenses.findIndex(
                        template =>
                            template.payee?._uri === (v as IndividualCustomer).details?.person?.registryTypeId ||
                            template.payee?._uri === (v as OrganizationCustomer).details?.legalEntity?.registryTypeId
                    ) > -1
            )
        }
        updatePayeeInfo(payeeList)
        return collectPayee(filterPayeeList)
    }

    descChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        const {buildPaymentScheduleInput} = this.state
        Object.assign(buildPaymentScheduleInput, {
            description: eventValue
        })
        this.setState({buildPaymentScheduleInput})
    }

    getIndexFormName = (name: string): number => {
        return Number(name.replace(/[^\d]/g, ''))
    }

    isBeneficiaryPayeeSelected = selectedPayee => {
        const beneficiariesPayeeList = this.props.beneficiaries.map(v => v.customer).filter(Boolean) as [
            IndividualCustomer | OrganizationCustomer
        ]
        const beneficiaryRegistryTypeIds = [] as string[]
        const individualRegistryTypeId = customer => (customer as IndividualCustomer)?.details?.person?.registryTypeId
        const organizationRegistryTypeId = customer =>
            (customer as OrganizationCustomer)?.details?.legalEntity?.registryTypeId
        beneficiariesPayeeList.forEach(v => {
            if (v._modelName === customerModelName.IndividualCustomer && individualRegistryTypeId(v)) {
                beneficiaryRegistryTypeIds.push(individualRegistryTypeId(v)!)
            } else if (v._modelName === customerModelName.OrganizationCustomer && organizationRegistryTypeId(v)) {
                beneficiaryRegistryTypeIds.push(organizationRegistryTypeId(v)!)
            }
        })
        return beneficiaryRegistryTypeIds.includes(selectedPayee)
    }

    getSettlementUri = settlement =>
        `gentity://CapSettlement/${settlement._modelName}//${settlement._key.rootId}/${settlement._key.revisionNo}`

    formatClaimOptions = (
        claimList: CapLoss[],
        currentPayee?: string,
        representBeneficiary?: string
    ): SelectInputOptions[] => {
        if (currentPayee && this.isBeneficiaryPayeeSelected(currentPayee)) {
            const lifeClaims = this.props.lifeClaims
            const filterLifeClaim = claim => {
                const currentLifeClaim = lifeClaims.filter(v => v.lossNumber === claim.lossNumber)?.[0]
                const currentLifeClaimPayee = [] as string[]
                const currentLifeGuardianPayee = [] as string[]
                currentLifeClaim?.lossDetail?.beneficiaryRole?.forEach(v => {
                    if (v.representativeRegistryId) {
                        currentLifeGuardianPayee.push(v.representativeRegistryId)
                    }
                    if (v.registryId) {
                        currentLifeClaimPayee.push(v.registryId)
                    }
                })
                if (representBeneficiary) {
                    return (
                        currentLifeGuardianPayee.indexOf(currentPayee) > -1 &&
                        currentLifeClaimPayee.indexOf(representBeneficiary) > -1
                    )
                }
                return currentLifeClaimPayee.indexOf(currentPayee) > -1
            }
            // filter claims with selected payee
            claimList = claimList.filter(claim => filterLifeClaim(claim))
        }
        return Array.from(
            new Set(
                claimList
                    .map(v => {
                        return {
                            code: v._key.rootId,
                            displayValue: v.lossNumber || ''
                        }
                    })
                    .filter(v => Boolean(v.displayValue))
                    .sort((a, b) => {
                        const aNum = Number(a.displayValue.replace(/[^\d]/g, ''))
                        const bNum = Number(b.displayValue.replace(/[^\d]/g, ''))
                        return bNum - aNum
                    }) || []
            )
        )
    }

    getClaimOptions = (): SelectOptionProps[] => {
        const {currentPayee, currentBehalfOfPayee} = this.state
        let representBeneficiary = currentBehalfOfPayee as string | undefined
        if (this.props.store.hasEnterStep2) {
            representBeneficiary = this.props.store.representBeneficiary
        }
        return this.formatClaimOptions(toJS(this.props.store.associatedClaims), currentPayee, representBeneficiary)
    }

    getAccumulatorElement = (accumulator: OptionValue[], element: OptionValue) =>
        ['STDCore', 'SMPCore', 'LTDCore', 'LeaveCore'].includes(element.displayValue)
            ? [element, ...accumulator]
            : [...accumulator, element]

    constructCoverage = (path: string, selectedClaimSettlements: Settlements[]): OptionValue[] => {
        return selectedClaimSettlements.reduce((acc, el) => {
            if (!el.policy?.[path]?.capCoverageInfo) {
                return acc
            }

            return [
                ...acc,
                el.policy?.[path].capCoverageInfo
                    .map(v => ({
                        displayValue: getTranslatedCoverageName(el.settlementResult?.coverageCd),
                        code: this.getSettlementUri(el)
                    }))
                    .reduce(
                        (accumulator, element) => this.getAccumulatorElement(accumulator, element),
                        [] as OptionValue[]
                    )[0]
            ]
        }, [] as OptionValue[])
    }

    getDateRangeFormat = (dateRange: Period): string =>
        dateRange
            ? `${dateRange.startDate ? dateUtils(dateRange.startDate).render : ''}~${
                  dateRange.endDate ? dateUtils(dateRange.endDate).render : ''
              }`
            : ''

    getDefaultSelectedClaimSettlements = (
        selectedClaimSettlements: Settlements[],
        selectedClaim: string
    ): SelectInputOptions[] => {
        const {currentPayee} = this.state ?? {}
        if (this.isBeneficiaryPayeeSelected(currentPayee)) {
            const lifeClaims = this.props.lifeClaims
            const currentLifeClaim = lifeClaims.filter(v => v._key?.rootId === selectedClaim)?.[0]
            const currentCoverageIds = [] as string[]
            // The guardian payee corresponds to the representativeRegistryId, so this judgment needs to be added
            // currentBehalfOfPayee will lost when return step 1
            const currentBeneficiaryRole = currentLifeClaim?.lossDetail?.beneficiaryRole?.filter(v => {
                if (this.props.store.representBeneficiary) {
                    return v.registryId === this.props.store.representBeneficiary
                }
                if (currentPayee && !this.props.store.representBeneficiary) {
                    return v.registryId === currentPayee
                }
                return false
            })

            currentBeneficiaryRole?.forEach(element => {
                element.beneficiaryDesignations?.forEach(v => {
                    currentCoverageIds.push(v.coverageId)
                })
            })

            const filterLifeClaimSettlements = selectedCoverageId => {
                return currentCoverageIds.indexOf(selectedCoverageId) > -1
            }
            // filter settlements with selected claim's coverageId
            selectedClaimSettlements = selectedClaimSettlements.filter(
                v => filterLifeClaimSettlements(this.getSettlementUri(v)) && !v.settlementDetail?.isCancelled
            )
        }
        return selectedClaimSettlements
            .filter(v => v.settlementDetail?._modelName !== SettlementModelName.PREMIUMWAIVER_SETTLEMENT)
            .map(v => {
                const dateRange = v.settlementDetail?.dateRange
                const incidentDate = v.settlementDetail?.incidentDate
                const dataRangeFormat = this.getDateRangeFormat(dateRange)
                const incidentDateFormat = incidentDate
                    ? moment(incidentDate).format(t('cap-core:default_date_format'))
                    : ''

                if (dateRange || incidentDate) {
                    return {
                        displayValue: v?.claimCoverageName
                            ? `${getTranslatedCoverageName(v?.claimCoverageName, v?.claimCoveragePrefix)}, ${
                                  incidentDate ? incidentDateFormat : dataRangeFormat
                              } `
                            : '',
                        code: this.getSettlementUri(v)
                    }
                }
                return {
                    displayValue: getTranslatedCoverageName(v?.claimCoverageName),
                    code: this.getSettlementUri(v)
                }
            })
    }

    getCoveragesOptionsBaseClaim = (selectedClaim: string): SelectInputOptions[] => {
        if (!selectedClaim) {
            return []
        }

        const selectedClaimSettlements = toJS(this.props.store.associatedSettlements).filter(
            v => v.associatedClaimRootId === selectedClaim
        )

        switch (selectedClaimSettlements[0]?._modelName) {
            case SettlementModelName.STD_SETTLEMENT:
                return selectedClaimSettlements[0]?.policy?.productCd === 'STDIndividual'
                    ? this.constructCoverage('stdCertInfo', selectedClaimSettlements)
                    : this.constructCoverage('stdMasterInfo', selectedClaimSettlements)
            case SettlementModelName.SMP_SETTLEMENT:
                return this.constructCoverage('smpMasterInfo', selectedClaimSettlements)
            case SettlementModelName.LTD_SETTLEMENT:
                return selectedClaimSettlements[0]?.policy?.productCd === 'LTDIndividual'
                    ? this.constructCoverage('ltdCertInfo', selectedClaimSettlements)
                    : this.constructCoverage('ltdMasterInfo', selectedClaimSettlements)
            case SettlementModelName.LEAVE_SETTLEMENT:
                return selectedClaimSettlements[0]?.policy?.productCd === 'LeaveIndividual'
                    ? this.constructCoverage('leaveCertInfo', selectedClaimSettlements)
                    : this.constructCoverage('leaveMasterInfo', selectedClaimSettlements)
            default:
                return this.getDefaultSelectedClaimSettlements(selectedClaimSettlements, selectedClaim)
        }
    }

    claimChange = (form: any, value: string, id?: string) => {
        const {associatedClaims} = this.props.store
        const allocations = form.getState().values.allocations
        const index = this.getIndexFormName(id ?? '')
        const curClaimModelName = associatedClaims.filter(v => v._key.rootId === value)[0]?._modelName
        const curClaimType = associatedClaims.filter(v => v._key.rootId === value)[0]?.claimType
        // set current row claim modelName
        allocations[index].claimModelName = Object.keys(LifeClaimTypesMap).includes(curClaimType)
            ? curClaimType
            : curClaimModelName
        allocations[index].allocationSource = undefined
        this.setState({currentAllocations: allocations})
        // reset current row selected coverage
        if (this.isAbsenceFlag(allocations[index]?.claimModelName)) {
            this.coverageChange(form, '', id)
        }
        form.change(`allocations[${index}].allocationSource`, '')
    }

    getCoverageOptions = (form: any, id?: string): SelectOptionProps[] => {
        const formValue = form.getState().values
        const {allocations} = formValue
        const index = this.getIndexFormName(id ?? '')
        return this.getCoveragesOptionsBaseClaim(allocations[index]?.selectedClaim)
    }

    collectAllocationPeriod = approvalPeriods => {
        return (
            approvalPeriods
                .filter(v => v.approvalStatus === 'Approved')
                .map(s => {
                    const startDate = dateUtils(s.approvalPeriod.startDate).render
                    const endDate = dateUtils(s.approvalPeriod.endDate).render
                    const allocationPeriod = [
                        dateUtils(s.approvalPeriod.startDate).toMoment,
                        dateUtils(s.approvalPeriod.endDate).toMoment
                    ]
                    return {
                        code: JSON.stringify(allocationPeriod),
                        displayValue: `${startDate}-${endDate}`
                    }
                }) || []
        )
    }

    getAllocationPeriodOptions = (form: any, id?: string): SelectOptionProps[] => {
        const associatedSettlements = toJS(this.props.store.associatedSettlements) || []
        const formValue = form.getState().values
        const {allocations} = formValue
        const index = this.getIndexFormName(id ?? '')
        const allocationSource = allocations?.[index]?.allocationSource || ''
        const currentSettlement = associatedSettlements.find(
            v => allocationSource && allocationSource.includes(v._key.rootId)
        )

        return this.collectAllocationPeriod(currentSettlement?.settlementResult?.approvalPeriods || [])
    }

    isAbsenceFlag = claimModelName => {
        return (
            [ClaimTypesMap.STD, ClaimTypesMap.SMP, ClaimTypesMap.LTD, ClaimTypesMap.Leave].indexOf(claimModelName) > -1
        )
    }

    coverageChange = (form: any, value: string, id?: string) => {
        const allocations = form.getState().values.allocations
        const index = this.getIndexFormName(id ?? '')
        const selectedSettlementId = value?.split('/')[5]
        allocations[index].allocationSource = value
        const selectedSettlement = toJS(this.props.store.associatedSettlements).filter(
            v => v._key.rootId === selectedSettlementId
        )?.[0]
        const isAbsence = this.isAbsenceFlag(allocations[index]?.claimModelName)
        if (isAbsence) {
            allocations[index].allocationPeriod = undefined
            form.change(`allocations[${index}].allocationPeriod`, '')
        }
        const dateRange = selectedSettlement?.settlementDetail?.dateRange
        const grossAmountMode = selectedSettlement?.coverageBasedConfiguration?.grossAmountMode
        if (grossAmountMode && dateRange) {
            const periodToMoment = [
                dateRange?.startDate ? dateUtils(dateRange?.startDate).toMoment : undefined,
                dateRange?.endDate ? dateUtils(dateRange?.endDate).toMoment : undefined
            ] as any[]
            allocations[index].allocationPeriod = periodToMoment
            form.change(`allocations[${index}].allocationPeriod`, periodToMoment)
            allocations[index].grossAmountMode = grossAmountMode
        } else {
            allocations[index].allocationPeriod = undefined
            form.change(`allocations[${index}].allocationPeriod`, undefined)
            allocations[index].grossAmountMode = undefined
        }
        this.setState({
            currentAllocations: allocations
        })
    }

    coverageTypeChange = (form, value, id) => {
        const allocations = form.getState().values.allocations
        const index = this.getIndexFormName(id ?? '')
        allocations[index].coverageType = value
        this.setState({
            currentAllocations: allocations
        })
    }

    eobRemarkCodesChange = (form, value, id) => {
        const allocations = form.getState().values.allocations
        const index = this.getIndexFormName(id ?? '')
        allocations[index].eobRemarkCodes = value
        allocations[index].isOtherEOBSelected = value.includes(OTHER)
        allocations[index].otherEOBMessage = value.otherEOBMessage || ''
        this.setState({
            currentAllocations: allocations
        })
    }

    updateAllocations = (name: string, value: any, prefix: string) => {
        const {currentAllocations} = this.state
        const index = this.getIndexFormName(prefix)
        Object.assign(currentAllocations[index], {[name]: value})
        this.setState({currentAllocations})
    }

    expenseChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        this.updateAllocations('allocationPaymentAmount', eventValue, prefix ?? '')
    }

    exGratiaChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        this.updateAllocations('exGratiaAmount', eventValue, prefix ?? '')
    }

    exGratiaDescriptionChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        this.updateAllocations('exGratiaDescription', eventValue, prefix ?? '')
    }

    expenseDescriptionChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        this.updateAllocations('expenseDescription', eventValue, prefix ?? '')
    }

    otherEOBRemarkChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        this.updateAllocations('otherEOBMessage', eventValue, prefix ?? '')
    }

    interestPaymentChange = async (values: any, props: any, eventValue: any, prefix?: string): Promise<any> => {
        this.updateAllocations('isInterestOnly', eventValue, prefix ?? '')
    }

    periodCalendarChange = (form: any, value: Moment[] | string, id?: string) => {
        const allocations = form.getState().values.allocations
        const index = this.getIndexFormName(id ?? '')
        allocations[index].allocationPeriod = value
        this.setState({
            currentAllocations: allocations
        })
    }

    addNewAllocation = () => {
        const {currentAllocations, currentPayee} = this.state
        const newItem = {
            isInterestOnly: false,
            selectedClaim: this.resetSelectedClaim(),
            coverageType: undefined,
            allocationPayeeSource: currentPayee,
            allocationPayeeProvider: this.checkPayeeProvider(currentPayee),
            key: v4(),
            representBeneficiary: this.props.store.representBeneficiary,
            claimModelName: this.getCurrentClaimModelName()
        } as Allocation
        currentAllocations.push(newItem)
        this.setState({
            currentAllocations
        })
    }

    removeButtonClick = (form, id) => {
        const {currentAllocations, currentPayee} = this.state
        const allocations = form.getState().values.allocations
        const index = this.getIndexFormName(id ?? '')
        let allocationPeriod = allocations[index].allocationPeriod
        const key = allocations[index].key
        const isInterestOnly = allocations[index].isInterestOnly

        this.setState(
            {
                currentAllocations:
                    allocations.length > 1 || this.props.store.createOrUpdatePayment === CreateOrUpdate.UPDATE
                        ? currentAllocations.filter(item => {
                              let currentPeriod = item.allocationPeriod

                              if (item.allocationPeriod && allocationPeriod) {
                                  if (typeof item.allocationPeriod === 'string') {
                                      const parsedPeriod = JSON.parse(item.allocationPeriod)
                                      currentPeriod = [moment(parsedPeriod[0], parsedPeriod[1])]
                                  }
                                  if (typeof allocationPeriod === 'string') {
                                      const parsedPeriod = JSON.parse(allocationPeriod)
                                      allocationPeriod = [moment(parsedPeriod[0], parsedPeriod[1])]
                                  }
                                  return !(
                                      allocationPeriod[0].isSame(currentPeriod?.[0]) &&
                                      item.key === key &&
                                      item.isInterestOnly === isInterestOnly
                                  )
                              }
                              return !(item.key === key && item.isInterestOnly === isInterestOnly)
                          })
                        : ([
                              {
                                  isInterestOnly: false,
                                  selectedClaim: this.resetSelectedClaim(),
                                  coverageType: undefined,
                                  allocationPayeeSource: currentPayee,
                                  allocationPayeeProvider: this.checkPayeeProvider(currentPayee),
                                  key: v4(),
                                  claimModelName: this.getCurrentClaimModelName()
                              }
                          ] as Allocation[])
            },
            () => {
                if (allocations.length === 1) {
                    form.blur('allocations[0].selectedClaim')
                    form.blur('allocations[0].coverageType')
                }
                this.props.isAllocationExist(this.state.currentAllocations?.length > 0)
            }
        )
    }

    getRemoveButtonPopMsg = (form, id) => {
        return t('cap-core:payment_detail_allocation_remove_button_pop_message')
    }

    resetSelectedClaim = () => {
        if (this.props.loss._modelName === CAP_EVENT_CASE) {
            return ''
        }
        if (this.props.store.associatedClaims === undefined || this.props.store.associatedClaims.length === 0) {
            return ''
        }
        return this.props.loss._key.rootId
    }

    getCurrentClaimModelName = () => {
        if (this.props.loss._modelName === CAP_EVENT_CASE) {
            return ''
        }
        if (this.props.store.associatedClaims === undefined || this.props.store.associatedClaims.length === 0) {
            return ''
        }
        const claimType = this.props.loss.claimType
        const modelName = this.props.loss._modelName
        return Object.keys(LifeClaimTypesMap).includes(claimType) ? claimType : modelName
    }

    getCoverageTypesOptions = () => {
        const coverageTypesOptions = this.state.coverageTypesOptions
        const hasExpensePrivilege = hasAuthorities([Privileges.FINANCIAL_EXPENSE_ALLOCATION_TYPE])
        const hasExGratiaPrivilege = hasAuthorities([Privileges.FINANCIAL_EXGRATIA_ALLOCATION_TYPE])

        return coverageTypesOptions
            .map(option =>
                option.code === ReserveType.EXPENSE
                    ? {
                          ...option,
                          disabled: !hasExpensePrivilege
                      }
                    : option
            )
            .map(option =>
                option.code === ReserveType.EXGRATIA
                    ? {
                          ...option,
                          disabled: !hasExGratiaPrivilege
                      }
                    : option
            )
    }

    getEobRemarkOptions = () => {
        return (
            this.props.eobRemarkValues?.map(s => ({
                ...s,
                displayValue: `${s.code} - ${s.displayValue}`
            })) || []
        )
    }

    isBenefitStatusChanged = (
        initialBuildPaymentScheduleInputSettlementsSnapshot: CapPaymentScheduleSettlementInfoInput[],
        currentPayee: string | undefined
    ): boolean => {
        if (
            initialBuildPaymentScheduleInputSettlementsSnapshot !== undefined &&
            this.props.store.allAssociatedSettlements !== undefined &&
            this.props.store.createOrUpdatePayment === CreateOrUpdate.UPDATE
        ) {
            return this.props.store.allAssociatedSettlements
                .filter(associatedSettlement =>
                    initialBuildPaymentScheduleInputSettlementsSnapshot
                        .filter(settlement => settlement.payeeDetails?.payee?._uri === currentPayee)
                        .some(
                            item =>
                                EntityLink.from(item.uri || '').rootId === associatedSettlement._key.rootId &&
                                EntityLink.from(item.uri || '').modelName === associatedSettlement._modelName
                        )
                )
                .some(
                    filteredSettlement =>
                        filteredSettlement.settlementResult?.eligibilityEvaluationCd !== 'ELIGIBLE' ||
                        filteredSettlement.settlementDetail?.isCancelled
                )
        }
        return false
    }

    render(): React.ReactNode {
        const {currentAllocations, buildPaymentScheduleInput, currentPayee, initialBuildPaymentScheduleInputSnapshot} =
            this.state
        const onBehalfOfOptionsCount = this.props.store.onBehalfOfOptions?.length
        return (
            <UIEngine
                {...editorConfig}
                formId={PAYMENT_DETAIL_FORM_ID}
                config={config}
                onNext={this.props.onNext}
                initialValues={{
                    selectCustomPropsMap: new Map<string, SelectCustomProps>([
                        ['payee', {onChange: this.payeeChange, getOptions: this.getPayeeOptions}],
                        ['selectedClaim', {onChange: this.claimChange, getOptions: this.getClaimOptions}],
                        ['allocationSource', {onChange: this.coverageChange, getOptions: this.getCoverageOptions}],
                        [
                            'allocationPeriod',
                            {onChange: this.periodCalendarChange, getOptions: this.getAllocationPeriodOptions}
                        ],
                        ['coverageType', {onChange: this.coverageTypeChange, getOptions: this.getCoverageTypesOptions}],
                        [
                            'eobRemarkCodes',
                            {
                                onChange: this.eobRemarkCodesChange,
                                getOptions: this.getEobRemarkOptions,
                                mode: 'multiple'
                            }
                        ]
                    ]),
                    rangePickerCustomPropsMap: new Map<string, RangePickerCustomProps>([
                        [
                            'allocationPeriod',
                            {onCalendarChange: this.periodCalendarChange, onChange: this.periodCalendarChange}
                        ]
                    ]),
                    removeButtonCustomerPropsMap: new Map<string, RemoveButtonProps>([
                        ['button', {onClick: this.removeButtonClick, getPopMsg: this.getRemoveButtonPopMsg}]
                    ]),
                    payee: currentPayee,
                    representBeneficiary: this.props.store.representBeneficiary,
                    manualCheck: false,
                    pullCheck: false,
                    buildPaymentScheduleInput,
                    allocations: currentAllocations,
                    isClaimPayment: this.props.loss._modelName !== CAP_EVENT_CASE,
                    isCheckHide: true,
                    hasExpensePrivilege: hasAuthorities([Privileges.FINANCIAL_EXPENSE_ALLOCATION_TYPE]),
                    hasExGratiaPrivilege: hasAuthorities([Privileges.FINANCIAL_EXGRATIA_ALLOCATION_TYPE]),
                    isBenefitStatusChanged: this.isBenefitStatusChanged(
                        initialBuildPaymentScheduleInputSnapshot?.settlements,
                        currentPayee
                    )
                }}
                apiServices={{
                    descChange: this.descChange,
                    expenseChange: this.expenseChange,
                    exGratiaChange: this.exGratiaChange,
                    interestPaymentChange: this.interestPaymentChange,
                    expenseDescriptionChange: this.expenseDescriptionChange,
                    otherEOBRemarkChange: this.otherEOBRemarkChange,
                    exGratiaDescriptionChange: this.exGratiaDescriptionChange
                }}
                slotComponents={{
                    PAYMENT_METHOD_VALIDATION: () => this.renderError(),
                    ON_BEHALF_OF: () => (
                        <OnBehalfOfSlot
                            count={onBehalfOfOptionsCount}
                            showOnBehalfOf={this.props.store.showOnBehalfOf}
                            options={this.props.store.onBehalfOfOptions}
                            onChange={this.onBehalfOfChange}
                        />
                    )
                }}
            >
                {currentPayee && (
                    <div>
                        <Button
                            type='link'
                            icon='action-add-medium'
                            onClick={this.addNewAllocation}
                            disabled={this.props.store.createOrUpdatePayment === CreateOrUpdate.UPDATE}
                            className={PAYMENT_DETAIL_ADD_ALLOCATION_BUTTON}
                        >
                            {t('cap-core:payment_detail_add_allocation_button')}
                        </Button>
                    </div>
                )}
                {this.renderError()}
                {this.props.controls}
            </UIEngine>
        )
    }

    private renderError(): React.ReactElement | null {
        const {coverageDuplicatedFlag, periodDuplicatedFlag, notOpenClaimFlag, paymentMethodErrorMsg} = this.props
        const errorMsg = (
            <>
                {coverageDuplicatedFlag && <p>{t('cap-core:duplicated_coverage')}</p>}
                {periodDuplicatedFlag && <p>{t('cap-core:duplicated_allocation_period')}</p>}
                {notOpenClaimFlag && <p>{t('cap-core:not_open_claim_payment')}</p>}
            </>
        )
        return (
            <div className={PAYMENT_DETAIL_ADD_ERROR_CONTAINER}>
                {paymentMethodErrorMsg ? (
                    <Alert key={v4()} type='warning' message={paymentMethodErrorMsg} closable={false} />
                ) : null}
                {(coverageDuplicatedFlag || periodDuplicatedFlag || notOpenClaimFlag) && (
                    <Alert type='error' message={errorMsg} closable={false} />
                )}
            </div>
        )
    }
}
