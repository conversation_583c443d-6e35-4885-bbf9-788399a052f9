/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import React from 'react'
import {render} from '@testing-library/react'
import {Observable} from 'rxjs'

import {authentication} from '@eisgroup/auth'
import {initLocalization} from '@eisgroup/cap-core/test/support/TestUtils'
import {Right} from '@eisgroup/data.either'
import {LocalizationUtils, resources} from '@eisgroup/i18n'
import {IoC} from '@eisgroup/ioc'

import {ClaimLossListViewComponent} from '../../src/claim-loss-list/ClaimLossListView'

const authServiceMock = {
    hasAuthorities: vi.fn()
}

vi.mock('@eisgroup/cap-core', async importOriginal => ({
    ...(await importOriginal()),
    hasAuthorities: vi.fn(() => authServiceMock.hasAuthorities())
}))

beforeAll(async () => {
    await initLocalization
    LocalizationUtils.addResourceBundles(resources)
    type AuthService = Partial<authentication.AuthenticationService>
    const authService: AuthService = {
        encodeRequest: vi.fn().mockImplementation(item => Observable.of(Right(item))),
        hasAuthorities: vi.fn().mockReturnValue(true)
    }
    IoC.get<{init: (s: AuthService) => void}>(authentication.TYPES.AuthenticationFacade).init(authService)
})

describe('ClaimLossListViewComponent', () => {
    it('renders without crashing', () => {
        const mockOnSearch = vi.fn()
        const mockData = [
            {
                id: 1,
                lossNumber: '123',
                state: 'Open',
                _modelName: 'TestModel'
            }
        ]

        const {container} = render(<ClaimLossListViewComponent onSearch={mockOnSearch} data={mockData} />)

        expect(container).toBeTruthy()
    })
})
