/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.lifeloss.impl;

import static java.lang.String.format;

import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import javax.inject.Inject;

import cap.adjuster.services.eventcase.converters.CapAdjusterGenericSettlementConverter;
import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import cap.adjuster.services.lifeloss.CapAdjusterLifeLossService;
import dataproviders.relationships.dto.GenesisTripletDTO;
import dataproviders.dto.CapSettlementDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;

public class CapAdjusterLifeLossServiceImpl implements CapAdjusterLifeLossService {

    private static final String CAP_SETTLEMENT = "CapSettlement";
    private static final String BASED_ON = "based_on";
    private static final String LOSS_URI = "gentity://CapLoss/%s//%s/%s";

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private CapAdjusterGenericSettlementConverter<CapSettlementDTO, CapGenericSettlement> genericSettlementConverter;

    /**
     * {@inheritDoc}
     */
    @Override
    public CompletionStage<List<CapGenericSettlement>> getLossSettlements(String rootId, Integer revisionNo, String modelName) {
        return getSettlementsAssociatedWithLoss(rootId, revisionNo, modelName)
            .thenApply(capLifeSettlements -> capLifeSettlements.stream()
                .map(genericSettlementConverter::convertToApiDTO)
                .collect(Collectors.toList()));
    }

    /**
     * Get Settlements Associated With Loss
     *
     * @param rootId Loss root id
     * @param revisionNo Loss revision no
     * @param modelName Loss model name
     * @return
     */
    @SuppressWarnings("unchecked")
    private CompletionStage<List<? extends CapSettlementDTO>> getSettlementsAssociatedWithLoss(String rootId, Integer revisionNo, String modelName) {
        String lossUri = format(LOSS_URI, modelName, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(lossUri, BASED_ON, CAP_SETTLEMENT);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapSettlementDTO.class);
    }

    @Inject
    public void setSearchRelationshipsDataProvider(GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    public void setGenericSettlementConverter(
        CapAdjusterGenericSettlementConverter<CapSettlementDTO, CapGenericSettlement> genericSettlementConverter) {
        this.genericSettlementConverter = genericSettlementConverter;
    }
}
