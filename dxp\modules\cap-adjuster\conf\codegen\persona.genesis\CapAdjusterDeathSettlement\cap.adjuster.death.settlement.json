{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.death.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Death Settlements API", "version": "1", "title": "CAP Adjuster: Death Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-death-settlements", "description": "CAP Adjuster: Death Settlements API"}], "paths": {"/losses-death-settlements/{rootId}/{revisionNo}": {"get": {"summary": "Get death settlement by rootId and revisionNo", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}, "/losses-death-settlements/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for death settlement", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}, "/losses-death-settlements/draft": {"post": {"summary": "Init death settlement", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/command/initSettlement", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}, "/losses-death-settlements/adjudicate": {"post": {"summary": "Adjudicate death settlement", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/command/adjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}, "put": {"summary": "Readjudicate death settlement", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}, "/losses-death-settlements/approve": {"post": {"summary": "Approve death settlement", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/command/approveSettlement", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}, "/losses-death-settlements/disapprove": {"post": {"summary": "Disapprove death settlement", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/command/disapproveSettlement", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}, "/losses-death-settlements/adjudication-input": {"post": {"summary": "Death settlement adjudication input", "x-dxp-path": "/api/capsettlement/DeathSettlement/v1/transformation/CapDeathSettlementAdjudicationInput", "tags": ["/cap-adjuster/v1/losses-death-settlements"]}}}}