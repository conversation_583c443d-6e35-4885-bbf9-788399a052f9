{"swagger": "2.0", "x-dxp-spec": {"imports": {"accidental.dismemberment": {"schema": "integration.cap.accidental.dismemberment.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Accidental Dismemberment Loss API", "version": "1", "title": "CAP Adjuster: Accidental Dismemberment Loss API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-accidental-dismemberment", "description": "CAP Adjuster: Accidental Dismemberment Loss API"}], "paths": {"/losses-accidental-dismemberment/{rootId}/{revisionNo}": {"get": {"summary": "Get accidental dismemberment loss by rootId and revisionNo", "x-dxp-path": "/api/caploss/AccidentalDismembermentLoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment"]}}, "/losses-accidental-dismemberment/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for accidental dismemberment loss", "x-dxp-path": "/api/caploss/AccidentalDismembermentLoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment"]}}, "/losses-accidental-dismemberment/draft": {"post": {"summary": "Init accidental dismemberment loss", "x-dxp-path": "/api/caploss/AccidentalDismembermentLoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment"]}}, "/losses-accidental-dismemberment": {"post": {"summary": "Create accidental dismemberment loss", "x-dxp-path": "/api/caploss/AccidentalDismembermentLoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment"]}, "put": {"summary": "Update accidental dismemberment loss", "x-dxp-method": "post", "x-dxp-path": "/api/caploss/AccidentalDismembermentLoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-accidental-dismemberment"]}}}}