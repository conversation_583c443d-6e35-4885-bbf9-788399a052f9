/**
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {action, observable} from 'mobx'
import {DiagnosisInformationEntity, eventCaseService, IcdCodeEntry} from '@eisgroup/cap-services'
import {opt} from '@eisgroup/common-types'
import {BusinessTypes} from '@eisgroup/cap-disability-models'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'

export interface IcdCodeStore extends BaseRootStore {
    icdList: IcdCodeEntry[]
    addIcdToList: (icd: IcdCodeEntry) => void
    loadIcdCodeEntries: (icdCodes: string[]) => void
    updateDiagnoses: <T extends BusinessTypes.CapDisabilityClaim>(
        diagnoses: DiagnosisInformationEntity[],
        claim: T
    ) => T
    handleUpdateDiagnoses: <T>(data: {item?: T; index: number}, claimDiagnoses?: T[]) => T[]
    icdTableInEdit: boolean
    updateIcdTableInEdit: (flag: boolean) => void
}

export class IcdCodeStoreImpl extends BaseRootStoreImpl implements IcdCodeStore {
    @observable icdList: IcdCodeEntry[] = observable([])

    @observable icdTableInEdit = false

    @action
    updateIcdTableInEdit = (flag: boolean) => {
        this.icdTableInEdit = flag
    }

    @action
    loadIcdCodeEntries = (icdCodes: string[]) => {
        const notLoadedIcdCodes = icdCodes.filter(code => !this.icdList.some(v => v.code === code)) || []
        if (notLoadedIcdCodes.length) {
            this.call(() => eventCaseService.getIcdByCodes(notLoadedIcdCodes)).subscribe(result =>
                result.get().forEach(this.addIcdToList)
            )
        }
    }

    @action
    addIcdToList = (icd: IcdCodeEntry) => {
        this.icdList.push(icd)
    }

    updateDiagnoses = <T extends BusinessTypes.CapDisabilityClaim>(
        diagnoses: DiagnosisInformationEntity[],
        claim: T
    ): T => {
        return {
            ...claim,
            lossDetail: {
                ...claim.lossDetail!,
                diagnoses
            }
        }
    }

    handleUpdateDiagnoses = <T>(data: {item?: T; index: number}, claimDiagnoses?: T[]): T[] => {
        const diagnoses = opt(claimDiagnoses)
            .map(v => [...v])
            .orElse([])
        if (data.index !== -1) {
            diagnoses[data.index] = data.item!
        } else {
            diagnoses.push(data.item!)
        }
        return diagnoses.filter(Boolean)
    }
}
