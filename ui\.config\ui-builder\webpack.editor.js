const merge = require('webpack-merge')
const path = require('path')
const customComponentWebpackConfig = require('@eisgroup/ui-desktop/config/webpack.custom-component')
const getDevWebpackConfig = require('@eisgroup/infra-scripts/config/webpack/webpack.base')

module.exports = merge.strategy({plugins: 'replace'})(
    getDevWebpackConfig({uiBasePath: path.resolve(__dirname, '../..')}),
    customComponentWebpackConfig
)
