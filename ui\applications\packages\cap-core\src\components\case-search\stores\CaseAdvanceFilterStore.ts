import {mapValues} from 'lodash'
import {action, observable} from 'mobx'

export class CaseAdvanceFilterStore {
    @observable advanceSearchVisible = false

    @observable advanceFilters: object | undefined = {}

    @action
    changeAdvanceSearchVisible = (visible: boolean) => {
        this.advanceSearchVisible = visible
    }

    @action
    changeAdvanceSearchFilter = (filters: object) => {
        // trims all filter values
        const newFilters = mapValues(filters, (value: unknown) => (typeof value === 'string' ? value?.trim() : value))
        this.advanceFilters = newFilters
    }

    @action
    resetAll = () => {
        this.advanceSearchVisible = false
        this.advanceFilters = {}
    }
}
