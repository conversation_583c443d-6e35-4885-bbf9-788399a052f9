import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "COMPONENT_SLOT",
      "props": {
        "md": 24,
        "span": 24,
        "slotId": "PREFERRED_PAYMENT_METHOD"
      },
      "id": "aa1f950b-29a3-4555-9a60-ea3272691b73"
    },
    {
      "type": "BUILDING_BLOCK",
      "props": {
        "blockId": "CheckPaymentMethodDetail",
        "condition": {
          "display": {
            "conditionInputType": "form",
            "isHiddenContentRendered": false,
            "type": "display",
            "rulesTree": {
              "rules": [
                {
                  "inputSource": {
                    "type": "FIELD",
                    "value": "preferredPaymentMethodType"
                  },
                  "operator": "$eq",
                  "outputSource": {
                    "type": "PRIMITIVE",
                    "value": "check"
                  }
                }
              ]
            }
          }
        }
      },
      "id": "54a6677f-3a7d-4c28-b1f7-f69ed1d4b65c"
    }
  ],
  "version": 8
}

export default config;
