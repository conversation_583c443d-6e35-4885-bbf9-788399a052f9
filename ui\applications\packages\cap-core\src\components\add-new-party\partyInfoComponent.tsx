/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {noop} from '@eisgroup/common'
import {t} from '@eisgroup/i18n'
import {Form, FormApi, PrefixProvider} from '@eisgroup/form'
import React, {useEffect, useState} from 'react'
import {CustomerType} from '@eisgroup/cap-services'
import {PartyDetailsForm} from '../party-details-form'
import {DrawerActions, DrawerFormStateType} from '../form-drawer'
import {OptionValue} from '../../common/Types'
import {PreferredContactTypes} from '../preferred-contact-info/PreferredContactInfo'
import {ADD_PARTY_INFO_DRAWER_ACTIONS} from '../../common/package-class-names'

export interface PartyInfoComponentProps {
    /**
     * individual customer or origization customer
     */
    customerType: CustomerType
    /**
     * initialValues
     */
    initialValues: any
    /**
     * render type
     */
    drawerFormState: DrawerFormStateType
    associatedWithOptions?: OptionValue[]
    hideRelationshipToParticipant?: boolean
    showDisabledSelfRelationship?: boolean
    preferredContactMethod: PreferredContactTypes
    onDrawerClose: () => void
    onsubmitParty: (form: FormApi) => void
    isSubject?: boolean
    currentAddressIdx?: number
}

export const PartyInfoComponent = (props: PartyInfoComponentProps) => {
    const {
        initialValues,
        customerType,
        associatedWithOptions,
        hideRelationshipToParticipant,
        showDisabledSelfRelationship,
        drawerFormState,
        preferredContactMethod,
        onsubmitParty,
        onDrawerClose,
        isSubject,
        currentAddressIdx
    } = props
    const [initValue, setInitValue] = useState({})
    useEffect(() => {
        setInitValue(initialValues)
    }, [])
    return (
        <Form onSubmit={noop} initialValues={initValue}>
            {() => {
                return (
                    <div className={ADD_PARTY_INFO_DRAWER_ACTIONS}>
                        <PrefixProvider prefixInputName='party'>
                            <PartyDetailsForm
                                currentAddressIdx={currentAddressIdx}
                                partyFormType={customerType}
                                drawerFormState={drawerFormState}
                                associatedWithOptions={associatedWithOptions}
                                preferredContactMethod={preferredContactMethod}
                                showDisabledSelfRelationship={showDisabledSelfRelationship}
                                hideAssociatedWith
                                hideRelationshipToParticipant={
                                    customerType === CustomerType.Organization || hideRelationshipToParticipant
                                }
                                isBirthDateRequired
                                isSubject={isSubject}
                                hideEinRequired={customerType === CustomerType.Organization}
                            />
                            <DrawerActions
                                handleFormCancel={onDrawerClose}
                                labels={{
                                    editButtonLabel: t('cap-core:save'),
                                    createButtonLabel: t('cap-core:save')
                                }}
                                drawerFormState={drawerFormState}
                                handleFormConfirm={onsubmitParty}
                            />
                        </PrefixProvider>
                    </div>
                )
            }}
        </Form>
    )
}
