Common.projectSettings

lazy val integrationEisGenesisCapAccelerated = project.in(file("integration-eisgenesis-cap-accelerated"))
lazy val integrationEisGenesisCapAcceleratedSettlement = project.in(file("integration-eisgenesis-cap-accelerated-settlement"))
lazy val integrationEisGenesisCapAccidentalDismemberment = project.in(file("integration-eisgenesis-cap-accidental-dismemberment"))
lazy val integrationEisGenesisCapAccidentalDismembermentSettlement = project.in(file("integration-eisgenesis-cap-accidental-dismemberment-settlement"))
lazy val integrationEisGenesisCapDeath = project.in(file("integration-eisgenesis-cap-death"))
lazy val integrationEisGenesisCapClaimWrapper = project.in(file("integration-eisgenesis-cap-claim-wrapper"))
lazy val integrationEisGenesisCapDeathSettlement = project.in(file("integration-eisgenesis-cap-death-settlement"))
lazy val integrationEisGenesisCapDental = project.in(file("integration-eisgenesis-cap-dental"))
lazy val integrationEisGenesisCapDentalPolicy = project.in(file("integration-eisgenesis-cap-dental-policy"))
lazy val integrationEisGenesisCapDentalSettlement = project.in(file("integration-eisgenesis-cap-dental-settlement"))
lazy val integrationEisGenesisCapLifeIntake = project.in(file("integration-eisgenesis-cap-life-intake"))
lazy val integrationEisGenesisCapLifeIntakeSettlement = project.in(file("integration-eisgenesis-cap-life-intake-settlement"))
lazy val integrationEisGenesisCapPartyRoleDefinition = project.in(file("integration-eisgenesis-cap-party-role-definition"))
lazy val integrationEisGenesisCapPaymentDefinition = project.in(file("integration-eisgenesis-cap-payment-definition"))
lazy val integrationEisGenesisCapPaymentTemplate = project.in(file("integration-eisgenesis-cap-payment-template"))
lazy val integrationEisGenesisCapPaymentSchedule = project.in(file("integration-eisgenesis-cap-payment-schedule"))
lazy val integrationEisGenesisCapSearch = project.in(file("integration-eisgenesis-cap-search"))
lazy val integrationEisGenesisCapLeave = project.in(file("integration-eisgenesis-cap-leave"))
lazy val integrationEisGenesisCapLeaveSettlement = project.in(file("integration-eisgenesis-cap-leave-settlement"))
lazy val integrationEisGenesisCapSmp = project.in(file("integration-eisgenesis-cap-smp"))
lazy val integrationEisGenesisCapSmpSettlement = project.in(file("integration-eisgenesis-cap-smp-settlement"))
lazy val integrationEisGenesisCapStd = project.in(file("integration-eisgenesis-cap-std"))
lazy val integrationEisGenesisCapStdSettlement = project.in(file("integration-eisgenesis-cap-std-settlement"))
lazy val integrationEisGenesisCapLtd = project.in(file("integration-eisgenesis-cap-ltd"))
lazy val integrationEisGenesisCapLtdSettlement = project.in(file("integration-eisgenesis-cap-ltd-settlement"))
lazy val integrationEisGenesisCapPremiumWaiver = project.in(file("integration-eisgenesis-cap-premium-waiver"))
lazy val integrationEisGenesisCapPremiumWaiverSettlement = project.in(file("integration-eisgenesis-cap-premium-waiver-settlement"))
lazy val integrationEisGenesisCapCI = project.in(file("integration-eisgenesis-cap-ci"))
lazy val integrationEisGenesisCapCISettlement = project.in(file("integration-eisgenesis-cap-ci-settlement"))
lazy val integrationEisGenesisCapHI = project.in(file("integration-eisgenesis-cap-hi"))
lazy val integrationEisGenesisCapHISettlement = project.in(file("integration-eisgenesis-cap-hi-settlement"))
lazy val integrationEisGenesisCapEventCase = project.in(file("integration-eisgenesis-cap-event-case"))
lazy val integrationEisGenesisCapAccumulatorContainer = project.in(file("integration-eisgenesis-cap-accumulator-container"))
lazy val integrationEisGenesisCapAccumulatorTransactionData = project.in(file("integration-eisgenesis-cap-accumulator-transaction-data"))
lazy val integrationEisGenesisCapSpecialHandling = project.in(file("integration-eisgenesis-cap-special-handling"))
lazy val integrationEisGenesisCapRelationship = project.in(file("integration-eisgenesis-cap-relationship"))
lazy val integrationEisGenesisCapComparison = project.in(file("integration-eisgenesis-cap-comparison"))
lazy val integrationEisGenesisCapCommon = project.in(file("integration-eisgenesis-cap-common"))
lazy val integrationEisGenesisCapIntegration = project.in(file("integration-eisgenesis-cap-integration"))
lazy val integrationEisGenesisCapWorkflowPublisher = project.in(file("integration-eisgenesis-cap-workflow-publisher"))
lazy val integrationEisGenesisCapPolicyModel = project.in(file("integration-eisgenesis-cap-policy-model"))

lazy val integrationEisGenesisCap = project.in(file("."))
  .enablePlugins(PlayMinimalJava)
  .aggregate(
    integrationEisGenesisCapAccelerated,
    integrationEisGenesisCapAcceleratedSettlement,
    integrationEisGenesisCapAccidentalDismemberment,
    integrationEisGenesisCapAccidentalDismembermentSettlement,
    integrationEisGenesisCapDeath,
    integrationEisGenesisCapClaimWrapper,
    integrationEisGenesisCapDeathSettlement,
    integrationEisGenesisCapDental,
    integrationEisGenesisCapDentalPolicy,
    integrationEisGenesisCapDentalSettlement,
    integrationEisGenesisCapLifeIntake,
    integrationEisGenesisCapLifeIntakeSettlement,
    integrationEisGenesisCapPartyRoleDefinition,
    integrationEisGenesisCapPaymentDefinition,
    integrationEisGenesisCapPaymentTemplate,
    integrationEisGenesisCapPaymentSchedule,
    integrationEisGenesisCapSearch,
    integrationEisGenesisCapLeave,
    integrationEisGenesisCapLeaveSettlement,
    integrationEisGenesisCapSmp,
    integrationEisGenesisCapSmpSettlement,
    integrationEisGenesisCapStd,
    integrationEisGenesisCapStdSettlement,
    integrationEisGenesisCapLtd,
    integrationEisGenesisCapLtdSettlement,
    integrationEisGenesisCapPremiumWaiver,
    integrationEisGenesisCapPremiumWaiverSettlement,
    integrationEisGenesisCapCI,
    integrationEisGenesisCapCISettlement,
    integrationEisGenesisCapHI,
    integrationEisGenesisCapHISettlement,
    integrationEisGenesisCapEventCase,
    integrationEisGenesisCapAccumulatorContainer,
    integrationEisGenesisCapAccumulatorTransactionData,
    integrationEisGenesisCapSpecialHandling,
    integrationEisGenesisCapRelationship,
    integrationEisGenesisCapComparison,
    integrationEisGenesisCapCommon,
    integrationEisGenesisCapIntegration,
    integrationEisGenesisCapWorkflowPublisher,
    integrationEisGenesisCapPolicyModel
  )
  .dependsOn(
    integrationEisGenesisCapAccelerated,
    integrationEisGenesisCapAcceleratedSettlement,
    integrationEisGenesisCapAccidentalDismemberment,
    integrationEisGenesisCapAccidentalDismembermentSettlement,
    integrationEisGenesisCapDeath,
    integrationEisGenesisCapClaimWrapper,
    integrationEisGenesisCapDeathSettlement,
    integrationEisGenesisCapDental,
    integrationEisGenesisCapDentalPolicy,
    integrationEisGenesisCapDentalSettlement,
    integrationEisGenesisCapLifeIntake,
    integrationEisGenesisCapLifeIntakeSettlement,
    integrationEisGenesisCapPartyRoleDefinition,
    integrationEisGenesisCapPaymentDefinition,
    integrationEisGenesisCapPaymentTemplate,
    integrationEisGenesisCapPaymentSchedule,
    integrationEisGenesisCapSearch,
    integrationEisGenesisCapLeave,
    integrationEisGenesisCapLeaveSettlement,
    integrationEisGenesisCapSmp,
    integrationEisGenesisCapSmpSettlement,
    integrationEisGenesisCapStd,
    integrationEisGenesisCapStdSettlement,
    integrationEisGenesisCapLtd,
    integrationEisGenesisCapLtdSettlement,
    integrationEisGenesisCapPremiumWaiver,
    integrationEisGenesisCapPremiumWaiverSettlement,
    integrationEisGenesisCapCI,
    integrationEisGenesisCapCISettlement,
    integrationEisGenesisCapHI,
    integrationEisGenesisCapHISettlement,
    integrationEisGenesisCapEventCase,
    integrationEisGenesisCapAccumulatorContainer,
    integrationEisGenesisCapAccumulatorTransactionData,
    integrationEisGenesisCapSpecialHandling,
    integrationEisGenesisCapRelationship,
    integrationEisGenesisCapComparison,
    integrationEisGenesisCapCommon,
    integrationEisGenesisCapIntegration,
    integrationEisGenesisCapWorkflowPublisher,
    integrationEisGenesisCapPolicyModel
  )
