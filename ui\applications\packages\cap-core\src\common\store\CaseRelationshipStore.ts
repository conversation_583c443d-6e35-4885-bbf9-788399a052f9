/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {observable, action, toJS} from 'mobx'
import {omit} from 'lodash'
import {
    ClaimCaseRelationshipService,
    claimCaseRelationshipService as defaultClaimCaseRelationshipService,
    eventCaseService as defaultEventCaseService,
    claimLossService as defaultClaimLossService,
    claimWrapperService,
    ClaimWrapperService,
    EventCaseService,
    ClaimLossService,
    IndividualCustomer,
    backofficeCustomerCommonService
} from '@eisgroup/cap-services'
import {GenesisCommonExceptionDTO, HttpResponse} from '@eisgroup/cap-gateway-client'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {CapRelationship} from '@eisgroup/cap-models'
import {PaginationConfig} from '@eisgroup/ui-kit'
import {Key} from '@eisgroup/models-api'
import {t} from '@eisgroup/i18n'
import {ErrorMessage, Writable} from '@eisgroup/common-types'
import {EntityLink} from '@eisgroup/claims-core'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import {BaseRootStoreImpl} from './BaseRootStore'
import {createLossLink} from '../utils'
import {DrawerFormStateType} from '../../components/form-drawer'
import {PROMISE_STATUS} from '../constants'

export type CaseRelationshipWithTargetCase = CapRelationship.CapRelationshipEntity & {
    targetCase?: CapEventCaseEntity
    customerNumber?: string
}

export type SubmitCaseRelationshipParams = {
    createRelationshipParams?: {
        originLoss: CapEventCaseEntity
        targetCaseNumber?: string
        manualAdd?: boolean
        note?: string
        relationshipTypeCd?: string
    }
    updateRelationshipParams?: {
        relationshipEntity?: CapRelationship.CapRelationshipEntity
        manualAdd?: boolean
        note?: string
        relationshipTypeCd?: string
    }
}

export interface CaseRelationshipStore {
    loadingRelationships: boolean
    setLoadingRelationships: (loading?: boolean) => void
    relationshipEditDrawerVisible: boolean
    setRelationshipEditDrawerVisible: (visible: boolean) => void

    caseRelationshipsPagination: PaginationConfig
    caseRelationshipsWithTargetCases: CaseRelationshipWithTargetCase[]

    removeRelationshipWithTargetCase: (key: Key) => Promise<CapRelationship.CapRelationshipEntity>
    getAllCaseRelationshipsWithTargetCases: (
        originLoss: CapEventCaseEntity,
        limit?: number,
        offset?: number
    ) => Promise<CaseRelationshipWithTargetCase[]>

    createCaseRelationship: (params: {
        sourceLossUri: string
        targetLossUri: string
        manualAdd?: boolean
        note?: string
        relationshipTypeCd: string
    }) => Promise<CapRelationship.CapRelationshipEntity>

    deleteCaseRelationship: (key: Key) => Promise<CapRelationship.CapRelationshipEntity>

    createOrUpdateCaseRelationship: (
        mode: DrawerFormStateType,
        params: SubmitCaseRelationshipParams
    ) => Promise<CapRelationship.CapRelationshipEntity | null>

    searchCaseByRootId: (rootId: string, revisionNo: number) => Promise<CapEventCaseEntity>

    getRelationshipDependecies: () => string
}

const defaultPaginationConfig = {
    size: 'small',
    defaultPageSize: 10,
    pageSizeOptions: ['10', '15', '20', '25'],
    showQuickJumper: true,
    showSizeChanger: true
}

export class CaseRelationshipStoreImpl extends BaseRootStoreImpl implements CaseRelationshipStore {
    @observable loadingRelationships = false

    @observable relationshipEditDrawerVisible = false

    @observable caseRelationshipsWithTargetCases: CaseRelationshipWithTargetCase[] = []

    @observable caseRelationshipsPagination: PaginationConfig = defaultPaginationConfig

    private caseSearchService: ClaimWrapperService

    private caseRelationshipService: ClaimCaseRelationshipService

    private caseEventLoadService: EventCaseService

    private claimLossService: ClaimLossService

    constructor(
        caseSearchService: ClaimWrapperService = claimWrapperService,
        caseRelationshipService: ClaimCaseRelationshipService = defaultClaimCaseRelationshipService,
        caseEventLoadService: EventCaseService = defaultEventCaseService,
        claimLossService: ClaimLossService = defaultClaimLossService
    ) {
        super()
        this.caseSearchService = caseSearchService
        this.caseRelationshipService = caseRelationshipService
        this.caseEventLoadService = caseEventLoadService
        this.claimLossService = claimLossService
    }

    @action
    setLoadingRelationships = (loading?: boolean) => {
        this.loadingRelationships = !!loading
    }

    @action
    setRelationshipEditDrawerVisible = (visible: boolean) => {
        this.relationshipEditDrawerVisible = visible
    }

    @action
    setCaseRelationshipsPagination = (pagination: PaginationConfig, override = false) => {
        if (override) {
            this.caseRelationshipsPagination = pagination
        }
        this.caseRelationshipsPagination = {
            ...this.caseRelationshipsPagination,
            ...pagination
        }
    }

    @action
    setCaseRelationshipsWithTargetCases = (relationshipsWithTargets: CaseRelationshipWithTargetCase[]) => {
        this.caseRelationshipsWithTargetCases = relationshipsWithTargets
    }

    @action
    addCaseRelationshipsWithTargetCases = async (addedRelationship: CapRelationship.CapRelationshipEntity) => {
        const {targetSource} = addedRelationship
        const actualTargetUri = targetSource?._uri
        if (actualTargetUri) {
            const rootId = EntityLink.from(actualTargetUri)?.rootId
            const revisionNo = parseInt(EntityLink.from(actualTargetUri)?.revisionNo ?? '1', 10)
            const targetLoss = await this.searchCaseByRootId(rootId, revisionNo)

            this.setCaseRelationshipsWithTargetCases([
                ...this.caseRelationshipsWithTargetCases,
                {...addedRelationship, targetCase: targetLoss}
            ])
            this.setCaseRelationshipsPagination({total: this.caseRelationshipsWithTargetCases.length})
        }
    }

    @action
    updateCaseRelationshipsWithTargetCases = (updatedRelationship: CapRelationship.CapRelationshipEntity) => {
        const newRelationships = this.caseRelationshipsWithTargetCases.map(relationship => {
            if (relationship?._key.rootId === updatedRelationship?._key?.rootId) {
                return {...updatedRelationship, targetCase: relationship.targetCase}
            }
            return relationship
        })
        this.setCaseRelationshipsWithTargetCases(newRelationships)
    }

    @action
    deleteCaseRelationshipsWithTargetCases = (relationshipsWithTarget: CaseRelationshipWithTargetCase) => {
        const newRelationships = this.caseRelationshipsWithTargetCases?.filter(relationship => {
            return relationship?._key?.rootId !== relationshipsWithTarget?._key?.rootId
        })
        this.setCaseRelationshipsWithTargetCases(newRelationships)
        this.setCaseRelationshipsPagination({total: newRelationships.length})
    }

    @action
    getAllCaseRelationships = async (sourceUri: string, limit: number, offset: number) => {
        return this.caseRelationshipService.getRelationShips({
            data: {
                entityURI: sourceUri,
                limit,
                offset
            }
        })
    }

    @action
    getAllCaseRelationshipsWithTargetCases = async (
        originLoss: CapEventCaseEntity,
        limit?: number,
        offset?: number
    ) => {
        const currentLimit =
            limit ?? this.caseRelationshipsPagination.pageSize ?? defaultPaginationConfig.defaultPageSize
        const sourceUri = createLossLink(originLoss)
        const relationships = await this.getAllCaseRelationships(sourceUri, currentLimit, offset ?? 0)
        const relationshipsPromises = relationships.map(async relationship => {
            const {originSource, targetSource} = relationship
            const [originSourceUri, targetSourceUri] = [originSource?._uri, targetSource?._uri]
            const actualTargetUri = originSourceUri === sourceUri ? targetSourceUri : originSourceUri
            let result: CaseRelationshipWithTargetCase = relationship
            if (actualTargetUri) {
                const rootId = EntityLink.from(actualTargetUri)?.rootId
                const revisionNo = parseInt(EntityLink.from(actualTargetUri)?.revisionNo ?? '1', 10)
                const targetLoss = await this.searchCaseByRootId(rootId, revisionNo)
                result = {
                    ...result,
                    targetCase: targetLoss
                }
            }
            return result
        })
        const allSettledValue = await Promise.allSettled(relationshipsPromises)
        const relationshipsWithTargets = allSettledValue
            .map(result => {
                if (result.status === PROMISE_STATUS.FULLFILLED) {
                    return (result as {status: string; value: CaseRelationshipWithTargetCase}).value
                }
                return null
            })
            .filter(Boolean) as CaseRelationshipWithTargetCase[]

        // search customer informations
        const subjectOfClaimLinks = [
            ...new Set(
                relationshipsWithTargets
                    .map(relationship => relationship?.targetCase?.memberRegistryTypeId)
                    .filter(Boolean)
            )
        ] as string[]
        const subjectOfClaimsMap = {}
        if (subjectOfClaimLinks.length > 0) {
            const subjectOfClaims = (await this.promiseCall(() =>
                backofficeCustomerCommonService().searchCustomersByRegistryTypeIds(subjectOfClaimLinks)
            )) as IndividualCustomer[]
            subjectOfClaims.forEach(item => {
                const registryTypeId = item?.details?.person?.registryTypeId
                if (registryTypeId) {
                    subjectOfClaimsMap[registryTypeId] = item?.customerNumber
                }
            })
        }

        const finalRelationships = relationshipsWithTargets.map(relationship => {
            const registryTypeId = relationship?.targetCase?.memberRegistryTypeId
            return {
                ...relationship,
                customerNumber: subjectOfClaimsMap?.[registryTypeId ?? '']
            }
        })

        this.setCaseRelationshipsWithTargetCases(finalRelationships)

        this.setCaseRelationshipsPagination({total: this.caseRelationshipsWithTargetCases.length})

        return finalRelationships
    }

    @action
    createCaseRelationship = (params: {
        sourceLossUri: string
        targetLossUri: string
        manualAdd?: boolean
        note?: string
        state?: string
        relationshipTypeCd: string
    }): Promise<CapRelationship.CapRelationshipEntity> => {
        const {sourceLossUri, targetLossUri, manualAdd = true, note = '', relationshipTypeCd, state} = params

        let newRelationship = CapRelationship.factory.newByType<Writable<CapRelationship.CapRelationshipEntity>>(
            CapRelationship.CapRelationshipEntity
        )
        newRelationship = {
            ...newRelationship,
            manualAdd,
            note,
            state,
            relationshipTypeCd,
            originSource: {
                '_uri': sourceLossUri
            },
            targetSource: {
                '_uri': targetLossUri
            }
        }

        return this.caseRelationshipService.createRelationship({
            data: {entity: omit(newRelationship, '_key') as CapRelationship.CapRelationshipEntity}
        })
    }

    @action
    updateCaseRelationship = (
        currentRelationshipEntity: CapRelationship.CapRelationshipEntity,
        params: {
            note?: string
            relationshipTypeCd?: string
        }
    ) => {
        const {note = '', relationshipTypeCd} = params
        const updateEntity: CapRelationship.CapRelationshipEntity = {
            ...currentRelationshipEntity,
            note,
            relationshipTypeCd
        }
        return this.caseRelationshipService.updateRelationship({
            data: {
                _key: updateEntity._key,
                entity: updateEntity
            }
        })
    }

    deleteCaseRelationship = async (key: Key) => {
        return this.caseRelationshipService.deleteRelationship({data: {_key: key}})
    }

    associationCaseRelationship = async (params: {
        sourceLoss: CapEventCaseEntity
        targetCaseNumber: string
        manualAdd?: boolean
        note?: string
        relationshipTypeCd: string
    }) => {
        const {sourceLoss, targetCaseNumber, manualAdd, note, relationshipTypeCd} = params
        const caseResults = await this.claimLossService
            .searchClaimLoss(targetCaseNumber)
            .toPromise()
            .then(response => response.get())
        const targetSource = caseResults?.result?.[0]

        const originSourceUri = createLossLink(sourceLoss)

        const targetSourceUri = createLossLink(targetSource as CapEventCase.CapEventCaseEntity)

        return this.createCaseRelationship({
            sourceLossUri: originSourceUri,
            targetLossUri: targetSourceUri,
            manualAdd,
            note,
            relationshipTypeCd
        })
    }

    removeRelationshipWithTargetCase = async (key: Key) => {
        const deletedRelationship = await this.deleteCaseRelationship(key)
        this.deleteCaseRelationshipsWithTargetCases(deletedRelationship)
        return Promise.resolve(deletedRelationship)
    }

    createOrUpdateCaseRelationship = async (mode: DrawerFormStateType, params: SubmitCaseRelationshipParams) => {
        const mapCreateOrUpdateRelationshipError = (e: GenesisCommonExceptionDTO) => {
            let errorMessage: string
            const firstChildError = e.errors?.[0]
            const {errorCode, message = ''} = firstChildError ?? {}
            switch (errorCode) {
                case 'crs-003':
                case 'unf0001':
                    // already exist this relationship
                    errorMessage = t('cap-core:case_relationship_drawer_relationship_already_exist')
                    break
                case 'CannotAddCapRelationshipToItself':
                    errorMessage = t('cap-core:case_relationship_drawer_relationship_could_not_self')
                    break
                default:
                    errorMessage = message
            }

            return {code: '', message: errorMessage} as ErrorMessage
        }
        try {
            if (mode === DrawerFormStateType.Create) {
                const {createRelationshipParams} = params
                const {originLoss, targetCaseNumber, manualAdd, note, relationshipTypeCd} =
                    createRelationshipParams ?? {}
                const addedRelationship = await this.associationCaseRelationship({
                    sourceLoss: originLoss!,
                    targetCaseNumber: targetCaseNumber!,
                    manualAdd,
                    note,
                    relationshipTypeCd: relationshipTypeCd!
                })
                this.addCaseRelationshipsWithTargetCases(addedRelationship)
            }
            if (mode === DrawerFormStateType.Edit) {
                const {updateRelationshipParams} = params
                const {relationshipEntity, note, relationshipTypeCd} = updateRelationshipParams ?? {}
                const updatedRelationship = await this.updateCaseRelationship(relationshipEntity!, {
                    note,
                    relationshipTypeCd
                })
                this.updateCaseRelationshipsWithTargetCases(updatedRelationship)
            }
        } catch (e: unknown) {
            if (e instanceof Response) {
                const error = (e as HttpResponse<unknown, GenesisCommonExceptionDTO>).error
                this.postError(mapCreateOrUpdateRelationshipError(error))
            }
            throw e
        }
        return null
    }

    getRelationshipDependecies = () => {
        return this.caseRelationshipsWithTargetCases.map(relationship => JSON.stringify(toJS(relationship))).join()
    }

    searchCaseByRootId = (rootId: string, revisionNo: number) => {
        return this.caseEventLoadService
            .loadEventCaseWithoutPoll({rootId, revisionNo})
            .toPromise()
            .then(res => {
                return res.get()
            })
    }
}
