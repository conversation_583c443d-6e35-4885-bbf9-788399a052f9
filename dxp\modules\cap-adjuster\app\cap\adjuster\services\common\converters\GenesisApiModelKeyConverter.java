/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.common.converters;

import cap.adjuster.services.common.dto.GenesisApiModelKey;
import dataproviders.common.dto.GenesisEntityKeyDTO;

public class GenesisApiModelKeyConverter<I extends GenesisEntityKeyDTO, A extends GenesisApiModelKey>
        extends GenesisApiModelRootKeyConverter<I, A> {

    @Override
    public I convertToInternalDTO(A apiDTO, I intDTO) {
        super.convertToInternalDTO(apiDTO, intDTO);
        intDTO.id = apiDTO.id;
        intDTO.parentId = apiDTO.parentId;
        return intDTO;
    }

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);
        apiDTO.id = intDTO.id;
        apiDTO.parentId = intDTO.parentId;
        return apiDTO;
    }
}
