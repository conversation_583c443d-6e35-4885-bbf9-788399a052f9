import {useLayoutEffect} from 'react'

export const useRelatedToAutoCompleteErrorBlock = (relatedToAutocompleteError = false) => {
    useLayoutEffect(() => {
        if (!relatedToAutocompleteError) {
            return
        }
        const errorBlock = document.querySelector('.case-relationship-slot .has-error .ant-form-explain') as HTMLElement
        if (errorBlock) {
            const tableBlock = document.querySelector('.case-relationship-slot .gen-case-search-component-result')
            const tableHeight = tableBlock?.getBoundingClientRect()?.height

            if (tableHeight && tableHeight > 0) {
                errorBlock.style.top = `-${tableHeight + 30}px`
            } else {
                errorBlock.style.top = '0px'
            }
        }
    })
}
