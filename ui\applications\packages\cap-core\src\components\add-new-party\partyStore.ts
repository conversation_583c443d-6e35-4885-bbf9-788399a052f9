/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    claimPartyService,
    IOCustomer,
    IndividualCustomer,
    OrganizationCustomer,
    backofficeOrgCustomerService,
    backofficeIndCustomerService
} from '@eisgroup/cap-services'
import {action} from 'mobx'
import {BaseRootStore, BaseRootStoreImpl} from '../../common/store'

export const PROVIDER_LIMIT_COUNT = 100
export interface PartyStore extends BaseRootStore {
    createCustomer: (customer: IOCustomer) => Promise<IOCustomer>
    updateCustomer: (customer: IOCustomer) => Promise<IOCustomer>
    updateCustomerCem: (customer: IndividualCustomer) => Promise<IndividualCustomer>
    updateOrganizationCustomerCem: (customer: OrganizationCustomer) => Promise<OrganizationCustomer>
}

export class PartyStoreImpl extends BaseRootStoreImpl implements PartyStore {
    @action
    createCustomer = (customer: IOCustomer): Promise<IOCustomer> => {
        return this.promiseCall(() => claimPartyService.createCustomer(customer))
    }

    @action
    updateCustomer = (customer: IOCustomer): Promise<IOCustomer> => {
        return this.promiseCall(() => claimPartyService.updateCustomer(customer))
    }

    @action
    updateCustomerCem = (customer: IndividualCustomer): Promise<IndividualCustomer> => {
        return this.promiseCall(() => backofficeIndCustomerService().updateIndividualCustomer(customer))
    }

    @action
    updateOrganizationCustomerCem = (customer: OrganizationCustomer): Promise<OrganizationCustomer> => {
        return this.promiseCall(() => backofficeOrgCustomerService().updateOrganizationCustomer(customer))
    }
}
