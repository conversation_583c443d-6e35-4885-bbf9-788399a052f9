/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.controllers.leaveloss;

import cap.adjuster.services.financial.dto.CapPayment;
import cap.adjuster.services.leaveloss.CapAdjusterLeaveLossService;
import cap.adjuster.services.leaveloss.dto.CapLeaveSettlement;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterLeaveLossApiController.TAG_API_CAP_ADJUSTER_LEAVE_LOSS,
                description = "CAP Adjuster: Leave Loss API")})
@Api(value = CapAdjusterLeaveLossApiController.TAG_API_CAP_ADJUSTER_LEAVE_LOSS,
        tags = CapAdjusterLeaveLossApiController.TAG_API_CAP_ADJUSTER_LEAVE_LOSS)
public class CapAdjusterLeaveLossApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_LEAVE_LOSS = "/cap-adjuster/v1/losses-leave";

    private CapAdjusterLeaveLossService leaveLossService;

    /**
     * Get payments for leave loss
     *
     * @param rootId leave loss identifier
     * @param revisionNo leave loss revision number
     * @return list of payments related to leave loss
     */
    @ApiOperation(value = "Get payments for leave loss",
            response = CapPayment.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getPayments(@ApiParam(value = "Leave loss identifier", required = true) String rootId,
                                              @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(leaveLossService.getPayments(rootId, revisionNo));
    }

    /**
     * Get settlements for leave loss
     *
     * @param rootId leave loss identifier
     * @param revisionNo leave loss revision number
     * @return list of settlements related to leave loss
     */
    @ApiOperation(value = "Get settlements for leave loss",
            response = CapLeaveSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Leave loss identifier", required = true) String rootId,
                                               @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(leaveLossService.getSettlements(rootId, revisionNo));
    }

    @Inject
    public void setLeaveLossService(CapAdjusterLeaveLossService leaveLossService) {
        this.leaveLossService = leaveLossService;
    }
}
