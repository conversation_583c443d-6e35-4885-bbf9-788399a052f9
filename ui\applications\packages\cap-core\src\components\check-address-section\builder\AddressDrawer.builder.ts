import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "COMPONENT_SLOT",
          "props": {
            "md": 24,
            "span": 24,
            "slotId": "ADDRESS_INFO"
          },
          "components": [],
          "id": "3eec868e-df09-43c4-bc86-6eeaf57ec0ac"
        }
      ],
      "id": "849d36b3-82ae-4f3e-8285-9f8c2ae13637"
    }
  ],
  "version": 139
}

export default config;
