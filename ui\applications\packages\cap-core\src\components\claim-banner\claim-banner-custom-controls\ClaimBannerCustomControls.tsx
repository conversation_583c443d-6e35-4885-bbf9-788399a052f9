/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {ClaimLoss, ClaimLossState} from '@eisgroup/cap-services'
import {t} from '@eisgroup/i18n'
import {CapLeave} from '@eisgroup/cap-disability-models'
import React from 'react'
import {CustomDropDown, FormDrawerStore, hasAuthorities, LEAVE_CLAIM, Privileges} from '../../..'
import {
    CAP_EVENT_CASE,
    CLAIM_WRAPPER,
    eligibleType,
    lossStatusMarkerClasses,
    lossTypeModuleNameMap,
    LTD_CLAIM,
    OTHER_CLAIM,
    SMD_CLAIM,
    STD_CLAIM
} from '../../../common/constants'
import {ActionItem, ClaimType, HeaderStatusCode, LossActionMode} from '../../../common/Types'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapLeaveClaimEntity = CapLeave.CapLeaveClaimEntity

export interface ClaimBannerCustomControlsProps {
    formDrawerStore: FormDrawerStore
    loss: CapEventCaseEntity | ClaimLoss | CapLeaveClaimEntity
    onActionSelect: (key: string) => void
}

export const ClaimBannerCustomControls: React.FC<ClaimBannerCustomControlsProps> = props => {
    const isEventCase = props.loss._modelName === CAP_EVENT_CASE
    const isEligible = props.loss.eligibilityResult?.eligibilityEvaluationCd === eligibleType.ELIGIBLE
    const hideCloseLossBtn =
        (isEventCase
            ? !hasAuthorities([Privileges.EVENT_CASE_CLOSE_CASE])
            : !hasAuthorities([Privileges.CLOSE_CASE])) || props.loss.state === ClaimLossState.Closed
    const hideReopenLossBtn = isEventCase
        ? !hasAuthorities([Privileges.EVENT_CASE_REOPEN_CASE])
        : !hasAuthorities([Privileges.REOPEN_CASE])
    const hideFollowUpTaskBtn =
        isEventCase ||
        !hasAuthorities([Privileges.CAPWORKFLOW_CLOSED_CLAIM_UP_TASK]) ||
        props.loss.state !== ClaimLossState.Closed
    const hideChangeSubStatus =
        (isEventCase
            ? !hasAuthorities([Privileges.EVENT_CASE_SET_SUB_STATUS])
            : !hasAuthorities([Privileges.SET_CASE_SUB_STATUS])) || props.loss.state === ClaimLossState.Closed

    const isOpenLoss = (lossState: string): boolean => {
        return lossStatusMarkerClasses.get(lossState) === lossStatusMarkerClasses.get(HeaderStatusCode.OPEN)
    }

    const isPendingLoss = (lossState: string): boolean => {
        return lossStatusMarkerClasses.get(lossState) === lossStatusMarkerClasses.get(HeaderStatusCode.PENDING)
    }

    const isClosedLoss = (lossState: string): boolean => {
        return lossStatusMarkerClasses.get(lossState) === lossStatusMarkerClasses.get(HeaderStatusCode.CLOSED)
    }

    const isClaimDisabled = (loss: CapEventCase.CapEventCaseEntity | ClaimLoss): boolean => {
        return !loss.lossDetail?.claimEvent?.claimEvents.includes('Absence')
    }

    const getTooltipMessage = (claim: string, disableClaim: boolean, open: boolean): string => {
        if (disableClaim) {
            return t('cap-core:banner_header_custom_dropdown_submenu_create_new_claim_disabled_no_absence', {
                claim
            })
        }
        if (!open) {
            return t('cap-core:banner_header_custom_dropdown_submenu_create_new_claim_disabled_closed', {claim})
        }
        return ''
    }

    const createActionItem = (labelKey: string, isHidden: boolean, disabled: boolean): ActionItem => {
        return {
            label: t(labelKey),
            isHidden,
            disabled
        }
    }

    const getDefaultActionItems = (
        loss: CapEventCase.CapEventCaseEntity | ClaimLoss,
        lossState: string
    ): {[key: string]: ActionItem} => {
        const isOpenOrPending = [
            lossStatusMarkerClasses.get(HeaderStatusCode.OPEN),
            lossStatusMarkerClasses.get(HeaderStatusCode.PENDING)
        ].includes(lossStatusMarkerClasses.get(lossState))
        const isClosed = [lossStatusMarkerClasses.get(HeaderStatusCode.CLOSED)].includes(
            lossStatusMarkerClasses.get(lossState)
        )
        return {
            [LossActionMode.CLOSE_LOSS]: {
                label: t(
                    `cap-core:${lossTypeModuleNameMap.get(
                        loss._modelName
                    )}_banner_header_custom_dropdown_menu_item_close_loss_label`
                ),
                isHidden: hideCloseLossBtn,
                disabled: isClosed
            },
            [LossActionMode.REOPEN_LOSS]: {
                label: t(
                    `cap-core:${lossTypeModuleNameMap.get(
                        loss._modelName
                    )}_banner_header_custom_dropdown_menu_item_reopen_loss_label`
                ),
                isHidden: hideReopenLossBtn,
                disabled: !isClosed
            },
            [LossActionMode.CHANGE_SUB_STATUS]: {
                label: t(
                    `cap-core:${lossTypeModuleNameMap.get(
                        loss._modelName
                    )}_banner_header_custom_dropdown_menu_item_change_loss_substatus_label`
                ),
                isHidden: hideChangeSubStatus,
                disabled: !isOpenOrPending
            }
        }
    }

    const handleCapEventCase = (
        loss: CapEventCase.CapEventCaseEntity | ClaimLoss,
        lossState: string,
        disableClaim: boolean,
        open: boolean
    ): {[key: string]: ActionItem} => {
        const isHiddenCreateClaim =
            !hasAuthorities([Privileges.LOSS_INTAKE_INITIALIZE_LOSS]) || props.loss.state === ClaimLossState.Closed
        const createClaimSubItems = {
            createNewClaim: {
                label: t('cap-core:banner_header_custom_dropdown_submenu_create_new_claim_label'),
                children: {
                    [LTD_CLAIM]: {
                        tooltip: getTooltipMessage(ClaimType.LTD, disableClaim, open),
                        disabled: disableClaim || !open,
                        label: t(
                            'cap-core:banner_header_custom_dropdown_submenu_create_new_claim_menu_item_ltd_claim_label'
                        )
                    },
                    [STD_CLAIM]: {
                        tooltip: getTooltipMessage(ClaimType.STD, disableClaim, open),
                        disabled: disableClaim || !open,
                        label: t(
                            'cap-core:banner_header_custom_dropdown_submenu_create_new_claim_menu_item_std_claim_label'
                        )
                    },
                    [SMD_CLAIM]: {
                        tooltip: getTooltipMessage(ClaimType.SMD, disableClaim, open),
                        disabled: disableClaim || !open,
                        label: t(
                            'cap-core:banner_header_custom_dropdown_submenu_create_new_claim_menu_item_smd_claim_label'
                        )
                    },
                    [OTHER_CLAIM]: {
                        disabled:
                            lossStatusMarkerClasses.get(lossState) ===
                            lossStatusMarkerClasses.get(HeaderStatusCode.CLOSED),
                        label: t(
                            'cap-core:banner_header_custom_dropdown_submenu_create_new_claim_menu_item_other_claim_label'
                        )
                    }
                }
            }
        }

        return {
            ...(isHiddenCreateClaim ? {} : createClaimSubItems),
            [LossActionMode.UPDATE_LOSS_INFORMATION]: createActionItem(
                'cap-core:banner_header_custom_dropdown_menu_item_update_information_label',
                !hasAuthorities([Privileges.EVENT_CASE_UPDATE_CASE]) ||
                    lossStatusMarkerClasses.get(lossState) === lossStatusMarkerClasses.get(HeaderStatusCode.CLOSED),
                false
            ),
            [LossActionMode.VIEW_CASE_DETAILS]: createActionItem(
                'cap-core:banner_header_custom_dropdown_menu_item_view_information_label',
                lossStatusMarkerClasses.get(lossState) !== lossStatusMarkerClasses.get(HeaderStatusCode.CLOSED),
                false
            ),
            ...getDefaultActionItems(loss, lossState),
            [LossActionMode.ADD_RELATED_CASE]: createActionItem(
                'cap-core:banner_header_custom_dropdown_menu_item_add_related_case_label',
                !hasAuthorities([Privileges.CAP_RELATIONSHIP_CREATE_UPDATE_REMOVE_RELATIONSHIP]),
                false
            )
        }
    }

    const handleClaimWrapperCase = (
        loss: CapEventCase.CapEventCaseEntity | ClaimLoss,
        lossState: string
    ): {[key: string]: ActionItem} => {
        const open = isOpenLoss(lossState)
        const pending = isPendingLoss(lossState)
        const pendingOrIncomplete =
            lossStatusMarkerClasses.get(lossState) === lossStatusMarkerClasses.get(HeaderStatusCode.PENDING) ||
            lossStatusMarkerClasses.get(lossState) === lossStatusMarkerClasses.get(HeaderStatusCode.INCOMPLETE)

        return {
            [LossActionMode.SUBMIT_LOSS]: createActionItem(
                'cap-core:claim_header_submit_claim_btn',
                !hasAuthorities([Privileges.CLAIM_SUBMIT_LOSS]) || props.loss.state === ClaimLossState.Closed,
                !pendingOrIncomplete || !isEligible
            ),
            ...getDefaultActionItems(loss, lossState),
            [LossActionMode.FOLLOW_UP_TASK]: createActionItem(
                t(`cap-core:action_follow_up_task`),
                hideFollowUpTaskBtn,
                !isClosedLoss(lossState)
            ),
            [LossActionMode.POLICY_REFRESH]: createActionItem(
                'cap-core:claim_header_policy_Refresh_btn',
                !hasAuthorities([Privileges.CLAIM_POLICY_REFRESH]) || (!open && !pending),
                false
            )
        }
    }

    const handleLeaveClaimCase = (
        loss: CapEventCase.CapEventCaseEntity | ClaimLoss,
        lossState: string
    ): {[key: string]: ActionItem} => {
        return {
            [LossActionMode.SUBMIT_LOSS]: createActionItem(
                'cap-core:claim_header_submit_claim_btn',
                !hasAuthorities([Privileges.CLAIM_SUBMIT_LOSS]) || lossState !== HeaderStatusCode.PENDING,
                false
            ),
            ...getDefaultActionItems(loss, lossState),
            [LossActionMode.FOLLOW_UP_TASK]: createActionItem(
                t(`cap-core:action_follow_up_task`),
                hideFollowUpTaskBtn,
                !isClosedLoss(lossState)
            )
        }
    }

    const getDropDownActions = (): {[key: string]: ActionItem} => {
        const loss = props.loss
        const lossState = loss.state?.toUpperCase() || ''
        const open = isOpenLoss(lossState)
        const disableClaim = isClaimDisabled(loss)

        if (lossState === undefined) {
            return {}
        }

        switch (loss._modelName) {
            case CAP_EVENT_CASE:
                return handleCapEventCase(loss, lossState, disableClaim, open)
            case CLAIM_WRAPPER:
                return handleClaimWrapperCase(loss, lossState)
            case LEAVE_CLAIM:
                return handleLeaveClaimCase(loss, lossState)
            default:
                return getDefaultActionItems(loss, lossState)
        }
    }

    return (
        <CustomDropDown
            onActionSelect={props.onActionSelect}
            dropDownActions={getDropDownActions()}
            buttonLabel={t('cap-core:banner_header_claim_heading_button')}
        />
    )
}
