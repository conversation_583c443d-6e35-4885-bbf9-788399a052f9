/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import * as React from 'react'
import {joinNotEmpty} from '@eisgroup/common'
import {LookupLabel} from '@eisgroup/react-components'
import {CrmLocation} from '@eisgroup/cap-services'

export interface CustomerAddressLineProps {
    location: CrmLocation
}

export const CustomerAddressLine: React.FC<CustomerAddressLineProps> = ({location}) => {
    if (!location) {
        return null
    }
    const {addressLine1, addressLine2, addressLine3, city, stateProvinceCd, postalCode} = location
    const provinceCode = stateProvinceCd
    const address = joinNotEmpty([addressLine1, addressLine2, addressLine3, city], ', ')
    let stateProvinceComponent
    let postalCodeComponent
    if (provinceCode) {
        stateProvinceComponent = (
            <>
                , <LookupLabel lookup='StateProv' code={provinceCode} />
            </>
        )
    }
    if (postalCode) {
        postalCodeComponent = <>, {postalCode}</>
    }
    return (
        <>
            {address}
            {provinceCode && stateProvinceComponent}
            {postalCode && postalCodeComponent}
        </>
    )
}
