@Library("ci-config@master")
@Library("ci-library@master")
@Library("cd-library@master")
import PodTemplateGenerator

def ENVIRONMENTS = k8s.environments('ci2-nightly')

pipeline {
    parameters {
        booleanParam(name: 'PUBLISH_BUILD_ARTIFACTS', defaultValue: false, description: 'Job will publish generated application artifacts to artifact repository if true, otherwise this step is ommited')
        string(name: 'POSTFIX', defaultValue: '', description: 'Postfix for artifacts version and docker tag. If left empty - a timestamp will be generated. Version will be retrieved from project during the build.')
        string(name: 'BRANCH', defaultValue: 'master', description: 'Branch name to build. Default is master.')
        choice(name: 'ENVIRONMENT', choices: ENVIRONMENTS, description: 'Environment name to deploy component')
        booleanParam(name: 'SKIP_DEPLOY', defaultValue: true, description: 'Set to true if deployment must be skipped')
    }

    options {
        skipStagesAfterUnstable()
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        timeout(time: 2, unit: 'HOURS')
    }

    agent {
        kubernetes {
            defaultContainer 'sbt'
            yaml new PodTemplateGenerator(this)
                .kind(templateKind: 'sbt21')
                .withParams(container: 'sbt', params: [cpuRequests: '6', memoryRequests: '12Gi', cpuLimits: '6', memoryLimits: '12Gi'])
                .generate()
        }
    }

    stages {
        stage('Build app') {
            steps {
                script {
                    dir('dxp'){
                        env.VERSION = dxpProjectVersion(project: './') + "-" + generateVersionPostfix(params.POSTFIX)
                        currentBuild.displayName = "#${BUILD_NUMBER} ${env.VERSION}"
                        println '######Checking version in version.sbt file and checkout to required branch######'
                                                sh '''
                                                    git_br=$(echo $GIT_BRANCH | cut -d '/' -f 2)
                                                    git checkout ${git_br}
                                                '''

                        def currentVersion = (readFile('version.sbt') =~ /ThisBuild \/ version := "(.+?)"/)[0][1]

                        if (PUBLISH_BUILD_ARTIFACTS == 'true') {

                            //building command with version to build and version from version.sbt file, command pushes artifacts to nexus
                            sh "sbt -Djava.io.tmpdir=$JENKINS_AGENT_WORKDIR -Dsbt.log.noformat=true \"release with-defaults default-tag-exists-answer o release-version $VERSION next-version $currentVersion \""
                            //backuping file and replacing content with build version form job, required to build files for docker image 
                            sh "mv version.sbt version.bkp"
                            sh "echo 'ThisBuild / version := \"$VERSION\"' > $workspace/dxp/version.sbt"
                            //building files for docker image, command is for dxpapi
                            sh "sbt -Djava.io.tmpdir=$JENKINS_AGENT_WORKDIR dist"
                            //reverting version.sbt
                            sh "mv -f version.bkp version.sbt"
                        } else {
                            sh "sbt -Djava.io.tmpdir=$JENKINS_AGENT_WORKDIR -Dsbt.log.noformat=true clean compile dist"
                        }
                    }
                }
            }
        }

        stage('Build image') {
            steps {
                script {
                   def registry = 'staging'

                   image.build(dockerfile: './dxp', name: 'dxp-cap-app', tag: VERSION, registry: registry, push: true)
                    
                }
            }
        }

        stage('Deploy') {
                             when {
                                 expression {
                                     (!params.SKIP_DEPLOY)
                                 }
                             }
                             steps {
                                 script {
                                     build(
                                         job: 'dxp-cap-deploy',
                                         parameters: [
                                             string(name: 'BRANCH', value: BRANCH),
                                             string(name: 'ENVIRONMENT', value: params.ENVIRONMENT),
                                             string(name: 'VERSION', value: VERSION),
                                             booleanParam(name: 'CLEAN', value: true)
                                         ],
                                         propagate: true,
                                         wait: true
                                     )
                                 }
                             }
                 }
    }
    post {
        success {
            script {
                release = new ReleaseAssembly(this)
                if (params.BRANCH in release.getStagingBranches()) {
                    //push to RA staging-master.json file if branch is master
                    release.publish(
                        name: "dxp-cap",
                        version: env.VERSION,
                        revsion: params.BRANCH,
                        branch: params.BRANCH,
                        imageTag: env.VERSION,
                        images: [
                            "dxp-cap-app"
                        ]
                    )
                    //push tag
                    gitlib.tag(VERSION)
                }
            }
        }
    }
}
