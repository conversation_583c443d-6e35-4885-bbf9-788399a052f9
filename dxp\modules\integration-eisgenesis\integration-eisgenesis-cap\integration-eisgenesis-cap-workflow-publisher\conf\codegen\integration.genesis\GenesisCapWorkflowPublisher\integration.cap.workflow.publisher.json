{"swagger": "2.0", "info": {"description": "API for CapWorkflowPublisher", "version": "1", "title": "CapWorkflowPublisher model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/common/CapWorkflowPublisher/v1/sendClosedClaimInquiryEvent": {"post": {"description": "Fires event for closed claim inquiry.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/ClosedClaimInquiryRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ClosedClaimInquiredSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"ClosedClaimInquired": {"properties": {"name": {"type": "string"}, "occurredAtTime": {"type": "string", "format": "date-time"}}, "title": "ClosedClaimInquired"}, "ClosedClaimInquiredSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ClosedClaimInquired"}}, "title": "ClosedClaimInquiredSuccess"}, "ClosedClaimInquiredSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ClosedClaimInquiredSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClosedClaimInquiredSuccessBody"}, "ClosedClaimInquiryRequest": {"properties": {"claimRefNo": {"type": "string"}, "claimUri": {"type": "string"}, "taskAssignee": {"type": "string"}, "taskNote": {"type": "string"}, "taskQueue": {"type": "string"}, "taskType": {"type": "string"}}, "title": "ClosedClaimInquiryRequest"}, "ClosedClaimInquiryRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClosedClaimInquiryRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClosedClaimInquiryRequestBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}}}