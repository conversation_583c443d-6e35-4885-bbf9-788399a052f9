/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import * as React from 'react'
import {CLAIM_POPOVER_LABEL, CLAIM_POPOVER_TEXT_LINE} from '../../../common/package-class-names'
import ReactNode = React.ReactNode

interface LabelValueInfoProps {
    readonly label: string
    readonly children?: string | ReactNode
}

/**
 * Simple label-value component to render varios fields data.
 * @param param0 object containing string label and value (string or react node)
 * @returns span element, containing vertically placed label and value
 */
export const LabelValueInfo = ({label, children}: LabelValueInfoProps) => (
    <span>
        <p className={CLAIM_POPOVER_LABEL}>{label}</p>
        <span className={CLAIM_POPOVER_TEXT_LINE}>{children}</span>
    </span>
)
