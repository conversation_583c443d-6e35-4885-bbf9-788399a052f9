"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enMT = void 0;
/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
const cap_core_1 = require("@eisgroup/cap-core");
const cap_adjuster_i18n_en_1 = require("./cap-adjuster-i18n.en");
exports.enMT = {
    locale: { country: 'MT', language: 'en' },
    ns: 'cap-adjuster',
    resources: cap_core_1.translateResources(cap_adjuster_i18n_en_1.enUS.resources)
};
//# sourceMappingURL=cap-adjuster-i18n.en_MT.js.map