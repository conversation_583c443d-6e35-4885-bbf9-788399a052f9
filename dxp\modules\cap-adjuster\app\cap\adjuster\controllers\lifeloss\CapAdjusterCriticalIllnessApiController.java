/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.lifeloss;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import cap.adjuster.services.lifeloss.CapAdjusterLifeLossService;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterCriticalIllnessApiController.TAG_API_CAP_ADJUSTER_CI,
                description = "CAP Adjuster: Critical Illness Loss API")})
@Api(value = CapAdjusterCriticalIllnessApiController.TAG_API_CAP_ADJUSTER_CI,
        tags = CapAdjusterCriticalIllnessApiController.TAG_API_CAP_ADJUSTER_CI)
public class CapAdjusterCriticalIllnessApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_CI = "/cap-adjuster/v1/losses-ci";
    protected static final String MODEL_NAME = "CILoss";

    private CapAdjusterLifeLossService lifeLossService;

    /**
     * Get Critical Illness Loss' settlements
     *
     * @param rootId Critical Illness Loss identifier
     * @param revisionNo Critical Illness Loss revision number
     * @return list of settlements related to Critical Illness loss
     */
    @ApiOperation(value = "Get settlements associated with critical illness loss",
            response = CapGenericSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Critical Illness Loss Identifier", required = true) String rootId,
                                                  @ApiParam(value = "Critical Illness Loss Revision Number", required = true) Integer revisionNo) {
        return completeOk(lifeLossService.getLossSettlements(rootId, revisionNo, MODEL_NAME));
    }

    @Inject
    public void setLifeLossService(CapAdjusterLifeLossService lifeLossService) {
        this.lifeLossService = lifeLossService;
    }
}