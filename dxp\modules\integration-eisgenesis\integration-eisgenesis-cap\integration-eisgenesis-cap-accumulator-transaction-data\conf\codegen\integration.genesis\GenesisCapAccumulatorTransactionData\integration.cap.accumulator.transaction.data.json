{"swagger": "2.0", "info": {"description": "API for CapAccumulatorTransactionData", "version": "1", "title": "CapAccumulatorTransactionData model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capaccumulatortransactiondataentry/CapAccumulatorTransactionData/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatortransactiondataentry/CapAccumulatorTransactionData/v1/load/customer": {"post": {"description": "Load accumulator transactions by customer partition. Policy Optional.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapAccumulatorTransactionLoadRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntityListSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capaccumulatortransactiondataentry/CapAccumulatorTransactionData/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapAccumulatorTransactionData"}, "_modelType": {"type": "string", "example": "CapAccumulatorTransactionDataEntry"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapAccumulatorTransactionDataEntryEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "amount": {"type": "number", "description": "Unit independent amount changed with this transaction. It can be Days/Money."}, "coverage": {"type": "string", "description": "Specific coverage value for which accumulator is created for"}, "customerURI": {"type": "string", "description": "Primary insured."}, "durable": {"type": "boolean", "description": "Always include transaction in the accumulator calculation in spite of the provided date range."}, "extension": {"type": "object", "description": "Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form."}, "caseNumber": {"type": "string", "description": "Case number for which accumulator transaction is created"}, "claimNumber": {"type": "string", "description": "Claim number for which accumulator transaction is created"}, "id": {"type": "string", "description": "Random unique value guaranteeing record uniqueness."}, "party": {"$ref": "#/definitions/EntityLink"}, "policyTermDetails": {"$ref": "#/definitions/CapAccumulatorTransactionData_Term"}, "policyURI": {"type": "string", "description": "URI of the source policy."}, "resource": {"$ref": "#/definitions/EntityLink"}, "sourceURI": {"type": "string", "description": "URI of the source entity."}, "transactionDate": {"type": "string", "format": "date-time", "description": "Date of the source event. Overridden to make a clustering part of the composite key."}, "transactionTimestamp": {"type": "string", "format": "date-time", "description": "Timestamp of the transaction."}, "type": {"type": "string", "description": "Transaction type, is used to track what kind of change was done."}}, "title": "CapAccumulatorTransactionData CapAccumulatorTransactionDataEntryEntity", "description": "Container for transaction related information."}, "CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntityListSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "array", "items": {"$ref": "#/definitions/CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntity"}}}, "title": "CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntityListSuccess"}, "CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntityListSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntityListSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccumulatorTransactionData_CapAccumulatorTransactionDataEntryEntityListSuccessBody"}, "CapAccumulatorTransactionData_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapAccumulatorTransactionData Term"}, "CapAccumulatorTransactionLoadRequest": {"required": ["customerURI"], "properties": {"customerURI": {"type": "string"}, "policyURI": {"type": "string"}}, "title": "CapAccumulatorTransactionLoadRequest"}, "CapAccumulatorTransactionLoadRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapAccumulatorTransactionLoadRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapAccumulatorTransactionLoadRequestBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}