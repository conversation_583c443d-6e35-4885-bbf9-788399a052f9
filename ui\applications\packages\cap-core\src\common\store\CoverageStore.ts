/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementResultEntity,
    CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementRulesInput,
    CapAdjusterCiAdjudicationOpenlCapCISettlementResultEntity,
    CapAdjusterCiAdjudicationOpenlCapCISettlementRulesInput,
    CapAdjusterClaimWrapperCapClaimWrapperUpdateInput,
    CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity,
    CapAdjusterHiAdjudicationOpenlCapHISettlementResultEntity,
    CapAdjusterHiAdjudicationOpenlCapHISettlementRulesInput
} from '@eisgroup/cap-gateway-client'
import {CapClaimDefaultSettlementWithEditingStatus} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {BaseRootStore} from './BaseRootStore'
import {ActionsStore} from './ActionsStore'
import {FormDrawerStore} from '../../components/form-drawer'

export interface CoverageStore extends BaseRootStore {
    actionsStore: ActionsStore
    formDrawerStore: FormDrawerStore
    coverages: CapClaimDefaultSettlementWithEditingStatus[]
    /**
     * Action to save coverage
     */
    initSettlements: (
        body: CapAdjusterClaimWrapperCapClaimWrapperUpdateInput
    ) => RxResult<CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity>
    updateCoverages: (coverages: any[]) => void
    loadCurrentSettlement: (body: any, isEdit?: boolean) => void
    grossAmountHICalculation: (
        body: CapAdjusterHiAdjudicationOpenlCapHISettlementRulesInput
    ) => RxResult<CapAdjusterHiAdjudicationOpenlCapHISettlementResultEntity>
    grossAmountCICalculation: (
        body: CapAdjusterCiAdjudicationOpenlCapCISettlementRulesInput
    ) => RxResult<CapAdjusterCiAdjudicationOpenlCapCISettlementResultEntity>
    grossAmountACCCalculation: (
        body: CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementRulesInput
    ) => RxResult<CapAdjusterAccidentalDismembermentAdjudicationOpenlCapAccidentalDismembermentSettlementResultEntity>
    updateCurrentSettlementTimestamp: (body: any) => RxResult<any>
    loadBySettlementType: (coverage: any, serviceLoadApi: any, isEdit?: boolean) => void
    callServiceToUpdateCoverages: (coverage: any, currentViewCoverages: any[]) => RxResult<any>
}
