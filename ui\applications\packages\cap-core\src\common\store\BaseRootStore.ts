/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {isErrorMessage, noop, parseGenesisBackendFailure} from '@eisgroup/common'
import {ErrorMessage, RxResult} from '@eisgroup/common-types'
import {Either} from '@eisgroup/data.either'
import {Observable} from 'rxjs'
import {tap} from 'rxjs/operators'
import {BreadcrumbsStore, BreadcrumbsStoreImpl} from '../../components/claim-breadcrumbs'
import {ActionsStore, ActionsStoreImpl} from './ActionsStore'
import {ErrorMessagesStore, ErrorMessagesStoreImpl} from './ErrorMessagesStore'
import {RoutingStore, RoutingStoreImpl} from './RoutingStore'

export interface BaseRootStore {
    actionsStore: ActionsStore
    errorStore: ErrorMessagesStore
    routingStore: RoutingStore
    breadcrumbsStore: BreadcrumbsStore
    /**
     * Handles status action status and error after response is received
     */
    handleStatus: <T>(result: Either<ErrorMessage, T>, actionName?: string) => void
    /**
     * Wrapper for service calls, which will trigger service call and also
     * handles action status in actionsStore and errors in errorStore,
     * then returns the result.
     * Example:
     *      this.call(() => customerService.findCustomer(rootId), 'FIND_CUSTOMER_ACTION')
     * @param serviceCall wrapped service call
     * @param actionName optional action name to track in actions store
     */
    call: <T>(serviceCall: () => RxResult<T>, actionName?: string) => RxResult<T>
    /**
     * Same as call(), but for methods returning more generic Observable<T>
     * @param serviceCall wrapped service call
     * @param actionName optional action name to track in actions store
     */
    genericCall: <T>(serviceCall: () => Observable<T>, actionName?: string) => Observable<T>
    /**
     * Wrapper for service calls, which will trigger service call and also
     * handles action status in actionsStore and errors in errorStore,
     * then returns the result or throws an error.
     * Example:
     *      this.promiseCall(() => customerService.findCustomer(rootId), 'FIND_CUSTOMER_ACTION')
     * @param serviceCall wrapped service call
     * @param actionName optional action name to track in actions store
     */
    promiseCall: <T>(serviceCall: () => Promise<T>, actionName?: string) => Promise<T>
    /**
     * @deprecated please use more reactivish {@link call}
     */
    callService: <T>(service: RxResult<T>, successHandler: (payload: T) => void, actionName?: string) => void
    handleError: (actionName: string, error: any) => void
    handleComplete: (actionName: string) => void
}

export class BaseRootStoreImpl implements BaseRootStore {
    actionsStore: ActionsStore

    errorStore: ErrorMessagesStore

    routingStore: RoutingStore

    breadcrumbsStore: BreadcrumbsStore

    constructor() {
        this.actionsStore = new ActionsStoreImpl()
        this.errorStore = new ErrorMessagesStoreImpl()
        this.routingStore = new RoutingStoreImpl()
        this.breadcrumbsStore = new BreadcrumbsStoreImpl(this)
    }

    callService<T>(service: RxResult<T>, successHandler: (payload: T) => void, actionName = ''): void {
        this.actionsStore.startAction(actionName)
        service.take(1).subscribe(either =>
            either.fold(
                error => {
                    this.actionsStore.failAction(actionName)
                    this.errorStore.handleError(error)
                },
                (payload: T) => {
                    successHandler(payload)
                    this.actionsStore.completeAction(actionName)
                }
            )
        )
    }

    call = <T>(serviceCall: () => RxResult<T>, actionName = ''): RxResult<T> => {
        this.actionsStore.startAction(actionName)
        return serviceCall.call(this).pipe(
            tap(
                r => this.handleStatus(r, actionName),
                e => this.handleError(actionName, e),
                () => this.handleComplete(actionName)
            )
        )
    }

    genericCall = <T>(serviceCall: () => Observable<T>, actionName = ''): Observable<T> => {
        this.actionsStore.startAction(actionName)
        return serviceCall.call(this).pipe(
            tap(
                noop,
                e => this.handleError(actionName, e),
                () => this.handleComplete(actionName)
            )
        )
    }

    promiseCall = async <T>(serviceCall: () => Promise<T>, actionName = ''): Promise<T> => {
        this.actionsStore.startAction(actionName)
        try {
            return await serviceCall.call(this).then(result => {
                this.handleComplete(actionName)
                return result
            })
        } catch (error) {
            let errorData = error
            if (error instanceof Response && typeof error.json === 'function') {
                errorData = await error.json()
                this.handleError(actionName, errorData)
            }

            return Promise.reject(errorData)
        }
    }

    handleComplete = (actionName = ''): void => {
        this.actionsStore.completeAction(actionName)
    }

    handleStatus = <T>(result: Either<ErrorMessage, T>, actionName = ''): void => {
        if (result.isLeft) {
            this.handleError(actionName, result.merge())
        }
    }

    handleError = (actionName: string, error: any) => {
        this.actionsStore.failAction(actionName)
        if ('errors' in error) {
            error.errors.map(this.postError)
        } else {
            this.postError(error)
        }
    }

    protected postError = (error: any) => {
        const errorMessage = isErrorMessage(error) ? error : parseGenesisBackendFailure(error)
        this.errorStore.handleError(errorMessage)
    }

    protected throwError = (message: string): never => {
        this.postError({message})
        throw new Error(message)
    }
}
