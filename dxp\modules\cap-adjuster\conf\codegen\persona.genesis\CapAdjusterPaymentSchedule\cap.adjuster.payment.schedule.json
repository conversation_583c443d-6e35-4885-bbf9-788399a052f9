{"swagger": "2.0", "x-dxp-spec": {"imports": {"payment.template": {"schema": "integration.cap.payment.schedule.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Claim Payment Schedule API", "version": "1", "title": "CAP Adjuster: Claim Payment Schedule API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/financial", "description": "CAP Adjuster: Financial API"}], "paths": {"/financial/payment-schedule/preview": {"post": {"x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/command/previewPaymentSchedule", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedule/cancel": {"post": {"x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/command/cancelPaymentSchedule", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedule/suspend": {"post": {"x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/command/suspendPaymentSchedule", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedule/unsuspend": {"post": {"x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/command/unsuspendPaymentSchedule", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedule/activate": {"post": {"x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/command/activatePaymentSchedule", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedule/generate-schedule-payments": {"post": {"description": "Generate Schedule Payments", "x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/command/generateSchedulePayments", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedules/load": {"post": {"x-dxp-path": "/api/cappaymentschedule/CapPaymentSchedule/v1/entities/loadPaymentSchedules", "tags": ["/cap-adjuster/v1/financial"]}}}}