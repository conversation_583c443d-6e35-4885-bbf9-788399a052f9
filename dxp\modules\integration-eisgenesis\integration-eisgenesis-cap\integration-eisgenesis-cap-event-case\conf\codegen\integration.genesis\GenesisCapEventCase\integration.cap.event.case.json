{"swagger": "2.0", "info": {"description": "API for CapEventCase", "version": "1", "title": "CapEventCase model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/CapEventCase/v1/command/closeCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/createCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/finalizeCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapEventCaseFinalizeInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/initCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapEventCaseInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/reopenCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/requestCloseCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/setCaseSubStatus": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapEventCaseSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/submitCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/updateCase": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/updateCaseDraft": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapEventCaseLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapEventCaseLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapEventCaseKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/timeline/{rootId}/{revisionNo}": {"get": {"description": "Returns available timeline details for the given entity.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapTimeline_CapTimelineEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/CapEventCaseApplicabilityInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCaseApplicabilityInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/capEventCaseClaims": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCaseClaimsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/capEventCaseFilteredClaims": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCaseFilteredClaimsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/capEventCaseRelatedClaimsAndPolicies": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCaseRelatedClaimsAndPoliciesOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/caseDuplicationValidate": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseDuplicationCheckResultEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/caseToClaimDetailsInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CaseToClaimDetailsInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/caseToHealthClaimDetailsInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CaseToHealthClaimDetailsInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/caseToHealthLossDetailsInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CaseToHealthLossDetailsInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/eventCaseClosureToOpenItems": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EventCaseClosureToOpenItemsOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/eventCaseRelatedHealthLosses": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EventCaseRelatedHealthLossesOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/transformation/eventCaseRelatedLosses": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/EventCaseRelatedLossesOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/CapEventCase/v1/command/overrideDuplication": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapEventCaseOverrideDuplicationInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapEventCaseApplicabilityInputOutputs": {"properties": {"output": {"$ref": "#/definitions/CapEventCase_CapEventCaseApplicabilityInput"}}, "title": "CapEventCaseApplicabilityInputOutputs"}, "CapEventCaseApplicabilityInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCaseApplicabilityInputOutputs"}}, "title": "CapEventCaseApplicabilityInputOutputsSuccess"}, "CapEventCaseApplicabilityInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCaseApplicabilityInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseApplicabilityInputOutputsSuccessBody"}, "CapEventCaseClaimsOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapEventCaseClaimsOutputs"}, "CapEventCaseClaimsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCaseClaimsOutputs"}}, "title": "CapEventCaseClaimsOutputsSuccess"}, "CapEventCaseClaimsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCaseClaimsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseClaimsOutputsSuccessBody"}, "CapEventCaseFilteredClaimsOutputs": {"properties": {"output": {"type": "object"}}, "title": "CapEventCaseFilteredClaimsOutputs"}, "CapEventCaseFilteredClaimsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCaseFilteredClaimsOutputs"}}, "title": "CapEventCaseFilteredClaimsOutputsSuccess"}, "CapEventCaseFilteredClaimsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCaseFilteredClaimsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseFilteredClaimsOutputsSuccessBody"}, "CapEventCaseFinalizeInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "skipRefresh": {"type": "boolean"}}, "title": "CapEventCaseFinalizeInput"}, "CapEventCaseFinalizeInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapEventCaseFinalizeInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseFinalizeInputBody"}, "CapEventCaseInitInput": {"required": ["entity"], "properties": {"entity": {"$ref": "#/definitions/CapEventCase_CapEventCaseDetailEntity"}, "memberRegistryTypeId": {"type": "string"}, "policyIds": {"type": "array", "items": {"type": "string"}}}, "title": "CapEventCaseInitInput"}, "CapEventCaseInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapEventCaseInitInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseInitInputBody"}, "CapEventCaseKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapEventCaseKrakenDeprecatedBundleRequest"}, "CapEventCaseKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapEventCaseKrakenDeprecatedBundleRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseKrakenDeprecatedBundleRequestBody"}, "CapEventCaseLoadHistoryResult": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntity"}}}, "title": "CapEventCaseLoadHistoryResult"}, "CapEventCaseLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCaseLoadHistoryResult"}}, "title": "CapEventCaseLoadHistoryResultSuccess"}, "CapEventCaseLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCaseLoadHistoryResultSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseLoadHistoryResultSuccessBody"}, "CapEventCaseOverrideDuplicationInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapEventCaseOverrideDuplicationInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseOverrideDuplicationInputBody"}, "CapEventCaseOverrideDuplicationInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "duplicationOverrideInd": {"type": "boolean"}, "duplicationOverrideReason": {"type": "string"}}, "title": "CapEventCaseOverrideDuplicationInput"}, "CapEventCaseRelatedClaimsAndPoliciesOutputs": {"properties": {"output": {"$ref": "#/definitions/CapRelatedClaimsAndPolicyIdsOutput"}}, "title": "CapEventCaseRelatedClaimsAndPoliciesOutputs"}, "CapEventCaseRelatedClaimsAndPoliciesOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCaseRelatedClaimsAndPoliciesOutputs"}}, "title": "CapEventCaseRelatedClaimsAndPoliciesOutputsSuccess"}, "CapEventCaseRelatedClaimsAndPoliciesOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCaseRelatedClaimsAndPoliciesOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseRelatedClaimsAndPoliciesOutputsSuccessBody"}, "CapEventCaseSubStatusInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "lossSubStatusCd": {"type": "string"}}, "title": "CapEventCaseSubStatusInput"}, "CapEventCaseSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapEventCaseSubStatusInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCaseSubStatusInputBody"}, "CapEventCase_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapEventCase AccessTrackInfo"}, "CapEventCase_CapAbsenceCPTEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceCPTEntity"}, "cptCode": {"type": "string", "description": "CPT code (Current procedural terminology)."}, "cptCodeDate": {"type": "string", "format": "date-time", "description": "Date of CPT procedure."}, "cptCodeDescription": {"type": "string", "description": "Description of CPT code."}}, "title": "CapEventCase CapAbsenceCPTEntity", "description": "Entity that encompases CPT codes of the Absence reasons OMC and Pregnancy"}, "CapEventCase_CapAbsenceDateInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceDateInformationEntity"}, "absenceDate": {"type": "string", "format": "date-time", "description": "This attribute describes the date user was/going to be absent."}, "missedTime": {"type": "string", "description": "This attribute describes how many hours and minutes the user was/going to be absent on the absenceDate."}}, "title": "CapEventCase CapAbsenceDateInformationEntity", "description": "Entity for Absence Dates information"}, "CapEventCase_CapAbsenceDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceDetailEntity"}, "absencePeriods": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsencePeriodEntity"}}, "absencePeriodsExpected": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsencePeriodExpectedEntity"}}, "absenceReason": {"$ref": "#/definitions/CapEventCase_CapAbsenceReasonEntity"}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations."}, "annualSalaryAmount": {"$ref": "#/definitions/Money"}, "finalPtoDate": {"type": "string", "format": "date-time", "description": "Determines the last day before absence if a member had paid time off, used in elimination period calc."}, "firstDayHospitalizationDate": {"type": "string", "format": "date-time", "description": "First Day of Hospitalization."}, "isOtherIncome": {"type": "boolean", "description": "Indicates if member has other income."}, "lossDate": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossDesc": {"type": "string", "description": "Description of loss itself."}, "outpatientSurgeryDate": {"type": "string", "format": "date-time", "description": "Outpatient Surgery Date."}, "paymentFrequencyCd": {"type": "string"}, "returnToWorkDate": {"type": "string", "format": "date-time", "description": "Return to Work date."}, "typicalWorkWeek": {"$ref": "#/definitions/CapEventCase_CapAbsenceTypicalWorkWeekEntity"}, "wasDaysUsedAfterLdw": {"type": "boolean", "description": "Identifies if a member was sick, on vacation or PTO after the last day at work. Used in determining if the claim can be AA."}}, "title": "CapEventCase CapAbsenceDetailEntity", "description": "Entity that encompasses Absence Claim Event details."}, "CapEventCase_CapAbsenceExpectedOccurrenceAbsentDatesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceExpectedOccurrenceAbsentDatesEntity"}, "absenceDate": {"type": "string", "format": "date-time", "description": "Actual absence date within the intermittent absence period."}, "absenceSeconds": {"type": "integer", "format": "int64", "description": "Actual absence time in seconds within the intermittent absence period date."}}, "title": "CapEventCase CapAbsenceExpectedOccurrenceAbsentDatesEntity", "description": "Entity for the specific absence data within the intermittent and reduced absence period of type Specific."}, "CapEventCase_CapAbsenceExpectedOccurrenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceExpectedOccurrenceInfoEntity"}, "absenceDateType": {"type": "string", "description": "Describes the type of absence date. Two types available, Specific Dates, and General Dates."}, "expectedAbsences": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsenceExpectedOccurrenceAbsentDatesEntity"}}, "numberOfOccurrences": {"type": "integer", "format": "int64", "description": "Describes the number of absence occurrences."}, "occurrenceType": {"type": "string", "description": "Describes the type of occurrence for an absence case."}, "perTimeframe": {"type": "string", "description": "Describes the timeframe within which the number of absence occurrences happens."}, "timePerOccurrence": {"type": "string", "description": "Describes the general number of absence days(hours and mins) per occurrence."}}, "title": "CapEventCase CapAbsenceExpectedOccurrenceInfoEntity", "description": "Entity to capture Expected Absence period details for Intermittent and Reduced Schedule Absence types."}, "CapEventCase_CapAbsenceIntermittentOccurrenceActualAbsencesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceIntermittentOccurrenceActualAbsencesEntity"}, "absenceDate": {"type": "string", "format": "date-time", "description": "Actual absence date within the intermittent absence period."}, "absenceSeconds": {"type": "integer", "format": "int64", "description": "Actual absence time in seconds within the intermittent absence period date."}}, "title": "CapEventCase CapAbsenceIntermittentOccurrenceActualAbsencesEntity", "description": "Entity for the Actual absence data within the intermittent absence period."}, "CapEventCase_CapAbsencePeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsencePeriodEntity"}, "absenceDateInformation": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsenceDateInformationEntity"}}, "absencePeriod": {"$ref": "#/definitions/CapEventCase_Period"}, "absenceTypeCd": {"type": "string", "description": "This attribute describes the type of absence in time perspective."}, "actualRtwDate": {"type": "string", "format": "date-time", "description": "Actual Return to work date."}, "estimatedRtwDate": {"type": "string", "format": "date-time", "description": "Estimated Return to work date."}, "fullDay": {"type": "boolean", "description": "Option to choose if absence took full day or not."}, "intermittentOccurrenceInfo": {"$ref": "#/definitions/CapEventCase_CapIntermittentOccurrenceInfoEntity"}, "reducedPeriodDetails": {"$ref": "#/definitions/CapEventCase_CapAbsenceReducedPeriodDetailsEntity"}, "returnToWorkDateDetails": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapReturnToWorkDateDetailEntity"}}}, "title": "CapEventCase CapAbsencePeriodEntity", "description": "Entity to capture absence period details"}, "CapEventCase_CapAbsencePeriodExpectedEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsencePeriodExpectedEntity"}, "absencePeriod": {"$ref": "#/definitions/CapEventCase_Period"}, "absenceTypeCd": {"type": "string", "description": "This attribute describes the type of Expected absence in time perspective."}, "occurrenceInfo": {"$ref": "#/definitions/CapEventCase_CapAbsenceExpectedOccurrenceInfoEntity"}}, "title": "CapEventCase CapAbsencePeriodExpectedEntity", "description": "Entity to capture Expected Absence period details"}, "CapEventCase_CapAbsenceReasonEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceReasonEntity"}, "absenceReasons": {"type": "array", "items": {"type": "string", "description": "Attribute for multiple Absence reasons."}}, "bondings": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapBondingReasonInformationEntity"}}, "careForFamilyMembers": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapCareForFamilyMemberReasonInformationEntity"}}, "military": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapMilitaryReasonInformationEntity"}}, "others": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapOtherReasonInformationEntity"}}, "ownMedicalConditions": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapOwnMedicalConditionReasonInformationEntity"}}, "pregnancies": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapPregnancyReasonInformationEntity"}}}, "title": "CapEventCase CapAbsenceReasonEntity", "description": "Entity that contains Absence reason information"}, "CapEventCase_CapAbsenceReducedPeriodDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceReducedPeriodDetailsEntity"}, "secondsFri": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Fridays within the Reduced absence period."}, "secondsMon": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Mondays within the Reduced absence period."}, "secondsSat": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Saturdays within the Reduced absence period."}, "secondsSun": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Sundays within the Reduced absence period."}, "secondsThu": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Thursdays within the Reduced absence period."}, "secondsTue": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Tuesdays within the Reduced absence period."}, "secondsWed": {"type": "integer", "format": "int64", "description": "Absence time in seconds on Wednesdays within the Reduced absence period."}}, "title": "CapEventCase CapAbsenceReducedPeriodDetailsEntity", "description": "Entity that contains absence period information for the period when a member is out of work in a predictable manner"}, "CapEventCase_CapAbsenceTypicalWorkWeekEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAbsenceTypicalWorkWeekEntity"}, "hoursFri": {"type": "number", "description": "Defines how many hours member works on Fridays."}, "hoursMon": {"type": "number", "description": "Defines how many hours member works on Mondays."}, "hoursSat": {"type": "number", "description": "Defines how many hours member works on Saturdays."}, "hoursSun": {"type": "number", "description": "Defines how many hours member works on Sundays."}, "hoursThu": {"type": "number", "description": "Defines how many hours member works on Thursdays."}, "hoursTue": {"type": "number", "description": "Defines how many hours member works on Tuesdays."}, "hoursWed": {"type": "number", "description": "Defines how many hours member works on Wednesdays."}}, "title": "CapEventCase CapAbsenceTypicalWorkWeekEntity", "description": "Entity to capture members Typical work week (working days and hours) information."}, "CapEventCase_CapAcceleratedDeathDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAcceleratedDeathDetailEntity"}, "dateOfDiagnosis": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed."}, "dateOfFirstTreatment": {"type": "string", "format": "date", "description": "Date of insured's been first treated."}, "dateOfLifeExpectancyPrescribed": {"type": "string", "format": "date", "description": "Date of insured's been diagnosed with life expectancy prescribed with terminal illness."}, "lifeExpectancy": {"type": "number", "description": "Life Expectancy(months)-remaining month of life to live."}}, "title": "CapEventCase CapAcceleratedDeathDetailEntity", "description": "Main object to encompass Claim Event Accelerated Death details."}, "CapEventCase_CapAccidentDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAccidentDetailEntity"}, "accidentDate": {"type": "string", "format": "date-time", "description": "The Date of Accident."}, "dateOfFirstTreatment": {"type": "string", "format": "date", "description": "The Date of First Treatment of Accident."}, "isChildOrganizedSport": {"type": "boolean", "description": "Is Child Organized Sport"}, "isWorkRelated": {"type": "boolean", "description": "Defines if Accident is work related."}, "locationComment": {"type": "string", "description": "Description of Accident Location."}, "locationOfAccident": {"type": "string", "description": "This attribute describes Accident Location."}, "lossItems": {"type": "array", "items": {"type": "string", "description": "Loss Items"}}}, "title": "CapEventCase CapAccidentDetailEntity", "description": "The Date of Accident details"}, "CapEventCase_CapAdditionalRole": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapAdditionalRole"}, "associatedWith": {"type": "array", "items": {"type": "string", "description": "Describes the party associated with which loss type(i.e. Absence, STD, SMP, Death, PremiemWaiver etc."}}, "checkAddressId": {"type": "string", "description": "Address for chech payment method."}, "paymentMethodId": {"type": "string", "description": "Preferred payment method."}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}, "relationshipToInsuredCd": {"type": "string", "description": "Defines party relationship to Insured"}, "relationshipToInsuredDesc": {"type": "string", "description": "Defines party relationship to Insured description, just for relationshipToInsuredCd=OTHER"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}, "vendorServiceCd": {"type": "array", "items": {"type": "string", "description": "Defines the list of supported service types by <PERSON><PERSON><PERSON>"}}}, "title": "CapEventCase CapAdditionalRole", "description": "<PERSON><PERSON><PERSON> for additional party role"}, "CapEventCase_CapBenefitRelatedEventEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBenefitRelatedEventEntity"}, "eventDate": {"type": "string", "format": "date", "description": "Describes Event Date of event."}, "eventTypeCd": {"type": "string", "description": "Describe Event type."}, "manualAdd": {"type": "boolean", "description": "The indicator marks if the event type & date is added manually."}}, "title": "CapEventCase CapBenefitRelatedEventEntity", "description": "Entity for Event details."}, "CapEventCase_CapBondingReasonInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapBondingReasonInformationEntity"}, "bondingType": {"type": "string", "description": "Defines bonding type."}, "bondingTypeDate": {"type": "string", "format": "date", "description": "Attribute to capture Ad<PERSON><PERSON> or Foster date."}, "registryTypeId": {"type": "string", "description": "Defines unique customer ID."}}, "title": "CapEventCase CapBondingReasonInformationEntity", "description": "Entity for Bonding information."}, "CapEventCase_CapCIDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCIDetailEntity"}, "dateOfCIDiagnosis": {"type": "string", "format": "date-time"}, "dateOfFirstTreatment": {"type": "string", "format": "date", "description": "Date of insured's been first treated."}, "isRecurring": {"type": "boolean", "description": "Recurring Indicator"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "The date of proof of Loss is received."}, "providerName": {"type": "string"}}, "title": "CapEventCase CapCIDetailEntity"}, "CapEventCase_CapCareForFamilyMemberReasonInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCareForFamilyMemberReasonInformationEntity"}, "registryTypeIds": {"type": "array", "items": {"type": "string", "description": "Defines unique customer ID."}}}, "title": "CapEventCase CapCareForFamilyMemberReasonInformationEntity", "description": "Entity for Care for family member reason information."}, "CapEventCase_CapClaimDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimDetailsEntity"}, "caseNumber": {"type": "string"}, "claimCase": {"$ref": "#/definitions/CapEventCase_CapClaimHeaderCase"}, "claimDate": {"type": "string", "format": "date-time"}, "claimModelName": {"type": "string"}, "claimNumber": {"type": "string"}, "claimType": {"type": "string"}, "claimant": {"$ref": "#/definitions/CapEventCase_ClaimantAware"}, "externalPeriods": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_ExternalPeriod"}}, "insured": {"$ref": "#/definitions/CapEventCase_InsuredAware"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapClaimHeaderPolicy"}}, "reportedDate": {"type": "string", "format": "date-time"}, "state": {"type": "string"}, "subjects": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_ClaimSubjectInfo"}}, "totalIncurred": {"$ref": "#/definitions/Money"}}, "title": "CapEventCase CapClaimDetailsEntity", "description": "Extended claim header."}, "CapEventCase_CapClaimEventEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimEventEntity"}, "absenceDetail": {"$ref": "#/definitions/CapEventCase_CapAbsenceDetailEntity"}, "acceleratedDeathDetail": {"$ref": "#/definitions/CapEventCase_CapAcceleratedDeathDetailEntity"}, "accidentDetail": {"$ref": "#/definitions/CapEventCase_CapAccidentDetailEntity"}, "ciDetail": {"$ref": "#/definitions/CapEventCase_CapCIDetailEntity"}, "claimEvents": {"type": "array", "items": {"type": "string", "description": "Collection of the Claim Events within the Event Case."}}, "deathDetail": {"$ref": "#/definitions/CapEventCase_CapDeathDetailEntity"}, "hiDetail": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapHIDetailEntity"}}, "wellnessDetail": {"$ref": "#/definitions/CapEventCase_CapWellnessDetailEntity"}}, "title": "CapEventCase CapClaimEventEntity", "description": "Main entity to encompass Claim Events of the Event Case."}, "CapEventCase_CapClaimHeaderCase": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimHeaderCase"}, "caseNumber": {"type": "string"}}, "title": "CapEventCase CapClaimHeaderCase"}, "CapEventCase_CapClaimHeaderPolicy": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapClaimHeaderPolicy"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}}, "title": "CapEventCase CapClaimHeaderPolicy"}, "CapEventCase_CapCoverageInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapCoverageInfo"}, "coverageCd": {"type": "string"}, "earningsDefinitionCd": {"type": "string"}, "employmentStatus": {"type": "string"}, "employmentTerminiationDate": {"type": "string", "format": "date"}, "partyTypeCd": {"type": "string"}, "workweekTypeCd": {"type": "integer", "format": "int64"}}, "title": "CapEventCase CapCoverageInfo"}, "CapEventCase_CapDeathDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDeathDetailEntity"}, "deathCertificateReceivedDate": {"type": "string", "format": "date-time", "description": "The Date Death Certificate was Received."}, "deathCountry": {"type": "string", "description": "The Country of Death."}, "deathState": {"type": "string", "description": "The State of Death."}, "officialDeathCause": {"type": "string", "description": "Official Death Cause."}, "officialDeathDate": {"type": "string", "format": "date-time", "description": "Official Death Date."}, "officialDeathManner": {"type": "string", "description": "Official Death Manner."}, "officialDeathMannerDesc": {"type": "string", "description": "Only display if OfficialDateOfDeathMandantory=Other for detail information capture."}, "underlyingConditions": {"type": "string", "description": "Underlying conditions of Death."}}, "title": "CapEventCase CapDeathDetailEntity", "description": "Main object to encompass Claim Event Death details."}, "CapEventCase_CapDiagnosisInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDiagnosisInformationEntity"}, "date": {"type": "string", "format": "date", "description": "Defines date of diagnosis."}, "icdCode": {"type": "string", "description": "Defines code of diagnosis."}, "primaryCode": {"type": "boolean", "description": "Defines if diagnosis code is primary."}}, "title": "CapEventCase CapDiagnosisInformationEntity", "description": "Entity for diagnosis information."}, "CapEventCase_CapEmployerRole": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEmployerRole"}, "checkAddressId": {"type": "string", "description": "Address for chech payment method."}, "paymentMethodId": {"type": "string", "description": "Preferred payment method"}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapEventCase CapEmployerRole", "description": "Entity for employer role"}, "CapEventCase_CapEmploymentDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEmploymentDetailEntity"}, "className": {"type": "string", "description": "Employee's Class name from CEM/Census with regards to selected Employer."}, "customerLink": {"$ref": "#/definitions/EntityLink"}, "employerRole": {"$ref": "#/definitions/CapEventCase_CapEmployerRole"}, "hireDate": {"type": "string", "format": "date", "description": "Employee's Date of hire with regards to selected Employer."}, "jobClassificationCd": {"type": "string", "description": "Employee's Job class code."}, "occupationClassCd": {"type": "string", "description": "Employee's Occupation Category code with regards to selected Employer."}, "workStateCodeCd": {"type": "string", "description": "Employee's Work State Code with regards to selected Employer."}}, "title": "CapEventCase CapEmploymentDetailEntity", "description": "Object depicts member employment details."}, "CapEventCase_CapEventCaseAPOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseAPOpenItemInfo"}, "apPeriod": {"$ref": "#/definitions/CapEventCase_Period"}, "apStatus": {"type": "string", "description": "The decision made upon each approval period whether it's accepted or not"}, "claimNumber": {"type": "string", "description": "Claim Number"}}, "title": "CapEventCase CapEventCaseAPOpenItemInfo"}, "CapEventCase_CapEventCaseApplicabilityInput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseApplicabilityInput"}, "absenceReasonCd": {"type": "array", "items": {"type": "string"}}, "caseDetails": {"$ref": "#/definitions/CapEventCase_CapEventCaseDetailEntity"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "currentDate": {"type": "string", "format": "date-time"}, "eliminationPeriodCd": {"type": "integer", "format": "int64"}, "externalClaims": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_ExternalClaimDataEntity"}}, "genderCd": {"type": "string"}, "initialAbsenceCaseDate": {"type": "string", "format": "date-time"}, "initialLifeCaseDate": {"type": "string", "format": "date"}, "paymentLevelCd": {"type": "string", "description": "Defines payments processing level. Payments can be processed per claim or per case."}, "policies": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapPolicyInfoEntity"}}, "workStateCd": {"type": "string"}}, "title": "CapEventCase CapEventCaseApplicabilityInput", "description": "Input for applicability rules evaluation"}, "CapEventCase_CapEventCaseApplicabilityResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseApplicabilityResultEntity"}, "applicableTypes": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseApplicableTypeEntity"}}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_MessageType"}}, "paymentLevelCd": {"type": "string", "description": "Defines payments processing level. Payments can be processed per claim or per case."}}, "title": "CapEventCase CapEventCaseApplicabilityResultEntity", "description": "Business entity defines a collection of Applicability results"}, "CapEventCase_CapEventCaseApplicableTypeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseApplicableTypeEntity"}, "absenceReasonCd": {"type": "string", "description": "Defines reason of absence"}, "activelyAtWorkDate": {"type": "string", "format": "date-time", "description": "Last date and time when the Insured considered actively at work prior to absence, including weekends and vacations."}, "applicabilityEvaluationCd": {"type": "string"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem"}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored in CAP subsystem."}, "claimTypeCd": {"type": "string"}, "coverageTypeCd": {"type": "string", "description": "This attribute describes the coverage type."}, "externalReference": {"type": "string", "description": "Holds external claim uri."}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "lossDate": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossTypeCd": {"type": "string", "description": "Type of loss to identify Life/CI/HI loss type "}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when lifeIntake was reported."}}, "title": "CapEventCase CapEventCaseApplicableTypeEntity", "description": "Business entity that defines applicability result."}, "CapEventCase_CapEventCaseBalanceOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseBalanceOpenItemInfo"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "CapEventCase CapEventCaseBalanceOpenItemInfo", "description": "Entity for balance open item info"}, "CapEventCase_CapEventCaseClaimOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseClaimOpenItemInfo"}, "claimLink": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "claimNumber": {"type": "string", "description": "Claim Number"}, "claimSubStatus": {"type": "string", "description": "Loss Sub Status"}, "claimType": {"type": "string", "description": "Claim Type"}}, "title": "CapEventCase CapEventCaseClaimOpenItemInfo", "description": "Entity for claim open item info"}, "CapEventCase_CapEventCaseClosureOpenItemsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseClosureOpenItemsOutput"}, "actualPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCasePaymentDefinitionOpenItemInfo"}}, "apInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseAPOpenItemInfo"}}, "balanceInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseBalanceOpenItemInfo"}}, "claimInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseClaimOpenItemInfo"}}, "coverageInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseCoverageOpenItemInfo"}}, "issuedPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCasePaymentDefinitionOpenItemInfo"}}, "scheduledPaymentInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseScheduledPaymentOpenItemInfo"}}}, "title": "CapEventCase CapEventCaseClosureOpenItemsOutput", "description": "Entity for closure open items output"}, "CapEventCase_CapEventCaseCoverageOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseCoverageOpenItemInfo"}, "apInfos": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseAPOpenItemInfo"}}, "claimNumber": {"type": "string", "description": "Claim Number"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "dateRange": {"$ref": "#/definitions/CapEventCase_Period"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Incident Date"}, "lossSource": {"type": "string", "description": "Loss Source"}, "totalGBAorDuration": {"type": "number", "description": "Total amount to be paid"}, "unpaidGBAorDuration": {"type": "number", "description": "Total amount to be unpaid"}}, "title": "CapEventCase CapEventCaseCoverageOpenItemInfo", "description": "Entity for coverage open item info"}, "CapEventCase_CapEventCaseCustomerInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseCustomerInformationEntity"}, "genderCd": {"type": "string", "description": "Is used to identify customer gender"}, "registryTypeId": {"type": "string", "description": "Is used to corelate which party this is pulled for"}}, "title": "CapEventCase CapEventCaseCustomerInformationEntity", "description": "Contains customer information that is needed for calculations"}, "CapEventCase_CapEventCaseDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapEventCase"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapEventCaseDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "additionalRole": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAdditionalRole"}}, "claimEvent": {"$ref": "#/definitions/CapEventCase_CapClaimEventEntity"}, "dependentLink": {"$ref": "#/definitions/EntityLink"}, "diagnoses": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapDiagnosisInformationEntity"}}, "employmentDetail": {"$ref": "#/definitions/CapEventCase_CapEmploymentDetailEntity"}, "eventDate": {"type": "string", "format": "date-time", "description": "The earliest date of all different loss dates."}, "events": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapBenefitRelatedEventEntity"}}, "externalReference": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_ExternalReference"}}, "financialAdjustment": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentEntity"}, "financialAdjustmentAddition": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentAdditionEntity"}, "isSicknessInjury": {"type": "string", "description": "Injury vs Sickness."}, "lastUpdatedOriginSource": {"type": "string", "description": "Last Updated Origin Source"}, "lastWorkDates": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapLastWorkDateEntity"}}, "lossDesc": {"type": "string", "description": "Capture any information of the Event Case intake."}, "memberCheckAddressId": {"type": "string", "description": "Address for member chech payment method."}, "memberPaymentMethodId": {"type": "string", "description": "Preferred payment method for a member."}, "paymentFrequencyCd": {"type": "string", "description": "Frequency of the payments for the Event Case."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when claim application is reported."}, "reportingMethod": {"type": "string", "description": "Indicate how the claim application is reported, e.g. by Phone Call,Email,Mail,Walk-In,Self-Service etc."}, "subjectOfCaseLink": {"type": "string", "description": "Subject of case, which can be member or dependent, unique identifier. "}, "subjectOfCaseRole": {"$ref": "#/definitions/CapEventCase_CapSubjectOfCaseRole"}}, "title": "CapEventCase CapEventCaseDetailEntity", "description": "Main entity to encompass Event Case details."}, "CapEventCase_CapEventCaseDuplicationCheckResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseDuplicationCheckResultEntity"}, "caseDuplicateList": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseDuplicationResultDetailsEntity"}}, "threshold": {"type": "integer", "format": "int64", "description": "<PERSON><PERSON><PERSON><PERSON>"}}, "title": "CapEventCase CapEventCaseDuplicationCheckResultEntity"}, "CapEventCase_CapEventCaseDuplicationCheckResultEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCase_CapEventCaseDuplicationCheckResultEntity"}}, "title": "CapEventCase_CapEventCaseDuplicationCheckResultEntitySuccess"}, "CapEventCase_CapEventCaseDuplicationCheckResultEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCase_CapEventCaseDuplicationCheckResultEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCase_CapEventCaseDuplicationCheckResultEntitySuccessBody"}, "CapEventCase_CapEventCaseDuplicationMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseDuplicationMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "ruleScore": {"type": "integer", "format": "int64", "description": "Rule Score"}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapEventCase CapEventCaseDuplicationMessageEntity", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapEventCase_CapEventCaseDuplicationResultDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseDuplicationResultDetailsEntity"}, "caseNumber": {"type": "string", "description": "Case Number"}, "caseStatus": {"type": "string", "description": "Case Status"}, "caseURI": {"type": "string", "description": "Case URI"}, "isSimilar": {"type": "boolean", "description": "Is Similar"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapEventCaseDuplicationMessageEntity"}}}, "title": "CapEventCase CapEventCaseDuplicationResultDetailsEntity"}, "CapEventCase_CapEventCaseEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapEventCase"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapEventCaseEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapEventCase_AccessTrackInfo"}, "applicabilityResult": {"$ref": "#/definitions/CapEventCase_CapEventCaseApplicabilityResultEntity"}, "claim": {"$ref": "#/definitions/EntityLink"}, "duplicationCheckResult": {"$ref": "#/definitions/CapEventCase_CapEventCaseDuplicationCheckResultEntity"}, "duplicationOverrideInd": {"type": "boolean"}, "duplicationOverrideReason": {"type": "string"}, "externalClaims": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_ExternalClaimDataEntity"}}, "lossDetail": {"$ref": "#/definitions/CapEventCase_CapEventCaseDetailEntity"}, "lossNumber": {"type": "string", "description": "A unique Event Case number."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "memberInfo": {"$ref": "#/definitions/CapEventCase_CapEventCaseCustomerInformationEntity"}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID."}, "memberRole": {"$ref": "#/definitions/CapEventCase_CapMemberRole"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapPolicyInfoEntity"}}, "policyIds": {"type": "array", "items": {"type": "string", "description": "Indicates all available policy Ids."}}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "refreshedPolicy": {"type": "string", "description": "Transient attribute to identify exact policy id is refreshed"}, "state": {"type": "string", "description": "A current Event Case status"}}, "title": "CapEventCase CapEventCaseEntity", "description": "Main entity to encompass Event Case data."}, "CapEventCase_CapEventCaseEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntity"}}, "title": "CapEventCase_CapEventCaseEntitySuccess"}, "CapEventCase_CapEventCaseEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapEventCase_CapEventCaseEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapEventCase_CapEventCaseEntitySuccessBody"}, "CapEventCase_CapEventCasePaymentDefinitionOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCasePaymentDefinitionOpenItemInfo"}, "claimNumber": {"type": "array", "items": {"type": "string", "description": "Claim Number"}}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Payment Number"}, "paymentState": {"type": "string", "description": "Payment State"}}, "title": "CapEventCase CapEventCasePaymentDefinitionOpenItemInfo", "description": "Entity for payment definition open item info"}, "CapEventCase_CapEventCaseScheduledPaymentOpenItemInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapEventCaseScheduledPaymentOpenItemInfo"}, "allocationPeriod": {"$ref": "#/definitions/CapEventCase_Period"}, "claimNumber": {"type": "string", "description": "Claim Number"}, "countOfUnposted": {"type": "integer", "format": "int64", "description": "Count of Unposted"}, "coverageName": {"type": "string", "description": "Coverage Name"}, "frequencyType": {"type": "string", "description": "Frequency Type"}, "payeeDisplay": {"type": "string", "description": "<PERSON>ee <PERSON>lay"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment Date"}, "paymentScheduleNumber": {"type": "string", "description": "Payment Schedule Number"}}, "title": "CapEventCase CapEventCaseScheduledPaymentOpenItemInfo", "description": "Entity for schedule payment open item info"}, "CapEventCase_CapFederalTaxTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFederalTaxTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapEventCase CapFederalTaxTermEntity"}, "CapEventCase_CapFinancialAdditionAncillaryActivitiesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdditionAncillaryActivitiesEntity"}, "financialAdditionRehab": {"$ref": "#/definitions/CapEventCase_CapFinancialAdditionRehabEntity"}, "type": {"type": "string", "description": "Contains information about type of ancillary Activity"}}, "title": "CapEventCase CapFinancialAdditionAncillaryActivitiesEntity", "description": "Contains information about ancillary Activities of financial Adjustment Addition"}, "CapEventCase_CapFinancialAdditionRehabEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdditionRehabEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "rehabTerm": {"$ref": "#/definitions/CapEventCase_Term"}}, "title": "CapEventCase CapFinancialAdditionRehabEntity", "description": "Contains information about Rehabilitation information of ancillary Activity when type is REHAB"}, "CapEventCase_CapFinancialAdjustmentAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentAdditionEntity"}, "ancillaryActivities": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapFinancialAdditionAncillaryActivitiesEntity"}}}, "title": "CapEventCase CapFinancialAdjustmentAdditionEntity", "description": "Contains information about financial Adjustment Addition"}, "CapEventCase_CapFinancialAdjustmentDeductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentDeductionEntity"}, "deductionAmountType": {"type": "string", "description": "Defines deduction amount type"}, "deductionBeneficiarySource": {"$ref": "#/definitions/EntityLink"}, "deductionFixedAmount": {"$ref": "#/definitions/Money"}, "deductionMonthlyAmount": {"$ref": "#/definitions/Money"}, "deductionPaidFrom": {"$ref": "#/definitions/EntityLink"}, "deductionPct": {"type": "number", "description": "This attribute represents the percentage of Deductions to be made from the Claim payment."}, "deductionProviderSource": {"$ref": "#/definitions/EntityLink"}, "deductionTerm": {"$ref": "#/definitions/CapEventCase_Term"}, "deductionType": {"type": "string", "description": "This attribute describes the target for the deduction amount that will be paid by the Claim."}, "deductionWeeklyAmount": {"$ref": "#/definitions/Money"}, "isPreTax": {"type": "boolean", "description": "This attribute defines if the deductions should be applied pre taxes. If the value is set to 'no', the deductions are applied post taxes."}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "lossTypes": {"type": "array", "items": {"type": "string", "description": "Types of related Claim Types this Deduction is applied to."}}, "nonProviderPaymentType": {"type": "string", "description": "This field describes the type of Non-Provider Payment."}, "stateProvided": {"type": "string", "description": "Ths attribute describes in which state the Child Support was provided."}}, "title": "CapEventCase CapFinancialAdjustmentDeductionEntity", "description": "This business entity describes the Deduction amount that can be paid to the Sponsor from the claim to cover other Premiums or External payments."}, "CapEventCase_CapFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentEntity"}, "deductions": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentDeductionEntity"}}, "isMedicareExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Medicare taxes."}, "isOasdiExempt": {"type": "boolean", "description": "Defines if a claimant is subject to FICA Social Security tax."}, "taxes": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentTaxEntity"}}, "withholdings": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentWithholdingEntity"}}, "ytdEarnings": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentYTDEarningsEntity"}}}, "title": "CapEventCase CapFinancialAdjustmentEntity", "description": "The main entity for financial adjustment details (taxes, deductions, offsets etc.)."}, "CapEventCase_CapFinancialAdjustmentTaxDetailsFederalEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentTaxDetailsFederalEntity"}, "federalTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines federal tax exemptions."}, "federalTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "federalTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for federal tax calculation."}, "federalTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "federalTaxPercentage": {"type": "number", "description": "Defines Federal tax percentage."}, "federalTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for federal tax calculation."}, "federalTaxTerm": {"$ref": "#/definitions/CapEventCase_CapFederalTaxTermEntity"}, "federalTaxType": {"type": "string", "description": "Defines Federal tax type."}, "federalTaxWeeklyAmount": {"$ref": "#/definitions/Money"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "lossTypes": {"type": "array", "items": {"type": "string", "description": "Links to the related Claims this Tax is applied to."}}}, "title": "CapEventCase CapFinancialAdjustmentTaxDetailsFederalEntity", "description": "Defines Federal tax details."}, "CapEventCase_CapFinancialAdjustmentTaxDetailsStateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentTaxDetailsStateEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "lossTypes": {"type": "array", "items": {"type": "string", "description": "Links to the related Claims this Tax is applied to."}}, "stateTaxExemptions": {"type": "integer", "format": "int64", "description": "Defines state tax exemptions."}, "stateTaxExtraWithholding": {"$ref": "#/definitions/Money"}, "stateTaxMaritalStatus": {"type": "string", "description": "Defines marital status used for state tax calculation."}, "stateTaxMonthlyAmount": {"$ref": "#/definitions/Money"}, "stateTaxPercentage": {"type": "number", "description": "Defines State tax percentage."}, "stateTaxState": {"type": "string", "description": "Defines <PERSON><PERSON><PERSON><PERSON>'s state of residence used for state tax calculation."}, "stateTaxTerm": {"$ref": "#/definitions/CapEventCase_CapStateTaxTermEntity"}, "stateTaxType": {"type": "string", "description": "Defines State tax type."}, "stateTaxWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapEventCase CapFinancialAdjustmentTaxDetailsStateEntity", "description": "Defines State tax details."}, "CapEventCase_CapFinancialAdjustmentTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentTaxEntity"}, "financialAdjustmentTaxDetailsFederal": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentTaxDetailsFederalEntity"}, "financialAdjustmentTaxDetailsState": {"$ref": "#/definitions/CapEventCase_CapFinancialAdjustmentTaxDetailsStateEntity"}, "jurisdictionType": {"type": "string", "description": "Defines jurisdiction type"}, "taxType": {"type": "string", "description": "Defines the type of a tax."}, "term": {"$ref": "#/definitions/CapEventCase_Term"}}, "title": "CapEventCase CapFinancialAdjustmentTaxEntity", "description": "This business entity describes the withholding taxes, and the amounts that will be applied to the Claim payments."}, "CapEventCase_CapFinancialAdjustmentWithholdingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentWithholdingEntity"}, "lossSources": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "lossTypes": {"type": "array", "items": {"type": "string", "description": "Claim Type to which withholding applies."}}, "withholdingMonthlyAmount": {"$ref": "#/definitions/Money"}, "withholdingPayee": {"$ref": "#/definitions/EntityLink"}, "withholdingPct": {"type": "number", "description": "Withholding percentage."}, "withholdingWeeklyAmount": {"$ref": "#/definitions/Money"}}, "title": "CapEventCase CapFinancialAdjustmentWithholdingEntity", "description": "Entity for withholding details."}, "CapEventCase_CapFinancialAdjustmentYTDEarningsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapFinancialAdjustmentYTDEarningsEntity"}, "year": {"type": "integer", "format": "int64", "description": "The year of the Year to date earnings amount defined manually by the user."}, "ytdEarningsAmount": {"$ref": "#/definitions/Money"}}, "title": "CapEventCase CapFinancialAdjustmentYTDEarningsEntity", "description": "Defines Year to Date Earnings amounts by Year."}, "CapEventCase_CapHIDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapHIDetailEntity"}, "cptCode": {"type": "string", "description": "CPT Code"}, "dateRange": {"$ref": "#/definitions/CapEventCase_Period"}, "numberOfUnits": {"type": "integer", "format": "int64", "description": "# Days / # Occurences"}, "placeOfService": {"type": "string", "description": "Place of Service"}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "POL Received Date"}, "providerName": {"type": "string", "description": "Provider Name"}, "serviceDate": {"type": "string", "format": "date-time", "description": "Service Date"}}, "title": "CapEventCase CapHIDetailEntity"}, "CapEventCase_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapEventCase CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapEventCase_CapIntermittentOccurrenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapIntermittentOccurrenceInfoEntity"}, "absenceDateType": {"type": "string", "description": "Describes the type of absence date. Two types available, Specific Dates, and General Dates."}, "actualAbsences": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsenceIntermittentOccurrenceActualAbsencesEntity"}}, "numberOfOccurrences": {"type": "integer", "format": "int64", "description": "Describes the number of absence occurrences."}, "occurrenceType": {"type": "string", "description": "Describes the type of occurrence for an absence case."}, "perTimeframe": {"type": "string", "description": "Describes the timeframe within which the number of absence occurrences happens."}, "timePerOccurrence": {"type": "string", "description": "Describes the general number of absence days(hours and mins) per occurrence."}}, "title": "CapEventCase CapIntermittentOccurrenceInfoEntity", "description": "Entity for the intermittent type of absence period details."}, "CapEventCase_CapLastWorkDateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapLastWorkDateEntity"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Collection of the member's Last work dates and times ."}}, "title": "CapEventCase CapLastWorkDateEntity", "description": "Entity to encompass Last work dates of the member."}, "CapEventCase_CapMemberRole": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMemberRole"}, "genderCd": {"type": "string", "description": "Gender Code"}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapEventCase CapMemberRole", "description": "Entity for member party role"}, "CapEventCase_CapMilitaryReasonInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapMilitaryReasonInformationEntity"}, "deploymentDate": {"type": "string", "format": "date", "description": "Describes date of Deployment."}, "militaryMember": {"type": "string", "description": "Describes who is Military member."}, "militaryType": {"type": "string", "description": "Defines military type"}, "registryTypeId": {"type": "string", "description": "Defines unique customer ID."}}, "title": "CapEventCase CapMilitaryReasonInformationEntity", "description": "Entity for military information."}, "CapEventCase_CapOmcHospitalInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapOmcHospitalInfoEntity"}, "surgeryDate": {"type": "string", "format": "date-time", "description": "Surgery date within the certain hospitalization"}}, "title": "CapEventCase CapOmcHospitalInfoEntity", "description": "Entity for OMC absence reason related information about Hospitals."}, "CapEventCase_CapOtherReasonInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapOtherReasonInformationEntity"}, "secondaryReasonCd": {"type": "string", "description": "Describes a secondary Absence reason."}, "secondaryReasonDetail": {"type": "string", "description": "Describes detail for secondary Absence Reason"}}, "title": "CapEventCase CapOtherReasonInformationEntity", "description": "Entity for other reason information."}, "CapEventCase_CapOwnMedicalConditionReasonInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapOwnMedicalConditionReasonInformationEntity"}, "cptCodes": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsenceCPTEntity"}}, "disabilityTypeCd": {"type": "string", "description": "Defines the type of disability."}, "hospitals": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapOmcHospitalInfoEntity"}}, "isWorkRelated": {"type": "boolean", "description": "Defines if OMC is work related."}}, "title": "CapEventCase CapOwnMedicalConditionReasonInformationEntity", "description": "Entity for OMC reason information."}, "CapEventCase_CapPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPolicyInfoEntity"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapCoverageInfo"}}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapInsuredInfo"}}, "isPriorClaimsAllowed": {"type": "boolean", "description": "Defines if prior claims is allowed from Master policy"}, "isReinsuranceExists": {"type": "boolean", "description": "Defines if the claim is Reinsured."}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "isWellnessApplied": {"type": "boolean", "description": "Defines if wellness claim is applied"}, "ltdInfo": {"$ref": "#/definitions/CapEventCase_LtdInfoEntity"}, "masterLink": {"type": "string", "description": "Master policy id of certificate"}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "priorClaimsRetroactiveEffectiveDate": {"type": "string", "description": "The date on which prior claims were retroactive"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "pwInfo": {"$ref": "#/definitions/CapEventCase_PremiumWaiverInfoEntity"}, "rehabIncentiveBenefitCd": {"type": "string", "description": "Defines if a member is applicable for Rehabilitation Incentive Benefit, possible values: Included or NotIncluded"}, "rehabIncentiveBenefitPct": {"type": "number", "description": "A percentage of benefit for disabled employees who agree to participate in rehabilitation programs"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/CapEventCase_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapEventCase CapPolicyInfoEntity", "description": "An object that stores policy information, related to the Claim products, managed within the EventCase."}, "CapEventCase_CapPregnancyReasonInformationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPregnancyReasonInformationEntity"}, "cptCodes": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapAbsenceCPTEntity"}}, "deliveryDate": {"type": "string", "format": "date", "description": "Date of birth."}, "deliveryTypeCd": {"type": "string", "description": "Type of birth."}, "dueDate": {"type": "string", "format": "date", "description": "Provides expected due date."}, "numberOfBirths": {"type": "integer", "format": "int64", "description": "Defines number of births."}}, "title": "CapEventCase CapPregnancyReasonInformationEntity", "description": "Entity for Pregnancy information."}, "CapEventCase_CapRelatedClaimsAndPolicyId": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapRelatedClaimsAndPolicyId"}, "claimUri": {"type": "string"}, "lossNumber": {"type": "string"}, "policyId": {"type": "string"}}, "title": "CapEventCase CapRelatedClaimsAndPolicyId"}, "CapEventCase_CapRelatedClaimsAndPolicyIdsOutput": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapRelatedClaimsAndPolicyIdsOutput"}, "relatedClaims": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapRelatedClaimsAndPolicyId"}}}, "title": "CapEventCase CapRelatedClaimsAndPolicyIdsOutput"}, "CapEventCase_CapReturnToWorkDateDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapReturnToWorkDateDetailEntity"}, "actualFTRTW": {"type": "string", "format": "date-time", "description": "Actual Full-Time Return to Work date"}, "actualPTRTW": {"type": "string", "format": "date-time", "description": "Actual Part-Time Return to Work date"}, "estimatedFTRTW": {"type": "string", "format": "date-time", "description": "Estimated Full-Time Return to Work date"}, "estimatedPTRTW": {"type": "string", "format": "date-time", "description": "Estimated Part-Time Return to Work date"}}, "title": "CapEventCase CapReturnToWorkDateDetailEntity", "description": "Entity for Return to Work Date details"}, "CapEventCase_CapStateTaxTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapStateTaxTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapEventCase CapStateTaxTermEntity"}, "CapEventCase_CapSubjectOfCaseRole": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapSubjectOfCaseRole"}, "checkAddressId": {"type": "string", "description": "Address for chech payment method."}, "paymentMethodId": {"type": "string", "description": "Preferred payment method."}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}, "roleCd": {"type": "array", "items": {"type": "string", "description": "Roles of Party associated to this Claim Party Role"}}}, "title": "CapEventCase CapSubjectOfCaseRole", "description": "Entity for member subject of case party role"}, "CapEventCase_CapWellnessDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapWellnessDetailEntity"}, "incidentDate": {"type": "string", "format": "date-time", "description": "Date of Incident"}}, "title": "CapEventCase CapWellnessDetailEntity", "description": "Entity for Welness details"}, "CapEventCase_ClaimSubjectInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ClaimSubjectInfo"}, "subject": {"type": "string"}, "subjectId": {"type": "string"}}, "title": "CapEventCase ClaimSubjectInfo"}, "CapEventCase_ClaimantAware": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ClaimantAware"}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}}, "title": "CapEventCase ClaimantAware"}, "CapEventCase_ExternalClaimDataEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ExternalClaimDataEntity"}, "claim": {"$ref": "#/definitions/CapEventCase_CapClaimDetailsEntity"}, "reference": {"type": "string", "description": "Holds external claim uri."}, "sourceId": {"type": "string", "description": "Entity ID in external system"}, "sourceSubType": {"type": "string", "description": "Entity subtype in external system"}, "sourceSystem": {"type": "string", "description": "External system name/description"}, "sourceType": {"type": "string", "description": "Entity type in external system"}}, "title": "CapEventCase ExternalClaimDataEntity", "description": "External claim data."}, "CapEventCase_ExternalPeriod": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ExternalPeriod"}, "cancelReason": {"type": "string", "description": "Period cancel reason."}, "code": {"type": "string", "description": "Period code."}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "description": "Period status."}}, "title": "CapEventCase ExternalPeriod", "description": "3rd party claim period."}, "CapEventCase_ExternalReference": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "ExternalReference"}, "reference": {"$ref": "#/definitions/EntityLink"}, "sourceId": {"type": "string", "description": "Entity ID in external system"}, "sourceSubType": {"type": "string", "description": "Entity subtype in external system"}, "sourceSystem": {"type": "string", "description": "External system name/description"}, "sourceType": {"type": "string", "description": "Entity type in external system"}}, "title": "CapEventCase ExternalReference", "description": "Holds external(3rd party) system reference related information"}, "CapEventCase_InsuredAware": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "InsuredAware"}, "registryId": {"type": "string", "description": "Party ID associated to this Party Role(i.e. Customer)"}}, "title": "CapEventCase InsuredAware"}, "CapEventCase_LtdInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "LtdInfoEntity"}, "eliminationPeriodCd": {"type": "integer", "format": "int64", "description": "Defines elimination Period in number of days."}}, "title": "CapEventCase LtdInfoEntity", "description": "Contains information about LTD policy Core coverage elimination Period"}, "CapEventCase_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapEventCase MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapEventCase_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapEventCase Period"}, "CapEventCase_PremiumWaiverInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PremiumWaiverInfoEntity"}, "waitingPeriod": {"type": "number", "description": "Defines elimination Period in number of days."}}, "title": "CapEventCase PremiumWaiverInfoEntity", "description": "Contains information about Life policy Premium Waiver coverage elimination Period"}, "CapEventCase_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapEventCase Term"}, "CapRelatedClaimsAndPolicyIdsOutput": {"properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapRelatedClaimsAndPolicyIdsOutput"}, "modelFactory": {"$ref": "#/definitions/ModelFactory"}, "relatedClaims": {"type": "array", "items": {"$ref": "#/definitions/CapEventCase_CapRelatedClaimsAndPolicyId"}}}, "title": "CapRelatedClaimsAndPolicyIdsOutput"}, "CapTimeline_CapTimelineClaimInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapTimelineClaimInfoEntity"}, "appliedCoverage": {"type": "string", "description": "The type of the associated coverage."}, "caseNumber": {"type": "string", "description": "Case Number"}, "claimNumber": {"type": "string", "description": "Claim Number"}, "claimType": {"type": "string", "description": "The type of the associated claim."}, "examinerName": {"type": "string", "description": "The name of the examiner of this row's source entity."}, "self": {"$ref": "#/definitions/EntityLink"}, "state": {"type": "string", "description": "The state of the associated loss."}}, "title": "CapTimeline CapTimelineClaimInfoEntity", "description": "Provides a header information about the timeline row."}, "CapTimeline_CapTimelineEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapTimeline"}, "_modelType": {"type": "string", "example": "CapTimeline"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapTimelineEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapTimeline_MessageType"}}, "rows": {"type": "array", "items": {"$ref": "#/definitions/CapTimeline_CapTimelineRowEntity"}}}, "title": "CapTimeline CapTimelineEntity", "description": "A multi-row represenation of the entity's timeline."}, "CapTimeline_CapTimelineEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapTimeline_CapTimelineEntity"}}, "title": "CapTimeline_CapTimelineEntitySuccess"}, "CapTimeline_CapTimelineEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapTimeline_CapTimelineEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapTimeline_CapTimelineEntitySuccessBody"}, "CapTimeline_CapTimelineEvent": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapTimelineEvent"}, "eventDate": {"type": "string", "format": "date-time", "description": "Date when the event occurred."}, "manuallyAdded": {"type": "boolean", "description": "An indicator whether the timeline event has been added manually."}, "type": {"type": "string", "description": "The type of the timeline event, represented by meaningful business code values."}}, "title": "CapTimeline CapTimelineEvent", "description": "Describes an event that occurred in the specific timeline."}, "CapTimeline_CapTimelineMetric": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapTimelineMetric"}, "type": {"type": "string", "description": "The type of this metric which should be some business meaningful code."}, "unit": {"type": "string", "description": "The unit of this metric."}, "value": {"type": "number", "description": "The numeric value of this metric."}}, "title": "CapTimeline CapTimelineMetric", "description": "Represents a single metric value relevant to a timeline row."}, "CapTimeline_CapTimelinePeriod": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapTimelinePeriod"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}, "type": {"type": "string", "description": "Type of the timeline period: Pending/Approved/Denied/Absence/etc."}}, "title": "CapTimeline CapTimelinePeriod", "description": "Represents a single period in an entity timeline."}, "CapTimeline_CapTimelineRowEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapTimelineRowEntity"}, "claimInfo": {"$ref": "#/definitions/CapTimeline_CapTimelineClaimInfoEntity"}, "metrics": {"type": "array", "items": {"$ref": "#/definitions/CapTimeline_CapTimelineMetric"}}, "periods": {"type": "array", "items": {"$ref": "#/definitions/CapTimeline_CapTimelinePeriod"}}, "rowEvents": {"type": "array", "items": {"$ref": "#/definitions/CapTimeline_CapTimelineEvent"}}, "type": {"type": "string", "description": "The type of the timeline row which allows timeline rows to be handled/displayed differently."}}, "title": "CapTimeline CapTimelineRowEntity", "description": "Represents entity's timeline which can consist of multiple periods."}, "CapTimeline_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapTimeline MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CaseToClaimDetailsInputOutputs": {"properties": {"output": {"type": "object"}}, "title": "CaseToClaimDetailsInputOutputs"}, "CaseToClaimDetailsInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CaseToClaimDetailsInputOutputs"}}, "title": "CaseToClaimDetailsInputOutputsSuccess"}, "CaseToClaimDetailsInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CaseToClaimDetailsInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CaseToClaimDetailsInputOutputsSuccessBody"}, "CaseToHealthClaimDetailsInputOutputs": {"properties": {"claimInitInputs": {"type": "array", "items": {"type": "object"}}}, "title": "CaseToHealthClaimDetailsInputOutputs"}, "CaseToHealthClaimDetailsInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CaseToHealthClaimDetailsInputOutputs"}}, "title": "CaseToHealthClaimDetailsInputOutputsSuccess"}, "CaseToHealthClaimDetailsInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CaseToHealthClaimDetailsInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CaseToHealthClaimDetailsInputOutputsSuccessBody"}, "CaseToHealthLossDetailsInputOutputs": {"properties": {"lossInitInputs": {"type": "array", "items": {"type": "object"}}}, "title": "CaseToHealthLossDetailsInputOutputs"}, "CaseToHealthLossDetailsInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CaseToHealthLossDetailsInputOutputs"}}, "title": "CaseToHealthLossDetailsInputOutputsSuccess"}, "CaseToHealthLossDetailsInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CaseToHealthLossDetailsInputOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CaseToHealthLossDetailsInputOutputsSuccessBody"}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossCloseInputBody"}, "ClaimLossReopenInput": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}, "reasonDescription": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossReopenInputBody"}, "ClaimLossUpdateInput": {"required": ["_key", "entity"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_updateStrategy": {"type": "string"}, "entity": {"$ref": "#/definitions/CapEventCase_CapEventCaseDetailEntity"}}, "title": "ClaimLossUpdateInput"}, "ClaimLossUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossUpdateInput"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ClaimLossUpdateInputBody"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "EntityLinkRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "offset": {"type": "integer", "format": "int64"}}, "title": "EntityLinkRequest"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EntityLinkRequestBody"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "EventCaseClosureToOpenItemsOutputs": {"properties": {"output": {"$ref": "#/definitions/CapEventCase_CapEventCaseClosureOpenItemsOutput"}}, "title": "EventCaseClosureToOpenItemsOutputs"}, "EventCaseClosureToOpenItemsOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/EventCaseClosureToOpenItemsOutputs"}}, "title": "EventCaseClosureToOpenItemsOutputsSuccess"}, "EventCaseClosureToOpenItemsOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/EventCaseClosureToOpenItemsOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EventCaseClosureToOpenItemsOutputsSuccessBody"}, "EventCaseRelatedHealthLossesOutputs": {"properties": {"out": {"type": "object"}}, "title": "EventCaseRelatedHealthLossesOutputs"}, "EventCaseRelatedHealthLossesOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/EventCaseRelatedHealthLossesOutputs"}}, "title": "EventCaseRelatedHealthLossesOutputsSuccess"}, "EventCaseRelatedHealthLossesOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/EventCaseRelatedHealthLossesOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EventCaseRelatedHealthLossesOutputsSuccessBody"}, "EventCaseRelatedLossesOutputs": {"properties": {"out": {"type": "array", "items": {"type": "object"}}}, "title": "EventCaseRelatedLossesOutputs"}, "EventCaseRelatedLossesOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/EventCaseRelatedLossesOutputs"}}, "title": "EventCaseRelatedLossesOutputsSuccess"}, "EventCaseRelatedLossesOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/EventCaseRelatedLossesOutputsSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EventCaseRelatedLossesOutputsSuccessBody"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LoadEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}, "title": "LoadEntityRootRequest"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadEntityRootRequestBody"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "LoadSingleEntityRootRequestBody"}, "ModelFactory": {"properties": {"modelName": {"type": "string"}}, "title": "ModelFactory"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectBody"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ObjectSuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}