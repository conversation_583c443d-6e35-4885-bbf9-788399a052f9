{"swagger": "2.0", "info": {"description": "API for AccidentalDismembermentLoss", "version": "1", "title": "AccidentalDismembermentLoss model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/AccidentalDismembermentLoss/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/rules/bundle": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Internal request for Kraken engine.It can be changed for any reason. Backwards compatibility is not supported on this data", "required": false, "schema": {"$ref": "#/definitions/ObjectBody"}}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/rules/{entryPoint}": {"post": {"description": "This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/AccidentalDismembermentLossKrakenDeprecatedBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AccidentalDismembermentLossLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/AccidentalDismembermentLossLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/transformation/AccidentalDismembermentLossToSettlementInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLossToSettlementInputOutputsSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/updateLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeLossUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/reopenLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/initLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeLossInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/submitLoss": {"post": {"description": "The command that performs validation of the provided claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/AccidentalDismembermentLoss/v1/command/closeLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"ClaimLossReopenInput": {"required": ["_key"], "properties": {"reasonDescription": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "AccidentalDismembermentLoss_MessageType": {"required": ["_type"], "properties": {"severity": {"type": "string", "description": "Message severity."}, "code": {"type": "string", "description": "Message code."}, "source": {"type": "string", "description": "Message source."}, "message": {"type": "string", "description": "Message text."}, "_type": {"type": "string", "example": "MessageType"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss MessageType", "description": "Holds information of message type."}, "AccidentalDismembermentLossToSettlementInputOutputsSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentLossToSettlementInputOutputsSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AccidentalDismembermentLossToSettlementInputOutputsSuccessBody"}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "LoadSingleEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntity": {"required": ["_modelName", "_type"], "properties": {"lossType": {"type": "string"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossPolicyInfoEntity"}}, "applicabilityResult": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossApplicabilityResult"}, "isGenerated": {"type": "boolean", "description": "Indicates if loss is auto generated."}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "state": {"type": "string", "description": "Current status of the Loss. Updated each time a new status is gained through state machine."}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "policyIds": {"type": "array", "items": {"type": "string", "description": "Indicates all available policyIds."}}, "policyId": {"type": "string"}, "policy": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapPolicyInfo"}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID."}, "lossDetail": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}, "_modelName": {"type": "string", "example": "AccidentalDismembermentLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_timestamp": {"type": "string", "example": "2024-02-02T10:14:51.11+02:00"}, "_archived": {"type": "boolean", "example": false}, "_version": {"type": "string", "example": "0_b3e150f3-6894-48fa-b1d5-a9a7bd509421"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentLossEntity"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "AccidentalDismembermentLoss CapAccidentalDismembermentLossEntity", "description": "Main object for the CAP Loss Domain."}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccessBody"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "AccidentalDismembermentLoss_CapInsuredInfo": {"required": ["_type"], "properties": {"registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "LoadEntityRootRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityRootRequest"}, "ClaimLossReopenInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossReopenInputBody"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "LoadEntityRootRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityRootRequestBody"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossApplicabilityResult": {"required": ["_type"], "properties": {"messages": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentLoss_MessageType"}}, "applicability": {"type": "string", "description": "Applicability Code returned from Applicability OpenL rules"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentLossApplicabilityResult"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapAccidentalDismembermentLossApplicabilityResult"}, "EntityLinkRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "ClaimLossCloseInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossCloseInputBody"}, "ObjectBody": {"required": ["body"], "properties": {"body": {"type": "object"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectBody"}, "AccidentalDismembermentLossToSettlementInputOutputsSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AccidentalDismembermentLossToSettlementInputOutputs"}}, "title": "AccidentalDismembermentLossToSettlementInputOutputsSuccess"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntity"}}, "title": "AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntitySuccess"}, "AccidentalDismembermentLossLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentLossLoadHistoryResultSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AccidentalDismembermentLossLoadHistoryResultSuccessBody"}, "AccidentalDismembermentLoss_Term": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Term"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss Term"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossDiagnosisInfoEntity": {"required": ["_type"], "properties": {"isPrimaryCode": {"type": "boolean", "description": "If ICD code is primary ICD code."}, "icdCode": {"type": "string"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentLossDiagnosisInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapAccidentalDismembermentLossDiagnosisInfoEntity"}, "LoadEntityByBusinessKeyRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "AccidentalDismembermentLossKrakenDeprecatedBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "AccidentalDismembermentLossKrakenDeprecatedBundleRequest"}, "AccidentalDismembermentLossLoadHistoryResult": {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossEntity"}}, "count": {"type": "integer", "format": "int64"}}, "title": "AccidentalDismembermentLossLoadHistoryResult"}, "CapLifeLossUpdateInput": {"required": ["_key", "entity"], "properties": {"_updateStrategy": {"type": "string"}, "policyId": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "entity": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossDetailEntity"}}, "title": "CapLifeLossUpdateInput"}, "AccidentalDismembermentLoss_CapPolicyInfo": {"required": ["_type"], "properties": {"currencyCd": {"type": "string", "description": "Currency Code"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapInsuredInfo"}}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "term": {"$ref": "#/definitions/AccidentalDismembermentLoss_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "_type": {"type": "string", "example": "CapPolicyInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapPolicyInfo", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"reasonDescription": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}, "reasonCd": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossPolicyInfoEntity": {"required": ["_type"], "properties": {"masterPolicyId": {"type": "string", "description": "master policy id"}, "masterPolicyNumber": {"type": "string", "description": "master policy number"}, "currencyCd": {"type": "string", "description": "Currency Code"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapInsuredInfo"}}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "term": {"$ref": "#/definitions/AccidentalDismembermentLoss_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "_type": {"type": "string", "example": "CapAccidentalDismembermentLossPolicyInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapAccidentalDismembermentLossPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "AccidentalDismembermentLossKrakenDeprecatedBundleRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/AccidentalDismembermentLossKrakenDeprecatedBundleRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "AccidentalDismembermentLossKrakenDeprecatedBundleRequestBody"}, "IdentifierRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "AccidentalDismembermentLossToSettlementInputOutputs": {"properties": {"out": {"type": "array", "items": {"type": "object"}}}, "title": "AccidentalDismembermentLossToSettlementInputOutputs"}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"lossSubStatusCd": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "ClaimLossSubStatusInput"}, "ClaimLossSubStatusInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossSubStatusInputBody"}, "AccidentalDismembermentLossLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/AccidentalDismembermentLossLoadHistoryResult"}}, "title": "AccidentalDismembermentLossLoadHistoryResultSuccess"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossDetailEntity": {"required": ["_modelName", "_type"], "properties": {"icdCodes": {"type": "array", "items": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossDiagnosisInfoEntity"}}, "incident": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossIncidentEntity"}, "lossDateTime": {"type": "string", "format": "date-time", "description": "Date when the incident happened."}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "_modelName": {"type": "string", "example": "AccidentalDismembermentLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_timestamp": {"type": "string", "example": "2024-02-02T10:14:51.11+02:00"}, "_archived": {"type": "boolean", "example": false}, "_version": {"type": "string", "example": "0_ea8aeec7-053e-4c73-b45c-ebed733822fb"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentLossDetailEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapAccidentalDismembermentLossDetailEntity", "description": "Defines what loss it is and what happened."}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "CapLifeLossInitInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeLossInitInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeLossInitInputBody"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "CapLifeLossInitInput": {"required": ["entity"], "properties": {"lossType": {"type": "string"}, "memberRegistryTypeId": {"type": "string"}, "policyIds": {"type": "array", "items": {"type": "string"}}, "entity": {"$ref": "#/definitions/AccidentalDismembermentLoss_CapAccidentalDismembermentLossDetailEntity"}, "eventCaseLink": {"$ref": "#/definitions/EntityLink"}}, "title": "CapLifeLossInitInput"}, "CapLifeLossUpdateInputBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CapLifeLossUpdateInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeLossUpdateInputBody"}, "AccidentalDismembermentLoss_CapAccidentalDismembermentLossIncidentEntity": {"required": ["_type"], "properties": {"reportedLocationOfAccident": {"type": "string", "description": "Capture where the accident happens"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when life intake was reported."}, "hospitalizedOvernight": {"type": "boolean", "description": "Number of overnight hospitalization for insured "}, "disabilityDate": {"type": "string", "format": "date", "description": "Date of disability."}, "ambulance": {"type": "boolean", "description": "Capture ambulance relating information"}, "isWorkRelated": {"type": "boolean", "description": "Is work related"}, "lossItems": {"type": "array", "items": {"type": "string", "description": "Loss Items"}}, "emergencyRoom": {"type": "boolean", "description": "emergency room information, eg. number"}, "firstTreatmentDate": {"type": "string", "format": "date", "description": "first day of treatment."}, "locationDesc": {"type": "string"}, "incidentDate": {"type": "string", "format": "date"}, "_type": {"type": "string", "example": "CapAccidentalDismembermentLossIncidentEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccidentalDismembermentLoss CapAccidentalDismembermentLossIncidentEntity"}}}