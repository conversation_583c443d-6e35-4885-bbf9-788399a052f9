/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.lifeclaim.impl;

import static core.dataproviders.impl.ContextPreservingCompletionStageFactory.completedFuture;
import static java.lang.String.format;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import javax.inject.Inject;

import org.apache.commons.collections4.CollectionUtils;

import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.GenesisCapAccumulatorContainerDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.dto.AccumulatorContainerLoadRequestBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.dto.AccumulatorContainerLoadRequestDTO;
import com.eisgroup.dxp.dataproviders.genesiscapaccumulatorcontainer.dto.CapAccumulatorContainer_CapAccumulatorDTO;
import com.eisgroup.dxp.dataproviders.genesiscapclaimwrapper.GenesisCapClaimWrapperDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscapclaimwrapper.dto.ClaimWrapper_CapClaimWrapperEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.GenesisCapLossSearchDataProvider;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapBalance_CapBalanceEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapBalance_CapBalanceItemEntityDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.MoneyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapBalanceEntitySearchEntityResponseV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.ResultCapBalanceEntitySearchEntityResponseV3SuccessBodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapBalanceEntitySearchEntityRequestV3BodyDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapBalanceEntitySearchEntityRequestV3DTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapBalanceEntitySearchQueryDTO;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.SearchCapBalanceEntitySearchValueMatcherDTO;
import com.eisgroup.dxp.services.capadjusteraccumulatorcontainer.converters.CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulatorApiConverter;
import com.eisgroup.dxp.services.capadjusteraccumulatorcontainer.dto.CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator;
import com.google.common.collect.Lists;

import cap.adjuster.services.lifeclaim.CapAdjusterClaimWrapperService;
import cap.adjuster.services.lifeclaim.converters.CapAdjusterClaimRelationshipSettlementConverter;
import cap.adjuster.services.lifeclaim.dto.CapLifeClaimSettlement;
import core.services.pagination.SortParam.Direction;
import dataproviders.dto.CapClaimSettlementLossDTO;
import dataproviders.dto.CapLifeSettlementDTO;
import dataproviders.relationships.GenesisSearchRelationshipsDataProvider;
import dataproviders.relationships.dto.GenesisTripletDTO;
import genesis.core.utils.GenesisJsonUtils;

public class CapAdjusterClaimWrapperServiceImpl implements CapAdjusterClaimWrapperService {

    private static final String BASELIFESETTLEMENT = "BaseLifeSettlement";
    private static final String SETTLEMENT_CLAIM = "settlement_claim";
    private static final String CLAIMWRAPPER_URI = "gentity://CapLoss/ClaimWrapper//%s/%s";
    private static final String SETTLEMENT_URI = "gentity://CapSettlement/%s//%s/%s";
    private static final String DEFAULT_CURRENCY_CD = "USD";
    private static final String BENEFIT_TERMBOUND_PERRESOURCE = "Benefit_TermBound_PerResource";
    private static final String ACCUMULATOR_UNIT_CODE_FIELD = "accumulatorUnitCd";
    private static final String ACCUMULATOR_UNIT_CODE = "Money";
    private static final Integer SETTLEMENTS_LIMIT_PER_PAGE = 100;

    private GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider;
    private CapAdjusterClaimRelationshipSettlementConverter<CapLifeSettlementDTO, CapLifeClaimSettlement> settlementConverter;
    private GenesisJsonUtils genesisJsonUtils;
    private GenesisCapClaimWrapperDataProvider capClaimWrapperDataProvider;
    private GenesisCapAccumulatorContainerDataProvider capAccumulatorContainerDataProvider;
    private GenesisCapLossSearchDataProvider capLossSearchDataProvider;
    private CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulatorApiConverter<CapAccumulatorContainer_CapAccumulatorDTO, CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator> capAccumulatorApiConverter;

    @Override
    public CompletionStage<List<CapLifeClaimSettlement>> getSettlements(String rootId, Integer revisionNo) {
        return getSettlementsAssociatedWithClaimWrapper(rootId, revisionNo)
            .thenApply(this::prefillPaidAmount)
                .thenApply(settlements -> settlements.stream()
                        .sorted(Comparator.comparing(settlement -> settlement.timestamp,Comparator.reverseOrder()))
                        .collect(Collectors.toList()));
    }

    /**
     * Get accumulators associated with claimWrapper
     *
     * @param rootId  ClaimWrapper rootId
     * @param revisionNo  ClaimWrapper revision number
     * @return list of accumulator
     */
    private CompletionStage<List<CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulator>> getAccumulators(String rootId, Integer revisionNo) {
        return capClaimWrapperDataProvider.apiCaplossClaimWrapperV1EntitiesRootIdRevisionNoGet(UUID.fromString(rootId), revisionNo, null, null)
                .thenCompose(claimWrapperSuccessBody -> {
                    ClaimWrapper_CapClaimWrapperEntityDTO claim = claimWrapperSuccessBody.body.success;
                    AccumulatorContainerLoadRequestBodyDTO internalDto = new AccumulatorContainerLoadRequestBodyDTO();
                    internalDto.body = new AccumulatorContainerLoadRequestDTO();
                    internalDto.body.customerURI = claim.memberRegistryTypeId;
                    internalDto.body.policyURI = claim.policyId;
                    return capAccumulatorContainerDataProvider.apiCapaccumulatorcontainerCapAccumulatorContainerV1AccumulatorLoadAccumulatorsPost(internalDto)
                            .thenApply(accumulatorSuccessBody -> accumulatorSuccessBody.body.success
                                    .stream()
                                    .flatMap(accumulatorContainer -> accumulatorContainer.accumulators.stream())
                                    .map(accumulatorDTO -> capAccumulatorApiConverter.convertToApiDTO(accumulatorDTO))
                                    .collect(Collectors.toList())
                            );
                });
    }

    @SuppressWarnings("unchecked")
    private CompletionStage<List<CapLifeSettlementDTO>> getSettlementsAssociatedWithClaimWrapper(String rootId, Integer revisionNo) {
        String lossUri = format(CLAIMWRAPPER_URI, rootId, revisionNo);
        GenesisTripletDTO triplet = new GenesisTripletDTO(BASELIFESETTLEMENT, SETTLEMENT_CLAIM, lossUri);
        return searchRelationshipsDataProvider.getRelationships(triplet, CapLifeSettlementDTO.class);
    }

    /**
     * Prefill Paid Amount for each settlement
     *
     * @return
     */
    private List<CapLifeClaimSettlement> prefillPaidAmount(List<CapLifeSettlementDTO> capLifeSettlementDTO) {
        List<CapLifeClaimSettlement> settlementAPIDtos = settlementConverter.convertToApiDTOs(capLifeSettlementDTO);
        if (CollectionUtils.isEmpty(settlementAPIDtos)) {
            return settlementAPIDtos;
        }
        String eventCaseLink = genesisJsonUtils.convertObjectToDto(settlementAPIDtos.getFirst().settlementLossInfo, CapClaimSettlementLossDTO.class).eventCaseLink._uri;
        return searchLatestBalanceItems(prepareBalanceSearchRequest(eventCaseLink))
            .thenApply(balanceItems -> settlementAPIDtos
                .stream()
                .map(settlememt -> calculatePaidAmount(balanceItems, settlememt))
                .collect(Collectors.toList()))
            .toCompletableFuture()
            .join();
    }

    /**
     * Calculate total paid amount for each settlement
     *
     * @param settlement
     * @return CapLifeClaimSettlement
     */
    private CapLifeClaimSettlement calculatePaidAmount(List<CapBalance_CapBalanceItemEntityDTO> balanceItems,CapLifeClaimSettlement settlement) {
        String settlementUri = format(SETTLEMENT_URI, settlement.modelName, settlement.key.rootId, settlement.modelVersion);
        BigDecimal termBoundTotalPaidAmount = balanceItems
            .stream()
            .flatMap(balanceItem -> balanceItem.actualAllocations.stream())
            .filter(allocation -> settlementUri.equals(allocation.allocationSource._uri))
            .map(allocation -> allocation.allocationBalancedGrossAmount.amount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        return setPaidAmount(settlement, termBoundTotalPaidAmount, DEFAULT_CURRENCY_CD);
    }

    /**
     * Set value of paid amount into settlement
     *
     * @param settlement
     * @param termBoundTotalPaidAmount
     * @param currencyCd
     * @return
     */
    private CapLifeClaimSettlement setPaidAmount(CapLifeClaimSettlement settlement, BigDecimal termBoundTotalPaidAmount, String currencyCd) {
        MoneyDTO paidAmountDTO = new MoneyDTO();
        paidAmountDTO.amount = termBoundTotalPaidAmount;
        paidAmountDTO.currency = currencyCd;
        if (settlement.settlementResult != null) {
            settlement.settlementResult.paidAmount = paidAmountDTO;
        }
        return settlement;
    }

    /**
     * Prepare balance search request
     *
     * @param eventCaseLink
     * @return
     */
    private SearchCapBalanceEntitySearchEntityRequestV3BodyDTO prepareBalanceSearchRequest(String eventCaseLink) {
        SearchCapBalanceEntitySearchEntityRequestV3BodyDTO request = new SearchCapBalanceEntitySearchEntityRequestV3BodyDTO();

        request.body = new SearchCapBalanceEntitySearchEntityRequestV3DTO();
        request.body.query = new SearchCapBalanceEntitySearchQueryDTO();
        request.body.query.eventCaseLink = new SearchCapBalanceEntitySearchValueMatcherDTO();
        request.body.query.eventCaseLink.matches = new ArrayList<>();
        request.body.query.eventCaseLink.matches.add(eventCaseLink);
        request.body.resolveEntity = true;

        Map<String, Object> creationDateSort = new HashMap<>();
        creationDateSort.put("creationDate", Direction.DESC.name());
        request.body.sorting = Lists.newArrayList(creationDateSort);
        return request;
    }

    /**
     * Search All Balance
     *
     * @param request
     * @return
     */
    private CompletionStage<ResultCapBalanceEntitySearchEntityResponseV3DTO> searchAllBalance(SearchCapBalanceEntitySearchEntityRequestV3BodyDTO request) {
        return capLossSearchDataProvider.apiCommonSearchV3CapBalancePost(request, null, null, SETTLEMENTS_LIMIT_PER_PAGE, null)
            .thenCompose(result -> {

                if (result == null || result.body == null) {
                    return completedFuture(null);
                } else if (result.body.success.count == 0 || result.body.success.count <= result.body.success.result.size()) {
                    return completedFuture(result.body.success);
                }
                Integer pageCount = Math.toIntExact(result.body.success.count / result.body.success.result.size());
                if (Math.toIntExact(result.body.success.count % result.body.success.result.size()) > 0) {
                    pageCount++;
                }

                List<CompletableFuture<ResultCapBalanceEntitySearchEntityResponseV3SuccessBodyDTO>> futuresList = Lists.newArrayList();
                for (int i = 1; i < pageCount; i++) {
                    futuresList.add(capLossSearchDataProvider.apiCommonSearchV3CapBalancePost(request, null, null, SETTLEMENTS_LIMIT_PER_PAGE, i * SETTLEMENTS_LIMIT_PER_PAGE).toCompletableFuture());
                }
                return CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[futuresList.size()]))
                    .thenApply(v -> futuresList.stream().map(future -> future.join()).collect(Collectors.toList()))
                    .thenApply(resultList -> {
                        result.body.success.result.addAll(resultList.stream()
                            .flatMap(response -> response.body.success.result.stream())
                            .collect(Collectors.toList()));
                        return result.body.success;
                    });
            });
    }


    private CompletionStage<List<CapBalance_CapBalanceItemEntityDTO>> searchLatestBalanceItems(SearchCapBalanceEntitySearchEntityRequestV3BodyDTO request) {
        return searchAllBalance(request)
            .thenApply(response -> {
                if (response == null || CollectionUtils.isEmpty(response.result)) {
                    return Lists.newArrayList();
                }
                return response.result.stream()
                    .map(result -> genesisJsonUtils.convertObjectToDto(result, CapBalance_CapBalanceEntityDTO.class))
                    .collect(
                        Collectors.groupingBy(balance -> balance.payee._uri,
                            Collectors.maxBy(Comparator.comparing(balance-> balance.creationDate))))
                    .values()
                    .stream()
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .flatMap(balance -> balance.balanceItems.stream())
                    .collect(Collectors.toList());
            });
    }

    @Inject
    public void setSearchRelationshipsDataProvider(GenesisSearchRelationshipsDataProvider searchRelationshipsDataProvider) {
        this.searchRelationshipsDataProvider = searchRelationshipsDataProvider;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setSettlementConverter(CapAdjusterClaimRelationshipSettlementConverter settlementConverter) {
        this.settlementConverter = settlementConverter;
    }

    @Inject
    public void setGenesisJsonUtils(GenesisJsonUtils genesisJsonUtils) {
        this.genesisJsonUtils = genesisJsonUtils;
    }

    @Inject
    public void setCapClaimWrapperDataProvider(GenesisCapClaimWrapperDataProvider capClaimWrapperDataProvider) {
        this.capClaimWrapperDataProvider = capClaimWrapperDataProvider;
    }

    @Inject
    public void setCapAccumulatorContainerDataProvider(GenesisCapAccumulatorContainerDataProvider capAccumulatorContainerDataProvider) {
        this.capAccumulatorContainerDataProvider = capAccumulatorContainerDataProvider;
    }

    @Inject
    public void setCapLossSearchDataProvider(GenesisCapLossSearchDataProvider capLossSearchDataProvider) {
        this.capLossSearchDataProvider = capLossSearchDataProvider;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setCapAccumulatorApiConverter(CapAdjusterAccumulatorContainerCapAccumulatorContainer_CapAccumulatorApiConverter capAccumulatorApiConverter) {
        this.capAccumulatorApiConverter = capAccumulatorApiConverter;
    }
}
