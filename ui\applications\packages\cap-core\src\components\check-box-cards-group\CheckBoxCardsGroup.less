/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

.@{PREFIX}-radio-cards-group {
    padding: @gen-padding-form-controls 0 @gen-padding-form-controls 0;

    &__title {
        margin-bottom: 0.75rem;
    }

    &__content {
        display: flex;
        flex-wrap: wrap;
        padding-right: 0;
        margin-bottom: -2.5rem;
    }

    &_items {
        margin-bottom: 1.5rem;
    }
}

.@{PREFIX}-dialog-radio-card {
    height: auto;
    box-sizing: border-box;
    &.disabled {
        background-color: @gen-color-neutral-l-6;
    }
    .@{ant-prefix}-card-body {
        display: flex;
        height: 100%;
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        height: 100%;
        padding: 0 0 0 0;
        &.disabled {
            background-color: @gen-color-neutral-l-6;
        }
        i.anticon {
            display: flex;
            align-items: center;
            height: 2.5rem;
        }
    }

    &__checkbox {
        position: absolute;
    }

    &__name {
        .gen-font-other-regular-16-left-main-primary;
        color: @color-black;
        letter-spacing: 0;
        line-height: 1.5rem;
        text-align: left;
        width: 100%;
        padding-left: 0.875rem;
        &.disabled {
            background-color: @gen-color-gray-light;
        }
    }
}
