/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.controllers.eventcase;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import cap.adjuster.services.eventcase.CapAdjusterEventCasesService;
import cap.adjuster.services.eventcase.dto.CapGenericLoss;
import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterEventCasesApiController.TAG_API_CAP_ADJUSTER_EVENT_CASE,
                description = "CAP Adjuster: Event Case API")})
@Api(value = CapAdjusterEventCasesApiController.TAG_API_CAP_ADJUSTER_EVENT_CASE,
        tags = CapAdjusterEventCasesApiController.TAG_API_CAP_ADJUSTER_EVENT_CASE)
public class CapAdjusterEventCasesApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_EVENT_CASE = "/cap-adjuster/v1/event-cases";

    private CapAdjusterEventCasesService eventCasesService;

    /**
     * Get absence losses related to event case
     *
     * @param rootId event case identifier
     * @param revisionNo event case revision number
     * @return list of absence losses related to event case
     */
    @ApiOperation(value = "Get absence losses related to event case",
            response = CapGenericLoss.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getAbsenceLosses(@ApiParam(value = "Event Case Identifier", required = true) String rootId,
                                                       @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(eventCasesService.getAbsenceLosses(rootId, revisionNo));
    }

    /**
     * Get life claims related to event case
     *
     * @param rootId event case identifier
     * @param revisionNo event case revision number
     * @return list of life claims related to event case
     */
    @ApiOperation(value = "Get life claims related to event case",
        response = CapGenericLoss.class,
        responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
            message = HttpStatusDescription.BAD_REQUEST,
            response = GenesisCommonExceptionDTO.class),
        @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
            message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
            response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getLifeClaims(@ApiParam(value = "Event Case Identifier", required = true) String rootId,
                                                    @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(eventCasesService.getLifeClaims(rootId, revisionNo));
    }

    /**
     * Get life losses related to event case
     *
     * @param rootId event case identifier
     * @param revisionNo event case revision number
     * @return list of life losses related to event case
     */
    @ApiOperation(value = "Get life losses related to event case",
        response = CapGenericLoss.class,
        responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
            message = HttpStatusDescription.BAD_REQUEST,
            response = GenesisCommonExceptionDTO.class),
        @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
            message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
            response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getLifeLosses(@ApiParam(value = "Event Case Identifier", required = true) String rootId,
                                                 @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(eventCasesService.getLifeLosses(rootId, revisionNo));
    }

    /**
     * Get life settlements related to event case
     *
     * @param rootId event case identifier
     * @param revisionNo event case revision number
     * @return list of life settlements related to event case
     */
    @ApiOperation(value = "Get life settlements related to event case",
        response = CapGenericSettlement.class,
        responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
            message = HttpStatusDescription.BAD_REQUEST,
            response = GenesisCommonExceptionDTO.class),
        @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
            message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
            response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getLifeSettlements(@ApiParam(value = "Event Case Identifier", required = true) String rootId,
                                                 @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(eventCasesService.getLifeSettlements(rootId, revisionNo));
    }

    /**
     * Get settlements related to event case
     *
     * @param rootId event case identifier
     * @param revisionNo event case revision number
     * @return list of settlements related to event case
     */
    @ApiOperation(value = "Get settlements related to event case",
        response = CapGenericSettlement.class,
        responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
            message = HttpStatusDescription.BAD_REQUEST,
            response = GenesisCommonExceptionDTO.class),
        @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
            message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
            response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Event Case Identifier", required = true) String rootId,
                                                      @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(eventCasesService.getSettlements(rootId, revisionNo));
    }

    @Inject
    public void setEventCasesService(CapAdjusterEventCasesService eventCasesService) {
        this.eventCasesService = eventCasesService;
    }
}
