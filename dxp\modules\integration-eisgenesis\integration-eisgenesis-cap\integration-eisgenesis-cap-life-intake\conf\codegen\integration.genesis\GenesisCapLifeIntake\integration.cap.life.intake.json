{"swagger": "2.0", "info": {"description": "API for LifeIntakeLoss", "version": "1", "title": "LifeIntakeLoss model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/caploss/LifeIntakeLoss/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/rules/{entryPoint}": {"post": {"description": "endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LifeIntakeLossKrakenBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/LifeIntakeLossLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/LifeIntakeLossLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/createLoss": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/setLossSubStatus": {"post": {"description": "The command that sets the sub status of Claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossSubStatusInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/reopenLoss": {"post": {"description": "The command that reopens claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossReopenInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/initLoss": {"post": {"description": "The command that initiate creation of new claim loss image with unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeIntakeInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/adjudicateLoss": {"post": {"description": "The command that instantiates adjudication process for claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/submitLoss": {"post": {"description": "The command that performs validation of the provided claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/updateLoss": {"post": {"description": "The command that updates claim loss with provided input data", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeIntakeUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "The command that updates claim loss with provided input data", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapLifeIntakeUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/caploss/LifeIntakeLoss/v1/command/closeLoss": {"post": {"description": "The command that closes claim loss", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/ClaimLossCloseInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapLifeIntakeInitInputBody": {"properties": {"body": {"$ref": "#/definitions/CapLifeIntakeInitInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeIntakeInitInputBody"}, "ClaimLossReopenInput": {"required": ["_key"], "properties": {"reasonDescription": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "reasonCd": {"type": "string"}}, "title": "ClaimLossReopenInput"}, "LifeIntakeLoss_CapLifeFinancialAdjustmentEntity": {"required": ["_type"], "properties": {"offsets": {"type": "array", "items": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeOffsetEntity"}}, "taxes": {"type": "array", "items": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeTaxEntity"}}, "deductions": {"type": "array", "items": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeDeductionEntity"}}, "isMedicareExempt": {"type": "boolean"}, "yearToDateEarnings": {"$ref": "#/definitions/Money"}, "isOasdiExempt": {"type": "boolean"}, "_type": {"type": "string", "example": "CapLifeFinancialAdjustmentEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeFinancialAdjustmentEntity", "description": "Main object for financial adjustment details (taxes, deductions, offsets etc.)."}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "LifeIntakeLoss_CapLifeIntakeLossEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntity"}}, "title": "LifeIntakeLoss_CapLifeIntakeLossEntitySuccess"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "LoadSingleEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "CapLifeIntakeUpdateInputBody": {"properties": {"body": {"$ref": "#/definitions/CapLifeIntakeUpdateInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapLifeIntakeUpdateInputBody"}, "LifeIntakeLossLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/LifeIntakeLossLoadHistoryResultSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LifeIntakeLossLoadHistoryResultSuccessBody"}, "LifeIntakeLossLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/LifeIntakeLossLoadHistoryResult"}}, "title": "LifeIntakeLossLoadHistoryResultSuccess"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "LifeIntakeLoss_CapLifeTaxEntity": {"required": ["_type"], "properties": {"amount": {"$ref": "#/definitions/Money"}, "jurisdictionType": {"type": "string", "description": "Defines jurisdiction type"}, "term": {"description": "Defines effective and expiration date for taxes.", "$ref": "#/definitions/LifeIntakeLoss_Term"}, "taxType": {"type": "string", "description": "Defines the type of a tax."}, "_type": {"type": "string", "example": "CapLifeTaxEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeTaxEntity", "description": "Entity for CAP Life intake tax detail."}, "LoadEntityRootRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityRootRequest"}, "ClaimLossReopenInputBody": {"properties": {"body": {"$ref": "#/definitions/ClaimLossReopenInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossReopenInputBody"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "LoadEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityRootRequestBody"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "LifeIntakeLossKrakenBundleRequestBody": {"properties": {"body": {"$ref": "#/definitions/LifeIntakeLossKrakenBundleRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LifeIntakeLossKrakenBundleRequestBody"}, "LifeIntakeLoss_CapLifeDiagnosisInfoEntity": {"required": ["_type"], "properties": {"isPrimaryCode": {"type": "boolean", "description": "If ICD code is primary ICD code."}, "icdCode": {"type": "string"}, "_type": {"type": "string", "example": "CapLifeDiagnosisInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeDiagnosisInfoEntity", "description": "Entity to capture ICD codes"}, "EntityLinkRequestBody": {"properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "LifeIntakeLoss_CapLifeDeductionEntity": {"required": ["_type"], "properties": {"isPrePostTax": {"type": "boolean", "description": "This attribute defines if the deductions should be applied pre taxes. If the value is set to 'no', the deductions are applied post taxes."}, "nonProviderPaymentType": {"type": "string", "description": "This field describes the type of Non-Provider Payment."}, "amount": {"description": "This attribute represents the flat dollar amount of the Deductions to be made from the Claim payment", "$ref": "#/definitions/Money"}, "deductionPct": {"type": "number", "description": "This attribute represents the percentage of Deductions to be made from the Claim payment"}, "lossTypes": {"type": "array", "items": {"type": "string", "description": "The attribute is used to map a Deduction to a particular Claim Type"}}, "deductionType": {"type": "string", "description": "This attribute describes the target for the deduction amount that will be paid by the Claim."}, "deductionTerm": {"description": "Deduction effective and expiration dates.", "$ref": "#/definitions/LifeIntakeLoss_Term"}, "deductionBeneficiary": {"type": "string", "description": "Defines deduction beneficiary"}, "stateProvided": {"type": "string", "description": "The attribute describes in which state the Child Support was provided."}, "_type": {"type": "string", "example": "CapLifeDeductionEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeDeductionEntity", "description": "Entity for CAP Life intake deductions detail."}, "ClaimLossCloseInputBody": {"properties": {"body": {"$ref": "#/definitions/ClaimLossCloseInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossCloseInputBody"}, "CapLifeIntakeInitInput": {"properties": {"lossTypes": {"type": "array", "items": {"type": "string"}}, "memberRegistryTypeId": {"type": "string"}, "entity": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeDetailEntity"}}, "title": "CapLifeIntakeInitInput"}, "LifeIntakeLoss_CapLifeIntakeDetailEntity": {"required": ["_modelName", "_type"], "properties": {"workInfo": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeWorkInfoEntity"}, "icdCodes": {"type": "array", "items": {"description": "Intake multiple ICD codes if need be. This is optional entity for claim case.", "$ref": "#/definitions/LifeIntakeLoss_CapLifeDiagnosisInfoEntity"}}, "earnings": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeEarningsInfoEntity"}, "reportingMethod": {"type": "string", "description": "Indicate how the claim application is reported, e.g. by Phone Call,Email,Mail,Walk-In,Self-Service etc."}, "lossDate": {"type": "string", "format": "date-time", "description": "Capture the date of the event or single type of loss"}, "reportedDate": {"type": "string", "format": "date-time", "description": "Date and time when claim application is reported."}, "lossEvent": {"description": "Entity to capture event information", "$ref": "#/definitions/LifeIntakeLoss_CapLifeLossEventEntity"}, "financialAdjustment": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeFinancialAdjustmentEntity"}, "hasReported": {"type": "boolean", "description": "Indicate if the claim application has been reported"}, "events": {"type": "array", "items": {"description": "Capture the list of events dates/periods, e.g. date of death, date of admission to ICU, hospitalization period, diagnosis date of critical illness etc.", "$ref": "#/definitions/LifeIntakeLoss_CapLifeEventsEntity"}}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "_modelName": {"type": "string", "example": "LifeIntakeLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_timestamp": {"type": "string", "example": "2021-12-21T14:18:14.707+08:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapLifeIntakeDetailEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeIntakeDetailEntity", "description": "Entity that encompasses life product details."}, "LoadEntityByBusinessKeyRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "LifeIntakeLoss_CapLifePolicyInfoEntity": {"required": ["_type"], "properties": {"riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "insureds": {"type": "array", "items": {"description": "An entity for insureds information.", "$ref": "#/definitions/LifeIntakeLoss_CapInsuredInfo"}}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "term": {"description": "Policy effective and expiration dates.", "$ref": "#/definitions/LifeIntakeLoss_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "_type": {"type": "string", "example": "CapLifePolicyInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifePolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntitySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LifeIntakeLoss_CapLifeIntakeLossEntitySuccessBody"}, "LifeIntakeLoss_CapLifeLossEventEntity": {"required": ["_type"], "properties": {"lossDesc": {"type": "string", "description": "Capture any information of the event or loss or sickness"}, "stateProv": {"type": "string", "description": "Capture state or Province of the accident if the event is Accident"}, "severity": {"type": "string", "description": "Capture severity of the event if the event is Accident"}, "isAccident": {"type": "boolean", "description": "Indicator to determine if the event is caused by Accident or Sickness"}, "countryCd": {"type": "string", "description": "Capture country of the location if the event is Accident"}, "locationDesc": {"type": "string", "description": "Capture accident location information if the event is Accident"}, "causeOfLoss": {"type": "string"}, "_type": {"type": "string", "example": "CapLifeLossEventEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeLossEventEntity", "description": "Entity that encompasses loss event details."}, "ClaimLossCloseInput": {"required": ["_key"], "properties": {"reasonDescription": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "reasonCd": {"type": "string"}}, "title": "ClaimLossCloseInput"}, "LifeIntakeLossLoadHistoryResult": {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeLossEntity"}}, "count": {"type": "integer", "format": "int64"}}, "title": "LifeIntakeLossLoadHistoryResult"}, "IdentifierRequestBody": {"properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "IdentifierRequestBody"}, "LifeIntakeLoss_CapLifeEarningsInfoEntity": {"required": ["_type"], "properties": {"commissionAmt": {"$ref": "#/definitions/Money"}, "baseSalary": {"$ref": "#/definitions/Money"}, "bonusAmt": {"$ref": "#/definitions/Money"}, "totalAnnualIncomeAmt": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "CapLifeEarningsInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeEarningsInfoEntity", "description": "Entity for Insured's Earnings information."}, "LifeIntakeLoss_Term": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Term"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "Term"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "ClaimLossSubStatusInput": {"required": ["_key"], "properties": {"lossSubStatusCd": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "ClaimLossSubStatusInput"}, "LifeIntakeLossKrakenBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "LifeIntakeLossKrakenBundleRequest"}, "CapLifeIntakeUpdateInput": {"required": ["_key"], "properties": {"_updateStrategy": {"type": "string"}, "memberRegistryTypeId": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "entity": {"$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeDetailEntity"}}, "title": "CapLifeIntakeUpdateInput"}, "ClaimLossSubStatusInputBody": {"properties": {"body": {"$ref": "#/definitions/ClaimLossSubStatusInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ClaimLossSubStatusInputBody"}, "LifeIntakeLoss_CapLifeOffsetEntity": {"required": ["_type"], "properties": {"isPrePostTax": {"type": "boolean"}, "proratingRate": {"type": "number"}, "amount": {"$ref": "#/definitions/Money"}, "offsetType": {"type": "string"}, "offsetTerm": {"$ref": "#/definitions/LifeIntakeLoss_Term"}, "_type": {"type": "string", "example": "CapLifeOffsetEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeOffsetEntity", "description": "Entity for CAP Life intake offset detail."}, "LifeIntakeLoss_CapLifeEventsEntity": {"required": ["_type"], "properties": {"lossNumber": {"type": "string", "description": "Identifier of the loss which is used to find the associated loss/claim with the eventType in the timeline when mouse over onto the event Date"}, "eventDateType": {"type": "string", "description": "Describes the type of the dates that need to be captured during loss information intake, e.g. date of death, date of diagonosis etc."}, "eventTypeCd": {"type": "string"}, "eventDate": {"type": "string", "format": "date-time", "description": "Describes Event Date of the loss events. The date comes from the loss (e.g. death loss, accelerated loss) for the purpose of timeline bar."}, "_type": {"type": "string", "example": "CapLifeEventsEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeEventsEntity", "description": "Entity for Life Event Dates details."}, "LifeIntakeLoss_CapLifeIntakeLossEntity": {"required": ["_modelName", "_type"], "properties": {"lossType": {"type": "string", "description": "Deprecated, use lossTypes instead"}, "lossTypes": {"type": "array", "items": {"type": "string", "description": "Indicate multipe types of losses happened in this claim application or from single event."}}, "policies": {"type": "array", "items": {"description": "Association with policies in the case that policies need to be applied at claim case level", "$ref": "#/definitions/LifeIntakeLoss_CapLifePolicyInfoEntity"}}, "memberRegistryTypeId": {"type": "string", "description": "Defines unique member ID"}, "lossSubStatusCd": {"type": "string", "description": "This attribute describes the sub-status of the loss."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "state": {"type": "string", "description": "Current status of the Loss. Updated each time a new status is gained through state machine."}, "reasonCd": {"type": "string", "description": "This attribute describes available reasons for closing or reopening a loss."}, "lossDetail": {"description": "Entity that captures claim case level of life intake information", "$ref": "#/definitions/LifeIntakeLoss_CapLifeIntakeDetailEntity"}, "_modelName": {"type": "string", "example": "LifeIntakeLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_timestamp": {"type": "string", "example": "2021-12-21T14:18:14.708+08:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapLifeIntakeLossEntity"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "CapLifeIntakeLossEntity", "description": "Main object to encompass life details."}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "LifeIntakeLoss_CapInsuredInfo": {"required": ["_type"], "properties": {"isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "The unique registry ID that identifies the subject of the claim."}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "LifeIntakeLoss_CapLifeWorkInfoEntity": {"required": ["_type"], "properties": {"terminationDate": {"type": "string", "format": "date", "description": "Employee's contract ending date with the employer "}, "hireDate": {"type": "string", "format": "date", "description": "Employee's Date of hire with regards to selected Employer"}, "workStateCodeCd": {"type": "string", "description": "Employee's Work State Code with regards to selected Employer"}, "lastWorkDate": {"type": "string", "format": "date-time", "description": "Insured's last work day date and time."}, "physicalJobDemandsCd": {"type": "string"}, "rtwDate": {"type": "string", "format": "date", "description": "Employee's return to work date"}, "classCd": {"type": "string", "description": "Employee's Class code with regards to selected Employer"}, "participantJobTitle": {"type": "string"}, "workAndEducationalHistory": {"type": "string"}, "participantClass": {"type": "string"}, "hoursNormallyWorkedPerWeek": {"type": "number"}, "occupationClassCd": {"type": "string", "description": "Employee's Occupation Category code with regards to selected Employer"}, "customerLink": {"$ref": "#/definitions/EntityLink"}, "_type": {"type": "string", "example": "CapLifeWorkInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapLifeWorkInfoEntity", "description": "Object depicts life employment details."}}}