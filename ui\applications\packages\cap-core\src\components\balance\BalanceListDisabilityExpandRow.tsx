/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils} from '@eisgroup/cap-services'
import React, {useEffect, useState} from 'react'
import {observer} from 'mobx-react'
import {Col, Row} from '@eisgroup/ui-kit'
import {useTranslate} from '@eisgroup/i18n'
import {sumBy} from 'lodash'
import {CapBalance} from '@eisgroup/cap-financial-models'
import {moneyByLocale, LossTypeAndClaimTypeMapWithoutPL} from '../..'
import {BalanceListCommonExpandRow} from './BalanceListCommonExpandRow'
import {getSortedScheduledAllocation} from './utils'

import CapBalanceItemActualAllocationEntity = CapBalance.CapBalanceItemActualAllocationEntity
import CapBalanceItemScheduledAllocationEntity = CapBalance.CapBalanceItemScheduledAllocationEntity

interface BalanceListDisabilityExpandRowProps {
    allocation: CapBalanceItemActualAllocationEntity | CapBalanceItemScheduledAllocationEntity
}

export const BalanceListDisabilityExpandRow: React.FC<BalanceListDisabilityExpandRowProps> = observer(props => {
    const [claimType, setClaimType] = useState<string>()
    const [startDate, setStartDate] = useState<string>()
    const [endDate, setEndDate] = useState<string>()
    const {t} = useTranslate()
    useEffect(() => {
        findClaimType()
        const initStartDate = props.allocation.allocationPayableItem?.benefitPeriod?.startDate
        const initEndDate = props.allocation.allocationPayableItem?.benefitPeriod?.endDate
        setStartDate(dateUtils(initStartDate).render)
        setEndDate(dateUtils(initEndDate).render)
    }, [])

    const findClaimType = () => {
        LossTypeAndClaimTypeMapWithoutPL.forEach((value, key) => {
            if (value.includes(props.allocation.allocationLossInfo?.lossType as string)) {
                setClaimType(key)
            }
        })
    }

    const scheduledAllocations = getSortedScheduledAllocation(props.allocation)

    return (
        <Row>
            <Col span={8}>
                <h4>{claimType}</h4>
                <p>
                    {t('cap-core:from')} {startDate}
                </p>
                <p>
                    {t('cap-core:through')} {endDate}
                </p>
            </Col>
            <Col span={8}>
                <div>
                    <label>{t('cap-core:balance_table_expand_total_allocation_label')}</label>
                    {moneyByLocale(props.allocation?.allocationGrossAmount?.amount || 0)}
                </div>
                <BalanceListCommonExpandRow allocation={props.allocation} />
                <div>
                    <label>{t('cap-core:balance_table_expand_total_payment_amount_label')}</label>
                    {moneyByLocale(props.allocation?.allocationNetAmount?.amount || 0)}
                </div>
            </Col>
            <Col span={8}>
                <div>
                    <label>{t('cap-core:balance_table_expand_total_allocation_label')}</label>
                    {moneyByLocale(
                        sumBy(
                            props.allocation?.scheduledAllocations,
                            (schedule: CapBalanceItemScheduledAllocationEntity) =>
                                schedule?.allocationGrossAmount!.amount
                        ) || 0
                    )}
                </div>
                {scheduledAllocations &&
                    scheduledAllocations.map(schedule => {
                        return <BalanceListCommonExpandRow allocation={schedule} />
                    })}
                <div>
                    <label>{t('cap-core:balance_table_expand_total_payment_amount_label')}</label>
                    {moneyByLocale(
                        sumBy(
                            props.allocation?.scheduledAllocations,
                            (schedule: CapBalanceItemScheduledAllocationEntity) => schedule?.allocationNetAmount!.amount
                        ) || 0
                    )}
                </div>
            </Col>
        </Row>
    )
})
