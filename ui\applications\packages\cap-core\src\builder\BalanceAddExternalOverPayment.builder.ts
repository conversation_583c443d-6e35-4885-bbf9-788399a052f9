import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "MONEY_INPUT",
          "props": {
            "md": 12,
            "name": "externalBalanceAmount",
            "allowDecimal": true,
            "label": "cap-core:balance_actions_drawer_external_overpayment_amount_label",
            "initialAmount": null,
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "External Overpayment Amount"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "b825d82d-c603-4015-b973-fb91e75d8e62"
        }
      ],
      "id": "850c189b-558e-4a14-84bb-da832073850f"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "TEXT_AREA",
          "props": {
            "md": 24,
            "label": "cap-core:balance_actions_drawer_comments_label",
            "autosize": {
              "minRows": 6,
              "maxRows": 6
            },
            "name": "comment",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Comments"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          },
          "id": "472049bf-9737-44ed-aa0b-867e87d3ba14"
        }
      ],
      "id": "57c67844-fa04-4b01-8b17-3e646da4239e"
    }
  ],
  "version": 26
}

export default config;
