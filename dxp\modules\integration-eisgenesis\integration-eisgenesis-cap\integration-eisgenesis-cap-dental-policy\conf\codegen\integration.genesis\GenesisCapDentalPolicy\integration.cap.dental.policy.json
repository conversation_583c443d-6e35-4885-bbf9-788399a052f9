{"swagger": "2.0", "info": {"description": "API for CapDentalUnverifiedPolicy", "version": "1", "title": "CapDentalUnverifiedPolicy model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/command/write": {"post": {"description": "The command that write new or updates existing claim policy", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapUnverifiedPolicyWriteInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapDentalUnverifiedPolicy_DNMaximum": {"required": ["_type"], "properties": {"maximumINAmount": {"$ref": "#/definitions/Money"}, "maximumType": {"type": "string"}, "maximumOONAmount": {"$ref": "#/definitions/Money"}, "maximumGradedYear": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNMaximum"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximum"}, "CapDentalUnverifiedPolicy_DNCoverageParticipant": {"required": ["_type"], "properties": {"insuredLink": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_MockedLink"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNCoverageParticipant"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoverageParticipant"}, "CapDentalUnverifiedPolicy_DNServiceType": {"required": ["_type"], "properties": {"serviceTypeCd": {"type": "string"}, "_type": {"type": "string", "example": "DNServiceType"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNServiceType"}, "CapDentalUnverifiedPolicy_DNDependentEligibility": {"required": ["_type"], "properties": {"childMaxAgeCd": {"type": "string"}, "fullTimeStudentAgeCd": {"type": "string"}, "includeDisabledDependentsInd": {"type": "boolean"}, "_type": {"type": "string", "example": "DNDependentEligibility"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDependentEligibility"}, "CapUnverifiedPolicyWriteInput": {"properties": {"entity": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicy"}}, "title": "CapUnverifiedPolicyWriteInput"}, "CapDentalUnverifiedPolicy_DNMaximumRollover": {"required": ["_type"], "properties": {"maximumRolloverThresholdAmount": {"$ref": "#/definitions/Money"}, "accumulatedRolloverMaximumAmount": {"$ref": "#/definitions/Money"}, "maximumRolloverAmount": {"$ref": "#/definitions/Money"}, "maximumBonusRolloverAmount": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "DNMaximumRollover"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximumRollover"}, "CapDentalUnverifiedPolicy_DNDeductibleDefinition": {"required": ["_type"], "properties": {"deductibleAccumulationPeriod": {"type": "string"}, "deductibleServicesOutOfNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNDeductibleServicesOutNetwork"}}, "isGradedDeductible": {"type": "boolean"}, "deductibles": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNDeductible"}}, "deductibleCredit": {"type": "boolean"}, "deductibleNumberOfGradedYears": {"type": "integer", "format": "int64"}, "deductibleCarryover": {"type": "boolean"}, "deductibleServicesInNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNDeductibleServicesInNetwork"}}, "_type": {"type": "string", "example": "DNDeductibleDefinition"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductibleDefinition"}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "LoadSingleEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "CapDentalUnverifiedPolicy_DNLocationParty": {"required": ["_type"], "properties": {"addressTypeCd": {"type": "string"}, "nationalId": {"type": "string"}, "stateProvinceCd": {"type": "string"}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "countryCd": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "registryTypeId": {"type": "string"}, "addressLine3": {"type": "string"}, "makePreferredInd": {"type": "boolean"}, "registryEntityNumber": {"type": "string"}, "_type": {"type": "string", "example": "DNLocationParty"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNLocationParty"}, "CapDentalUnverifiedPolicy_DNEligibility": {"required": ["_type"], "properties": {"waitingPeriodAmount": {"type": "integer", "format": "int64"}, "waitingPeriodModeCd": {"type": "string"}, "minHourlyReq": {"type": "number"}, "eligibilityTypeCd": {"type": "string"}, "waitingPeriodDefCd": {"type": "string"}, "_type": {"type": "string", "example": "DNEligibility"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNEligibility"}, "CapDentalUnverifiedPolicy_DNTier": {"required": ["_type"], "properties": {"tierCd": {"type": "string"}, "_type": {"type": "string", "example": "D<PERSON>ier"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "D<PERSON>ier"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "CapDentalUnverifiedPolicy_DNDeductible": {"required": ["_type"], "properties": {"familyDeductibleInNetwork": {"type": "string"}, "deductibleType": {"type": "string"}, "deductibleYear": {"type": "integer", "format": "int64"}, "deductibleInNetworkAmount": {"$ref": "#/definitions/Money"}, "familyDeductibleOutOfNetwork": {"type": "string"}, "deductibleOutOfNetworkAmount": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "DNDeductible"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductible"}, "CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicy": {"required": ["_modelName", "_type"], "properties": {"majorOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "preventiveOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "patientTerm": {"description": "NOT USED", "$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalTermEntity"}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "basicWaitingPeriod": {"type": "string"}, "planName": {"type": "string", "description": "Plan Name"}, "policyPaidToDate": {"type": "string", "format": "date"}, "planCategory": {"type": "string", "description": "Plan Category"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "personParties": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNPersonParty"}}, "pcdTerm": {"description": "PCD Assignment Effective/Termination Period", "$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalTermEntity"}, "coinsurances": {"type": "array", "items": {"description": "Coinsurances percentages details", "$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalPolicyInfoCoinsuranceEntity"}}, "pcdId": {"type": "string", "description": "Primary Care Dentist ID"}, "applyLateEntrantBenefitWaitingPeriods": {"type": "boolean", "description": "Is late entrant benefit period waiting applied"}, "policyPaidToDateWithGracePeriod": {"type": "string", "format": "date"}, "lateEntrantWaitingPeriodsDetails": {"type": "array", "items": {"description": "Late Entrant Waiting Period details", "$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}}, "term": {"description": "Policy effective and expiration dates.", "$ref": "#/definitions/CapDentalUnverifiedPolicy_Term"}, "majorWaitingPeriod": {"type": "string"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "plan": {"type": "string"}, "unverifiedInfo": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedInfoEntity"}, "basicINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "orthoWaitingPeriod": {"type": "string"}, "childMaxAgeCd": {"type": "string", "description": "Child Max Age limit"}, "isWaitingPeriodWaived": {"type": "boolean"}, "insureds": {"type": "array", "items": {"description": "An entity for insureds information.", "$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalPolicyInfoInsuredDetailsEntity"}}, "nonStandardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "orthoINNCoinsurancePercent": {"type": "number"}, "fullTimeStudentAgeCd": {"type": "string", "description": "Full time student Age limit"}, "preventiveINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "individualPackagingDetail": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNPolicyPackage"}, "majorINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "basicOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "waiveINNInd": {"type": "boolean", "description": "NOT USED"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "preventWaitingPeriod": {"type": "string"}, "orthoOONCoinsurancePercent": {"type": "number"}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "standardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "implantsWaitingPeriod": {"type": "string"}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "waiveOONInd": {"type": "boolean", "description": "NOT USED"}, "customer": {"$ref": "#/definitions/EntityLink"}, "_modelName": {"type": "string", "example": "CapDentalUnverifiedPolicy"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "UnverifiedPolicy"}, "_timestamp": {"type": "string", "example": "2022-05-20T14:10:18.865+03:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapDentalUnverifiedPolicy"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "CapDentalUnverifiedPolicy"}, "CapDentalUnverifiedPolicy_DNPerson": {"required": ["_type"], "properties": {"lastName": {"type": "string"}, "deceased": {"type": "boolean"}, "birthDate": {"type": "string", "format": "date"}, "registryEntityNumber": {"type": "string"}, "suffixCd": {"type": "string"}, "firstName": {"type": "string"}, "communicationInfo": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNCommunicationInfo"}, "genderCd": {"type": "string"}, "taxId": {"type": "string"}, "isFullTimeStudent": {"type": "boolean"}, "tobaccoCd": {"type": "string"}, "middleName": {"type": "string"}, "registryTypeId": {"type": "string"}, "salutation": {"type": "string"}, "deceasedDate": {"type": "string", "format": "date"}, "age": {"type": "integer", "format": "int64"}, "maritalStatus": {"type": "string"}, "_type": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "<PERSON><PERSON><PERSON>"}, "CapDentalUnverifiedPolicy_DNDentalMaximumAmount": {"required": ["_type"], "properties": {"dentalMaximums": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNMaximum"}}, "isMaximumCredit": {"type": "boolean"}, "maximumAmountNumberOfGradedYears": {"type": "integer", "format": "int64"}, "maximumServicesInNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNMaximumServicesInNetwork"}}, "maximumServicesOutOfNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNMaximumServicesOutOfNetwork"}}, "isGradedMaximum": {"type": "boolean"}, "isMaximumRollover": {"type": "boolean"}, "maximumAccumulationPeriod": {"type": "string"}, "maxRolloverDetails": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNMaximumRollover"}, "_type": {"type": "string", "example": "DNDentalMaximumAmount"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDentalMaximumAmount"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicy"}}, "title": "CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccess"}, "CapDentalUnverifiedPolicy_DNLateEntrantBenefitWaitingPeriods": {"required": ["_type"], "properties": {"basicWaitingPeriod": {"type": "integer", "format": "int64"}, "majorWaitingPeriod": {"type": "integer", "format": "int64"}, "preventiveWaitingPeriod": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNLateEntrantBenefitWaitingPeriods"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNLateEntrantBenefitWaitingPeriods"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "CapDentalUnverifiedPolicy_CapDentalPolicyInfoCoinsuranceEntity": {"required": ["_type"], "properties": {"coinsuranceOONPct": {"type": "number", "description": "Coinsurance percentage outside network"}, "coinsuranceServiceType": {"type": "string", "description": "Coinsurance Service Type"}, "coinsuranceINPct": {"type": "number", "description": "Coinsurance percentage in network"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoCoinsuranceEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoCoinsuranceEntity"}, "CapDentalUnverifiedPolicy_DNDeductibleServicesInNetwork": {"required": ["_type"], "properties": {"deductibleAppliesToServicesInNetwork": {"type": "string"}, "_type": {"type": "string", "example": "DNDeductibleServicesInNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductibleServicesInNetwork"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "CapDentalUnverifiedPolicy_DNFundingStructure": {"required": ["_type"], "properties": {"participantContributionPct": {"type": "number"}, "contributionBasisCd": {"type": "string"}, "contributionTypeCd": {"type": "string"}, "sponsorContributionAmount": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "DNFundingStructure"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNFundingStructure"}, "CapDentalUnverifiedPolicy_DNWaitingPeriods": {"required": ["_type"], "properties": {"applyLateEntrantBenefitWaitingPeriods": {"type": "boolean"}, "basicWaitingPeriod": {"type": "integer", "format": "int64"}, "lateEntrantWaitingPeriodsDetails": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNLateEntrantBenefitWaitingPeriods"}, "majorWaitingPeriod": {"type": "integer", "format": "int64"}, "waitingPeriodApplyTo": {"type": "string"}, "preventiveWaitingPeriod": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNWaitingPeriods"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNWaitingPeriods"}, "CapDentalUnverifiedPolicy_DNCommunicationInfo": {"required": ["_type"], "properties": {"emails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNEmailInfo"}}, "phones": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNPhoneInfo"}}, "_type": {"type": "string", "example": "DNCommunicationInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCommunicationInfo"}, "CapDentalUnverifiedPolicy_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity": {"required": ["_type"], "properties": {"preventWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant preventive Waiting Period"}, "basicWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant basic Waiting Period"}, "majorWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant major Waiting Period"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "EntityLinkRequestBody": {"properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "CapDentalUnverifiedPolicy_DNMaximumServicesOutOfNetwork": {"required": ["_type"], "properties": {"maximumAppliesToServicesOON": {"type": "string"}, "_type": {"type": "string", "example": "DNMaximumServicesOutOfNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximumServicesOutOfNetwork"}, "CapDentalUnverifiedPolicy_CapDentalPolicyInfoInsuredDetailsEntity": {"required": ["_type"], "properties": {"isFullTimeStudent": {"type": "boolean", "description": "Is patient full time student?"}, "insuredRoleNameCd": {"type": "string", "description": "Insure<PERSON>'s role"}, "relationshipToPrimaryInsuredCd": {"type": "string", "description": "Patient's relationship to primary insured"}, "insuredInfo": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_MockedLink"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "The unique registry ID that identifies the subject of the claim."}, "_type": {"type": "string", "example": "CapDentalPolicyInfoInsuredDetailsEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoInsuredDetailsEntity", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapDentalUnverifiedPolicy_DNEmailInfo": {"required": ["_type"], "properties": {"updatedOn": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "value": {"type": "string"}, "preferred": {"type": "boolean"}, "_type": {"type": "string", "example": "DNEmailInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNEmailInfo"}, "CapDentalUnverifiedPolicy_DNDeductibleServicesOutNetwork": {"required": ["_type"], "properties": {"deductibleAppliesToServicesOutOfNetwork": {"type": "string"}, "_type": {"type": "string", "example": "DNDeductibleServicesOutNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductibleServicesOutNetwork"}, "CapDentalUnverifiedPolicy_DNCoverageRating": {"required": ["_type"], "properties": {"rateBasisCd": {"type": "string"}, "_type": {"type": "string", "example": "DNCoverageRating"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoverageRating"}, "CapDentalUnverifiedPolicy_DNMaximumServicesInNetwork": {"required": ["_type"], "properties": {"maximumAppliesToServicesIN": {"type": "string"}, "_type": {"type": "string", "example": "DNMaximumServicesInNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximumServicesInNetwork"}, "LoadEntityByBusinessKeyRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "CapDentalUnverifiedPolicy_DNPhoneInfo": {"required": ["_type"], "properties": {"phoneExtension": {"type": "string"}, "countryCd": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "value": {"type": "string"}, "preferred": {"type": "boolean"}, "_type": {"type": "string", "example": "DNPhoneInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNPhoneInfo"}, "CapDentalUnverifiedPolicy_CapDentalUnverifiedInfoEntity": {"required": ["_type"], "properties": {"employerName": {"type": "string", "description": "Claim Without Policy Employer Name"}, "groupNumber": {"type": "string", "description": "Claim Without Policy Group Number"}, "_type": {"type": "string", "example": "CapDentalUnverifiedInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalUnverifiedInfoEntity"}, "CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicySuccessBody"}, "CapDentalUnverifiedPolicy_DNPersonParty": {"required": ["_type"], "properties": {"personAddress": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNLocationParty"}}, "personInfo": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNPerson"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNPersonParty"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNPersonParty"}, "CapDentalUnverifiedPolicy_DNPolicyPackage": {"required": ["_type"], "properties": {"plan": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNBenefitPlan"}, "packageCd": {"type": "string"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNPolicyPackage"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNPolicyPackage"}, "CapDentalUnverifiedPolicy_DNBenefitPlan": {"required": ["_type"], "properties": {"roundingAmount": {"type": "integer", "format": "int64"}, "planCd": {"type": "string"}, "roundingFactorCd": {"type": "string"}, "planName": {"type": "string"}, "covDef": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNCoverageDefinition"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNBenefitPlan"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNBenefitPlan"}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "CapDentalUnverifiedPolicy_DNCoinsurance": {"required": ["_type"], "properties": {"coinsuranceINAmount": {"type": "number"}, "coinsuranceOONAmount": {"type": "number"}, "coinsuranceServiceType": {"type": "string"}, "coinsuranceGradedYear": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNCoinsurance"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoinsurance"}, "CapDentalUnverifiedPolicy_DNCoinsuranceDefinition": {"required": ["_type"], "properties": {"reimbursementOONOptions": {"type": "string"}, "isGradedCoinsurance": {"type": "boolean"}, "coinsurances": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNCoinsurance"}}, "coinsuranceNumberOfGradedYears": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNCoinsuranceDefinition"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoinsuranceDefinition"}, "CapUnverifiedPolicyWriteInputBody": {"properties": {"body": {"$ref": "#/definitions/CapUnverifiedPolicyWriteInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapUnverifiedPolicyWriteInputBody"}, "CapDentalUnverifiedPolicy_DNCoverageDefinition": {"required": ["_type"], "properties": {"coverageParticipants": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNCoverageParticipant"}}, "code": {"type": "string"}, "originalEffDate": {"type": "string", "format": "date-time"}, "typeOfServicesCovered": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNServiceType"}}, "dentalMaximumAmount": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNDentalMaximumAmount"}, "waitingPeriods": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNWaitingPeriods"}, "lastUpdateDate": {"type": "string", "format": "date-time"}, "coverageBasisCd": {"type": "string"}, "fundingStructure": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNFundingStructure"}, "dependentEligibilityDetails": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNDependentEligibility"}, "enrollmentDate": {"type": "string", "format": "date"}, "offerStatus": {"type": "string"}, "terminationAge": {"type": "integer", "format": "int64"}, "effDate": {"type": "string", "format": "date-time"}, "eligibilities": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNEligibility"}, "whoCovered": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNTier"}, "coinsuranceDetails": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNCoinsuranceDefinition"}, "deductibleDetails": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNDeductibleDefinition"}, "ratingDetails": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_DNCoverageRating"}, "_type": {"type": "string", "example": "DNCoverageDefinition"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoverageDefinition"}, "CapDentalUnverifiedPolicy_Term": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Term"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "Term"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "CapDentalUnverifiedPolicy_CapDentalTermEntity": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "CapDentalTermEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalTermEntity"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "CapDentalUnverifiedPolicy_MockedLink": {"required": ["_type"], "properties": {"ref": {"type": "string"}, "_type": {"type": "string", "example": "MockedLink"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "MockedLink"}}}