{"swagger": "2.0", "info": {"description": "API for CapPartyRoleDefinition", "version": "1", "title": "CapPartyRoleDefinition model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/cappartyrole/CapPartyRoleDefinition/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/rules/{entryPoint}": {"post": {"description": "endpoint for internal communication for Kraken UI engine", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "entryPoint", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapPartyRoleDefinitionKrakenBundleRequestBody"}}, {"name": "delta", "in": "query", "description": "", "required": false, "type": "boolean"}], "responses": {"200": {"description": "Container for Kraken rules evaluation in front end. This data is internal for Kraken engine. It can be changed for any reason. Backwards compatibility is not supported on this data", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/command/deletePartyRole": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/command/initPartyRole": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapPartyRoleDetailInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/cappartyrole/CapPartyRoleDefinition/v1/command/updatePartyRole": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRoleDetailUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapRoleDetailUpdateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapPartyRoleDetailInitInput": {"properties": {"resource": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleDetailEntity"}, "party": {"$ref": "#/definitions/EntityLink"}}, "title": "CapPartyRoleDetailInitInput"}, "CapPartyRoleDetailInitInputBody": {"properties": {"body": {"$ref": "#/definitions/CapPartyRoleDetailInitInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapPartyRoleDetailInitInputBody"}, "CapPartyRoleDefinition_CapBeneficiaryRoleInformationEntity": {"required": ["_type"], "properties": {"relationshipToDecedentDesc": {"type": "string", "description": "This field will only be displayed if Relationship To Decedent = Other, to capture detail information."}, "relationshipToDecedentCd": {"type": "string", "description": "Relationship to Decedent for such Beneficiary party role"}, "beneficiaryDesignations": {"type": "array", "items": {"description": "An entity for beneficiary designation information.", "$ref": "#/definitions/CapPartyRoleDefinition_CapBeneficiaryDesignationEntity"}}, "_type": {"type": "string", "example": "CapBeneficiaryRoleInformationEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapBeneficiaryRoleInformationEntity", "description": "An entity for beneficiary party role additional information."}, "LoadEntityByBusinessKeyRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntitySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapPartyRoleDefinition_CapPartyRoleEntitySuccessBody"}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "LoadSingleEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "CapPartyRoleDefinition_CapPartyRoleEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleEntity"}}, "title": "CapPartyRoleDefinition_CapPartyRoleEntitySuccess"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "CapPartyRoleDefinition_CapPartyRoleDetailEntity": {"required": ["_modelName", "_type"], "properties": {"associatedWith": {"type": "array", "items": {"type": "string", "description": "Describes the party associated with which loss type(i.e. Absence, STD, SMP, Death, PremiemWaiver etc."}}, "witnessInformation": {"description": "An entity for witness party role additional information.", "$ref": "#/definitions/CapPartyRoleDefinition_CapWitnessRoleInformationEntity"}, "roles": {"type": "array", "items": {"type": "string", "description": "Defines party roles"}}, "relationshipToInsuredDesc": {"type": "string", "description": "Defines party relationship to Insured description, just for relationshipToInsuredCd=OTHER"}, "relationshipToInsuredCd": {"type": "string", "description": "Defines party relationship to Insured"}, "beneficiaryInformation": {"description": "An entity for beneficiary party role additional information.", "$ref": "#/definitions/CapPartyRoleDefinition_CapBeneficiaryRoleInformationEntity"}, "vendorServiceCd": {"type": "array", "items": {"type": "string", "description": "Attribute stores service type for Vendors"}}, "_modelName": {"type": "string", "example": "CapPartyRoleDefinition"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapPartyRole"}, "_timestamp": {"type": "string", "example": "2022-03-25T01:00:19.571+02:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapPartyRoleDetailEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapPartyRoleDetailEntity", "description": "An entity for party role details information."}, "IdentifierRequestBody": {"properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "IdentifierRequestBody"}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "CapRoleDetailUpdateInput": {"required": ["_key"], "properties": {"_updateStrategy": {"type": "string"}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "entity": {"$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleDetailEntity"}}, "title": "CapRoleDetailUpdateInput"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "CapRoleDetailUpdateInputBody": {"properties": {"body": {"$ref": "#/definitions/CapRoleDetailUpdateInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapRoleDetailUpdateInputBody"}, "CapPartyRoleDefinitionKrakenBundleRequest": {"properties": {"dimensions": {"type": "object"}}, "title": "CapPartyRoleDefinitionKrakenBundleRequest"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "CapPartyRoleDefinition_CapWitnessRoleInformationEntity": {"required": ["_type"], "properties": {"eventDescription": {"type": "string"}, "_type": {"type": "string", "example": "CapWitnessRoleInformationEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapWitnessRoleInformationEntity", "description": "An entity for witness party role additional information."}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "EntityLinkRequestBody": {"properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "CapPartyRoleDefinition_CapBeneficiaryDesignationEntity": {"required": ["_type"], "properties": {"coverageId": {"type": "string", "description": "Defines unique coverage(settlement) ID."}, "isQualifiedToReceiveBenefit": {"type": "boolean", "description": "If the beneficiary is qualified to receive the benefit."}, "beneficiaryPercentage": {"type": "number", "description": "Percentage of Benefit Coverage Designation must be within range of 0-100 and cannot exceed 2 decimal places."}, "beneficiaryType": {"type": "string", "description": "Type of beneficiary, Primary and Contingent."}, "hasWillReceiveBenefit": {"type": "boolean", "description": "If the beneficiary is willing to receive the benefit."}, "isRetainedAssetApproved": {"type": "boolean", "description": "If Retained Asset Account is approved to use by beneficairy."}, "proofOfLossReceivedDate": {"type": "string", "format": "date-time", "description": "The date of proof of Loss is received."}, "_type": {"type": "string", "example": "CapBeneficiaryDesignationEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapBeneficiaryDesignationEntity"}, "CapPartyRoleDefinitionKrakenBundleRequestBody": {"properties": {"body": {"$ref": "#/definitions/CapPartyRoleDefinitionKrakenBundleRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapPartyRoleDefinitionKrakenBundleRequestBody"}, "CapPartyRoleDefinition_CapPartyRoleEntity": {"required": ["_modelName", "_type"], "properties": {"details": {"description": "An entity for party role details information.", "$ref": "#/definitions/CapPartyRoleDefinition_CapPartyRoleDetailEntity"}, "party": {"$ref": "#/definitions/EntityLink"}, "resource": {"$ref": "#/definitions/EntityLink"}, "_modelName": {"type": "string", "example": "CapPartyRoleDefinition"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapPartyRole"}, "_timestamp": {"type": "string", "example": "2022-03-25T01:00:19.573+02:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapPartyRoleEntity"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "CapPartyRoleEntity", "description": "An object that defines party role (i.e. insured, claimant, etc.) information."}}}