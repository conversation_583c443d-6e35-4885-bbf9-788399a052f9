/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {dateUtils, IndividualCustomer, OrganizationCustomer} from '@eisgroup/cap-services'
import {useTranslate} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {Button, ColumnProps, Spin, Table} from '@eisgroup/ui-kit'
import {SettingEditMedium} from '@eisgroup/ui-kit-icons'
import {observer} from 'mobx-react'
import React, {useState} from 'react'
import {DrawerWidth} from '../../common/constants'
import {CLAIM_APPROVAL_PERIODS} from '../../common/package-class-names'
import {LOAD_SETTLEMENT, POLL_SETTLEMENT, READJUDICATE_SETTLEMENT} from '../../common/store'
import {moneyByLocale} from '../../common/utils'
import {collectPayee} from '../../utils'
import {DrawerFormStateType, FormDrawer} from '../form-drawer'
import {APPROVAL_PERIODS_FORM_ID, ApprovalPeriodsDrawer} from './ApprovalPeriodsDrawer'
import {ApprovalPeriodsProps, ClaimDisabilityApprovalPeriodEntity} from './types'

export const ApprovalPeriods: React.FC<ApprovalPeriodsProps> = observer(props => {
    const {t} = useTranslate()
    const [drawerFormStateType, setDrawerFormStateType] = useState<DrawerFormStateType>(DrawerFormStateType.Create)
    const [approvalPeriodIndex, setApprovalPeriodIndex] = useState<number>(0)
    const {viewStore} = props

    const getPayeeName = (record: ClaimDisabilityApprovalPeriodEntity): string => {
        const payeeList = [
            ...viewStore.partyInformationStore.claimPartyStore.parties,
            ...viewStore.allClaimsBeneficiaries
        ].map(v => v.customer) as (IndividualCustomer | OrganizationCustomer)[]
        const payeeUri = record.payee?._uri
        const payeeOption = collectPayee(payeeList).find(option => option.code === payeeUri)
        return payeeOption?.displayValue ?? ''
    }

    const getApprovalColumns = (): ColumnProps<any>[] => {
        return [
            {
                title: t('cap-core:start_date'),
                dataIndex: 'approvalPeriod.startDate',
                key: 'approvalPeriod.startDate',
                render: (text, record) => dateUtils(record.approvalPeriod.startDate).render
            },
            {
                title: t('cap-core:end_date'),
                dataIndex: 'approvalPeriod.endDate',
                key: 'approvalPeriod.endDate',
                render: (text, record) => dateUtils(record.approvalPeriod.endDate).render
            },
            {
                title: t('cap-core:settlement_item_detail_period_approvalStatus'),
                dataIndex: 'approvalStatus',
                key: 'approvalStatus',
                render: (text, record) => {
                    return <LookupLabel lookup='ApprovalStatus' code={text} />
                }
            },
            {
                title: t('cap-core:payee'),
                dataIndex: 'approvalPeriod.payee',
                key: 'payee',
                render: (text, record) => getPayeeName(record)
            },
            {
                title: t('cap-core:settlement_item_detail_period_approvalPerson'),
                dataIndex: 'approverPersonFullName',
                key: 'approverPersonFullName'
            },
            {
                title: t('cap-core:settlement_item_detail_period_dateofStatusChange'),
                dataIndex: 'dateOfStatusChange',
                key: 'dateOfStatusChange',
                render: (text, record) => {
                    return dateUtils(record.dateOfStatusChange).render
                }
            },
            {
                title: t('cap-core:settlement_item_detail_period_cancelReason'),
                dataIndex: 'cancelReason',
                key: 'cancelReason'
            },
            {
                title: t('cap-core:approval_period_payment_frequency'),
                dataIndex: 'frequencyType',
                key: 'frequencyType',
                render: (text, record) => {
                    return <LookupLabel lookup='FrequencyType' code={text} />
                }
            },
            {
                title: t('cap-core:approval_period_partial'),
                dataIndex: 'partialDisability.isPartialDisability',
                key: 'partialDisability.isPartialDisability',
                render: (text, record) => {
                    return text ? t('cap-core:yes') : t('cap-core:no')
                }
            },
            {
                title: t('cap-core:approval_period_current_earnings'),
                dataIndex: 'partialDisability.currentEarningsAmount',
                key: 'partialDisability.currentEarningsAmount',
                render: (text, record) => {
                    return text ? moneyByLocale(text.amount) : t('cap-core:not_available')
                }
            },
            {
                title: t('cap-core:approval_period_pol_received_date'),
                dataIndex: 'proofOfLossReceivedDate',
                key: 'proofOfLossReceivedDate',
                render: (text, record) => dateUtils(record.proofOfLossReceivedDate).render
            },
            {
                title: '',
                dataIndex: 'operation',
                key: 'operation',
                align: 'right',
                render: (text, record, index) => {
                    return !props.isHiddenBtn ? (
                        <SettingEditMedium
                            className='approval-periods-edit'
                            onClick={() => showApprovalPeriodDrawer(record, index, DrawerFormStateType.Edit)}
                        />
                    ) : null
                }
            }
        ]
    }

    const showApprovalPeriodDrawer = (_, index: number, mode: DrawerFormStateType): void => {
        setDrawerFormStateType(mode)
        setApprovalPeriodIndex(index)
        viewStore.formDrawerStore.openDrawer(APPROVAL_PERIODS_FORM_ID)
    }

    const renderDrawerContent = (
        <ApprovalPeriodsDrawer
            viewStore={viewStore}
            drawerFormStateType={drawerFormStateType}
            approvalPeriodIndex={approvalPeriodIndex}
            krakenRulesEvaluationContext={props.krakenRulesEvaluationContext}
        />
    )

    const sortedData = React.useMemo(() => {
        const showData =
            viewStore.settlement?.settlementDetail?.approvalPeriods?.map(approvalPeriod => ({
                ...approvalPeriod,
                approverPersonFullName:
                    viewStore.eventCaseStore.getApproverPersonFullName(approvalPeriod.approverPerson ?? '') ||
                    approvalPeriod.approverPerson
            })) || []
        return showData.sort((a, b) =>
            dateUtils(b?.approvalPeriod?.startDate).toMoment.diff(dateUtils(a?.approvalPeriod?.startDate).toMoment)
        )
    }, [viewStore.settlement?.settlementDetail?.approvalPeriods, viewStore.eventCaseStore])

    const isLoading =
        viewStore.actionsStore.isRunning(LOAD_SETTLEMENT) ||
        viewStore.actionsStore.isRunning(POLL_SETTLEMENT) ||
        viewStore.actionsStore.isRunning(READJUDICATE_SETTLEMENT)

    return (
        <Spin spinning={isLoading}>
            <div className={CLAIM_APPROVAL_PERIODS}>
                <div className='approval-periods-section-title'>
                    <h3>{t('cap-core:approval_period_title')}</h3>
                    <Button
                        type='link'
                        icon='action-add-small'
                        hidden={props.isHiddenBtn}
                        onClick={() => showApprovalPeriodDrawer({}, 0, DrawerFormStateType.Create)}
                    >
                        &nbsp; {t('cap-core:add_approval_period')}
                    </Button>
                </div>
                <Table
                    columns={getApprovalColumns()}
                    size='small'
                    dataSource={sortedData}
                    rowKey={record => record._key.id}
                    pagination={false}
                />
            </div>
            {viewStore.formDrawerStore.openedDrawerKey === APPROVAL_PERIODS_FORM_ID && (
                <FormDrawer
                    drawerWidth={DrawerWidth.SMALL}
                    formTitle={
                        drawerFormStateType === DrawerFormStateType.Edit
                            ? t('cap-core:approval_period_Update')
                            : t('cap-core:add_approval_period')
                    }
                    onFormCancel={viewStore.formDrawerStore.closeDrawer}
                    formToRender={renderDrawerContent}
                />
            )}
        </Spin>
    )
})
