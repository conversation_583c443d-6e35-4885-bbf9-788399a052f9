import React from 'react'
import {
    EisIconType,
    ActionChevronUpMedium,
    IconProps,
    SettingEllipsesMedium,
    ValidationMediumError
} from '@eisgroup/ui-kit-icons'
/**
 * Type representing a map of EisIconType to their respective icon.
 * @typedef {Object} IconMap
 * @property {Object.<EisIconType, (props: IconProps) => React.ReactElement>} IconMap - The map of icon types to icon.
 */

/**
 * Map of EisIconType to their respective icon.
 * @type {IconMap}
 */
type IconMap = {
    [K in EisIconType]?: (props: IconProps) => React.ReactElement
}

/**
 * Map of EisIconType to their respective icon.
 * @type {IconMap}
 */
const eisIconTypeMap: IconMap = {
    'validation-error': props => <ValidationMediumError {...props} />,
    'action-chevron-up-large': props => <ActionChevronUpMedium {...props} />,
    'setting-ellipses': props => <SettingEllipsesMedium {...props} />
}
/**
 * Retrieves the React element for a given EisIconType with optional custom props.
 *
 * @function
 * @param {EisIconType} iconType - The type of the icon.
 * @param {IconProps} [customProps={}] - Optional custom props for the icon.
 * @returns {React.ReactElement | null} The React element for the specified icon type.
 */
export const getEisIcon = (iconType: EisIconType, customProps: IconProps = {}): React.ReactElement | null => {
    const iconCreator = eisIconTypeMap[iconType]
    return iconCreator ? iconCreator(customProps) : null
}
