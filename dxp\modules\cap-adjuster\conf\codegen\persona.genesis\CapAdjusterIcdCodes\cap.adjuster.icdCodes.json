{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap.integration": {"schema": "integration.cap.integration.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: ICD Codes", "version": "1", "title": "CAP Adjuster: ICD Codes API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/icd-codes", "description": "CAP Adjuster: ICD Codes operations"}], "paths": {"/icd-code/{icdCode}": {"get": {"x-dxp-path": "/api/common/Integration/v1/icdCode/{icdCode}", "tags": ["/cap-adjuster/v1/icd-codes"]}}, "/icd-code": {"get": {"x-dxp-path": "/api/common/Integration/v1/icdCode", "tags": ["/cap-adjuster/v1/icd-codes"], "parameters": [{"name": "icdKeyword", "in": "query", "description": "Partial match of the ICD code or description. It will match using contains logic.", "required": true, "type": "string"}, {"name": "resultLimit", "in": "query", "description": "Number of entries to be return. default is 10", "required": true, "type": "integer"}]}}, "/icd-code/icdCodes": {"get": {"x-dxp-path": "/api/common/Integration/v1/icdCode/batch", "tags": ["/cap-adjuster/v1/icd-codes"], "parameters": [{"name": "icdCodes", "in": "query", "description": "List of ICD codes to resolve", "required": false, "type": "string"}]}}}}