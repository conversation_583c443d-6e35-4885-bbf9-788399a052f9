import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "RADIO_INPUT",
          "props": {
            "md": 12,
            "options": [
              {
                "label": "cap-core:balance_actions_drawer_amount",
                "value": "true"
              },
              {
                "label": "cap-core:percentage",
                "value": "false"
              }
            ],
            "label": "cap-core:remaining_balance_type",
            "name": "remainingBalanceType",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Remaining Balance Type"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            },
            "events": [],
            "handlers": []
          },
          "id": "a99b9894-d63e-44ca-9261-54264f783579"
        }
      ],
      "id": "0b0b53f7-f907-4d22-b8bd-65047405b654"
    },
    {
      "type": "ROW",
      "props": {
        "gutter": 16,
        "md": 24,
        "itemCount": null
      },
      "components": [
        {
          "type": "COMPONENT_SLOT",
          "props": {
            "md": 12,
            "span": 24,
            "slotId": "WithholdingAmount",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "remainingBalanceType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": true
                      }
                    }
                  ]
                }
              }
            },
            "offset": 0,
            "push": 0,
            "pull": 0,
            "blockClassName": "claim-reduce-payment-withholding-amount"
          },
          "components": [
            {
              "type": "COMPONENT_SLOT",
              "props": {
                "md": 24,
                "span": 24
              },
              "components": [],
              "id": "37030326-66e1-4995-9d9e-a8e36bfe66bc"
            }
          ],
          "id": "d8a79298-e71c-4961-9977-de8968c202c5"
        },
        {
          "type": "COMPONENT_SLOT",
          "props": {
            "md": 12,
            "span": 24,
            "blockClassName": "claim-reduce-payment-withholding-amount",
            "slotId": "WithholdingPercentage",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "remainingBalanceType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": false
                      }
                    }
                  ]
                }
              }
            }
          },
          "components": [],
          "id": "94081e01-b000-4541-9a75-3a32e4a07d51"
        }
      ],
      "id": "dc8e7f66-9e81-4163-bce0-0fc643af5ff3"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24,
        "offset": 0,
        "itemCount": 2,
        "blockClassName": "claim_reduce_payment_drawer_calculate",
        "gutter": 8
      },
      "components": [
        {
          "type": "NUMBER_INPUT",
          "props": {
            "md": 12,
            "name": "numberOfEstimatedWeekly",
            "hideControls": true,
            "allowDecimal": true,
            "fractionSize": 1,
            "label": "cap-core:balance_actions_drawer_number_of_estimated_payments_label",
            "condition": {
              "disabled": {
                "conditionInputType": "form",
                "type": "boolean",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "numberOfEstimatedPayments"
                      },
                      "operator": "$empty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    },
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "numberOfEstimatedPayments"
                      },
                      "operator": "$notEmpty",
                      "outputSource": {
                        "type": "PRIMITIVE"
                      }
                    }
                  ],
                  "operator": "$or"
                }
              },
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "remainingBalanceType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": true
                      }
                    }
                  ]
                }
              }
            },
            "prohibitNegative": false
          },
          "id": "c1505799-8398-4168-a430-ed3c503ccdac"
        }
      ],
      "id": "d1f9b5c9-2185-462c-911e-85cfaa53c794"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "CUSTOM_SELECT_INPUT",
          "id": "38105eb6-6c65-4d75-8c44-68de2971851b",
          "props": {
            "md": 12,
            "name": "selectedClaim",
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "remainingBalanceType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": true
                      }
                    }
                  ]
                }
              }
            },
            "label": "cap-core:balance_actions_drawer_claim_label",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Claim"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required"
                }
              ]
            }
          }
        },
        {
          "type": "CUSTOM_SELECT_INPUT",
          "id": "df3f5ccc-3c5f-46c4-a24f-3f3c3f5b8350",
          "props": {
            "name": "selectedClaims",
            "md": 12,
            "condition": {
              "display": {
                "conditionInputType": "form",
                "type": "display",
                "rulesTree": {
                  "rules": [
                    {
                      "inputSource": {
                        "type": "FIELD",
                        "value": "remainingBalanceType"
                      },
                      "operator": "$eq",
                      "outputSource": {
                        "type": "PRIMITIVE",
                        "value": false
                      }
                    }
                  ]
                }
              }
            },
            "label": "cap-core:balance_actions_drawer_claim_label",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required"
                },
                {
                  "skipOnEmpty": false,
                  "type": "array-length",
                  "fieldValues": {
                    "fieldName": {
                      "type": "value",
                      "valueType": "string",
                      "value": "cap-core:balance_actions_drawer_claim_label"
                    }
                  }
                }
              ]
            }
          }
        }
      ],
      "id": "e9919b0a-aa32-44fc-a0d3-87aff1ec37b5"
    }
  ],
  "version": 243,
  "globalEvents": {},
  "actionChains": {}
}

export default config;
