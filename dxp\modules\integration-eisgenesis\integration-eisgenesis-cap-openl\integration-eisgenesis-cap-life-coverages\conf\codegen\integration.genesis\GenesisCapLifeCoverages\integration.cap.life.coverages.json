{"openapi": "3.0.1", "info": {"description": "Auto-generated OpenAPI schema from the OpenL rules", "title": "claim-life-settlement", "version": "1.0.0"}, "servers": [{"url": "/claim-life-settlement", "variables": {}}], "security": [{"BasicAuth": [], "SSOToken": []}], "paths": {"/_api_claim_wrapper_coverages_applicability": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimCoverageInfo[] _api_claim_wrapper_coverages_applicability(org.openl.generated.beans.CapClaimWrapperCoveragesAutomationInput input)", "operationId": "_api_claim_wrapper_coverages_applicability", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperCoveragesAutomationInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimCoverageInfo"}}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimCoverageInfo[] _api_claim_wrapper_coverages_applicability(CapClaimWrapperCoveragesAutomationInput)"}}, "/_api_claim_wrapper_eligibility": {"post": {"description": "Rules method: org.openl.generated.beans.CapClaimWrapperEligibilityResult _api_claim_wrapper_eligibility(org.openl.generated.beans.CapClaimWrapperEligibilityInput request)", "operationId": "_api_claim_wrapper_eligibility", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperEligibilityInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperEligibilityResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapClaimWrapperEligibilityResult _api_claim_wrapper_eligibility(CapClaimWrapperEligibilityInput)"}}, "/_api_coverages_add_to_accumulatorTx": {"post": {"description": "Rules method: org.openl.generated.beans.AccumulatorTransactionResponse _api_coverages_add_to_accumulatorTx(org.openl.generated.beans.CapClaimWrapperCoveragesToAccumulatorInput input)", "operationId": "_api_coverages_add_to_accumulatorTx", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperCoveragesToAccumulatorInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccumulatorTransactionResponse"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "AccumulatorTransactionResponse _api_coverages_add_to_accumulatorTx(CapClaimWrapperCoveragesToAccumulatorInput)"}}, "/_api_coverages_refresh_to_accumulatorTx": {"post": {"description": "Rules method: org.openl.generated.beans.AccumulatorTransactionResponse _api_coverages_refresh_to_accumulatorTx(org.openl.generated.beans.CapClaimWrapperCoveragesToAccumulatorInput input)", "operationId": "_api_coverages_refresh_to_accumulatorTx", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapClaimWrapperCoveragesToAccumulatorInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccumulatorTransactionResponse"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "AccumulatorTransactionResponse _api_coverages_refresh_to_accumulatorTx(CapClaimWrapperCoveragesToAccumulatorInput)"}}, "/_api_life_coverage_config": {"post": {"description": "Rules method: org.openl.generated.beans.CapPolicyCoverageBasedConfigInfo _api_life_coverage_config(org.openl.generated.beans.CapPolicyCoverageBasedConfigInput input)", "operationId": "_api_life_coverage_config", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapPolicyCoverageBasedConfigInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapPolicyCoverageBasedConfigInfo"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapPolicyCoverageBasedConfigInfo _api_life_coverage_config(CapPolicyCoverageBasedConfigInput)"}}, "/_api_life_coverages": {"post": {"description": "Rules method: org.openl.generated.beans.CapPolicyCoveragesList _api_life_coverages(org.openl.generated.beans.CapPolicyCoverageBasedInput input)", "operationId": "_api_life_coverages", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapPolicyCoverageBasedInput"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CapPolicyCoveragesList"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "CapPolicyCoveragesList _api_life_coverages(CapPolicyCoverageBasedInput)"}}, "/_api_settlement_approval": {"post": {"description": "Rules method: org.openl.generated.beans.SettlementApprovalResult _api_settlement_approval(org.openl.generated.beans.SettlementApprovalRequest theInput)", "operationId": "_api_settlement_approval", "parameters": [{"example": "en-GB", "in": "header", "name": "Accept-Language", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettlementApprovalRequest"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettlementApprovalResult"}}}, "description": "Successful operation"}, "204": {"description": "Successful operation"}, "400": {"content": {"application/json": {"example": {"message": "Cannot parse 'bar' to JSON", "type": "BAD_REQUEST"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Invalid request format e.g. missing required field, unparseable JSON value, etc."}, "422": {"content": {"application/json": {"examples": {"Example 1": {"description": "Example 1", "value": {"message": "Some message", "type": "USER_ERROR"}}, "Example 2": {"description": "Example 2", "value": {"message": "Some message", "code": "code.example", "type": "USER_ERROR"}}}, "schema": {"oneOf": [{"$ref": "#/components/schemas/JAXRSUserErrorResponse"}, {"$ref": "#/components/schemas/JAXRSErrorResponse"}]}}}, "description": "Custom user errors in rules or validation errors in input parameters"}, "500": {"content": {"application/json": {"example": {"message": "Failed to load lazy method.", "type": "COMPILATION"}, "schema": {"$ref": "#/components/schemas/JAXRSErrorResponse"}}}, "description": "Internal server errors e.g. compilation or parsing errors, runtime exceptions, etc."}}, "summary": "SettlementApprovalResult _api_settlement_approval(SettlementApprovalRequest)"}}}, "components": {"schemas": {"AccumulatorTransactionData": {"type": "object", "properties": {"amount": {"type": "number"}, "coverage": {"type": "string"}, "extension": {"$ref": "#/components/schemas/AccumulatorTransactionExtension"}, "party": {"$ref": "#/components/schemas/EntityLink"}, "resource": {"$ref": "#/components/schemas/EntityLink"}, "transactionDate": {"type": "string", "format": "date-time"}, "type": {"type": "string"}}}, "AccumulatorTransactionExtension": {"type": "object", "properties": {"_type": {"type": "string", "default": "JsonType"}, "accumulatorUnitCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "transactionEffectiveDate": {"type": "string", "format": "date-time"}}}, "AccumulatorTransactionResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/AccumulatorTransactionData"}}}}, "CapAccumulatorEntity": {"type": "object", "properties": {"accumulatorUnitCd": {"type": "string"}, "coverage": {"type": "string"}, "limitAmount": {"type": "number"}, "party": {"$ref": "#/components/schemas/EntityLink"}, "resource": {"$ref": "#/components/schemas/EntityLink"}, "term": {"$ref": "#/components/schemas/Term"}, "type": {"type": "string"}}}, "CapAgeReductionDetailsEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "ageCd": {"type": "integer", "format": "int32"}, "reducedToCd": {"type": "number"}}}, "CapClaimCoverageInfo": {"type": "object", "properties": {"benefitCategory": {"type": "string"}, "claimCoverageLabel": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "isAutomaticCoverage": {"type": "boolean"}, "lossType": {"type": "array", "items": {"type": "string"}}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}}}, "CapClaimEarningsEntity": {"type": "object", "properties": {"appliedCoverageGroup": {"type": "string"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimPolicyInfo": {"type": "object", "properties": {"capPolicyId": {"type": "string"}, "productCd": {"type": "string"}}}, "CapClaimSettlementInfo": {"type": "object", "properties": {"accumulatorTerms": {"type": "array", "items": {"$ref": "#/components/schemas/Term"}}, "burnDegree": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "coverageConfiguration": {"$ref": "#/components/schemas/CapPolicyCoverageBasedConfigInfo"}, "dependentRegistryId": {"type": "string"}, "policyBenefitCd": {"type": "string"}, "policyBenefitInfo": {"$ref": "#/components/schemas/CapPolicyBenefitInfo"}, "policyCoverageCd": {"type": "string"}, "reductionAmountType": {"type": "string"}, "uri": {"$ref": "#/components/schemas/EntityLink"}}}, "CapClaimSubjectInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "age": {"type": "integer", "format": "int32"}, "registryId": {"type": "string"}, "relationshipToInsuredCd": {"type": "string"}}}, "CapClaimWrapperAvailableCoverageEntity": {"type": "object", "properties": {"benefitCategory": {"type": "string"}, "claimCoverageLabel": {"type": "string"}, "isAutomaticCoverage": {"type": "boolean"}, "lossType": {"type": "array", "items": {"type": "string"}}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}}}, "CapClaimWrapperCoverageInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "ageReductionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/CapAgeReductionDetailsEntity"}}, "benefitStructures": {"type": "array", "items": {"$ref": "#/components/schemas/CapCoverageBenefitStructuresEntity"}}, "coverageCd": {"type": "string"}, "eligibility": {"$ref": "#/components/schemas/CapEligibilityEntity"}, "isAgeReductionApplied": {"type": "boolean"}}}, "CapClaimWrapperCoveragesAutomationInput": {"type": "object", "properties": {"claimWrapperInfo": {"$ref": "#/components/schemas/CapClaimWrapperInfo"}, "coverages": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimCoverageInfo"}}, "eventCaseInfo": {"$ref": "#/components/schemas/CapEventCaseInfoEntity"}}}, "CapClaimWrapperCoveragesToAccumulatorInput": {"type": "object", "properties": {"accumulators": {"type": "array", "items": {"$ref": "#/components/schemas/CapAccumulatorEntity"}}, "action": {"type": "string"}, "claimWrapperInfo": {"$ref": "#/components/schemas/CapClaimWrapperEntity"}, "currentDateTime": {"type": "string", "format": "date-time"}, "settlements": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimSettlementInfo"}}}}, "CapClaimWrapperEarningsEntity": {"type": "object", "properties": {"appliedCoverageGroup": {"type": "string"}, "overrideFaceValueAmount": {"$ref": "#/components/schemas/Money"}}}, "CapClaimWrapperEligibilityInput": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "availableCoverages": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimWrapperAvailableCoverageEntity"}}, "dateOfHire": {"type": "string", "format": "date-time"}, "eligibilityOverrideCd": {"type": "string"}, "eligibilityOverrideReason": {"type": "string"}, "lossInfo": {"$ref": "#/components/schemas/CapDamageLossInfoEntity"}, "policy": {"$ref": "#/components/schemas/CapLifeLossPolicyInfoEntity"}, "subjectOfClaim": {"$ref": "#/components/schemas/CapClaimSubjectInfoEntity"}}}, "CapClaimWrapperEligibilityResult": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "eligibilityEvaluationCd": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageType"}}}}, "CapClaimWrapperEntity": {"type": "object", "properties": {"claimSubjectId": {"type": "string"}, "claimType": {"type": "string"}, "earnings": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimEarningsEntity"}}, "policy": {"$ref": "#/components/schemas/CapClaimPolicyInfo"}, "uri": {"$ref": "#/components/schemas/EntityLink"}}}, "CapClaimWrapperInfo": {"type": "object", "properties": {"availableLossTypes": {"type": "array", "items": {"type": "string"}}, "claimType": {"type": "string"}, "earnings": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimWrapperEarningsEntity"}}, "existingCoverages": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimCoverageInfo"}}}}, "CapCoverageBenefitStructuresEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "annualEarningsAmount": {"$ref": "#/components/schemas/Money"}, "approvedAmount": {"$ref": "#/components/schemas/Money"}, "benefitAmount": {"$ref": "#/components/schemas/Money"}, "benefitTypeCd": {"type": "string"}, "employeeAmtpct": {"type": "number"}, "incrementAmount": {"$ref": "#/components/schemas/Money"}, "individualAmtPct": {"type": "number"}, "maxBenefitAmount": {"$ref": "#/components/schemas/Money"}, "minBenefitAmount": {"$ref": "#/components/schemas/Money"}, "roundingMethod": {"type": "string"}, "salaryMultiple": {"type": "number"}, "typeOfBenefitStructure": {"type": "string"}}}, "CapDamageLossInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "accidentDate": {"type": "string", "format": "date-time"}, "claimEvents": {"type": "array", "items": {"type": "string"}}, "claimLossDate": {"type": "string", "format": "date-time"}, "dateOfDiagnosis": {"type": "string", "format": "date-time"}, "officialDeathDate": {"type": "string", "format": "date-time"}, "primaryDiagnosisDate": {"type": "string", "format": "date-time"}}}, "CapEligibilityEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "eligibilityTypeCd": {"type": "string"}, "waitingPeriodAmount": {"type": "integer", "format": "int32"}, "waitingPeriodDefCd": {"type": "string"}, "waitingPeriodModeCd": {"type": "string"}}}, "CapEventCaseInfoEntity": {"type": "object", "properties": {"claimEvents": {"type": "array", "items": {"type": "string"}}, "lossItems": {"type": "array", "items": {"type": "string"}}}}, "CapInsuredInfo": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "isMain": {"type": "boolean"}, "registryTypeId": {"type": "string"}}}, "CapLifeLossPolicyInfoEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "capCoverageInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapClaimWrapperCoverageInfoEntity"}}, "capPolicyId": {"type": "string"}, "capPolicyVersionId": {"type": "string"}, "insureds": {"type": "array", "items": {"$ref": "#/components/schemas/CapInsuredInfo"}}, "isVerified": {"type": "boolean"}, "masterAvailablePlans": {"type": "array", "items": {"$ref": "#/components/schemas/CapMasterPolicyPlanDetailsEntity"}}, "masterPolicyId": {"type": "string"}, "masterPolicyNumber": {"type": "string"}, "policyNumber": {"type": "string"}, "policyPackageCd": {"type": "string"}, "policyPlanCd": {"type": "string"}, "policyPlanId": {"type": "string"}, "policyStatus": {"type": "string"}, "policyType": {"type": "string"}, "productCd": {"type": "string"}, "riskStateCd": {"type": "string"}, "term": {"$ref": "#/components/schemas/Term"}, "txEffectiveDate": {"type": "string"}}}, "CapLifeSettlementAttrOptions": {"type": "object", "properties": {"attrName": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}}}, "CapMasterPolicyPlanDetailsEntity": {"type": "object", "properties": {"planCd": {"type": "string"}, "planId": {"type": "string"}, "planName": {"type": "string"}}}, "CapPolicyBenefitInfo": {"type": "object", "properties": {"benefitPct": {"type": "number"}, "lifetimeMaxBenefitAmountPct": {"type": "number"}, "maxBenefitAmount": {"$ref": "#/components/schemas/Money"}, "maxBenefitNumber": {"type": "integer", "format": "int32"}, "maxBenefitNumber2": {"type": "integer", "format": "int32"}, "multiplePct": {"type": "number"}, "reductionInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CapReductionAmountInfoEntity"}}, "timePeriodCd": {"type": "string"}}}, "CapPolicyCoverageBasedConfigInfo": {"type": "object", "properties": {"accidentInd": {"type": "boolean"}, "accumulationCategoryGroup": {"type": "string"}, "applicableBurnDegrees": {"type": "array", "items": {"type": "string"}}, "applicableReductionAmountTypes": {"type": "array", "items": {"type": "string"}}, "attrOptions": {"type": "array", "items": {"$ref": "#/components/schemas/CapLifeSettlementAttrOptions"}}, "benefitCategory": {"type": "string"}, "benefitLevelMapping": {"type": "array", "items": {"type": "string"}}, "calculationFormulaId": {"type": "string"}, "grossAmountMode": {"type": "string"}, "groupUnit": {"type": "string"}, "incurralPeriodUnit": {"type": "string"}, "limitLevels": {"type": "array", "items": {"$ref": "#/components/schemas/CapPolicyCoverageLimitLevel"}}, "unit": {"type": "string"}}}, "CapPolicyCoverageBasedConfigInput": {"type": "object", "properties": {"policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}, "policyProductCd": {"type": "string"}}}, "CapPolicyCoverageBasedInfo": {"type": "object", "properties": {"benefitCategory": {"type": "string"}, "claimCoverageLabel": {"type": "string"}, "claimCoveragePrefix": {"type": "string"}, "isAutomaticCoverage": {"type": "boolean"}, "lossType": {"type": "array", "items": {"type": "string"}}, "pathOfAvailableIndicator": {"type": "string"}, "policyBenefitCd": {"type": "string"}, "policyCoverageCd": {"type": "string"}}}, "CapPolicyCoverageBasedInput": {"type": "object", "properties": {"policyPackageCd": {"type": "string"}, "policyPlanCd": {"type": "string"}, "policyProductCd": {"type": "string"}, "relationshipToParticipant": {"type": "string"}}}, "CapPolicyCoverageLimitLevel": {"type": "object", "properties": {"limitLevelType": {"type": "string"}, "timePeriodCd": {"type": "string"}}}, "CapPolicyCoveragesList": {"type": "object", "properties": {"coverages": {"type": "array", "items": {"$ref": "#/components/schemas/CapPolicyCoverageBasedInfo"}}}}, "CapReductionAmountInfoEntity": {"type": "object", "properties": {"reductionAmount": {"$ref": "#/components/schemas/Money"}, "reductionAmountTypeCd": {"type": "string"}}}, "CapSettlementEntity": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "calculatedCaseAmount": {"$ref": "#/components/schemas/Money"}, "calculatedClaimAmount": {"$ref": "#/components/schemas/Money"}, "calculatedLossAmount": {"$ref": "#/components/schemas/Money"}, "caseNumber": {"type": "string"}, "claimNumber": {"type": "string"}, "lossNumber": {"type": "string"}}}, "EntityKey": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string"}}}, "EntityLink": {"type": "object", "properties": {"_uri": {"type": "string"}}}, "JAXRSErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["USER_ERROR", "RULES_RUNTIME", "COMPILATION", "SYSTEM", "BAD_REQUEST", "VALIDATION"]}, "message": {"type": "string"}}}, "JAXRSUserErrorResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "MessageType": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "source": {"type": "string"}}}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}}}, "ResultEntryType": {"type": "object", "properties": {"authorityLimit": {"$ref": "#/components/schemas/Money"}, "calculatedAmount": {"$ref": "#/components/schemas/Money"}, "entityRefNo": {"type": "string"}, "messageCd": {"type": "string"}, "modelScope": {"type": "string"}, "settlementApprovalCd": {"type": "string"}}}, "SettlementApprovalRequest": {"type": "object", "properties": {"settlement": {"$ref": "#/components/schemas/CapSettlementEntity"}, "userAuthority": {"$ref": "#/components/schemas/UserAuthorityLevel"}}}, "SettlementApprovalResult": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "approvalStatus": {"type": "string"}, "resultDetails": {"$ref": "#/components/schemas/ResultEntryType"}}}, "Term": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}}, "UserAuthorityLevel": {"type": "object", "properties": {"_key": {"$ref": "#/components/schemas/EntityKey"}, "_type": {"type": "string"}, "authorityLevel": {"type": "integer", "format": "int64"}, "authorityLevelType": {"type": "string"}, "modelScopes": {"type": "array", "items": {"type": "string"}}, "typeCd": {"type": "string"}}}}, "securitySchemes": {"BasicAuth": {"description": "Genesis Basic credentials", "scheme": "basic", "type": "http"}, "SSOToken": {"description": "Genesis Authorization header. Value format: `[Token {token}]`", "in": "header", "name": "Authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}