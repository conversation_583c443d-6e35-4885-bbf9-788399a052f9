/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.lifeclaim.converters;

import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.lifeclaim.dto.CapLifeClaimSettlement;
import dataproviders.dto.CapLifeSettlementDTO;

public class CapAdjusterClaimRelationshipSettlementConverter<I extends CapLifeSettlementDTO, A extends CapLifeClaimSettlement>
        extends GenesisRootApiModelConverter<I, A> {

        @Override
        public A convertToApiDTO(I intDTO, A apiDTO) {
            super.convertToApiDTO(intDTO, apiDTO);
            apiDTO.settlementType = intDTO.settlementType;
            apiDTO.claimLossIdentification = intDTO.claimLossIdentification;
            apiDTO.claimWrapperIdentification = intDTO.claimWrapperIdentification;
            apiDTO.policy = intDTO.policy;
            apiDTO.policyId = intDTO.policyId;
            apiDTO.settlementNumber = intDTO.settlementNumber;
            apiDTO.settlementDetail = intDTO.settlementDetail;
            apiDTO.state = intDTO.state;
            apiDTO.applicabilityResult = intDTO.applicabilityResult;
            apiDTO.settlementLossInfo = intDTO.settlementLossInfo;
            apiDTO.coverageBasedConfiguration = intDTO.coverageBasedConfiguration;
            apiDTO.settlementResult = intDTO.settlementResult;
            apiDTO.settlementApprovalResult = intDTO.settlementApprovalResult;
            apiDTO.settlementLifeIntakeInfo = intDTO.settlementLifeIntakeInfo;
            apiDTO.claimCoverageName = intDTO.claimCoverageName;
            apiDTO.claimCoveragePrefix = intDTO.claimCoveragePrefix;
            return apiDTO;
        }
}
