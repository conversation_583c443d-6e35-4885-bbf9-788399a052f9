/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery} from '@eisgroup/cap-gateway-client'
import {
    ClaimLoss,
    IndividualCustomer,
    OrganizationCustomer,
    CaseSystemService,
    CSClaimWrapperService,
    CapGenericLoss
} from '@eisgroup/cap-services'
import {LocalizationUtils, Money, t} from '@eisgroup/i18n'
import {AntDropdown, AntMenu, Button, Collapse, Select, Tooltip} from '@eisgroup/ui-kit'
import {ActionChevronUpMedium} from '@eisgroup/ui-kit-icons'
import classnames from 'classnames'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'
import React, {useEffect, useState} from 'react'
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {
    collectPayee,
    POST_RECOVERY_DRAWER_FORM_ID,
    PostRecoveryDrawer,
    SelectOptionProps,
    FINANCIAL_INFORMATION_BALANCE_TABLE,
    hasAuthorities,
    Privileges,
    MoneyFormat,
    BALANCE_ACTIONS_MAP,
    BALANCE_HEADER_AMOUNT_BOX,
    BALANCE_HEADER_LEFT_SECTION,
    BALANCE_HEADER_RIGHT_SECTION,
    BALANCE_HEADER_RIGHT_SECTION_TOOLTIP,
    BALANCE_TABLE_TITLE,
    ClaimsForReducePayment,
    CUSTOM_DROPDOWN,
    CUSTOM_DROPDOWN_ACTION_ARROW,
    ICaseSystem,
    CapEventCaseEntity,
    EligibilityStatus,
    BALANCE_HEADER_WITH_NO_ACTION_DROPDOWN,
    ADD_OR_EDIT,
    StoreTypeForBalanceProps,
    SILENT_POLL_LOAD_EVENT_CASE,
    LOAD_BALANCE_CHANGE_LOG,
    UPDATE_EVENT_CASE,
    LOAD_BALANCE,
    isPaymentLevelClaim
} from '../..'
import {BalanceActionDrawer} from './action-drawer/BalanceActionDrawer'
import {BalanceActivities} from './BalanceActivities'
import {RecalculationPayments} from './RecalculationPayments'
import {PaymentWithholdingTable} from './PaymentWithholdingTable'
import moneyValue = LocalizationUtils.moneyValue
import CapFinancialAdjustmentWithholdingEntity = CapEventCase.CapFinancialAdjustmentWithholdingEntity

const Option = Select.Option
const {Panel} = Collapse

const ACTION_DRAWER_KEY = 'balanceActionForm'

export type BalanceTableProps<
    CS extends ICaseSystem,
    CSService extends CaseSystemService | CSClaimWrapperService<CS>
> = {
    store: StoreTypeForBalanceProps<CS, CSService>
    losses: ClaimLoss[]
    claims: CapGenericLoss[]
    eventCase: CapEventCaseEntity
    isClaimLevel?: boolean
    isHiddenBtn?: boolean
}

export interface BalanceTableState {
    actionKey: string
    isDropdownVisible: boolean
    payeeOptions: SelectOptionProps[]
    payeeLink: string
}

const REDUCE_PAYMENT = 'REDUCE_PAYMENT'

export const BalanceTable: React.FC<
    BalanceTableProps<ICaseSystem, CaseSystemService | CSClaimWrapperService<ICaseSystem>>
> = observer(props => {
    const [actionKey, setActionKey] = useState('')
    const [payeeLink, setPayeeLink] = useState('')
    const [reducePaymentMode, setReducePaymentMode] = useState(ADD_OR_EDIT.ADD)
    const [isDropdownVisible, setIsDropdownVisible] = useState(false)
    const [payeeOptions, setPayeeOptions] = useState<SelectOptionProps[]>([])
    const [currentWithholding, setCurrentWithholding] = useState<CapFinancialAdjustmentWithholdingEntity>(
        {} as CapFinancialAdjustmentWithholdingEntity
    )

    const hasPostRecoveryPrivilege = hasAuthorities([Privileges.FINANCIAL_INITIATE_RECOVERY])
    const hasAddExternalOverpaymentPrivilege = hasAuthorities([Privileges.FINANCIAL_INITIATE_EXTERNAL_BALANCE])
    const hasOverpaymentWaivePrivilege = hasAuthorities([Privileges.FINANCIAL_INITIATE_OVERPAYMENT_WAIVE])
    const hasPayUnderpaymentPrivilege = hasAuthorities([Privileges.FINANCIAL_INITIATE_UNDERPAYMENT])
    const hasReducePaymentPrivilege = hasAuthorities([Privileges.CASE_REDUCE_PAYMENT_WITHHOLDING])
    const hasCancelExternalOverpaymentPrivilege = hasAuthorities([Privileges.FINANCIAL_CANCEL_EXTERNAL_BALANCE])
    const hasCancelOverpaymentWaivePrivilege = hasAuthorities([Privileges.FINANCIAL_CANCEL_OVERPAYMENT_WAIVE])

    const claims = toJS(props.claims)
    const associatedLosses = toJS(props.losses)
    const associatedLossesCap = associatedLosses.map(v => {
        return {
            _modelName: v._modelName,
            claimType: v.lossType || '',
            lossNumber: v.lossNumber || '',
            rootId: v._key?.rootId || '',
            revisionNo: v._key?.revisionNo || 1
        }
    })

    const otherClaims = claims
        .filter(s => s.eligibilityResult?.eligibilityEvaluationCd !== EligibilityStatus.NOT_ELIGIBLE)
        .map(v => {
            return {
                _modelName: v._modelName,
                claimType: v.claimType || '',
                lossNumber: v.lossNumber || '',
                rootId: v._key?.rootId || '',
                revisionNo: v._key?.revisionNo || 1
            }
        })

    const filteredClaims: ClaimsForReducePayment[] = [...associatedLossesCap, ...otherClaims]

    const {
        subjectOfClaim,
        formDrawerStore,
        paymentsStore,
        claimPartyStore,
        eventCaseStore,
        balanceStore,
        allClaimsBeneficiaries
    } = props.store
    const {eventCase, isHiddenBtn, isClaimLevel} = props

    let payeeDisplay = ''
    payeeOptions.forEach(v => {
        if (payeeLink === v.code) {
            payeeDisplay = v.displayValue
        }
    })
    const isNotActivitiesPriv = hasAuthorities([Privileges.FINANCIAL_INITIATE_BALANCE])
    const isActivitiesPriv = hasAuthorities([Privileges.FINANCIAL_INITIATE_BALANCE_CHANGE_LOG])

    useEffect(() => {
        const payeeList = [...toJS(claimPartyStore.parties), ...toJS(allClaimsBeneficiaries)].map(v => v.customer) as [
            IndividualCustomer | OrganizationCustomer
        ]
        const {
            mainInsured,
            employmentStore: {employee}
        } = props.store
        if (subjectOfClaim) {
            payeeList.push(subjectOfClaim)
        }
        if (mainInsured) {
            payeeList.push(mainInsured)
        }
        if (employee) {
            payeeList.push(employee)
        }
        const collectPayeeOptions = collectPayee(payeeList)
        const link =
            collectPayeeOptions.filter(
                v =>
                    v.code === (subjectOfClaim as IndividualCustomer)?.details.person?.registryTypeId ||
                    v.code === (subjectOfClaim as OrganizationCustomer)?.details.legalEntity?.registryTypeId
            )[0]?.code ?? ''
        setPayeeOptions(collectPayeeOptions)
        setPayeeLink(link)
        if (link) {
            balanceStore.loadBalance(link)
        }
    }, [])

    const renderTotalAmount = (totalBalanceAmount?: Money): string => {
        let totalAmountView = t('cap-core:balance_table_amount_box_0')
        if (totalBalanceAmount && totalBalanceAmount?.amount > 0) {
            totalAmountView = t('cap-core:balance_table_amount_box_underpayment')
        } else if (totalBalanceAmount && totalBalanceAmount?.amount < 0) {
            totalAmountView = t('cap-core:balance_table_amount_box_overpayment')
        }
        return totalAmountView
    }

    const renderAmountBox = () => {
        const totalBalanceAmount = balanceStore.balance?.totalBalanceAmount
        const totalAmountView = renderTotalAmount(totalBalanceAmount)

        return (
            <div className={BALANCE_HEADER_RIGHT_SECTION}>
                <div className={BALANCE_HEADER_AMOUNT_BOX}>
                    <Tooltip
                        overlayClassName={BALANCE_HEADER_RIGHT_SECTION_TOOLTIP}
                        title={totalBalanceAmount?.amount ? <MoneyFormat value={totalBalanceAmount?.amount} /> : ''}
                        placement='top'
                        trigger='hover'
                    >
                        <h2
                            className={
                                totalBalanceAmount &&
                                checkIfMoneyIsPositive(moneyValue(totalBalanceAmount?.amount).toString())
                                    ? 'total-balance-right-section-money text-balance-right-section-money-red'
                                    : 'text-balance-right-section-money'
                            }
                        >
                            {totalBalanceAmount?.amount || totalBalanceAmount?.amount === 0 ? (
                                <MoneyFormat value={totalBalanceAmount?.amount} />
                            ) : (
                                t('cap-core:not_available')
                            )}
                        </h2>
                    </Tooltip>
                    <div>{totalAmountView}</div>
                </div>
            </div>
        )
    }

    const handleClickActionMenu = key => {
        if (!payeeLink) {
            return
        }
        setActionKey(key)
        switch (BALANCE_ACTIONS_MAP[key]) {
            case BALANCE_ACTIONS_MAP.WAIVE_OVERPAYMENT:
            case BALANCE_ACTIONS_MAP.PAY_UNDERPAYMENT:
            case BALANCE_ACTIONS_MAP.ADD_EXTERNAL_OVERPAYMENT:
            case BALANCE_ACTIONS_MAP.CANCEL_EXTERNAL_OVERPAYMENT:
            case BALANCE_ACTIONS_MAP.CANCEL_WAIVE_OVERPAYMENT:
                formDrawerStore.openDrawer(ACTION_DRAWER_KEY)
                break

            case BALANCE_ACTIONS_MAP.REDUCE_PAYMENT:
                setReducePaymentMode(ADD_OR_EDIT.ADD)
                setCurrentWithholding({} as CapFinancialAdjustmentWithholdingEntity)
                formDrawerStore.openDrawer(ACTION_DRAWER_KEY)
                break
            case BALANCE_ACTIONS_MAP.POST_RECOVERY:
                formDrawerStore.openDrawer(POST_RECOVERY_DRAWER_FORM_ID)
                break
            default:
                break
        }
    }

    const editWithholding = (record: CapFinancialAdjustmentWithholdingEntity) => {
        setActionKey(REDUCE_PAYMENT)
        setReducePaymentMode(ADD_OR_EDIT.EDIT)
        setCurrentWithholding(record)
        formDrawerStore.openDrawer(ACTION_DRAWER_KEY)
    }

    const getBalanceChangeLog = (nextCount?: number) => {
        const requestBody = {
            payee: {
                matches: [payeeLink]
            },
            originSource: {
                matches: [balanceStore.balanceSource]
            }
        } as CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery
        balanceStore.getBalanceChangeLog(requestBody, nextCount)
    }

    const getLoadPayments = () => {
        const lossParam = isPaymentLevelClaim(eventCase)
            ? {
                  rootId: filteredClaims[0].rootId,
                  revisionNo: filteredClaims[0].revisionNo,
                  modelName: filteredClaims[0]._modelName
              }
            : {
                  rootId: eventCase._key.rootId,
                  revisionNo: eventCase._key.revisionNo,
                  modelName: eventCase._modelName
              }
        paymentsStore.loadPayments([lossParam]).subscribe()
    }

    const onUpdateDeleteWithHolding = (eventCaseEntity: CapEventCaseEntity) => {
        return eventCaseStore.updateEventCaseManually(eventCaseEntity).subscribe(() => {
            eventCaseStore.pollLoadEventCase(props.eventCase._key, SILENT_POLL_LOAD_EVENT_CASE)
            balanceStore.loadBalance(payeeLink)
            getBalanceChangeLog()
        })
    }

    const loadingReducePayments = [
        {store: eventCaseStore, actions: [SILENT_POLL_LOAD_EVENT_CASE, UPDATE_EVENT_CASE]},
        {store: balanceStore, actions: [LOAD_BALANCE, LOAD_BALANCE_CHANGE_LOG]}
    ]
        .map(({store, actions}) => {
            return actions.map(action => store.actionsStore.isRunning(action)).some(Boolean)
        })
        .some(Boolean)

    const renderMenu = () => {
        const totalBalanceAmountNumber = balanceStore.balance?.totalBalanceAmount?.amount
        const disabledMenus = [] as string[]
        if (totalBalanceAmountNumber || totalBalanceAmountNumber === 0) {
            if (!(totalBalanceAmountNumber < 0)) {
                disabledMenus.push(BALANCE_ACTIONS_MAP.WAIVE_OVERPAYMENT, BALANCE_ACTIONS_MAP.REDUCE_PAYMENT)
            }
        } else {
            disabledMenus.push(
                BALANCE_ACTIONS_MAP.WAIVE_OVERPAYMENT,
                BALANCE_ACTIONS_MAP.REDUCE_PAYMENT,
                BALANCE_ACTIONS_MAP.PAY_UNDERPAYMENT
            )
        }
        if (
            isClaimLevel &&
            props.claims?.[0].eligibilityResult?.eligibilityEvaluationCd === EligibilityStatus.NOT_ELIGIBLE
        ) {
            disabledMenus.push(BALANCE_ACTIONS_MAP.REDUCE_PAYMENT)
        }
        if (
            paymentsStore.paymentList?.find(e => e._variation === 'externalBalance' && e.state === 'Issued') ===
            undefined
        ) {
            disabledMenus.push(BALANCE_ACTIONS_MAP.CANCEL_EXTERNAL_OVERPAYMENT)
        }
        if (
            paymentsStore.paymentList?.find(e => e._variation === 'overpaymentWaive' && e.state === 'Issued') ===
            undefined
        ) {
            disabledMenus.push(BALANCE_ACTIONS_MAP.CANCEL_WAIVE_OVERPAYMENT)
        }
        const isClosedCase = eventCaseStore.eventCase.state === 'Closed'

        return (
            <AntMenu>
                {hasPrivilegeForAction().map(key => (
                    <AntMenu.Item
                        key={key}
                        onClick={() => handleClickActionMenu(key)}
                        disabled={disabledMenus.indexOf(BALANCE_ACTIONS_MAP[key]) > -1 || isClosedCase}
                    >
                        {t(`cap-core:balance_actions_${key.toLowerCase()}`)}
                    </AntMenu.Item>
                ))}
            </AntMenu>
        )
    }

    const hasPrivilegeForAction = (): string[] => {
        const balanceActionKeys = Object.keys(BALANCE_ACTIONS_MAP)

        return balanceActionKeys
            .filter(
                key =>
                    hasAddExternalOverpaymentPrivilege &&
                    BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.ADD_EXTERNAL_OVERPAYMENT
            )
            .concat(
                balanceActionKeys.filter(
                    key =>
                        hasCancelExternalOverpaymentPrivilege &&
                        BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.CANCEL_EXTERNAL_OVERPAYMENT
                )
            )
            .concat(
                balanceActionKeys.filter(
                    key =>
                        hasOverpaymentWaivePrivilege &&
                        BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.WAIVE_OVERPAYMENT
                )
            )
            .concat(
                balanceActionKeys.filter(
                    key =>
                        hasCancelOverpaymentWaivePrivilege &&
                        BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.CANCEL_WAIVE_OVERPAYMENT
                )
            )
            .concat(
                balanceActionKeys.filter(
                    key =>
                        hasPayUnderpaymentPrivilege && BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.PAY_UNDERPAYMENT
                )
            )
            .concat(
                balanceActionKeys.filter(
                    key => hasReducePaymentPrivilege && BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.REDUCE_PAYMENT
                )
            )
            .concat(
                balanceActionKeys.filter(
                    key => hasPostRecoveryPrivilege && BALANCE_ACTIONS_MAP[key] === BALANCE_ACTIONS_MAP.POST_RECOVERY
                )
            )
    }

    const renderActionDropDown = () => {
        return (
            <AntDropdown
                trigger={['click']}
                overlay={renderMenu()}
                className={CUSTOM_DROPDOWN}
                onVisibleChange={val => setIsDropdownVisible(val)}
            >
                <Button>
                    {t('cap-core:balance_actions_button_label')}
                    <ActionChevronUpMedium
                        className={classnames(CUSTOM_DROPDOWN_ACTION_ARROW, !isDropdownVisible && 'activated')}
                    />
                </Button>
            </AntDropdown>
        )
    }

    const changePayee = (paramPayeeLink: string) => {
        setPayeeLink(paramPayeeLink)
        balanceStore.loadBalance(paramPayeeLink)
    }

    const reloadBalanceData = (payee: string) => {
        const requestBody = {
            payee: {
                matches: [payee]
            },
            originSource: {
                matches: [balanceStore.balanceSource]
            }
        } as CapAdjusterFinancialSearchSearchCapBalanceChangeLogEntitySearchQuery
        if (payee === payeeLink) {
            balanceStore.getBalanceChangeLog(requestBody, balanceStore.balanceChangeLogCount + 1)
        }
    }

    const renderAreaUnderTitle = () => {
        return (
            <div
                className={isNotActivitiesPriv ? BALANCE_HEADER_LEFT_SECTION : BALANCE_HEADER_WITH_NO_ACTION_DROPDOWN}
                onClick={e => e.stopPropagation()}
            >
                <Select value={payeeLink} onChange={val => changePayee(val)}>
                    {payeeOptions.map(payee => (
                        <Option key={payee.code}>{payee.displayValue}</Option>
                    ))}
                </Select>
                {isNotActivitiesPriv && !isHiddenBtn && renderActionDropDown()}
            </div>
        )
    }

    const checkIfMoneyIsPositive = (value?: string): boolean => {
        return value ? value.includes('-') : false
    }

    return (
        <Collapse
            defaultActiveKey='balanceTable'
            isWhite
            accordion
            bordered={false}
            className={FINANCIAL_INFORMATION_BALANCE_TABLE}
        >
            <Panel
                key='balanceTable'
                header={
                    <div className={BALANCE_TABLE_TITLE}>
                        <div>{t('cap-core:balance_title')}</div>
                        {renderAreaUnderTitle()}
                    </div>
                }
                extra={isNotActivitiesPriv && renderAmountBox()}
            >
                {isActivitiesPriv && <BalanceActivities balanceStore={balanceStore} payeeLink={payeeLink} />}
                {isNotActivitiesPriv && (
                    <RecalculationPayments
                        balanceStore={balanceStore}
                        allAssociatedSettlements={paymentsStore.allAssociatedSettlements}
                    />
                )}
                <PaymentWithholdingTable
                    loading={loadingReducePayments}
                    eventCase={balanceStore.eventCaseStore.eventCase}
                    editWithholding={editWithholding}
                    onUpdateDeleteWithHolding={onUpdateDeleteWithHolding}
                    claims={filteredClaims}
                    payeeLink={payeeLink}
                    hasReducePaymentPrivilege={hasReducePaymentPrivilege}
                />
                {formDrawerStore.openedDrawerKey === ACTION_DRAWER_KEY && (
                    <BalanceActionDrawer
                        eventCase={eventCase}
                        payeeLink={payeeLink}
                        payeeDisplay={payeeDisplay}
                        actionKey={actionKey}
                        closeDrawer={formDrawerStore.closeDrawer}
                        store={props.store}
                        balanceStore={balanceStore}
                        paymentsStore={paymentsStore}
                        claims={filteredClaims}
                        currentWithholding={currentWithholding}
                        reducePaymentMode={reducePaymentMode}
                        parties={toJS(claimPartyStore.parties)}
                        beneficiaries={toJS(allClaimsBeneficiaries)}
                        onUpdateDeleteWithHolding={onUpdateDeleteWithHolding}
                        getBalanceChangeLog={getBalanceChangeLog}
                        getLoadPayments={getLoadPayments}
                    />
                )}
                {formDrawerStore.openedDrawerKey === POST_RECOVERY_DRAWER_FORM_ID && (
                    <PostRecoveryDrawer
                        parties={toJS(claimPartyStore.parties)}
                        subjectOfClaim={toJS(subjectOfClaim)}
                        beneficiaries={toJS(allClaimsBeneficiaries)}
                        closeDrawer={formDrawerStore.closeDrawer}
                        paymentsStore={paymentsStore}
                        eventCase={eventCaseStore.eventCase}
                        onSuccess={reloadBalanceData}
                        originSource={balanceStore.balanceSource}
                    />
                )}
            </Panel>
        </Collapse>
    )
})
