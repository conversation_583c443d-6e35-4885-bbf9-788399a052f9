/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {
    BuilderAppProps,
    ControlType,
    defaultControls,
    ElementDragType,
    ElementFormContextType,
    GlobalValidationConfig,
    withFormBlock
} from '@eisgroup/builder'
import {ApiServicesMap, BuilderIntegrationConfig} from '@eisgroup/builder-integration'
import {t} from '@eisgroup/i18n'
import {Moment} from 'moment'
import {FormApi} from '@eisgroup/form'
import {
    rulesModel_CapEventCase as eventCaseRuleModel,
    rulesModel_ClaimWrapper as ClaimWrapperRuleModel,
    rulesModel_PremiumWaiverSettlement as PremiumWaiverSettlementRuleModel
} from '@eisgroup/cap-event-case-models'
import {rulesModel_CapPaymentTemplate as CapPaymentTemplateRuleModel} from '@eisgroup/cap-financial-models'
import {
    rulesModel_CapLeave as CapLeaveRuleModel,
    rulesModel_CapLeaveSettlement as CapLeaveSettlementRuleModel,
    rulesModel_CapSmp as CapSmpRuleModel,
    rulesModel_CapStd as CapStdRuleModel
} from '@eisgroup/cap-disability-models'
import {Validation} from '../components/validation/Validation'
import {enUS} from '../i18n/cap-core-i18n.en'
import {enMT} from '../i18n/cap-core-i18n.en_MT'
import {enGB} from '../i18n/cap-core-i18n.en_GB'
import {zhTW} from '../i18n/cap-core-i18n.zh_TW'
import {koKR} from '../i18n/cap-core-i18n.ko_KR'
import {SelectInputWrapper} from '../components/select-input-wrapper/SelectInputWrapper'
import {RadioInputWrapper} from '../components/radio-input-wrapper/RadioInputWrapper'
import {RangePickerWrapper} from '../components/range-picker/RangerPickerWrapper'
import {RemoveButton} from '../components/remove-button/RemoveButton'
import {Divider} from '../components/divider'
import {isNotEmpty} from '../common/utils'
import {validateBirthDate} from '../common/PartyFormRules'
import {BuilderAlert} from './builder-alert/BuilderAlert'
import {BuilderInlineLabel} from './builder-inline-label/BuilderInlineLabel'

/* eslint-disable */
/* tslint:disable */
const builderConfig: BuilderIntegrationConfig = {
    elementGroups: [
        {
            name: 'cap-core components',
            elementConfigs: [
                {
                    component: BuilderAlert,
                    displayType: 'ALERT',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            message: {
                                type: ControlType.TranslationLabels
                            },
                            type: {
                                type: ControlType.Enum,
                                options: [
                                    {code: 'warning', displayValue: 'Warning'},
                                    {code: 'error', displayValue: 'Error'},
                                    {code: 'info', displayValue: 'Info'},
                                    {code: 'success', displayValue: 'Success'}
                                ]
                            }
                        }
                    },
                    engineConfig: {
                        withPositioned: true
                    }
                },
                {
                    component: BuilderInlineLabel,
                    displayType: 'INLINE_LABEL',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            label: {
                                type: ControlType.TranslationLabels
                            },
                            applyBold: {
                                title: 'Apply bold font weight',
                                type: ControlType.Boolean
                            }
                        }
                    },
                    engineConfig: {
                        withPositioned: true,
                        formContextType: ElementFormContextType.NONE
                    }
                },
                {
                    component: Validation,
                    displayType: 'CUSTOM_VALIDATION',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            name: {
                                type: ControlType.String
                            },
                            disablePrefixName: {
                                type: ControlType.Boolean,
                                title: 'Disable Prefix'
                            }
                        }
                    },
                    engineConfig: {
                        withPositioned: true
                    }
                },
                {
                    component: withFormBlock()(RangePickerWrapper),
                    displayType: 'CUSTOM_RANGEPICKER',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            ...defaultControls.formBlock
                        }
                    },
                    engineConfig: {
                        withPositioned: true,
                        formContextType: {
                            type: ElementFormContextType.FIELD
                        }
                    }
                },
                {
                    component: withFormBlock()(SelectInputWrapper),
                    displayType: 'CUSTOM_SELECT_INPUT',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            ...defaultControls.formBlock,
                            label: {
                                type: ControlType.String
                            },
                            placeholder: {
                                type: ControlType.String
                            }
                        }
                    },
                    engineConfig: {
                        withPositioned: true,
                        formContextType: {
                            type: ElementFormContextType.FIELD
                        }
                    }
                },
                {
                    component: withFormBlock()(RadioInputWrapper),
                    displayType: 'CUSTOM_RADIO_INPUT',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            ...defaultControls.formBlock,
                            label: {
                                type: ControlType.String
                            }
                        }
                    },
                    engineConfig: {
                        withPositioned: true,
                        formContextType: {
                            type: ElementFormContextType.FIELD
                        }
                    }
                },
                {
                    component: withFormBlock()(RemoveButton),
                    displayType: 'CUSTOM_REMOVE_BUTTON',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            ...defaultControls.formBlock
                        }
                    },
                    engineConfig: {
                        withPositioned: true,
                        formContextType: {
                            type: ElementFormContextType.FIELD
                        }
                    }
                },
                {
                    component: Divider,
                    displayType: 'FORM_DIVIDER',
                    builderConfig: {
                        elementGroupType: 'common',
                        dragType: ElementDragType.DRAGGABLE,
                        propertyControls: {
                            title: {
                                type: ControlType.String
                            }
                        }
                    },
                    engineConfig: {
                        withPositioned: true
                    }
                }
            ]
        }
    ]
}

const apiServices: ApiServicesMap = {
    saveTaxEntity: () => Promise.resolve(),
    paymentFrequencyChange: async (values, props) => undefined,
    onIsFixedAmountChange: (values: any, props: any, event: any) => Promise.resolve(),
    onDeductionLossSourcesChange: (values: any, props: any, event: any) => Promise.resolve(),
    onTaxesLossSourcesChange: (values: any, props: any, event: any) => Promise.resolve(),
    onAncillaryLossSourcesChange: (values: any, props: any, event: any) => Promise.resolve(),
    onSubmit: () => Promise.resolve(),
    onExpandChange: (values: any, props: any, event: any) => Promise.resolve(),
    descChange: (values: any, props: any, event: any) => Promise.resolve(),
    expenseChange: (values: any, props: any, event: any, prefix?: string) => Promise.resolve(),
    exGratiaChange: (values: any, props: any, event: any, prefix?: string) => Promise.resolve(),
    exGratiaDescriptionChange: (values: any, props: any, event: any, prefix?: string) => Promise.resolve(),
    searchCompany: (values: any, props: any, event: any, prefix?: string) => Promise.resolve(),
    serviceTypeChange: (values: any, props: any, event: any, prefix?: string) => Promise.resolve(),
    onOverpaymentChanged: (values: any, props: any, event: any, prefix: string, formApi: FormApi) => Promise.resolve()
}

export const validatorConfigs: GlobalValidationConfig[] = [
    {
        type: 'or',
        validator:
            ({firstField, secondField}) =>
            (): string | undefined => {
                if (firstField && secondField) {
                    return t('cap-core:or_validation_only_one_field')
                }
                if (firstField !== undefined || secondField !== undefined) {
                    return undefined
                }
                return t('cap-core:or_validation_at_least_one_field')
            },
        parameters: ['firstField', 'secondField']
    },
    {
        type: 'minmax',
        validator:
            ({minValue, maxValue}) =>
            (value: number): string | undefined => {
                if (value < minValue || value > maxValue) {
                    return t('cap-core:minmax_validation_deduction_amount', {min: minValue, max: maxValue})
                }
                return undefined
            },
        parameters: ['minValue', 'maxValue']
    },
    {
        type: 'date-range',
        validator: (period: Moment[]) => (): string | undefined => {
            if (!period[0] && !period[1]) {
                return t('cap-core:date_range_validation_required')
            }
            if (!period[0] || !period[1]) {
                return t('cap-core:date_range_validation_valid')
            }
            return undefined
        },
        singleParameter: true
    },
    {
        type: 'amount-valid',
        validator:
            (totalBalance: number) =>
            (value: number): string | undefined => {
                if (value <= 0) {
                    return t('cap-core:amount_has_to_be_greater_than_0')
                }
                if (value > totalBalance) {
                    return t('cap-core:amount_must_not_be_greater_than_total_balance')
                }
                return undefined
            },
        singleParameter: true
    },
    {
        type: 'value-for-underpayment-amount',
        validator:
            (totalBalance: number) =>
            (value: number): string | undefined => {
                if (totalBalance <= 0) {
                    return t('cap-core:no_underpayment')
                }
                if (value <= 0) {
                    return t('cap-core:amount_has_to_be_greater_than_0')
                }
                if (value > totalBalance) {
                    return t('cap-core:amount_must_not_be_greater_than_total_balance')
                }
                return undefined
            },
        singleParameter: true
    },
    {
        type: 'array-length',
        validator:
            ({fieldName}) =>
            (value: string[]): string | undefined => {
                if (!value.length) {
                    return t('cap-core:error_required_named', {nameKey: t(fieldName)})
                }
                return undefined
            },
        parameters: ['fieldName']
    },
    {
        type: 'mandatory_by_value',
        validator:
            ({fieldName, fieldValue}) =>
            (value: any): string | undefined => {
                if (fieldName === fieldValue) {
                    // email has it's own default mask passed as value
                    if (fieldName === 'Email') {
                        return value !== '_@_._'
                            ? undefined
                            : t('cap-core:error_required_named', {nameKey: t(fieldName)})
                    }
                    return isNotEmpty(value) ? undefined : t('cap-core:error_required_named', {nameKey: t(fieldName)})
                }
                return undefined
            },
        parameters: ['fieldName', 'fieldValue']
    },
    {
        type: 'dob_validate',
        validator: () => validateBirthDate,
        singleParameter: true
    },
    {
        type: 'amount-gt0',
        validator:
            () =>
            (value: number): string | undefined =>
                value <= 0 ? t('cap-core:amount_has_to_be_greater_than_0') : undefined
    },
    {
        type: 'gt-or-eq-0',
        validator:
            () =>
            (value: number): string | undefined =>
                value && value < 0 ? t('cap-core:amount_has_to_be_greater_than_or_equal_to_0') : undefined
    },
    {
        type: 'range-inclusive',
        validator:
            ({min, max}) =>
            (value: number): string | undefined => {
                if (value < min || value > max) {
                    return t('cap-core:range_min_max_inclusive', {min: min, max: max})
                }
                return undefined
            },
        parameters: ['min', 'max']
    }
]

const editorConfig: BuilderAppProps = {
    includeBaseComponents: true,
    localizationBundles: [enUS, enMT, enGB, zhTW, koKR],
    builderConfig,
    apiServices,
    validatorConfigs,
    validationRules: {
        rulesModels: [
            eventCaseRuleModel,
            CapPaymentTemplateRuleModel,
            CapSmpRuleModel,
            CapStdRuleModel,
            PremiumWaiverSettlementRuleModel,
            ClaimWrapperRuleModel,
            CapLeaveRuleModel,
            CapLeaveSettlementRuleModel
        ]
    }
}

export default editorConfig
