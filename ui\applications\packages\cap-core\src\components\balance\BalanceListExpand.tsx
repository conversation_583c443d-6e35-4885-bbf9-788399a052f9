/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'
import {CapBalance} from '@eisgroup/cap-financial-models'
import {BALANCE_TABLE_EXPAND_SECTION, LifeAndDisabilityScope, Settlements} from '../..'
import {BalanceStore} from '../../common/store/BalanceStore'
import {BalanceListLifeExpandRow} from './BalanceListLifeExpandRow'
import {BalanceListDisabilityExpandRow} from './BalanceListDisabilityExpandRow'

import CapBalanceItemEntity = CapBalance.CapBalanceItemEntity
import CapBalanceItemActualAllocationEntity = CapBalance.CapBalanceItemActualAllocationEntity

export interface BalanceListExpandProps {
    balanceStore: BalanceStore
    balanceItem: CapBalanceItemEntity
    allAssociatedSettlements: Settlements[]
}

export const BalanceListExpand: React.FC<BalanceListExpandProps> = observer(props => {
    return (
        <div className={BALANCE_TABLE_EXPAND_SECTION}>
            {props.balanceItem.actualAllocations &&
                props.balanceItem.actualAllocations.map((allocation: CapBalanceItemActualAllocationEntity) => {
                    if (LifeAndDisabilityScope.TL.includes(allocation.allocationLossInfo?.lossType as string)) {
                        return (
                            <BalanceListLifeExpandRow
                                balanceStore={props.balanceStore}
                                allocation={allocation}
                                allAssociatedSettlements={props.allAssociatedSettlements}
                            />
                        )
                    }
                    return <BalanceListDisabilityExpandRow allocation={allocation} />
                })}
        </div>
    )
})
