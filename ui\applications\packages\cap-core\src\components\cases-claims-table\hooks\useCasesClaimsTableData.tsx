/**
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CrmLocation, IndividualCustomer, loadLookupOptions} from '@eisgroup/cap-services'
import {noop} from '@eisgroup/common'
import {t, useIoCLocale} from '@eisgroup/i18n'
import {ColumnProps, TableProps} from '@eisgroup/ui-kit'
import isEqual from 'lodash/isEqual'
import {useCallback, useMemo, useState} from 'react'
import {DENTAL_CLAIM_TYPE_OPTIONS, P_AND_C_CLAIM_TYPE_OPTIONS} from '../../../common/constants'
import {LookupValue} from '../../../common/Types'
import {useQuery} from '../../../common/utils'
import {CasesClaimsTableProps} from '../CasesClaimsTable'
import {createColumns} from '../columns'

export const getSortingParams = (sorter): any[] => {
    const sortingParams = [] as any[]
    let sorterOrder = ''
    if (sorter?.order === 'ascend') {
        sorterOrder = 'asc'
    } else if (sorter?.order === 'descend') {
        sorterOrder = 'desc'
    }
    switch (sorter?.columnKey) {
        case 'caseClaimNumber':
            sortingParams.push({
                caseNumber: sorterOrder,
                claimNumber: sorterOrder
            })
            break
        case 'state':
            sortingParams.push({
                state: sorterOrder
            })
            break
        case 'claimDate':
            sortingParams.push({
                claimDate: sorterOrder
            })
            break
        default:
            break
    }
    return sortingParams
}

export const formatDataFilter = (filters: {[key: string]: any}) => {
    let params = {}
    let searchParams = {}
    const needFormatParams = [
        'claimPolicyNumber',
        'claimCustomerNumber',
        'claimFirstName',
        'claimLastName',
        'claimStateProvinceCd',
        'claimCity',
        'claimPostalCode',
        'claimTaxId',
        'claimLegalName'
    ]
    Object.keys(filters).forEach((key: string) => {
        if (needFormatParams.includes(key)) {
            const cleanedKey = key.split('claim').join('')
            const newKey = cleanedKey.replace(/^./, c => c.toLowerCase())
            searchParams = {
                ...searchParams,
                [newKey]: {matches: [filters[key]]}
            }
        } else {
            switch (key) {
                case 'content':
                    params = {singleLineQuery: filters.content}
                    break
                case 'claimDate':
                    searchParams = {
                        ...searchParams,
                        claimDate: {
                            from: `${filters.claimDate}T00:00:00.000Z`,
                            to: `${filters.claimDate}T23:59:59.999Z`
                        }
                    }
                    break
                case 'claimType':
                    searchParams = {
                        ...searchParams,
                        [key]: {matches: filters[key].indexOf(',') > -1 ? filters[key].split(',') : [filters[key]]}
                    }
                    break
                default:
                    searchParams = {
                        ...searchParams,
                        [key]: {matches: [filters[key]]}
                    }
                    break
            }
        }
    })
    return {...params, query: searchParams}
}

type UseCasesClaimsTableDataProps = CasesClaimsTableProps
export declare type OptionItem = Partial<any>
export const useCasesClaimsTableData = ({
    dataFilter = {},
    onChange = noop,
    setReloadingItems = noop,
    isLoading,
    casesClaimsSearchService,
    emptyText,
    onFiltersChange
}: UseCasesClaimsTableDataProps): {
    columns: ColumnProps<any>[]
    dataSource: any[]
    onTableChange: TableProps<any>['onChange']
    locale: any
    loading: boolean
    pagination: {
        current: number
        total: number
        pageSize: number
    }
    onPaginationChange: (pageNum: number, pageSize?: number) => void
} => {
    const [caseClaimList, setCaseClaimList] = useState<any>([])
    const [claimTypeList, setClaimTypeList] = useState<{text: string; value: string}[]>([])
    const locale = useIoCLocale()
    const [tableFilters, setTableFilters] = useState({})
    const [tableSorter, setTableSorter] = useState(null)
    const columns = useMemo(() => {
        return createColumns(claimTypeList, caseClaimList, {...dataFilter, ...tableFilters})
    }, [claimTypeList, caseClaimList, locale, dataFilter, tableFilters])
    const [pagination, setPagination] = useState({
        current: 1,
        total: 0,
        pageSize: 10
    })
    const tableLocale = useMemo(
        () => ({
            emptyText,
            filterConfirm: t('cap-core:apply'),
            filterReset: t('cap-core:clear')
        }),
        [emptyText, locale]
    )

    const loadCasesClaims = useCallback(async ({pageNum, pageSize, sorting}) => {
        setReloadingItems(true)

        const offset = (pageNum - 1) * pageSize
        const limit = pageSize
        const params = {
            ...formatDataFilter(dataFilter),
            offset,
            limit,
            sorting
        }
        const casesClaimsInfo = await casesClaimsSearchService.searchCasesClaims(params)
        const {items = [], count = 0} = casesClaimsInfo.get()
        const subjectOfClaimLinks = [
            ...new Set(
                items
                    .filter(claim => claim.subjects?.[0] && claim.claimModelName !== 'EXTERNAL_CLAIM')
                    .map(claim => claim?.subjects[0]?.subject)
            )
        ]
        const subjectOfClaimsInfo = await loadSubjectOfClaimsInfo(subjectOfClaimLinks)
        const newCaseClaimList = [] as any[]
        items.forEach(item => {
            newCaseClaimList.push({
                ...item,
                subjectOfClaimInfo: subjectOfClaimsInfo[item.subjects?.[0]?.subject]
            })
        })
        setCaseClaimList(newCaseClaimList)
        setReloadingItems(false)
        return {items: newCaseClaimList, count}
    }, [])

    const loadSubjectOfClaimsInfo = useCallback(async subjectOfClaimLinks => {
        if (subjectOfClaimLinks?.length === 0) {
            return {}
        }
        const subjectOfClaims = (await casesClaimsSearchService.searchCustomerByLinks(
            subjectOfClaimLinks
        )) as IndividualCustomer[]
        const subjectOfClaimsMap = {}
        subjectOfClaims.forEach(item => {
            const registryTypeId = item?.details?.person?.registryTypeId
            if (registryTypeId) {
                const addressInfo = item.communicationInfo.addresses?.[0]?.location || ({} as CrmLocation)
                subjectOfClaimsMap[registryTypeId] = {
                    customerName:
                        item && item._modelName === 'INDIVIDUALCUSTOMER'
                            ? [item.details.person.firstName, item.details.person.lastName].join(' ')
                            : '',
                    address: [addressInfo?.addressLine1, addressInfo?.city, addressInfo?.stateProvinceCd].join(' '),
                    postalCode: addressInfo?.postalCode
                }
            }
        })
        return subjectOfClaimsMap
    }, [])

    const loadClaimType = useCallback(async () => {
        let result = [] as LookupValue[]
        await loadLookupOptions('ClaimTypes')
            .toPromise()
            .then(either =>
                either.fold(
                    err => console.error(err),
                    values => {
                        result = [...result, ...values] as LookupValue[]
                    }
                )
            )

        await loadLookupOptions('DisabilityClaimType')
            .toPromise()
            .then(either =>
                either.fold(
                    err => console.error(err),
                    values => {
                        result = [...result, ...values] as LookupValue[]
                    }
                )
            )
        return [
            ...result,
            ...[...P_AND_C_CLAIM_TYPE_OPTIONS, ...DENTAL_CLAIM_TYPE_OPTIONS].map(item => ({
                displayValue: t(`cap-core:${item.displayValue}`),
                code: item.code
            }))
        ]
    }, [])

    const initTable = useCallback(async () => {
        const {items, count} = await loadCasesClaims({
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            sorting: null
        })
        setCaseClaimList(items)

        const mergedClaimTypes = await loadClaimType()
        setClaimTypeList([...mergedClaimTypes.map(v => ({text: v.displayValue, value: v.code}))])

        onChange({
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            count
        })

        setPagination({
            ...pagination,
            total: count
        })
    }, [])

    const onTableChange = useCallback(
        async (tablePagination, filters, sorter) => {
            const sorting = getSortingParams(sorter)
            const hasFiltersChanged = Object.keys(filters).some(
                key => !isEqual(Array.isArray(filters[key]) ? filters[key].join(',') : filters[key], dataFilter[key])
            )
            if (hasFiltersChanged && onFiltersChange) {
                onFiltersChange(filters)
                return
            }

            const data = (await loadCasesClaims({
                pageNum: pagination.current,
                pageSize: pagination.pageSize,
                sorting
            })) as any

            setTableFilters(filters)
            setTableSorter(sorter)
            onChange({
                pageNum: pagination.current,
                pageSize: pagination.pageSize,
                count: data.count
            })
            setPagination({
                ...pagination,
                total: data.count
            })
        },
        [pagination, onChange, loadCasesClaims, dataFilter, onFiltersChange]
    )

    const onPaginationChange = async (pageNum: number, pageSize?: number) => {
        const sorting = getSortingParams(tableSorter)

        const data = await loadCasesClaims({
            pageNum,
            pageSize,
            sorting
        })

        onChange({
            pageNum,
            pageSize: pageSize || pagination.pageSize,
            count: data.count
        })

        setPagination({
            pageSize: pageSize || pagination.pageSize,
            current: pageNum,
            total: data.count
        })
    }

    const {loading: isTableInitDataLoading} = useQuery(initTable)
    return {
        columns,
        dataSource: caseClaimList,
        onTableChange,
        locale: tableLocale,
        loading: isLoading || isTableInitDataLoading,
        pagination,
        onPaginationChange
    }
}
