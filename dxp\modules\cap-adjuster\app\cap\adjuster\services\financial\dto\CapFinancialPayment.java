/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial.dto;

import java.time.ZonedDateTime;
import java.util.List;

import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.MoneyDTO;
import com.fasterxml.jackson.annotation.JsonFormat;

import cap.adjuster.services.common.dto.GenesisRootApiModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import core.utils.JsonUtils;

public class CapFinancialPayment extends GenesisRootApiModel {

    public String paymentNumber;
    public MoneyDTO paymentNetAmount;
    public String state;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = JsonUtils.DATE_ONLY_FORMAT)
    public ZonedDateTime paymentDate;
    public String payee;
    public Object paymentDetails;
    public Object withholdingDetails;
    @JsonProperty("_variation")
    public String variation;
    public List<CapPaymentMessage> messages;
}
