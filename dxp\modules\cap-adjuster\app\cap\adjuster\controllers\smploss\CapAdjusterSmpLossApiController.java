/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.controllers.smploss;

import static core.swagger.SwaggerConstants.RESPONSE_TYPE_LIST;

import java.util.concurrent.CompletionStage;

import javax.inject.Inject;

import cap.adjuster.services.smploss.CapAdjusterSmpLossService;
import cap.adjuster.services.smploss.dto.CapSmpSettlement;
import cap.adjuster.services.financial.dto.CapPayment;
import core.controllers.ApiController;
import core.exceptions.common.HttpStatusCode;
import core.exceptions.common.HttpStatusDescription;
import genesis.core.exceptions.dto.GenesisCommonExceptionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.SwaggerDefinition;
import io.swagger.annotations.Tag;
import play.mvc.Result;

@SwaggerDefinition(
        consumes = {"application/json"},
        produces = {"application/json"},
        schemes = {SwaggerDefinition.Scheme.HTTP, SwaggerDefinition.Scheme.HTTPS},
        tags = {@Tag(name = CapAdjusterSmpLossApiController.TAG_API_CAP_ADJUSTER_SMP_LOSS,
                description = "CAP Adjuster: SMP Loss API")})
@Api(value = CapAdjusterSmpLossApiController.TAG_API_CAP_ADJUSTER_SMP_LOSS,
        tags = CapAdjusterSmpLossApiController.TAG_API_CAP_ADJUSTER_SMP_LOSS)
public class CapAdjusterSmpLossApiController extends ApiController {

    protected static final String TAG_API_CAP_ADJUSTER_SMP_LOSS = "/cap-adjuster/v1/losses-smp";

    private CapAdjusterSmpLossService smpLossService;

    /**
     * Get payments for smp loss
     *
     * @param rootId smp loss identifier
     * @param revisionNo smp loss revision number
     * @return list of payments related to smp loss
     */
    @ApiOperation(value = "Get payments for smp loss",
            response = CapPayment.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getPayments(@ApiParam(value = "Smp loss identifier", required = true) String rootId,
                                              @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(smpLossService.getPayments(rootId, revisionNo));
    }

    /**
     * Get settlements for smp loss
     *
     * @param rootId smp loss identifier
     * @param revisionNo smp loss revision number
     * @return list of settlements related to smp loss
     */
    @ApiOperation(value = "Get settlements for smp loss",
            response = CapSmpSettlement.class,
            responseContainer = RESPONSE_TYPE_LIST)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatusCode.BAD_REQUEST,
                    message = HttpStatusDescription.BAD_REQUEST,
                    response = GenesisCommonExceptionDTO.class),
            @ApiResponse(code = HttpStatusCode.UNPROCESSABLE_ENTITY,
                    message = HttpStatusDescription.UNPROCESSABLE_ENTITY,
                    response = GenesisCommonExceptionDTO.class)})
    public CompletionStage<Result> getSettlements(@ApiParam(value = "Smp loss identifier", required = true) String rootId,
                                               @ApiParam(value = "Revision number", required = true) Integer revisionNo) {
        return completeOk(smpLossService.getSettlements(rootId, revisionNo));
    }

    @Inject
    public void setSmpLossService(CapAdjusterSmpLossService smpLossService) {
        this.smpLossService = smpLossService;
    }
}