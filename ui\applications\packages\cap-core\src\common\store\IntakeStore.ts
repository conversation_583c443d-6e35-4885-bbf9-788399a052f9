/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapEventCase} from '@eisgroup/cap-event-case-models'
import {LossParams} from '@eisgroup/cap-services'
import {StepStatus} from '@eisgroup/react-components'
import {DefaultQuery, EventCaseIntakeWizardStepKey} from '../Types'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import {BaseRootStore} from './BaseRootStore'
import {ClaimPartyStore} from './ClaimPartyStore'
import {FormDrawerStore} from '../../components/form-drawer'
import {EmploymentStore} from './EmploymentStore'
import {EventCaseStore} from './EventCaseStore'

export const CLAIM_SUBJECT_FORM_DRAWER_KEY = 'claimSubjectFormDrawer'

export interface IntakeStore extends BaseRootStore {
    claimPartyStore: ClaimPartyStore
    formDrawerStore: FormDrawerStore
    employmentStore: EmploymentStore
    eventCaseStore: EventCaseStore
    /**
     * EventCase entity
     */
    eventCase: CapEventCaseEntity
    /**
     * The key of active intake wizard step.
     */
    activeStep: EventCaseIntakeWizardStepKey
    /**
     * Wizard steps validation statuses.
     */
    stepsStatuses: Record<string, StepStatus>
    loadEventCaseForEnterIntakePage: (lossParams: LossParams) => any
    /**
     * Action to change active wizard step.
     */
    changeStep: (key: EventCaseIntakeWizardStepKey) => void
    /**
     * Action to set employee step data to store
     */
    setEmployeeStepData: (values: any) => void
    /**
     * Updates status of active wizard step
     */
    updateActiveStepStatus: (stepStatus: StepStatus) => void
    /**
     * If prior earnings should be displayed quarterly
     */
    isQuarterlyPriorEarnings: ConstrainBoolean
    urlParams?: DefaultQuery
    setUrlParams: (urlParams?: DefaultQuery) => void
    eventCaseAfterInvokeDraft?: CapEventCaseEntity
}
