/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial.converters;

import cap.adjuster.services.financial.dto.CapPaymentMessage;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.PaymentDefinition_CapPaymentMessageEntityDTO;
import org.apache.commons.lang3.StringUtils;

import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.PaymentDefinition_CapPaymentEntityDTO;

import cap.adjuster.services.common.dto.GenesisApiModelKey;
import cap.adjuster.services.financial.dto.CapFinancialPayment;
import core.services.converters.CommonDTOConverter;

import javax.inject.Inject;

public class CapAdjusterActualPaymentConverter<I extends PaymentDefinition_CapPaymentEntityDTO, A extends CapFinancialPayment>
    extends CommonDTOConverter<I, A> {

    private CapPaymentMessageConverter<PaymentDefinition_CapPaymentMessageEntityDTO, CapPaymentMessage> paymentMessageConverter;

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);

        apiDTO.key = new GenesisApiModelKey();
        apiDTO.key.id = intDTO._key.rootId.toString();
        apiDTO.key.revisionNo = Math.toIntExact(intDTO._key.revisionNo);
        apiDTO.gentityType = intDTO._type;
        apiDTO.modelName = intDTO._modelName;
        apiDTO.modelVersion = intDTO._modelVersion;
        apiDTO.timestamp = intDTO._timestamp;

        apiDTO.paymentNetAmount = intDTO.paymentNetAmount;
        apiDTO.paymentDate = StringUtils.equals(intDTO._variation, "underpayment") || StringUtils.equals(intDTO._variation, "recovery") ? intDTO.creationDate : intDTO.paymentDetails.paymentDate;
        apiDTO.payee = intDTO.paymentDetails.payeeDetails != null ? intDTO.paymentDetails.payeeDetails.payee._uri : intDTO.paymentDetails.payeeRoleDetails.registryId;
        apiDTO.paymentDetails = intDTO.paymentDetails;
        apiDTO.state = intDTO.state;
        apiDTO.paymentNumber = intDTO.paymentNumber;
        apiDTO.withholdingDetails = intDTO.withholdingDetails;
        apiDTO.variation = intDTO._variation;
        apiDTO.messages = paymentMessageConverter.convertToApiDTOs(intDTO.messages);
        return apiDTO;
    }

    @Inject
    @SuppressWarnings("unchecked")
    public void setPaymentMessageConverter(CapPaymentMessageConverter paymentMessageConverter) {
        this.paymentMessageConverter = paymentMessageConverter;
    }
}
