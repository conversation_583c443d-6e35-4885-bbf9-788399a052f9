{"swagger": "2.0", "info": {"description": "API for CapDentalSettlement", "version": "1", "title": "CapDentalSettlement model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/capsettlement/CapDentalSettlement/v1/entities/{rootId}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/entities/{businessKey}/{revisionNo}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "revisionNo", "in": "path", "description": "", "required": true, "type": "integer"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/entities/{rootId}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "onDate", "in": "query", "description": "", "required": false, "type": "string", "format": "date-time"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/entities/{businessKey}": {"get": {"description": "Returns factory entity root record for a given path parameters.", "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "onDate", "in": "query", "description": "", "required": false, "type": "string", "format": "date-time"}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/link/": {"post": {"description": "Returns all factory model root entity records for given links.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/EntityLinkRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/model/": {"get": {"description": "Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)", "produces": ["application/json"], "parameters": [{"name": "modelType", "in": "query", "description": "", "required": false, "type": "string"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/history/{businessKey}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "businessKey", "in": "path", "description": "", "required": true, "type": "string"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapDentalSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/history/{rootId}": {"post": {"description": "Returns all factory entity root records for a given path parameters.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "rootId", "in": "path", "description": "", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/LoadEntityRootRequestBody"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapDentalSettlementLoadHistoryResultSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/transformation/adjudicationInput": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapEndpointRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/transformation/getDentalFinancialData": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CapEndpointRequestBody"}}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ObjectSuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/command/readjudicateSettlement": {"post": {"description": "Executes command for a given path parameters and request body data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDentalSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}, "patch": {"description": "Executes command for a given path parameters and partial-request data.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDentalSettlementReadjudicateInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/command/adjudicateSettlement": {"post": {"description": "The command that adjudicates settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/command/approveSettlement": {"post": {"description": "The command that approves adjudicated settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/command/closeSettlement": {"post": {"description": "The command that closes settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/command/disapproveSettlement": {"post": {"description": "The command that disapprove settlement", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/IdentifierRequestBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/capsettlement/CapDentalSettlement/v1/command/initSettlement": {"post": {"description": "The command that initiate creation of new settlement image with unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/CapDentalSettlementInitInputBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapDentalSettlement_CapDentalPolicyInfoCoinsuranceEntity": {"required": ["_type"], "properties": {"coinsuranceOONPct": {"type": "number", "description": "Coinsurance percentage outside network"}, "coinsuranceServiceType": {"type": "string", "description": "Coinsurance Service Type"}, "coinsuranceINPct": {"type": "number", "description": "Coinsurance percentage in network"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoCoinsuranceEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoCoinsuranceEntity"}, "CapDentalSettlement_DNMaximumRollover": {"required": ["_type"], "properties": {"maximumRolloverThresholdAmount": {"$ref": "#/definitions/Money"}, "accumulatedRolloverMaximumAmount": {"$ref": "#/definitions/Money"}, "maximumRolloverAmount": {"$ref": "#/definitions/Money"}, "maximumBonusRolloverAmount": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "DNMaximumRollover"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximumRollover"}, "CapDentalSettlement_CapDentalProviderFeesEntity": {"required": ["_type"], "properties": {"fee": {"type": "string", "description": "Fee"}, "type": {"type": "string", "description": "Type"}, "_type": {"type": "string", "example": "CapDentalProviderFeesEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalProviderFeesEntity"}, "CapDentalSettlement_CapDentalBaseClaimDataEntity": {"required": ["_type"], "properties": {"providerFees": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProviderFeesEntity"}}, "digitalImageNumbers": {"type": "array", "items": {"type": "string", "description": "Digital Image Numbers"}}, "payeeType": {"type": "string", "description": "Payee Type"}, "cleanClaimDate": {"type": "string", "format": "date", "description": "Clean Claim Date"}, "dateOfBirth": {"type": "string", "format": "date", "description": "Deprected this should come from customer"}, "remark": {"type": "string", "description": "Remarks"}, "source": {"type": "string", "description": "Source"}, "providerDiscount": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProviderDiscountEntity"}, "missingTooths": {"type": "array", "items": {"type": "string", "description": "Missing <PERSON><PERSON>"}}, "transactionType": {"type": "string", "description": "Type of Claim"}, "placeOfTreatment": {"type": "string", "description": "Place of Treatment"}, "cob": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalClaimCoordinationOfBenefitsEntity"}}, "receivedDate": {"type": "string", "format": "date", "description": "Received Date"}, "policyholder": {"$ref": "#/definitions/EntityLink"}, "provider": {"$ref": "#/definitions/EntityLink"}, "patient": {"$ref": "#/definitions/EntityLink"}, "alternatePayee": {"$ref": "#/definitions/EntityLink"}, "_type": {"type": "string", "example": "CapDentalBaseClaimDataEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalBaseClaimDataEntity"}, "CapDentalSettlement_DNBenefitPlan": {"required": ["_type"], "properties": {"roundingAmount": {"type": "integer", "format": "int64"}, "planCd": {"type": "string"}, "roundingFactorCd": {"type": "string"}, "planName": {"type": "string"}, "covDef": {"$ref": "#/definitions/CapDentalSettlement_DNCoverageDefinition"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNBenefitPlan"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNBenefitPlan"}, "CapDentalSettlement_CapDentalClaimInfoEntity": {"required": ["_type"], "properties": {"submittedProcedures": {"type": "array", "items": {"description": "Submited procedured from Intake", "$ref": "#/definitions/CapDentalSettlement_CapDentalProcedureEntity"}}, "patient": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPatientEntity"}, "claimData": {"$ref": "#/definitions/CapDentalSettlement_CapDentalBaseClaimDataEntity"}, "source": {"type": "string"}, "receivedDate": {"type": "string", "format": "date"}, "_type": {"type": "string", "example": "CapDentalClaimInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalClaimInfoEntity", "description": "Business entity that houses the information from loss to use in settlement."}, "CapDentalSettlement_CapDentalAccumulatorEntity": {"required": ["_type"], "properties": {"accumulatorType": {"type": "string", "description": "Type for the Maximum"}, "reservedAmount": {"description": "Reserved Amount of Maximum", "$ref": "#/definitions/Money"}, "remainingAmount": {"description": "Remaining maximum in network amount", "$ref": "#/definitions/Money"}, "renewalType": {"type": "string", "description": "Renewal type for maximum"}, "appliesToProcedureCategory": {"type": "string", "description": "Defines to which procedure category maximum applies to"}, "networkType": {"type": "string", "description": "Remaining  maximum ouf of network amount"}, "_type": {"type": "string", "example": "CapDentalAccumulatorEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalAccumulatorEntity"}, "CapDentalSettlement_Period": {"required": ["_type"], "properties": {"endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Period"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "Period"}, "CapDentalSettlement_CapDentalProcedureCoordinationOfBenefitsEntity": {"required": ["_type"], "properties": {"coverageType": {"type": "string", "description": "Type of Coverage"}, "primaryCoverageStatus": {"type": "string", "description": "Primary Coverage Status"}, "allowed": {"description": "Other Carrier Allowed", "$ref": "#/definitions/Money"}, "considered": {"description": "Other Carrier Considered", "$ref": "#/definitions/Money"}, "paid": {"description": "Other Carrier Paid", "$ref": "#/definitions/Money"}, "innOnn": {"type": "string", "description": "Paid INN/Paid OON"}, "_type": {"type": "string", "example": "CapDentalProcedureCoordinationOfBenefitsEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalProcedureCoordinationOfBenefitsEntity"}, "EntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "parentId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "LoadSingleEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadSingleEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadSingleEntityRootRequestBody"}, "IdentifierRequest": {"required": ["_key"], "properties": {"_key": {"$ref": "#/definitions/RootEntityKey"}, "_version": {"type": "string"}}, "title": "IdentifierRequest"}, "CapDentalSettlement_DNLocationParty": {"required": ["_type"], "properties": {"addressTypeCd": {"type": "string"}, "nationalId": {"type": "string"}, "stateProvinceCd": {"type": "string"}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "countryCd": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "registryTypeId": {"type": "string"}, "addressLine3": {"type": "string"}, "makePreferredInd": {"type": "boolean"}, "registryEntityNumber": {"type": "string"}, "_type": {"type": "string", "example": "DNLocationParty"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNLocationParty"}, "CapDentalSettlement_DNDependentEligibility": {"required": ["_type"], "properties": {"childMaxAgeCd": {"type": "string"}, "fullTimeStudentAgeCd": {"type": "string"}, "includeDisabledDependentsInd": {"type": "boolean"}, "_type": {"type": "string", "example": "DNDependentEligibility"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDependentEligibility"}, "CapAccidentalDismembermentSettlementAdjudicationInput": {"properties": {"rootId": {"type": "string"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "CapAccidentalDismembermentSettlementAdjudicationInput"}, "CapDentalSettlementLoadHistoryResult": {"properties": {"result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntity"}}, "count": {"type": "integer", "format": "int64"}}, "title": "CapDentalSettlementLoadHistoryResult"}, "CapDentalSettlement_DNFundingStructure": {"required": ["_type"], "properties": {"participantContributionPct": {"type": "number"}, "contributionBasisCd": {"type": "string"}, "contributionTypeCd": {"type": "string"}, "sponsorContributionAmount": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "DNFundingStructure"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNFundingStructure"}, "CapDentalSettlement_CapDentalPreauthorizationEntity": {"required": ["_type"], "properties": {"authorizedBy": {"type": "string", "description": "Name of the person who authorized the procedure"}, "authorizationPeriod": {"description": "Authorization Period", "$ref": "#/definitions/CapDentalSettlement_Period"}, "isProcedureAuthorized": {"type": "boolean", "description": "Is procedure authorized?"}, "_type": {"type": "string", "example": "CapDentalPreauthorizationEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPreauthorizationEntity"}, "RootEntityKey": {"properties": {"rootId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}}, "title": "RootEntityKey"}, "CapDentalSettlement_CapDentalFeeRateEntity": {"required": ["_type"], "properties": {"feeAmount": {"$ref": "#/definitions/Money"}, "feeCode": {"type": "string"}, "_type": {"type": "string", "example": "CapDentalFeeRateEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalFeeRateEntity"}, "CapDentalSettlement_DNDentalMaximumAmount": {"required": ["_type"], "properties": {"dentalMaximums": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNMaximum"}}, "isMaximumCredit": {"type": "boolean"}, "maximumAmountNumberOfGradedYears": {"type": "integer", "format": "int64"}, "maximumServicesInNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNMaximumServicesInNetwork"}}, "maximumServicesOutOfNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNMaximumServicesOutOfNetwork"}}, "isGradedMaximum": {"type": "boolean"}, "isMaximumRollover": {"type": "boolean"}, "maximumAccumulationPeriod": {"type": "string"}, "maxRolloverDetails": {"$ref": "#/definitions/CapDentalSettlement_DNMaximumRollover"}, "_type": {"type": "string", "example": "DNDentalMaximumAmount"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDentalMaximumAmount"}, "CapDentalSettlement_DNDeductible": {"required": ["_type"], "properties": {"familyDeductibleInNetwork": {"type": "string"}, "deductibleType": {"type": "string"}, "deductibleYear": {"type": "integer", "format": "int64"}, "deductibleInNetworkAmount": {"$ref": "#/definitions/Money"}, "familyDeductibleOutOfNetwork": {"type": "string"}, "deductibleOutOfNetworkAmount": {"$ref": "#/definitions/Money"}, "_type": {"type": "string", "example": "DNDeductible"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductible"}, "ObjectSuccess": {"properties": {"response": {"type": "string"}, "success": {"type": "object"}}, "title": "ObjectSuccess"}, "CapDentalSettlement_CapDentalPreprocessMessageEntity": {"required": ["_type"], "properties": {"preprocessCode": {"type": "string", "description": "Preprocess Message Code"}, "preprocessMessage": {"type": "string", "description": "Preprocess Message"}, "preprocessSeverity": {"type": "string", "description": "Preprocess Message Severity"}, "_type": {"type": "string", "example": "CapDentalPreprocessMessageEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPreprocessMessageEntity"}, "CapDentalSettlement_CapDentalUnverifiedInfoEntity": {"required": ["_type"], "properties": {"employerName": {"type": "string", "description": "Claim Without Policy Employer Name"}, "groupNumber": {"type": "string", "description": "Claim Without Policy Group Number"}, "_type": {"type": "string", "example": "CapDentalUnverifiedInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalUnverifiedInfoEntity"}, "CapDentalSettlement_CapDentalDentistEntity": {"required": ["_type"], "properties": {"dentistID": {"type": "string"}, "inOutNetwork": {"type": "string"}, "pcdID": {"type": "string"}, "providerTIN": {"type": "string"}, "feeScheduleType": {"type": "string"}, "dentistSpecialties": {"type": "array", "items": {"type": "string"}}, "practiceType": {"type": "string"}, "practiceTerm": {"$ref": "#/definitions/CapDentalSettlement_Term"}, "_type": {"type": "string", "example": "CapDentalDentistEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalDentistEntity"}, "EndpointFailureFailure": {"properties": {"response": {"type": "string"}, "failure": {"$ref": "#/definitions/EndpointFailure"}}, "title": "EndpointFailureFailure"}, "CapDentalSettlement_CapDentalBypassClaimLogicEntity": {"required": ["_type"], "properties": {"bypassOverpaymentLogic": {"type": "boolean", "description": "Bypass Overpayment Recoupment Logic"}, "bypassCobLogic": {"type": "boolean", "description": "Bypass COB Logic"}, "bypassDuplicateServiceLogic": {"type": "boolean", "description": "Bypass Duplicate Service Logic"}, "bypassToothExtractionRules": {"type": "boolean", "description": "Bypass Tooth Extraction Rules"}, "bypassGracePeriodLogic": {"type": "boolean", "description": "Bypass Grace Period Logic"}, "bypassClinicalReview": {"type": "boolean", "description": "Bypass Clinical/Consultant Review"}, "bypassMissingToothLogic": {"type": "boolean", "description": "Bypass Missing Tooth Exclusion Logic"}, "bypassInterestLogic": {"type": "boolean", "description": "Bypass Interest Logic"}, "bypassAllRules": {"type": "boolean", "description": "Bypass All Rules and Dental Review Logic"}, "_type": {"type": "string", "example": "CapDentalBypassClaimLogicEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalBypassClaimLogicEntity"}, "CapDentalSettlement_CapDentalTreatmentReasonEntity": {"required": ["_type"], "properties": {"treatmentResultingFrom": {"type": "string", "description": "Treatment Resulting From"}, "autoAccidentState": {"type": "string", "description": "Auto Accident State"}, "dateOfAccident": {"type": "string", "format": "date", "description": "Date of Accident"}, "_type": {"type": "string", "example": "CapDentalTreatmentReasonEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalTreatmentReasonEntity"}, "CapDentalSettlement_CapDentalWaiveServiceValueEntity": {"required": ["_type"], "properties": {"waiveServiceFrequencyLimit": {"type": "boolean", "description": "Waive Service Frequency Limit"}, "waiveDeductible": {"type": "boolean", "description": "Waive Deductible"}, "waiveMaximumLimitAmounts": {"type": "boolean", "description": "Waive Maximum Amount Limits"}, "waiveReplacementLimit": {"type": "boolean", "description": "Waive Replacement Limit"}, "waiveEligibilityPriorStartDate": {"type": "boolean", "description": "Waive Eligibility Prior to Coverage Start Date"}, "waiveServiceWaitingPeriod": {"type": "boolean", "description": "Waive Service Category Waiting Period"}, "waiveEligibilityAfterStartDate": {"type": "boolean", "description": "Waive Eligibility After Coverage Start Date"}, "waiveLateEntrantWaitingPeriod": {"type": "boolean", "description": "Waive Late Entrant Waiting Period"}, "_type": {"type": "string", "example": "CapDentalWaiveServiceValueEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalWaiveServiceValueEntity"}, "CapDentalSettlement_DNDeductibleDefinition": {"required": ["_type"], "properties": {"deductibleAccumulationPeriod": {"type": "string"}, "deductibleServicesOutOfNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNDeductibleServicesOutNetwork"}}, "isGradedDeductible": {"type": "boolean"}, "deductibles": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNDeductible"}}, "deductibleCredit": {"type": "boolean"}, "deductibleNumberOfGradedYears": {"type": "integer", "format": "int64"}, "deductibleCarryover": {"type": "boolean"}, "deductibleServicesInNetwork": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNDeductibleServicesInNetwork"}}, "_type": {"type": "string", "example": "DNDeductibleDefinition"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductibleDefinition"}, "CapDentalSettlement_DNPhoneInfo": {"required": ["_type"], "properties": {"phoneExtension": {"type": "string"}, "countryCd": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "value": {"type": "string"}, "preferred": {"type": "boolean"}, "_type": {"type": "string", "example": "DNPhoneInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNPhoneInfo"}, "CapDentalSettlementInitInput": {"properties": {"settlementType": {"type": "string"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "entity": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementDetailEntity"}}, "title": "CapDentalSettlementInitInput"}, "CapDentalSettlementLoadHistoryResultSuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapDentalSettlementLoadHistoryResult"}}, "title": "CapDentalSettlementLoadHistoryResultSuccess"}, "CapDentalSettlementLoadHistoryResultSuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapDentalSettlementLoadHistoryResultSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapDentalSettlementLoadHistoryResultSuccessBody"}, "CapDentalSettlement_CapDentalProcedureEntity": {"required": ["_type"], "properties": {"procedureStatus": {"type": "string", "description": "Historical Procedures"}, "feeUCRME": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalFeeRateEntity"}}, "feeSchedule": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalFeeRateEntity"}}, "count": {"type": "integer", "format": "int64"}, "consultantReview": {"$ref": "#/definitions/CapDentalSettlement_CapDentalConsultantReviewEntity"}, "dentist": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDentistEntity"}, "submittedFeeUCR": {"$ref": "#/definitions/Money"}, "feeUCR": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalFeeRateEntity"}}, "coveredFeeUCR": {"$ref": "#/definitions/Money"}, "submittedFeeSchedule": {"$ref": "#/definitions/Money"}, "submittedFeeUCRME": {"$ref": "#/definitions/Money"}, "coveredFeeUCRME": {"$ref": "#/definitions/Money"}, "masterPolicyInfo": {"type": "array", "items": {"description": "Master Policy details", "$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoEntity"}}, "coveredFeeSchedule": {"$ref": "#/definitions/Money"}, "frequencyDHMO": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDHMOFrequencyEntity"}, "paidAmount": {"$ref": "#/definitions/Money"}, "frequencyPPO": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPPOFrequencyEntity"}, "certPolicyInfo": {"description": "Individual Policy details", "$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoEntity"}, "status": {"$ref": "#/definitions/CapDentalSettlement_CapDentalCalculationStatusEntity"}, "toothSystem": {"type": "string", "description": "Tooth System"}, "procedureType": {"type": "string", "description": "Procedure Type"}, "quantity": {"type": "integer", "format": "int64", "description": "Quantity"}, "procedureCode": {"type": "string", "description": "CDT Code"}, "preauthorization": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPreauthorizationEntity"}, "ortho": {"$ref": "#/definitions/CapDentalSettlement_CapDentalOrthodonticEntity"}, "description": {"type": "string", "description": "Procedure Description"}, "treatmentReason": {"$ref": "#/definitions/CapDentalSettlement_CapDentalTreatmentReasonEntity"}, "diagnosisCodes": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDiagnosisCodeEntity"}}, "surfaces": {"type": "array", "items": {"type": "string", "description": "Tooth Surface"}}, "preauthorizationNumber": {"type": "string", "description": "Preauthorization Number"}, "toothArea": {"type": "string", "description": "Area of Oral Cavity"}, "cob": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProcedureCoordinationOfBenefitsEntity"}, "toothCodes": {"type": "array", "items": {"type": "string", "description": "Tooth Numbers/Letters"}}, "submittedFee": {"description": "Charge", "$ref": "#/definitions/Money"}, "predetInd": {"type": "boolean", "description": "Is this procedure part of predetermination or real service?"}, "dateOfService": {"type": "string", "format": "date", "description": "Date of Service"}, "priorProsthesisPlacementDate": {"type": "string", "format": "date", "description": "Date of Prior Prosthesis Placement"}, "_type": {"type": "string", "example": "CapDentalProcedureEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalProcedureEntity"}, "CapDentalSettlement_DNEmailInfo": {"required": ["_type"], "properties": {"updatedOn": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "value": {"type": "string"}, "preferred": {"type": "boolean"}, "_type": {"type": "string", "example": "DNEmailInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNEmailInfo"}, "CapDentalSettlement_DNServiceType": {"required": ["_type"], "properties": {"serviceTypeCd": {"type": "string"}, "_type": {"type": "string", "example": "DNServiceType"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNServiceType"}, "CapDentalSettlement_DNDeductibleServicesInNetwork": {"required": ["_type"], "properties": {"deductibleAppliesToServicesInNetwork": {"type": "string"}, "_type": {"type": "string", "example": "DNDeductibleServicesInNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductibleServicesInNetwork"}, "CapDentalSettlement_DNPerson": {"required": ["_type"], "properties": {"lastName": {"type": "string"}, "deceased": {"type": "boolean"}, "birthDate": {"type": "string", "format": "date"}, "registryEntityNumber": {"type": "string"}, "suffixCd": {"type": "string"}, "firstName": {"type": "string"}, "communicationInfo": {"$ref": "#/definitions/CapDentalSettlement_DNCommunicationInfo"}, "genderCd": {"type": "string"}, "taxId": {"type": "string"}, "isFullTimeStudent": {"type": "boolean"}, "tobaccoCd": {"type": "string"}, "middleName": {"type": "string"}, "registryTypeId": {"type": "string"}, "salutation": {"type": "string"}, "deceasedDate": {"type": "string", "format": "date"}, "age": {"type": "integer", "format": "int64"}, "maritalStatus": {"type": "string"}, "_type": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "<PERSON><PERSON><PERSON>"}, "CapDentalSettlement_CapDentalPPOFrequencyEntity": {"required": ["_type"], "properties": {"procedureType": {"type": "string"}, "frequencyComment": {"type": "string"}, "frequencyRange": {"type": "string"}, "frequencyRule": {"type": "string"}, "frequencyPeriodType": {"type": "string"}, "frequencyPeriod": {"type": "integer", "format": "int64"}, "frequency": {"type": "string"}, "_type": {"type": "string", "example": "CapDentalPPOFrequencyEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPPOFrequencyEntity"}, "CapDentalSettlement_CapDentalBypassServiceLogicEntity": {"required": ["_type"], "properties": {"bypassOverpaymentLogic": {"type": "boolean", "description": "Bypass Overpayment Recoupment Logic"}, "bypassCobLogic": {"type": "boolean", "description": "Bypass COB Logic"}, "bypassDuplicateServiceLogic": {"type": "boolean", "description": "Bypass Duplicate Service Logic"}, "bypassToothExtractionRules": {"type": "boolean", "description": "Bypass Tooth Extraction Rules"}, "bypassGracePeriodLogic": {"type": "boolean", "description": "Bypass Grace Period Logic"}, "bypassClinicalReview": {"type": "boolean", "description": "Bypass Clinical/Consultant Review"}, "bypassMissingToothLogic": {"type": "boolean", "description": "Bypass Missing Tooth Exclusion Logic"}, "bypassInterestLogic": {"type": "boolean", "description": "Bypass Interest Logic"}, "bypassAllRules": {"type": "boolean", "description": "Bypass All Rules and Dental Review Logic"}, "_type": {"type": "string", "example": "CapDentalBypassServiceLogicEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalBypassServiceLogicEntity"}, "CapDentalSettlement_CapDentalProviderDiscountEntity": {"required": ["_type"], "properties": {"discountPercentage": {"type": "number", "description": "Concession %"}, "discountName": {"type": "string", "description": "Concession Name"}, "discountAmount": {"description": "Concession Amount", "$ref": "#/definitions/Money"}, "discountType": {"type": "string", "description": "Concession Type"}, "_type": {"type": "string", "example": "CapDentalProviderDiscountEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalProviderDiscountEntity"}, "CapDentalSettlement_CapDentalDHMOFrequencyEntity": {"required": ["_type"], "properties": {"isAppliedTowardsMOOP": {"type": "string"}, "frequencyRange": {"type": "string"}, "frequencyPeriodType": {"type": "string"}, "frequencyPeriod": {"type": "integer", "format": "int64"}, "isCovered": {"type": "boolean"}, "frequency": {"type": "string"}, "_type": {"type": "string", "example": "CapDentalDHMOFrequencyEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalDHMOFrequencyEntity"}, "CapDentalSettlement_CapDentalConsultantReviewEntity": {"required": ["_type"], "properties": {"surface": {"type": "string"}, "consultantReplyLetter": {"type": "string"}, "consultantReply": {"type": "string"}, "alternateCDTCode": {"type": "string"}, "_type": {"type": "string", "example": "CapDentalConsultantReviewEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalConsultantReviewEntity"}, "CapDentalSettlement_CapDentalTermEntity": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "CapDentalTermEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalTermEntity"}, "CapDentalSettlement_DNTier": {"required": ["_type"], "properties": {"tierCd": {"type": "string"}, "_type": {"type": "string", "example": "D<PERSON>ier"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "D<PERSON>ier"}, "CapDentalSettlement_DNCoinsurance": {"required": ["_type"], "properties": {"coinsuranceINAmount": {"type": "number"}, "coinsuranceOONAmount": {"type": "number"}, "coinsuranceServiceType": {"type": "string"}, "coinsuranceGradedYear": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNCoinsurance"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoinsurance"}, "EntityLinkRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "EntityLinkRequest"}, "CapDentalSettlement_DNPolicyPackage": {"required": ["_type"], "properties": {"plan": {"$ref": "#/definitions/CapDentalSettlement_DNBenefitPlan"}, "packageCd": {"type": "string"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNPolicyPackage"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNPolicyPackage"}, "CapDentalSettlement_CapDentalOverrideClaimValueEntity": {"required": ["_type"], "properties": {"overridePaymentInterestAmount": {"description": "Override Payment Interest Amount", "$ref": "#/definitions/Money"}, "overridePaymentInterestState": {"type": "string", "description": "Override Payment Interest State"}, "overrideBasicWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Basic Services Waiting Period"}, "overridePreventiveWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Preventive Services Waiting Period"}, "overrideOrthoWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Ortho Services Waiting Period"}, "overrideGracePeriod": {"type": "integer", "format": "int64", "description": "Override Grace Period"}, "overrideEligibilityPeriod": {"description": "Override Eligibility Period", "$ref": "#/definitions/CapDentalSettlement_Period"}, "overridePaymentInterestDays": {"type": "integer", "format": "int64", "description": "Override Payment Interest Number of Days"}, "overrideStudentDependentStatus": {"type": "string", "description": "Override Student Dependent Status"}, "overrideFeeSchedule": {"type": "string", "description": "Override Fee Schedule"}, "overrideMajorWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Major Services Waiting Period"}, "overrideLateEntrantWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Late Entrant Waiting Period"}, "_type": {"type": "string", "example": "CapDentalOverrideClaimValueEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalOverrideClaimValueEntity"}, "CapDentalSettlement_CapDentalDecisionResultEntity": {"required": ["_type"], "properties": {"proposal": {"type": "string", "description": "Proposal code which identifies next steps for system and/or user: Additional Reviewer or generate payment"}, "entries": {"type": "array", "items": {"description": "Result for each procedure", "$ref": "#/definitions/CapDentalSettlement_CapDentalResultEntryEntity"}}, "payeeRef": {"type": "string", "description": "Provide or insured reference for payment generation"}, "preprocessMessages": {"type": "array", "items": {"description": "Preprocess messages generated during rules processing", "$ref": "#/definitions/CapDentalSettlement_CapDentalPreprocessMessageEntity"}}, "reservedAccumulators": {"type": "array", "items": {"description": "Object for reserved Accumulators details", "$ref": "#/definitions/CapDentalSettlement_CapDentalAccumulatorEntity"}}, "paymentAmount": {"description": "Final amount for payment generation", "$ref": "#/definitions/Money"}, "reserve": {"type": "number"}, "_type": {"type": "string", "example": "CapDentalDecisionResultEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalDecisionResultEntity", "description": "Business entity defines settlement result."}, "CapDentalSettlement_CapDentalResultEntryEntity": {"required": ["_type"], "properties": {"serviceSource": {"type": "string", "description": "This link to Procedure from Intake. Defines which response is associated to which submited procedure."}, "calculationResult": {"$ref": "#/definitions/CapDentalSettlement_CapDentalCalculationResultEntity"}, "status": {"$ref": "#/definitions/CapDentalSettlement_CapDentalCalculationStatusEntity"}, "_type": {"type": "string", "example": "CapDentalResultEntryEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalResultEntryEntity"}, "CapDentalSettlement_DNCoverageParticipant": {"required": ["_type"], "properties": {"insuredLink": {"$ref": "#/definitions/CapDentalSettlement_MockedLink"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNCoverageParticipant"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoverageParticipant"}, "CapDentalSettlement_CapDentalSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"settlementResult": {"description": "Business entity defines settlement result.", "$ref": "#/definitions/CapDentalSettlement_CapDentalDecisionResultEntity"}, "settlementAbsenceInfo": {"description": "Obsolete. Should be deleted", "$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementAbsenceInfoEntity"}, "settlementLossInfo": {"description": "Business entity that houses the information from loss to use in settlement.", "$ref": "#/definitions/CapDentalSettlement_CapDentalClaimInfoEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "accessTrackInfo": {"description": "Core base type that stores information who created or last updated entities. It is part of extended settlements, e.g. CapAbsenceSettlementEntity", "$ref": "#/definitions/CapDentalSettlement_AccessTrackInfo"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementDetail": {"description": "This Business entity houses the detail of the settlement.", "$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementDetailEntity"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "_modelName": {"type": "string", "example": "CapDentalSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_timestamp": {"type": "string", "example": "2022-05-20T14:10:18.766+03:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapDentalSettlementEntity"}, "_key": {"$ref": "#/definitions/RootEntityKey"}}, "title": "CapDentalSettlementEntity", "description": "Main object for the CAP Settlement Domain."}, "CapDentalSettlement_DNCoinsuranceDefinition": {"required": ["_type"], "properties": {"reimbursementOONOptions": {"type": "string"}, "isGradedCoinsurance": {"type": "boolean"}, "coinsurances": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNCoinsurance"}}, "coinsuranceNumberOfGradedYears": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNCoinsuranceDefinition"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoinsuranceDefinition"}, "CapDentalSettlement_CapDentalWaiveClaimValueEntity": {"required": ["_type"], "properties": {"waiveServiceFrequencyLimit": {"type": "boolean", "description": "Waive Service Frequency Limit"}, "waivePreventiveWaitingPeriod": {"type": "boolean", "description": "Waive Preventive Services Waiting Period"}, "waiveOrthoWaitingPeriod": {"type": "boolean", "description": "Waive Ortho Services Waiting Period"}, "waiveDeductible": {"type": "boolean", "description": "Waive Deductible"}, "waiveMaximumLimitAmounts": {"type": "boolean", "description": "Waive Maximum Amount Limits"}, "waiveMajorWaitingPeriod": {"type": "boolean", "description": "Waive Major Services Waiting Period"}, "waiveReplacementLimit": {"type": "boolean", "description": "Waive Replacement Limit"}, "waiveBasicWaitingPeriod": {"type": "boolean", "description": "Waive Basic Services Waiting Period"}, "waiveEligibilityPriorStartDate": {"type": "boolean", "description": "Waive Eligibility Prior to Coverage Start Date"}, "waiveEligibilityAfterStartDate": {"type": "boolean", "description": "Waive Eligibility After Coverage Start Date"}, "waiveLateEntrantWaitingPeriod": {"type": "boolean", "description": "Waive Late Entrant Waiting Period"}, "_type": {"type": "string", "example": "CapDentalWaiveClaimValueEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalWaiveClaimValueEntity"}, "CapDentalSettlement_CapDentalPatientEntity": {"required": ["_type"], "properties": {"isDisabledChild": {"type": "boolean", "description": "NOT USED"}, "disabilities": {"type": "array", "items": {"type": "string", "description": "Patient's disabilities"}}, "isDependentChild": {"type": "boolean", "description": "NOT USED"}, "patientID": {"type": "string"}, "isFullTimeStudent": {"type": "boolean", "description": "NOT USED"}, "historyProcedures": {"type": "array", "items": {"description": "Patient history procedures", "$ref": "#/definitions/CapDentalSettlement_CapDentalProcedureEntity"}}, "dateOfBirth": {"type": "string", "format": "date", "description": "NOT USED"}, "birthDate": {"type": "string", "format": "date", "description": "<PERSON><PERSON>'s date of birth"}, "accumulators": {"type": "array", "items": {"description": "Object for remaining accumulators details", "$ref": "#/definitions/CapDentalSettlement_CapDentalAccumulatorEntity"}}, "_type": {"type": "string", "example": "CapDentalPatientEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPatientEntity"}, "CapDentalSettlement_CapDentalCalculationResultEntity": {"required": ["_type"], "properties": {"procedureType": {"type": "string"}, "charge": {"$ref": "#/definitions/Money"}, "coveredFee": {"$ref": "#/definitions/Money"}, "payableDeductible": {"$ref": "#/definitions/Money"}, "procedureID": {"type": "string"}, "submittedCode": {"type": "string"}, "allowedFee": {"$ref": "#/definitions/Money"}, "coinsuranceAmt": {"$ref": "#/definitions/Money"}, "coveredCode": {"type": "string"}, "patientResponsibility": {"$ref": "#/definitions/Money"}, "consideredFee": {"$ref": "#/definitions/Money"}, "netBenefitAmount": {"$ref": "#/definitions/Money"}, "copay": {"$ref": "#/definitions/Money"}, "contributionToMOOP": {"$ref": "#/definitions/Money"}, "coinsurancePercentage": {"type": "number"}, "_type": {"type": "string", "example": "CapDentalCalculationResultEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalCalculationResultEntity"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "CapDentalSettlement_DNCommunicationInfo": {"required": ["_type"], "properties": {"emails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNEmailInfo"}}, "phones": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNPhoneInfo"}}, "_type": {"type": "string", "example": "DNCommunicationInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCommunicationInfo"}, "CapDentalSettlement_CapDentalDiagnosisCodeEntity": {"required": ["_type"], "properties": {"code": {"type": "string", "description": "Diagnosis Code"}, "qualifier": {"type": "string", "description": "Diagnosis Code List Qualifier"}, "_type": {"type": "string", "example": "CapDentalDiagnosisCodeEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalDiagnosisCodeEntity"}, "CapDentalSettlementInitInputBody": {"properties": {"body": {"$ref": "#/definitions/CapDentalSettlementInitInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapDentalSettlementInitInputBody"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EndpointFailureFailureBody"}, "CapDentalSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}, "createdOn": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "AccessTrackInfo"}, "CapDentalSettlement_DNCoverageRating": {"required": ["_type"], "properties": {"rateBasisCd": {"type": "string"}, "_type": {"type": "string", "example": "DNCoverageRating"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoverageRating"}, "LoadEntityRootRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityRootRequest"}, "CapDentalSettlement_DNWaitingPeriods": {"required": ["_type"], "properties": {"applyLateEntrantBenefitWaitingPeriods": {"type": "boolean"}, "basicWaitingPeriod": {"type": "integer", "format": "int64"}, "lateEntrantWaitingPeriodsDetails": {"$ref": "#/definitions/CapDentalSettlement_DNLateEntrantBenefitWaitingPeriods"}, "majorWaitingPeriod": {"type": "integer", "format": "int64"}, "waitingPeriodApplyTo": {"type": "string"}, "preventiveWaitingPeriod": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNWaitingPeriods"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNWaitingPeriods"}, "CapDentalSettlement_DNPersonParty": {"required": ["_type"], "properties": {"personAddress": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNLocationParty"}}, "personInfo": {"$ref": "#/definitions/CapDentalSettlement_DNPerson"}, "offerStatus": {"type": "string"}, "_type": {"type": "string", "example": "DNPersonParty"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNPersonParty"}, "CapDentalSettlement_MockedLink": {"required": ["_type"], "properties": {"ref": {"type": "string"}, "_type": {"type": "string", "example": "MockedLink"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "MockedLink"}, "CapDentalSettlement_CapDentalRemarkMessageEntity": {"required": ["_type"], "properties": {"remarkCode": {"type": "string", "description": "Remark Code"}, "remarkMessage": {"type": "string", "description": "Remark Message"}, "_type": {"type": "string", "example": "CapDentalRemarkMessageEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalRemarkMessageEntity"}, "LoadEntityRootRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityRootRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityRootRequestBody"}, "CapEndpointRequestBody": {"properties": {"body": {"$ref": "#/definitions/CapEndpointRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapEndpointRequestBody"}, "CapDentalSettlement_DNDeductibleServicesOutNetwork": {"required": ["_type"], "properties": {"deductibleAppliesToServicesOutOfNetwork": {"type": "string"}, "_type": {"type": "string", "example": "DNDeductibleServicesOutNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNDeductibleServicesOutNetwork"}, "CapDentalSettlement_CapDentalPolicyInfoEntity": {"required": ["_type"], "properties": {"majorOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "preventiveOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "patientTerm": {"description": "NOT USED", "$ref": "#/definitions/CapDentalSettlement_CapDentalTermEntity"}, "basicWaitingPeriod": {"type": "string"}, "planName": {"type": "string", "description": "Plan Name"}, "policyPaidToDate": {"type": "string", "format": "date"}, "planCategory": {"type": "string", "description": "Plan Category"}, "personParties": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNPersonParty"}}, "pcdTerm": {"description": "PCD Assignment Effective/Termination Period", "$ref": "#/definitions/CapDentalSettlement_CapDentalTermEntity"}, "coinsurances": {"type": "array", "items": {"description": "Coinsurances percentages details", "$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoCoinsuranceEntity"}}, "pcdId": {"type": "string", "description": "Primary Care Dentist ID"}, "applyLateEntrantBenefitWaitingPeriods": {"type": "boolean", "description": "Is late entrant benefit period waiting applied"}, "policyPaidToDateWithGracePeriod": {"type": "string", "format": "date"}, "lateEntrantWaitingPeriodsDetails": {"type": "array", "items": {"description": "Late Entrant Waiting Period details", "$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}}, "majorWaitingPeriod": {"type": "string"}, "plan": {"type": "string"}, "basicINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "unverifiedInfo": {"$ref": "#/definitions/CapDentalSettlement_CapDentalUnverifiedInfoEntity"}, "orthoWaitingPeriod": {"type": "string"}, "isWaitingPeriodWaived": {"type": "boolean"}, "childMaxAgeCd": {"type": "string", "description": "Child Max Age limit"}, "insureds": {"type": "array", "items": {"description": "An entity for insureds information.", "$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoInsuredDetailsEntity"}}, "nonStandardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "orthoINNCoinsurancePercent": {"type": "number"}, "preventiveINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "fullTimeStudentAgeCd": {"type": "string", "description": "Full time student Age limit"}, "individualPackagingDetail": {"$ref": "#/definitions/CapDentalSettlement_DNPolicyPackage"}, "waiveINNInd": {"type": "boolean", "description": "NOT USED"}, "basicOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "majorINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "orthoOONCoinsurancePercent": {"type": "number"}, "preventWaitingPeriod": {"type": "string"}, "standardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "implantsWaitingPeriod": {"type": "string"}, "waiveOONInd": {"type": "boolean", "description": "NOT USED"}, "riskStateCd": {"type": "string", "description": "Situs state"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "capPolicyId": {"type": "string", "description": "Identification number of the policy in CAP subsystem."}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "term": {"description": "Policy effective and expiration dates.", "$ref": "#/definitions/CapDentalSettlement_Term"}, "policyStatus": {"type": "string", "description": "Policy version status"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "customer": {"$ref": "#/definitions/EntityLink"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapDentalSettlement_DNCoverageDefinition": {"required": ["_type"], "properties": {"coverageParticipants": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNCoverageParticipant"}}, "code": {"type": "string"}, "originalEffDate": {"type": "string", "format": "date-time"}, "typeOfServicesCovered": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_DNServiceType"}}, "dentalMaximumAmount": {"$ref": "#/definitions/CapDentalSettlement_DNDentalMaximumAmount"}, "waitingPeriods": {"$ref": "#/definitions/CapDentalSettlement_DNWaitingPeriods"}, "lastUpdateDate": {"type": "string", "format": "date-time"}, "coverageBasisCd": {"type": "string"}, "fundingStructure": {"$ref": "#/definitions/CapDentalSettlement_DNFundingStructure"}, "dependentEligibilityDetails": {"$ref": "#/definitions/CapDentalSettlement_DNDependentEligibility"}, "enrollmentDate": {"type": "string", "format": "date"}, "offerStatus": {"type": "string"}, "terminationAge": {"type": "integer", "format": "int64"}, "effDate": {"type": "string", "format": "date-time"}, "eligibilities": {"$ref": "#/definitions/CapDentalSettlement_DNEligibility"}, "whoCovered": {"$ref": "#/definitions/CapDentalSettlement_DNTier"}, "coinsuranceDetails": {"$ref": "#/definitions/CapDentalSettlement_DNCoinsuranceDefinition"}, "deductibleDetails": {"$ref": "#/definitions/CapDentalSettlement_DNDeductibleDefinition"}, "ratingDetails": {"$ref": "#/definitions/CapDentalSettlement_DNCoverageRating"}, "_type": {"type": "string", "example": "DNCoverageDefinition"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNCoverageDefinition"}, "EntityLinkRequestBody": {"properties": {"body": {"$ref": "#/definitions/EntityLinkRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "EntityLinkRequestBody"}, "CapDentalSettlement_CapDentalOverrideServiceValueEntity": {"required": ["_type"], "properties": {"overridePaymentInterestAmount": {"description": "Override Payment Interest Amount", "$ref": "#/definitions/Money"}, "overrideDeductible": {"description": "Override Deductible", "$ref": "#/definitions/Money"}, "overrideMaximumAmount": {"description": "Override all max limits with value and deducatables to 0", "$ref": "#/definitions/Money"}, "overrideGracePeriod": {"type": "integer", "format": "int64", "description": "Override Grace Period"}, "overrideEligibilityPeriod": {"description": "Override Eligibility Start Date", "$ref": "#/definitions/CapDentalSettlement_Period"}, "overridePaymentInterestDays": {"type": "integer", "format": "int64", "description": "Override Payment Interest Number of Days"}, "overrideStudentDependentStatus": {"type": "string", "description": "Override Student Dependent Status"}, "overrideCobApplied": {"description": "Override COB Applied", "$ref": "#/definitions/Money"}, "overrideServiceWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Service Category Waiting Period"}, "overrideLateEntrantWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Late Entrant Waiting Period"}, "overrideServiceFrequencyLimit": {"type": "integer", "format": "int64", "description": "Override Service Frequency Limit"}, "overridePaymentInterestState": {"type": "string", "description": "Override Payment Interest State"}, "overrideAllowedAmount": {"description": "Override DHMO Allowed Amount", "$ref": "#/definitions/Money"}, "overridePatientResponsibility": {"description": "Override Patient Responsibility", "$ref": "#/definitions/Money"}, "overrideCopayAmount": {"description": "Override <PERSON><PERSON> Amount", "$ref": "#/definitions/Money"}, "overrideCoveredCdtCode": {"type": "string", "description": "Override Covered CDT Code"}, "overrideServiceCategory": {"type": "string", "description": "Override Category of Service"}, "overrideFeeSchedule": {"type": "string", "description": "Override Fee Schedule"}, "overrideConsideredAmount": {"description": "Override Considered Amount", "$ref": "#/definitions/Money"}, "overrideReplacementLimit": {"type": "integer", "format": "int64", "description": "Override Replacement Limit"}, "overrideEssentialHealthBenefit": {"type": "string", "description": "Override Essential Health Benefit"}, "overrideCoveredAmount": {"description": "Override Covered Amount", "$ref": "#/definitions/Money"}, "overrideCoinsurancePct": {"type": "number", "description": "Override Coinsurance %"}, "_type": {"type": "string", "example": "CapDentalOverrideServiceValueEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalOverrideServiceValueEntity"}, "CapDentalSettlementReadjudicateInput": {"properties": {"claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "_updateStrategy": {"type": "string"}, "entity": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementDetailEntity"}}, "title": "CapDentalSettlementReadjudicateInput"}, "CapDentalSettlement_DNMaximum": {"required": ["_type"], "properties": {"maximumINAmount": {"$ref": "#/definitions/Money"}, "maximumType": {"type": "string"}, "maximumOONAmount": {"$ref": "#/definitions/Money"}, "maximumGradedYear": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNMaximum"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximum"}, "CapDentalSettlement_CapDentalClaimCoordinationOfBenefitsEntity": {"required": ["_type"], "properties": {"period": {"description": "Start and End Dates", "$ref": "#/definitions/CapDentalSettlement_Term"}, "address": {"type": "string", "description": "Policyholder Address"}, "otherPolicyType": {"type": "string", "description": "Other Policy Type"}, "PolicyNumber": {"type": "string", "description": "Policy Number"}, "otherInsuranceCompany": {"type": "string", "description": "Other Insurance Company Name"}, "policyholderFirstName": {"type": "string", "description": "First Name of Policyholder"}, "typeOfCob": {"type": "string", "description": "Type of Coordination of Benefits"}, "otherCoverageType": {"type": "string", "description": "Other Coverage Type"}, "policyholderRelationshipToPatient": {"type": "string", "description": "Patient Relationship to Policyholder"}, "policyholderGender": {"type": "string", "description": "Policyholder Gender"}, "policyholderDateOfBirth": {"type": "string", "format": "date", "description": "Policyholder Date of Birth"}, "policyholderLastName": {"type": "string", "description": "Last Name of Policyholder"}, "plan": {"type": "string", "description": "Plan or Group Number"}, "_type": {"type": "string", "example": "CapDentalClaimCoordinationOfBenefitsEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalClaimCoordinationOfBenefitsEntity"}, "CapDentalSettlement_CapDentalClaimOverrideEntity": {"required": ["_type"], "properties": {"waiveClaimValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalWaiveClaimValueEntity"}, "overrideClaimValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalOverrideClaimValueEntity"}, "isAllowed": {"type": "boolean", "description": "Allow Service"}, "isDenied": {"type": "boolean", "description": "Deny Service"}, "suppressMemberEob": {"type": "boolean", "description": "Suppress EOB for Member"}, "suppressProviderEob": {"type": "boolean", "description": "Suppress EOB for Provider"}, "bypassClaimLogic": {"$ref": "#/definitions/CapDentalSettlement_CapDentalBypassClaimLogicEntity"}, "_type": {"type": "string", "example": "CapDentalClaimOverrideEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalClaimOverrideEntity"}, "LoadEntityByBusinessKeyRequestBody": {"properties": {"body": {"$ref": "#/definitions/LoadEntityByBusinessKeyRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "LoadEntityByBusinessKeyRequestBody"}, "CapDentalSettlement_CapDentalPolicyInfoInsuredDetailsEntity": {"required": ["_type"], "properties": {"isFullTimeStudent": {"type": "boolean", "description": "Is patient full time student?"}, "insuredRoleNameCd": {"type": "string", "description": "Insure<PERSON>'s role"}, "relationshipToPrimaryInsuredCd": {"type": "string", "description": "Patient's relationship to primary insured"}, "insuredInfo": {"$ref": "#/definitions/CapDentalSettlement_MockedLink"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "The unique registry ID that identifies the subject of the claim."}, "_type": {"type": "string", "example": "CapDentalPolicyInfoInsuredDetailsEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoInsuredDetailsEntity", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "CapDentalSettlementReadjudicateInputBody": {"properties": {"body": {"$ref": "#/definitions/CapDentalSettlementReadjudicateInput"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapDentalSettlementReadjudicateInputBody"}, "CapDentalSettlement_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity": {"required": ["_type"], "properties": {"preventWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant preventive Waiting Period"}, "basicWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant basic Waiting Period"}, "majorWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant major Waiting Period"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "CapDentalSettlement_CapDentalSettlementEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntity"}}, "title": "CapDentalSettlement_CapDentalSettlementEntitySuccess"}, "CapEndpointRequest": {"properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "fields": {"type": "array", "items": {"type": "string"}}, "_key": {"$ref": "#/definitions/CapAccidentalDismembermentSettlementAdjudicationInput"}}, "title": "CapEndpointRequest"}, "CapDentalSettlement_DNMaximumServicesOutOfNetwork": {"required": ["_type"], "properties": {"maximumAppliesToServicesOON": {"type": "string"}, "_type": {"type": "string", "example": "DNMaximumServicesOutOfNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximumServicesOutOfNetwork"}, "IdentifierRequestBody": {"properties": {"body": {"$ref": "#/definitions/IdentifierRequest"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "IdentifierRequestBody"}, "CapDentalSettlement_CapDentalSettlementAbsenceInfoEntity": {"required": ["_type"], "properties": {"id": {"type": "string"}, "_type": {"type": "string", "example": "CapDentalSettlementAbsenceInfoEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalSettlementAbsenceInfoEntity", "description": "The object that includes information taken from Absence case."}, "LoadEntityByBusinessKeyRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadEntityByBusinessKeyRequest"}, "CapDentalSettlement_Term": {"required": ["_type"], "properties": {"effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "_type": {"type": "string", "example": "Term"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "Term"}, "CapDentalSettlement_DNLateEntrantBenefitWaitingPeriods": {"required": ["_type"], "properties": {"basicWaitingPeriod": {"type": "integer", "format": "int64"}, "majorWaitingPeriod": {"type": "integer", "format": "int64"}, "preventiveWaitingPeriod": {"type": "integer", "format": "int64"}, "_type": {"type": "string", "example": "DNLateEntrantBenefitWaitingPeriods"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNLateEntrantBenefitWaitingPeriods"}, "CapDentalSettlement_CapDentalCalculationStatusEntity": {"required": ["_type"], "properties": {"remarkMessages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalRemarkMessageEntity"}}, "flag": {"type": "string"}, "statusReason": {"type": "string"}, "code": {"type": "string"}, "percentage": {"type": "number"}, "fee": {"$ref": "#/definitions/Money"}, "questions": {"type": "array", "items": {"type": "string"}}, "predetInd": {"type": "boolean"}, "reasonCode": {"type": "string"}, "procedureID": {"type": "string"}, "submittedCode": {"type": "string"}, "coveredCode": {"type": "string"}, "_type": {"type": "string", "example": "CapDentalCalculationStatusEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalCalculationStatusEntity"}, "CapDentalSettlement_CapDentalSettlementEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntitySuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "CapDentalSettlement_CapDentalSettlementEntitySuccessBody"}, "CapDentalSettlement_CapDentalOrthodonticEntity": {"required": ["_type"], "properties": {"months": {"type": "integer", "format": "int64", "description": "Deprecated"}, "orthoFrequencyCd": {"type": "string", "description": "Orthodontic Payment Frequency"}, "orthoMonthQuantity": {"type": "integer", "format": "int64", "description": "Number of Months of Treatment"}, "downPayment": {"description": "Downpayment Amount", "$ref": "#/definitions/Money"}, "appliancePlacedDate": {"type": "string", "format": "date", "description": "Date Appliance Placed"}, "frequency": {"type": "string", "description": "Deprecated"}, "_type": {"type": "string", "example": "CapDentalOrthodonticEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalOrthodonticEntity"}, "CapDentalSettlement_DNEligibility": {"required": ["_type"], "properties": {"waitingPeriodAmount": {"type": "integer", "format": "int64"}, "waitingPeriodModeCd": {"type": "string"}, "minHourlyReq": {"type": "number"}, "eligibilityTypeCd": {"type": "string"}, "waitingPeriodDefCd": {"type": "string"}, "_type": {"type": "string", "example": "DNEligibility"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNEligibility"}, "CapDentalSettlement_DNMaximumServicesInNetwork": {"required": ["_type"], "properties": {"maximumAppliesToServicesIN": {"type": "string"}, "_type": {"type": "string", "example": "DNMaximumServicesInNetwork"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "DNMaximumServicesInNetwork"}, "LoadSingleEntityRootRequest": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}}, "title": "LoadSingleEntityRootRequest"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "CapDentalSettlement_CapDentalSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"claimOverride": {"description": "EOB Details", "$ref": "#/definitions/CapDentalSettlement_CapDentalClaimOverrideEntity"}, "overrideCd": {"type": "string", "description": "NOT USED"}, "serviceOverrides": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalServiceOverrideEntity"}}, "eobFreeFormMessage": {"type": "string", "description": "EOB Free Form Message"}, "_modelName": {"type": "string", "example": "CapDentalSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_timestamp": {"type": "string", "example": "2022-05-20T14:10:18.766+03:00"}, "_archived": {"type": "boolean", "example": false}, "_type": {"type": "string", "example": "CapDentalSettlementDetailEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalSettlementDetailEntity", "description": "This Business entity houses the detail of the settlement."}, "ObjectSuccessBody": {"properties": {"body": {"$ref": "#/definitions/ObjectSuccess"}, "requestId": {"type": "string"}, "finalResponse": {"type": "boolean"}}, "title": "ObjectSuccessBody"}, "CapDentalSettlement_CapDentalServiceOverrideEntity": {"required": ["_type"], "properties": {"isAllowed": {"type": "boolean", "description": "Allow Service"}, "isDenied": {"type": "boolean", "description": "Deny Service"}, "overrideRemark": {"type": "string", "description": "Override Remark"}, "overrideServiceValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalOverrideServiceValueEntity"}, "suppressMemberEob": {"type": "boolean", "description": "Suppress EOB for Member"}, "suppressProviderEob": {"type": "boolean", "description": "Suppress EOB for Provider"}, "serviceSource": {"type": "string", "description": "Link to Service"}, "bypassServiceLogic": {"$ref": "#/definitions/CapDentalSettlement_CapDentalBypassServiceLogicEntity"}, "waiveServiceValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalWaiveServiceValueEntity"}, "_type": {"type": "string", "example": "CapDentalServiceOverrideEntity"}, "_key": {"$ref": "#/definitions/EntityKey"}}, "title": "CapDentalServiceOverrideEntity"}}}