/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package cap.adjuster.services.financial.converters;

import cap.adjuster.services.common.dto.GenesisApiModelKey;
import cap.adjuster.services.financial.dto.CapPaymentMessage;
import com.eisgroup.dxp.dataproviders.genesiscaplosssearch.dto.CapPaymentSchedule_CapScheduledPaymentMessageEntityDTO;
import core.services.converters.CommonDTOConverter;

public class CapScheduledPaymentMessageConverter<I extends CapPaymentSchedule_CapScheduledPaymentMessageEntityDTO, A extends CapPaymentMessage>
        extends CommonDTOConverter<I, A> {

    @Override
    public A convertToApiDTO(I intDTO, A apiDTO) {
        super.convertToApiDTO(intDTO, apiDTO);

        apiDTO.key = new GenesisApiModelKey();
        apiDTO.code = intDTO.code;
        apiDTO.message = intDTO.message;
        apiDTO.source = intDTO.source;
        apiDTO.severity = intDTO.severity;
        apiDTO.allocationPeriod = intDTO.allocationPeriod;
        apiDTO.gentityType = intDTO._type;

        return apiDTO;
    }
}
