import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "SWITCH",
          "props": {
            "md": 7,
            "sideLabelText": "cap-core:deductions_form_deduction_apply_pre_tax_label",
            "name": "isPreTax",
            "disableStyledColumn": false,
            "disabled": false
          },
          "id": "e7cdfe1b-b776-48ce-9527-83c658db43be"
        }
      ],
      "id": "aa79aa19-542d-4fff-abbf-fab5689f57b7"
    }
  ],
  "version": 12,
  "globalEvents": {},
  "actionChains": {}
}

export default config;
