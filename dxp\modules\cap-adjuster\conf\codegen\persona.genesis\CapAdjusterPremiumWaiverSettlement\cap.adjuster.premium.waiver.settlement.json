{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.premium.waiver.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Premium Waiver Settlements API", "version": "1", "title": "CAP Adjuster: Premium Waiver Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-premium-waiver-settlements", "description": "CAP Adjuster: Premium Waiver Settlements API"}], "paths": {"/losses-premium-waiver-settlements/{rootId}/{revisionNo}": {"get": {"summary": "Get premium waive settlement by rootId and revisionNo", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for premium waive settlement", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/rules/bundle": {"post": {"summary": "Rules bundle for Premium Waiver Settlement", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/rules/bundle", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements": {"put": {"summary": "Update the settlement without triggering the workflow", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/command/updateSettlement", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/draft": {"post": {"summary": "Init premium waive settlement", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/command/initSettlement", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/adjudicate": {"post": {"summary": "Adjudicate premium waive settlement", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/command/adjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}, "put": {"summary": "Readjudicate premium waive settlement", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/approve": {"post": {"summary": "Approve premium waive settlement", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/command/approveSettlement", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/disapprove": {"post": {"summary": "Disapprove premium waive settlement", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/command/disapproveSettlement", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}, "/losses-premium-waiver-settlements/adjudication-input": {"post": {"summary": "Premium waiver settlement adjudication input", "x-dxp-path": "/api/capsettlement/PremiumWaiverSettlement/v1/transformation/CapPremiumWaiverSettlementAdjudicationInput", "tags": ["/cap-adjuster/v1/losses-premium-waiver-settlements"]}}}}