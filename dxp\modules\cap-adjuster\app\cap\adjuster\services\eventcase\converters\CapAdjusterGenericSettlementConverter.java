/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/

package cap.adjuster.services.eventcase.converters;

import cap.adjuster.services.common.converters.GenesisRootApiModelConverter;
import cap.adjuster.services.eventcase.dto.CapGenericSettlement;
import dataproviders.dto.CapSettlementDTO;

public class CapAdjusterGenericSettlementConverter<I extends CapSettlementDTO, A extends CapGenericSettlement>
        extends GenesisRootApiModelConverter<I, A> {

        @Override
        public A convertToApiDTO(I intDTO, A apiDTO) {
            super.convertToApiDTO(intDTO, apiDTO);
            apiDTO.claimLossIdentification = intDTO.claimLossIdentification;
            apiDTO.claimWrapperIdentification = intDTO.claimWrapperIdentification;
            apiDTO.policy = intDTO.policy;
            apiDTO.policyId = intDTO.policyId;
            apiDTO.settlementDetail = intDTO.settlementDetail;
            apiDTO.settlementNumber = intDTO.settlementNumber;
            apiDTO.settlementType = intDTO.settlementType;
            apiDTO.settlementLossInfo = intDTO.settlementLossInfo;
            apiDTO.settlementAbsenceInfo = intDTO.settlementAbsenceInfo;
            apiDTO.coverageBasedConfiguration = intDTO.coverageBasedConfiguration;
            apiDTO.applicabilityResult = intDTO.applicabilityResult;
            apiDTO.settlementResult = intDTO.settlementResult;
            apiDTO.settlementApprovalResult = intDTO.settlementApprovalResult;
            apiDTO.state = intDTO.state;
            apiDTO.claimCoverageName = intDTO.claimCoverageName;
            apiDTO.claimCoveragePrefix = intDTO.claimCoveragePrefix;
            return apiDTO;
        }
}
