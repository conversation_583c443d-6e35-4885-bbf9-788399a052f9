/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useState} from 'react'
import {observer} from 'mobx-react'
import {Field, OnBlur, useForm} from '@eisgroup/form'
import {Col, Row} from '@eisgroup/ui-kit'
import {t} from '@eisgroup/i18n'
import {
    CLAIM_REDUCE_PAYMENT_WITHHOLDING_AMOUNT,
    ClaimType,
    composeValidators,
    isValuePositiveAndNotZeroForFieldName,
    required
} from '../..'
import {BalanceStore} from '../../common/store/BalanceStore'

const {MoneyInput} = Field

export interface WithholdingAmountProps {
    balanceStore: BalanceStore
}

export const WithholdingAmount: React.FC<WithholdingAmountProps> = observer(props => {
    const form = useForm()
    const [claimType, setClaimType] = useState<string>()
    const handleOnBlur = (name: string) => {
        const {values = {}} = form.getState()
        const claim = values.claims.find(v => v.code === values.selectedClaim)
        setClaimType(claim?.displayValue)
        const totalBalanceAmount = props.balanceStore.balance?.totalBalanceAmount?.amount ?? 0
        if (totalBalanceAmount < 0) {
            const totalBalance = Math.abs(props.balanceStore.balance?.totalBalanceAmount?.amount ?? 0)
            if (values.withholdingAmount?.amount) {
                const amount = Number((totalBalance / values.withholdingAmount.amount).toFixed(1))
                form.change('numberOfEstimatedWeekly', amount)
            }
        }
    }
    return (
        <Row gutter={24}>
            <Col span={12} className={CLAIM_REDUCE_PAYMENT_WITHHOLDING_AMOUNT}>
                <OnBlur>{(name: string) => handleOnBlur(name)}</OnBlur>
                <MoneyInput
                    label='cap-core:balance_actions_drawer_withholding_amount_label'
                    name='withholdingAmount'
                    required
                    validate={composeValidators(required('cap-core:withholding_amount_required'), v =>
                        isValuePositiveAndNotZeroForFieldName(
                            v,
                            claimType?.includes(ClaimType.LTD)
                                ? t('cap-core:withholding_monthly_amount')
                                : t('cap-core:withholding_weekly_amount')
                        )
                    )}
                    allowDecimal
                />
            </Col>
        </Row>
    )
})
