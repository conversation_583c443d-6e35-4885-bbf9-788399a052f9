{"swagger": "2.0", "info": {"description": "API for CapPolicyModel", "version": "1", "title": "CapPolicyModel model API facade"}, "basePath": "/", "schemes": ["http"], "paths": {"/api/cappolicy/CapPolicyModel/v1/load/": {"post": {"description": "Loads CAP canonical policy by the given request.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/CanonicalPolicyLoadRequestBody"}}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/CapPolicyModel_CapPolicyEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CanonicalPolicyLoadRequest": {"properties": {"capPolicyId": {"type": "string", "description": "Deprecated, specific policy version should be loaded."}, "capPolicyVersionId": {"type": "string", "description": "Identifier of versioned policy"}}, "title": "CanonicalPolicyLoadRequest"}, "CanonicalPolicyLoadRequestBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/CanonicalPolicyLoadRequest"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CanonicalPolicyLoadRequestBody"}, "CapPolicyModel_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapPolicyModel CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapPolicyModel_CapPolicyEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapPolicyModel"}, "_modelType": {"type": "string", "example": "CapPolicy"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapPolicyEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "coverableItems": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CoverableItem"}}, "currencyCd": {"type": "string", "description": "Currency Code"}, "exclusions": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CapPolicyExclusion"}}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "risks": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_InsurableRisk"}}, "term": {"$ref": "#/definitions/CapPolicyModel_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapPolicyModel CapPolicyEntity", "description": "Canonical policy entity, which represents a standard policy used in claims."}, "CapPolicyModel_CapPolicyEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapPolicyModel_CapPolicyEntity"}}, "title": "CapPolicyModel_CapPolicyEntitySuccess"}, "CapPolicyModel_CapPolicyEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapPolicyModel_CapPolicyEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapPolicyModel_CapPolicyEntitySuccessBody"}, "CapPolicyModel_CapPolicyExclusion": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapPolicyExclusion"}, "code": {"type": "string", "description": "Exclusion identifier."}, "isExclusion": {"type": "boolean", "description": "Exclusion indicator."}}, "title": "CapPolicyModel CapPolicyExclusion", "description": "Defines a policy exclusion."}, "CapPolicyModel_CompositeCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CompositeCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "items": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CoverableItem"}}}, "title": "CapPolicyModel CompositeCoverableItemCondition", "description": "Describes other item conditions to support item hierarchy."}, "CapPolicyModel_CoverableItem": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CoverableItem"}, "code": {"type": "string", "description": "Code representing this coverable item ."}, "compositeCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_CompositeCoverableItemCondition"}, "delayableCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_DelayableCoverableItemCondition"}, "distanceBoundCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_DistanceBoundCoverableItemCondition"}, "exclusions": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CapPolicyExclusion"}}, "limitedCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_LimitedCoverableItemCondition"}, "occurrableCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_OccurrableCoverableItemCondition"}, "preExistingCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_PreExistingCoverableItemCondition"}, "restrictedCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_RestrictedCoverableItemCondition"}, "timedCoverableItemCondition": {"$ref": "#/definitions/CapPolicyModel_TimedCoverableItemCondition"}, "type": {"type": "string", "description": "Type of the coverable item: plan/coverage/service/limitation/etc."}}, "title": "CapPolicyModel CoverableItem", "description": "Describes coverage-applicable policy item, which is composed of conditions extending CoverableItemCondition type."}, "CapPolicyModel_DelayableCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "DelayableCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "delay": {"type": "string", "description": "Delay in ISO 8601 format."}}, "title": "CapPolicyModel DelayableCoverableItemCondition", "description": "Condition describing some delay."}, "CapPolicyModel_DistanceBoundCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "DistanceBoundCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "distanceMoreThan": {"type": "number", "description": "Decimal number indicating the distance lower bound."}}, "title": "CapPolicyModel DistanceBoundCoverableItemCondition", "description": "Condition describing some distance limit."}, "CapPolicyModel_InsurableRisk": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "InsurableRisk"}, "coverableItems": {"type": "array", "items": {"$ref": "#/definitions/CapPolicyModel_CoverableItem"}}, "id": {"type": "string", "description": "ID of this insurable risk."}}, "title": "CapPolicyModel InsurableRisk", "description": "Holds information about the subject of insurance."}, "CapPolicyModel_LimitedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "LimitedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "amount": {"type": "number", "description": "Limit amount."}}, "title": "CapPolicyModel LimitedCoverableItemCondition", "description": "Condition describing some limit amount."}, "CapPolicyModel_OccurrableCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "OccurrableCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "maxOccurrences": {"type": "integer", "format": "int64", "description": "Maximum number of occurrences."}}, "title": "CapPolicyModel OccurrableCoverableItemCondition", "description": "Condition describing some number of occurrences."}, "CapPolicyModel_PreExistingCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "PreExistingCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "continuouslyInsuredPeriod": {"type": "integer", "format": "int64", "description": "Period during which the insured was continuously insured by the plan (e.g. 3, 6, 12, 24 months) after the initial coverage began or a coverage increase happened. If the pre-existing condition occurs outside this period, then limitations don't apply."}, "duration": {"type": "integer", "format": "int64", "description": "Pre-condition duration in months (should be applicable to limitations only)."}, "isApplied": {"type": "boolean", "description": "Indicates if the pre-existing condition provision applies."}, "lookBackPeriod": {"type": "integer", "format": "int64", "description": "Period before a person becomes insured by the plan (e.g. 3, 6, 12, 24 months) or before the coverage increase, during which the pre-existing condition may have occurred."}, "treatmentFreePeriod": {"type": "integer", "format": "int64", "description": "Period prior to the occurrence of the pre-existing condition during which insured may have received treatment for the condition (e.g. n/a, 3, 6, 12 months)."}, "type": {"type": "string", "description": "Indicates the type of the pre-existing condition: exclusion/limitation."}}, "title": "CapPolicyModel PreExistingCoverableItemCondition", "description": "Defines a composite pre-existing condition (composed of limitations) that impacts claim's eligibility."}, "CapPolicyModel_RestrictedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "RestrictedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "appliesTo": {"type": "string", "description": "Restriction application context."}, "restriction": {"type": "string", "description": "Restriction value."}}, "title": "CapPolicyModel RestrictedCoverableItemCondition", "description": "Defines some restriction to the coverable item."}, "CapPolicyModel_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapPolicyModel Term"}, "CapPolicyModel_TimedCoverableItemCondition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "TimedCoverableItemCondition"}, "accumulatorType": {"type": "string", "description": "Coverable item associated accumulator type"}, "period": {"type": "string", "description": "Period in ISO 8601 format."}}, "title": "CapPolicyModel TimedCoverableItemCondition", "description": "Condition describing some time period."}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}}}