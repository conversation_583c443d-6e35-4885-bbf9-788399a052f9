ARG BASE_IMG=sfoeisgennexus01-docker-group.exigengroup.com/genesis-base:2.1.2
FROM ${BASE_IMG}
ARG HTTP_PORT=9191
ARG CONFIG_RESOURCE_PATH=./envs/
ARG CONFIG_RESOURCE_NAME=application.envs.genesis.conf
ENV WORKSPACE /usr/lib/genesis
WORKDIR ${WORKSPACE}
RUN chown -R eisci:root ${WORKSPACE}
COPY --chown=eisci:root target/universal/gateway*.zip ${WORKSPACE}
COPY --chown=eisci:root ${CONFIG_RESOURCE_PATH}${CONFIG_RESOURCE_NAME} ${WORKSPACE}
USER eisci
#Extract and run sources
RUN pwd && unzip "*.zip" && rm -f ${WORKSPACE}/gateway*.zip &&  cd gateway*
#Parse variables before passing to cmd
ENV HTTP_PORT ${HTTP_PORT}
ENV CONFIG_RESOURCE ${CONFIG_RESOURCE_NAME}
EXPOSE ${HTTP_PORT}
CMD cd ${WORKSPACE}/*/bin && ./gateway -Dplay.http.parser.maxMemoryBuffer=${MAX_MEMORY_BUFFER} -Dplay.http.parser.maxDiskBuffer=${MAX_DISK_BUFFER} -Dhttp.port=${HTTP_PORT} -Dconfig.file=${WORKSPACE}/${CONFIG_RESOURCE} -Dlog.level=${LOG_LEVEL}