{"swagger": "2.0", "x-dxp-spec": {"imports": {"cap": {"schema": "integration.cap.hi.settlement.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Hospital Indemnity Settlements API", "version": "1", "title": "CAP Adjuster: Hospital Indemnity Settlements API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-hi-settlements", "description": "CAP Adjuster: Hospital Indemnity Settlements API"}], "paths": {"/losses-hi-settlements/{rootId}/{revisionNo}": {"get": {"summary": "Get hospital indemnity settlement by rootId and revisionNo", "x-dxp-path": "/api/capsettlement/HISettlement/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}, "/losses-hi-settlements/rules/{entryPoint}": {"post": {"summary": "Get kraken rules for hospital indemnity settlement", "x-dxp-path": "/api/capsettlement/HISettlement/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}, "/losses-hi-settlements/draft": {"post": {"summary": "Init hospital indemnity settlement", "x-dxp-path": "/api/capsettlement/HISettlement/v1/command/initSettlement", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}, "/losses-hi-settlements/adjudicate": {"post": {"summary": "Adjudicate hospital indemnity settlement", "x-dxp-path": "/api/capsettlement/HISettlement/v1/command/adjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}, "put": {"summary": "Readjudicate hospital indemnity settlement", "x-dxp-method": "post", "x-dxp-path": "/api/capsettlement/HISettlement/v1/command/readjudicateSettlement", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}, "/losses-hi-settlements/approve": {"post": {"summary": "Approve hospital indemnity settlement", "x-dxp-path": "/api/capsettlement/HISettlement/v1/command/approveSettlement", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}, "/losses-hi-settlements/disapprove": {"post": {"summary": "Disapprove hospital indemnity settlement", "x-dxp-path": "/api/capsettlement/HISettlement/v1/command/disapproveSettlement", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}, "/losses-hi-settlements/adjudication-input": {"post": {"summary": "HI settlement adjudication input", "x-dxp-path": "/api/capsettlement/HISettlement/v1/transformation/CapHISettlementAdjudicationInput", "tags": ["/cap-adjuster/v1/losses-hi-settlements"]}}}}