.@{PREFIX}-case-search-component {
    .@{PREFIX}-case-search-component-auto-complete-with-filter {
        position: relative;

        .@{PREFIX}-filter-search-icon {
            cursor: pointer;
            position: absolute;
            top: 50%;
            right: 1rem;
            transform: translateY(-50%);
            z-index: 1;
        }

        .@{PREFIX}-filter-search-icon_selected {
            color: #2863a9;
        }

        .ant-input-suffix {
            right: 1.5rem !important;
        }
    }

    .@{PREFIX}-case-search-component-result {
        margin-top: 1rem;
    }

    .@{PREFIX}-case-search-component-result-filter-title {
        font-size: 1rem;
    }

    .@{PREFIX}-case-search-component-result-filter-tags {
        margin-top: 1rem;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 0.5rem;
    }

    .@{PREFIX}-case-search-component-result-filter-tags-item {
        align-items: center;
        background-color: #f2f7fd;
        border-radius: 4px;
        display: flex;
        flex-wrap: nowrap;
        margin-bottom: 0.5rem;
        padding: 0.625rem 1rem;

        &:not(:last-of-type) {
            margin-right: 0.5rem;
        }
    }

    .@{PREFIX}-case-search-component-result-filter-tags-item-value {
        border: 1px solid #096eb6;
        border-radius: 2px;
        color: #096eb6;
        letter-spacing: 0;
        margin: 0 0.5rem;
        padding: 0.25rem 0.5rem;
    }

    .@{PREFIX}-case-search-component-result-table {
        margin-top: 1rem;
    }
}

.has-error {
    .@{PREFIX}-case-search-component-auto-complete .ant-input-clear-icon {
        right: 3.5rem;
    }

    .@{PREFIX}-case-search-component-auto-complete-with-filter .@{PREFIX}-filter-search-icon {
        right: 2rem;
    }

    .@{PREFIX}-case-search-component-result {
        margin-top: 2rem;
    }
}

.@{PREFIX}-case-search-component-dropdown {
    .ant-select-dropdown-menu {
        max-height: none;
    }

    .ant-select-dropdown-menu-item-active {
        background-color: #0000000a !important;
    }
    .@{PREFIX}-auto-search-suggestion-selected {
        background-color: #e6f4ff !important;
    }
}

.@{PREFIX}-case-search-component-filter-search-popover {
    width: 100%;

    .base-form div.ant-row.ant-form-item {
        margin-right: 1rem;
    }
}
