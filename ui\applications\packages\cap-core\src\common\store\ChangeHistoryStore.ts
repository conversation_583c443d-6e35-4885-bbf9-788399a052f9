/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {CapEventCase, ClaimWrapper} from '@eisgroup/cap-event-case-models'
import {CapPaymentTemplate} from '@eisgroup/cap-financial-models'
import {
    backofficeWorkService,
    CapGenericLoss,
    ChangeHistoryPatch,
    ChangeHistoryRecord,
    ChangeHistoryRecordWithUser,
    ChangeHistoryRequestFilter,
    claimPaymentTemplatesService,
    ClaimWrapperService,
    createTemplateUri,
    dateComparison,
    eventCaseService,
    EventCaseService,
    loadLookupOptions,
    OrgPerson
} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {Right} from '@eisgroup/data.either'
import {lookups} from '@eisgroup/lookups'
import {isEmpty} from 'lodash'
import get from 'lodash/get'
import {action, computed, observable, runInAction, toJS} from 'mobx'
import {Observable} from 'rxjs'
import {forkJoin} from 'rxjs/observable/forkJoin'
import {_if as iif} from 'rxjs/observable/if'
import {of} from 'rxjs/observable/of'
import {finalize, map, switchMap} from 'rxjs/operators'
import {PAYMENT_TEMPLATE_MODEL_NAME} from '../../components/change-history/utils/types'
import {Settlements} from '../Types'
import {fromEntityLink} from '../utils'
import {BaseRootStore, BaseRootStoreImpl} from './BaseRootStore'
import {CaseRelationshipStore} from './CaseRelationshipStore'
import {ClaimPartyStore} from './ClaimPartyStore'
import {ClaimStore} from './ClaimStore'
import {EventCaseStore} from './EventCaseStore'
import {CaseSystemPaymentStoreImpl} from './impl'
import {SpecialHandlingStore} from './SpecialhandlingStore'
import CapEventCaseEntity = CapEventCase.CapEventCaseEntity
import CapPaymentTemplateEntity = CapPaymentTemplate.CapPaymentTemplateEntity
import CapClaimWrapperEntity = ClaimWrapper.CapClaimWrapperEntity
import CodeValueLookup = lookups.CodeValueLookup

export const LOAD_CHANGE_HISTORY_ACTION = 'loadChangeHistoryAction'

export interface ChangeHistoryViewStore {
    settlement?: Settlements
    loss?: CapGenericLoss
    eventCaseStore: EventCaseStore
    paymentsStore: CaseSystemPaymentStoreImpl<
        CapEventCaseEntity | CapClaimWrapperEntity,
        EventCaseService | ClaimWrapperService
    >
    claimPartyStore: ClaimPartyStore
    claimStore: ClaimStore
    specialHandlingStore: SpecialHandlingStore
    caseRelationshipStore?: CaseRelationshipStore
    eventTypes?: CodeValueLookup[]
}

export type ChangeHistoryStore = {
    eventCase: CapEventCaseEntity
    settlements: Settlements[]
    templates: CapPaymentTemplateEntity[]
    changeHistoryRecords: ChangeHistoryRecordWithUser[]
    loadChangeHistory: (isClaimOverview?: boolean) => void
    getUserFullNameFromUUID: (uuid: string) => string
    loadChangeHistoryTemplates: (
        changeHistoryRecords: ChangeHistoryRecord[]
    ) => RxResult<CapPaymentTemplate.CapPaymentTemplateEntity[]>
} & ChangeHistoryViewStore &
    BaseRootStore

export class ChangeHistoryStoreImpl extends BaseRootStoreImpl implements ChangeHistoryStore {
    @observable changeHistoryRecords: ChangeHistoryRecordWithUser[] = []

    viewStore: ChangeHistoryViewStore

    settlementUris: string[] = []

    @observable templates: CapPaymentTemplateEntity[] = []

    claimUri = ''

    users: OrgPerson[] = []

    eventTypes: CodeValueLookup[] = []

    private tryLoadPayments: boolean

    constructor(viewStore?: ChangeHistoryViewStore) {
        super()
        if (viewStore) {
            this.viewStore = viewStore
        }
    }

    @computed
    public get eventCaseStore(): EventCaseStore {
        return this.viewStore.eventCaseStore
    }

    @computed
    public get claimStore(): ClaimStore {
        return this.viewStore.claimStore
    }

    @computed
    public get eventCase(): CapEventCaseEntity {
        return this.eventCaseStore.eventCase || ({} as CapEventCaseEntity)
    }

    @computed
    public get loss(): CapGenericLoss {
        return this.viewStore.loss || ({} as CapGenericLoss)
    }

    @computed
    public get paymentsStore(): CaseSystemPaymentStoreImpl<
        CapEventCaseEntity | CapClaimWrapperEntity,
        EventCaseService | ClaimWrapperService
    > {
        return this.viewStore.paymentsStore
    }

    @computed
    public get caseRelationshipStore(): CaseRelationshipStore | undefined {
        return this.viewStore.caseRelationshipStore
    }

    @computed get settlements(): Settlements[] {
        if (this.viewStore.settlement) {
            return [this.viewStore.settlement]
        }
        return this.paymentsStore.allAssociatedSettlements
    }

    @computed
    public get claimPartyStore(): ClaimPartyStore {
        return this.viewStore.claimPartyStore
    }

    @computed
    public get specialHandlingStore(): SpecialHandlingStore {
        return this.viewStore.specialHandlingStore
    }

    aggregateClaimData(): ChangeHistoryRequestFilter[] {
        this.claimUri = `gentity://CapLoss/${this.loss._modelName}//${this.loss._key?.rootId}/${this.loss._key?.revisionNo}`
        const filter: ChangeHistoryRequestFilter[] = []
        const formatFilter = entity =>
            filter.push({
                modelName: entity._modelName,
                rootId: entity._key?.rootId,
                revisionNo: entity._key?.revisionNo?.toString()
            })

        formatFilter(this.loss)
        if (this.settlements.length) {
            this.settlements.forEach(settlement => {
                formatFilter(settlement)
                this.settlementUris.push(
                    `gentity://CapSettlement/${settlement._modelName}//${settlement._key?.rootId}/${settlement._key?.revisionNo}`
                )
            })
        }
        if (this.specialHandlingStore.specialHandling) {
            formatFilter(this.specialHandlingStore.specialHandling)
        }
        return filter
    }

    resolveDecrement(inversePatch: ChangeHistoryPatch, idx: number) {
        if (!inversePatch || inversePatch.patch[idx].path.value.includes('expenses')) {
            return 0
        }
        return 1
    }

    filterDeductionRecordsByClaim(records: ChangeHistoryRecord[]): ChangeHistoryRecord[] {
        const pathRegex = /^(.*?[0-9]+)\/?/
        return records
            .map(record => {
                const isCurrentClaimPatch = patch => {
                    const match = patch.path.value.match(pathRegex)
                    const path = match ? match[1].replace(/^\//, '').replace(/\//g, '.') : patch.path.value
                    return (
                        path.includes('deductions') &&
                        patch.value?.lossSources?.map(v => v._uri.value)?.includes(this.claimUri)
                    )
                }
                const forwardPatchByPmT = record.forwardPatch.patch.filter(isCurrentClaimPatch)
                const inversePatchByPmt = record.inversePatch.patch.filter(isCurrentClaimPatch)
                if (isEmpty(forwardPatchByPmT) && isEmpty(inversePatchByPmt)) {
                    return {}
                }
                return {...record, inversePatch: {patch: inversePatchByPmt}, forwardPatch: {patch: forwardPatchByPmT}}
            })
            .filter(value => !isEmpty(value)) as unknown as ChangeHistoryRecord[]
    }

    filterPaymentRecordsByClaim(records: ChangeHistoryRecord[]): ChangeHistoryRecord[] {
        const paymentTemplates = toJS(this.templates)
        const pathRegex = /^(.*?[0-9]+)\/?/
        return records
            .map(record => {
                const isCurrentClaimPatch = (patch, idx) => {
                    const pmt = paymentTemplates.find(
                        template =>
                            template._key.rootId === record.filter.values.rootId &&
                            template._key.revisionNo ===
                                Number(record.filter.values.revisionNo) -
                                    this.resolveDecrement(record.inversePatch, idx)
                    )
                    const match = patch.path.value.match(pathRegex)
                    const path = match ? match[1].replace(/^\//, '').replace(/\//g, '.') : patch.path.value
                    const settlement = path.includes('settlements') && get(pmt, path)
                    const expenses = path.includes('expenses') && get(pmt, path)
                    const exGratias = path.includes('exGratias') && get(pmt, path)
                    return (
                        this.settlementUris.includes(settlement?.uri) ||
                        expenses?.lossSource?._uri === this.claimUri ||
                        exGratias?.lossSource?._uri === this.claimUri
                    )
                }
                const forwardPatchByPmT = record.forwardPatch.patch.filter(isCurrentClaimPatch)
                const inversePatchByPmt = record.inversePatch.patch.filter(isCurrentClaimPatch)
                if (isEmpty(forwardPatchByPmT) && isEmpty(inversePatchByPmt)) {
                    return {}
                }
                return {...record, inversePatch: {patch: inversePatchByPmt}, forwardPatch: {patch: forwardPatchByPmT}}
            })
            .filter(value => !isEmpty(value)) as unknown as ChangeHistoryRecord[]
    }

    loadChangeHistory = (isClaimOverview?: boolean) => {
        this.actionsStore.startAction(LOAD_CHANGE_HISTORY_ACTION)
        this.tryLoadPayments =
            !!this.paymentsStore.paymentTemplates.length || !!this.paymentsStore.paymentScheduleList.length
        if (!isClaimOverview) {
            this.loadEcHistory()
        } else {
            this.loadClaimHistory()
        }
    }

    doCall = (sourceUri: string, filter?: ChangeHistoryRequestFilter[]): Observable<ChangeHistoryRecord[]> =>
        this.call(() => eventCaseService.loadChangeHistory(sourceUri, filter)).map(either => either.getOrElse([]))

    addUserProfilesToRecords = (combinedRecords: ChangeHistoryRecord[]) => {
        const records = combinedRecords.sort((a, b) =>
            dateComparison(a._key.timestamp).isAfter(b._key.timestamp) ? 1 : -1
        )
        const userUUIDs = [...new Set(records.map(record => record.user.split('/').slice(-2, -1)?.[0]))].filter(
            (id): id is string => Boolean(id)
        )
        return userUUIDs.length ? this.loadUserProfiles(userUUIDs, records) : []
    }

    @action
    loadEventTypes = () => {
        loadLookupOptions('EventType')
            .map(either =>
                either.fold(
                    err => console.error(err),
                    values =>
                        runInAction(() => {
                            this.eventTypes = values
                        })
                )
            )
            .subscribe()
    }

    /**
     * This function serves as a Change History loader specifically for Event Cases.
     * It retrieves all records related to Event Cases, along with the payment records of associated claims.
     */
    @action
    loadEcHistory = () => {
        const eventCaseUri = `gentity://CapLoss/CapEventCase//${this.eventCase._key?.rootId}/${this.eventCase._key?.revisionNo}`
        this.loadEventTypes()
        this.doCall(eventCaseUri)
            .pipe(
                switchMap(ecHistory => {
                    const allAssociatedClaims = this.paymentsStore.allAssociatedClaims
                    const associatedClaimMapper = claim =>
                        this.doCall(
                            `gentity://CapLoss/${claim._modelName}//${claim._key?.rootId}/${claim._key?.revisionNo}`
                        )

                    const getExternalObservables = (
                        data: any[],
                        mapper: (data: any) => Observable<ChangeHistoryRecord[]>
                    ) => {
                        return data?.length ? forkJoin(data.map(mapper)).pipe(map(results => results.flat())) : of([])
                    }
                    const claims = getExternalObservables(allAssociatedClaims, associatedClaimMapper)

                    const relationships = this.caseRelationshipStore?.caseRelationshipsWithTargetCases
                    const relationshipMapper = relationship => this.doCall(fromEntityLink(relationship))
                    const relations = relationships?.length
                        ? forkJoin(relationships?.map(relationshipMapper)).pipe(map(relations => relations.flat()))
                        : of([])

                    return forkJoin(of(ecHistory), claims, relations).pipe(map(values => values.flat()))
                }),
                switchMap(combinedRecords => {
                    return this.loadChangeHistoryTemplates(combinedRecords).map(templates => {
                        runInAction(() => {
                            this.templates = templates.get()
                        })
                        return combinedRecords
                    })
                }),
                switchMap(combinedRecords => this.addUserProfilesToRecords(combinedRecords)),
                finalize(() => this.actionsStore.completeAction(LOAD_CHANGE_HISTORY_ACTION))
            )
            .subscribe(either => {
                const records = either.get()
                runInAction(() => {
                    this.changeHistoryRecords = records
                })
            })
    }

    /**
     * This function serves as a Change History loader specifically for Claims.
     * It retrieves all Payment Templates from Event Case Change History records and then narrows them down to those relevant to the current claim.
     * Additionally, it fetches payment information for the current claim and other related records from the Event Case.
     */
    @action
    loadClaimHistory = () => {
        const eventCaseUri = `gentity://CapLoss/CapEventCase//${this.eventCase._key?.rootId}/${this.eventCase._key?.revisionNo}`
        const claimFilters: ChangeHistoryRequestFilter[] = this.aggregateClaimData()
        const paymentFilter: ChangeHistoryRequestFilter[] = [
            {
                modelName: this.paymentsStore.currentPaymentTemplate?._modelName,
                rootId: this.paymentsStore.currentPaymentTemplate?._key?.rootId
            }
        ]

        iif(
            // load all payment templates
            () => this.tryLoadPayments,
            this.doCall(eventCaseUri, paymentFilter),
            of([])
        )
            .pipe(
                switchMap(eventCasePayments =>
                    iif(
                        () => this.tryLoadPayments,
                        // load claim level payments
                        this.doCall(this.claimUri).pipe(map(result => [...eventCasePayments, ...result])),
                        of(eventCasePayments)
                    )
                ),
                switchMap(combinedRecords => {
                    return this.loadChangeHistoryTemplates(combinedRecords).map(templates => {
                        runInAction(() => {
                            this.templates = templates.get()
                        })
                        return combinedRecords
                    })
                }),
                switchMap(paymentsHistory => {
                    const modifiedPaymentsHistoryResult = this.filterPaymentRecordsByClaim(paymentsHistory)
                    // load claim related history from eventCase
                    return this.doCall(eventCaseUri, claimFilters).pipe(
                        map(result => [...modifiedPaymentsHistoryResult, ...result])
                    )
                }),
                switchMap(allHistory =>
                    this.doCall(eventCaseUri).pipe(
                        map(result => [...allHistory, ...this.filterDeductionRecordsByClaim(result)])
                    )
                ),
                switchMap(combinedRecords => this.addUserProfilesToRecords(combinedRecords)),
                finalize(() => this.actionsStore.completeAction(LOAD_CHANGE_HISTORY_ACTION))
            )
            .subscribe(either => {
                const records = either.get()
                runInAction(() => {
                    this.changeHistoryRecords = records
                })
            })
    }

    @action
    loadUserProfiles = (userUUIDs: string[], records: ChangeHistoryRecord[]) => {
        return backofficeWorkService.loadUserProfiles(userUUIDs).map(either => {
            const users = either.get()
            runInAction(() => {
                this.users = users
            })
            this.actionsStore.completeAction(LOAD_CHANGE_HISTORY_ACTION)
            return Right(
                records.map(record => ({
                    ...record,
                    userDomain: users.find(user => user.securityIdentity === record.user.split('/').slice(-2, -1)?.[0])
                }))
            )
        })
    }

    @action
    loadChangeHistoryTemplates = (
        changeHistoryRecords: ChangeHistoryRecord[]
    ): RxResult<CapPaymentTemplate.CapPaymentTemplateEntity[]> => {
        if (!changeHistoryRecords.length) {
            return of(Right([]))
        }
        const getTemplateUris = record => {
            const {rootId, revisionNo} = record.filter.values
            const recordRevisionNo = parseInt(revisionNo, 10) || 1
            const hasNonAddOperations = record.forwardPatch.patch.some(path => path.op.value !== 'add')

            return recordRevisionNo > 1 && hasNonAddOperations
                ? [createTemplateUri(rootId, recordRevisionNo - 1), createTemplateUri(rootId, revisionNo)]
                : [createTemplateUri(rootId, revisionNo)]
        }

        const distinctTemplateUris = [
            ...new Set(
                changeHistoryRecords
                    .filter(record => record.filter?.values?.modelName === PAYMENT_TEMPLATE_MODEL_NAME)
                    .flatMap(record => getTemplateUris(record))
            )
        ]
        if (distinctTemplateUris.length === this.templates.length) {
            return of(Right(toJS(this.templates)))
        }
        const links = distinctTemplateUris.map(uri => ({_uri: uri}))
        return claimPaymentTemplatesService.loadPaymentTemplatesByLinks({links})
    }

    getUserFullNameFromUUID = (uuid: string): string => {
        const {firstName, lastName} = this.users.find(user => user.securityIdentity === uuid)?.personInfo || {}
        return firstName && lastName ? `${firstName} ${lastName}` : uuid
    }
}
