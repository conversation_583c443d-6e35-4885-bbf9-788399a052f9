/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import React, {FC} from 'react'
import {
    CasesClaimsTable as CasesClaimsTableCore,
    CasesClaimsTableProps as CasesClaimsTablePropsCore
} from './CasesClaimsTable'

import {casesClaimsSearchService} from './services/CasesClaimsSearchService'

type CasesClaimsTableProps = Omit<CasesClaimsTablePropsCore, 'casesClaimsSearchService'>

export const CasesClaimsTable: FC<CasesClaimsTableProps> = props => (
    <CasesClaimsTableCore casesClaimsSearchService={casesClaimsSearchService} {...props} />
)
