import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "CONTAINER",
      "props": {
        "md": 24,
        "bordered": false,
        "bodyPadding": "md",
        "statusViewMode": "border",
        "shadow": false,
        "blockClassName": "header-wrapper",
        "push": 0,
        "closable": false
      },
      "components": [
        {
          "type": "COMPONENT_SLOT",
          "props": {
            "md": 24,
            "span": 24,
            "slotId": "BREADCRUMBS"
          },
          "id": "9be1eec6-123-44c5-8717-987e6ccc8e4a"
        },
        {
          "type": "COMPONENT_SLOT",
          "props": {
            "md": 24,
            "span": 24,
            "slotId": "HEADER",
            "offset": 0
          },
          "id": "9be1eec6-522c-44c5-8717-987e6ccc8e4a"
        }
      ],
      "id": "66c270a9-3c7e-4704-8a36-1e8405a10582"
    }
  ],
  "version": 1
}

export default config;
