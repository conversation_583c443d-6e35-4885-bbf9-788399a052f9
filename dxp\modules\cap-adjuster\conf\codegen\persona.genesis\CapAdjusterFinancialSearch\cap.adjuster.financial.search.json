{"swagger": "2.0", "x-dxp-spec": {"imports": {"payment.search": {"schema": "integration.cap.loss.search.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster: Financial Search API", "version": "1", "title": "CAP Adjuster: Financial Search API"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/payment-templates", "description": "CAP Adjuster: Claim Payment Template API"}, {"name": "/cap-adjuster/v1/financial", "description": "CAP Adjuster: Financial API"}], "paths": {"/payment-templates/search": {"post": {"summary": "Search payment template", "x-dxp-path": "/api/common/search/v3/CapPaymentTemplate", "tags": ["/cap-adjuster/v1/payment-templates"]}}, "/financial/balance/search": {"post": {"summary": "Search balance", "x-dxp-path": "/api/common/search/v3/CapBalance", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment-schedule/search": {"post": {"summary": "Search payment schedule", "x-dxp-path": "/api/common/search/v3/CapPaymentSchedule", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/payment/search": {"post": {"summary": "Search payment", "x-dxp-path": "/api/common/search/v3/CapPayment", "tags": ["/cap-adjuster/v1/financial"]}}, "/financial/balance-change-log/search": {"post": {"summary": "Retrieve balance change log list", "x-dxp-path": "/api/common/search/v3/CapBalanceChangeLog", "tags": ["/cap-adjuster/v1/financial"]}}}}