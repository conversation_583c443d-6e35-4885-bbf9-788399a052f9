/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {action, observable, runInAction} from 'mobx'
import {
    claimLtdService,
    claimSmpService,
    claimWrapperService,
    eventCaseService,
    claimStdService,
    claimLeaveService,
    CapGenericLoss,
    claimLossService,
    WorkflowTask,
    EFolderDocument,
    Note,
    backOfficeWorkflowService
} from '@eisgroup/cap-services'
import {RxResult} from '@eisgroup/common-types'
import {
    CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity,
    CapAdjusterEventCaseCapEventCaseCapEventCaseEntity,
    CapAdjusterLossSearchResultCapLossSearchEntityResponseV3,
    CapAdjusterLtdLossCapLtdCapLTDisabilityClaimEntity,
    CapAdjusterSmpLossCapSmpCapSMPClaimEntity,
    CapAdjusterStdLossCapStdCapDisabilityClaimEntity,
    CapAdjusterLeaveLossCapLeaveCapLeaveClaimEntity
} from '@eisgroup/cap-gateway-client'

const DEFAULT_LIMIT = 10
const DEFAULT_OFFSET = 0

export class ActivitiesListStore {
    @observable tasks: WorkflowTask[]

    @observable notes: Note[]

    @observable docs: EFolderDocument[]

    @action
    fetchTaskActivities = activityParams => {
        backOfficeWorkflowService.getActiveTasks(activityParams).subscribe(response => {
            runInAction(() => {
                this.tasks = response.get()
            })
        })
    }

    @action
    fetchNotesActivities = activityParams => {
        backOfficeWorkflowService
            .getNotes({
                primaryEntity: activityParams,
                limit: DEFAULT_LIMIT,
                offset: DEFAULT_OFFSET
            })
            .subscribe(response => {
                runInAction(() => {
                    this.notes = response.get()
                })
            })
    }

    @action
    fetchDocsActivities = activityParams => {
        backOfficeWorkflowService
            .getDocuments({
                entityURI: activityParams.join(),
                limit: DEFAULT_LIMIT,
                offset: DEFAULT_OFFSET
            })
            .subscribe(response => {
                runInAction(() => {
                    this.docs = response.get()
                })
            })
    }

    @action
    fetchCaseActivities = activityParams => {
        return backOfficeWorkflowService.searchCaseByEntityURIs(activityParams, DEFAULT_LIMIT)
    }

    @action
    getEventCase = (rootId): RxResult<CapAdjusterEventCaseCapEventCaseCapEventCaseEntity> => {
        return eventCaseService.getEventCaseReference(rootId)
    }

    @action
    getStdLoss = (rootId): RxResult<CapAdjusterStdLossCapStdCapDisabilityClaimEntity> => {
        return claimStdService.getStdReference(rootId)
    }

    @action
    getLtdLoss = (rootId): RxResult<CapAdjusterLtdLossCapLtdCapLTDisabilityClaimEntity> => {
        return claimLtdService.getLtdReference(rootId)
    }

    @action
    getSmpLoss = (rootId): RxResult<CapAdjusterSmpLossCapSmpCapSMPClaimEntity> => {
        return claimSmpService.getSmpReference(rootId)
    }

    @action
    getLeaveLoss = (rootId): RxResult<CapAdjusterLeaveLossCapLeaveCapLeaveClaimEntity> => {
        return claimLeaveService.getLeaveReference(rootId)
    }

    @action
    getLifeClaimLoss = (rootId): RxResult<CapAdjusterClaimWrapperClaimWrapperCapClaimWrapperEntity> => {
        return claimWrapperService.getLifeClaimReference(rootId)
    }

    @action
    getLifeClaimLossReference = (rootId): RxResult<CapGenericLoss[]> => {
        return eventCaseService.getGetLifeReference(rootId)
    }

    @action
    searchEventCaseClaims = (
        eventCaseRootId,
        eventCaseRevisionNo
    ): RxResult<CapAdjusterLossSearchResultCapLossSearchEntityResponseV3> => {
        return claimLossService.searchRelatedClaims(eventCaseRootId, eventCaseRevisionNo)
    }
}

export const activitiesListStore = new ActivitiesListStore()
