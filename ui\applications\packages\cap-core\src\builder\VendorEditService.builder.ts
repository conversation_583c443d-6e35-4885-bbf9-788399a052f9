import {UISchemaType} from '@eisgroup/builder'
/* eslint-disable */
/* tslint:disable */

/* IMPORTANT: This file was generated automatically by the tool.
 * Changes to this file may cause incorrect behavior. */

const config: UISchemaType = {
  "display": "form",
  "components": [
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "label": "cap-core:claim_party_information_table_party_name",
            "template": "{name}"
          },
          "id": "cf42154f-cf12-4071-940e-54f93cd1dc02"
        },
        {
          "type": "DISPLAY_FORM",
          "props": {
            "md": 12,
            "label": "cap-core:type",
            "template": "{type}"
          },
          "id": "f4c08193-e315-4776-8456-0c758005dfad"
        }
      ],
      "id": "769117f4-a8c9-47d4-8165-192124e9e5be"
    },
    {
      "type": "ROW",
      "props": {
        "md": 24
      },
      "components": [
        {
          "type": "LOOKUP_SELECT",
          "props": {
            "md": 24,
            "name": "serviceType",
            "lookupName": "ProviderService",
            "valueType": "string",
            "mode": "multiple",
            "field": {
              "validations": [
                {
                  "skipOnEmpty": false,
                  "type": "required-named",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string",
                    "value": "Service Type"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "required",
                  "fieldValue": {
                    "type": "value",
                    "valueType": "string"
                  }
                },
                {
                  "skipOnEmpty": false,
                  "type": "array-length",
                  "fieldValues": {
                    "fieldName": {
                      "type": "value",
                      "valueType": "string",
                      "value": "cap-core:claim_party_info_form_service_type"
                    }
                  }
                }
              ]
            },
            "label": "cap-core:claim_party_info_form_service_type",
            "events": [
              {
                "id": "00628a1f-31dc-424b-8a6f-374fad09a217",
                "eventName": "SERVICE_TYPE_CHANGE",
                "dispatchEventProperty": "onChange"
              }
            ]
          },
          "id": "7f5d5872-1f04-4bbd-bf39-89f1db84d8bb"
        }
      ],
      "id": "58431bc0-6b89-4de1-b241-3b3d788b8a9d"
    }
  ],
  "version": 51,
  "actionChains": {
    "SERVICE_TYPE_CHANGE": {
      "type": "API",
      "apiSettings": {
        "method": "serviceTypeChange"
      },
      "nestedActions": {}
    }
  }
}

export default config;
