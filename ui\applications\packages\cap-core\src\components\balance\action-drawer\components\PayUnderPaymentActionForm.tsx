import React, {FC, useState} from 'react'
import {observer} from 'mobx-react'
import {Money} from '@eisgroup/models-api'
// import {Alert} from '@eisgroup/ui-kit'
// import {v4} from 'uuid'
import {CapAdjusterPaymentDefinitionEntityLink} from '@eisgroup/cap-gateway-client'
import {CaseSystemService, CSClaimWrapperService} from '@eisgroup/cap-services'
import {PAY_UNDERPAYMENT} from '../../../../common/store'
import {BALANCE_ACTION_FORM_ID, PaymentStatus, TRANSACTIONTYPE} from '../../../../common/constants'
import {ActionFormBase} from './ActionFormBase'
import payUnderPaymentConfig from '../../../../builder/BalancePayUnderPayment.builder'
import {getPaymentAppliedWithholdingsSlot} from '../../../../builder/slots/PaymentAppliedWithholdingsSlot'
import {DrawerFormStateType} from '../../../form-drawer'
// import {getPaymentMethodIdAndCheckAddressIdValidationMsg} from '../../utils'
import {ICaseSystem} from '../../../../common/Types'
import {BalanceActionDrawerProps} from '../BalanceActionDrawer'

type PayUnderPaymentActionFormProps = BalanceActionDrawerProps<
    ICaseSystem,
    CaseSystemService | CSClaimWrapperService<ICaseSystem>
> & {
    totalBalance: number
    getPayUnderpaymentAmount: (key: string) => Money | undefined
}

export const PayUnderPaymentActionForm: FC<PayUnderPaymentActionFormProps> = observer(props => {
    const {
        eventCase,
        actionKey: key,
        payeeLink,
        payeeDisplay,
        totalBalance,
        getPayUnderpaymentAmount,
        getBalanceChangeLog,
        // parties,
        // beneficiaries,
        balanceStore,
        paymentsStore,
        closeDrawer
        // store
    } = props
    const isLoading = balanceStore.actionsStore.isRunning(PAY_UNDERPAYMENT)

    // const [paymentMethodErrorMsg, setPaymentMethodErrorMsg] = useState<string>('')
    const [underPaymentAmount, setUnderPaymentAmount] = useState<Money | undefined>(
        getPayUnderpaymentAmount('PAY_UNDERPAYMENT')
    )

    const getTotalBalanceAmount = () => balanceStore.balance?.totalBalanceAmount?.amount ?? 0

    const underpaymentList = paymentsStore.payments.filter(v => v._variation === 'underpayment')
    const allocationWithholdingCancelationPaymentList: string[] = []
    underpaymentList.forEach(v => {
        v.paymentDetails?.paymentAllocations.forEach(s => {
            if (s.allocationWithholdingCancelationDetails?.paymentNumber) {
                allocationWithholdingCancelationPaymentList.push(
                    s.allocationWithholdingCancelationDetails.paymentNumber
                )
            }
        })
    })
    const issuedPaymentsWhichContainsWithholdings =
        paymentsStore.payments
            ?.filter(
                v =>
                    v.state === PaymentStatus.Issued &&
                    v.withholdingDetails?.withholdingAllocations?.length &&
                    payeeLink === v.payee
            )
            .filter(s => s.paymentNumber && !allocationWithholdingCancelationPaymentList.includes(s.paymentNumber)) ||
        []

    const onFormConfirm = (values): void => {
        const params = {
            payee: {_uri: payeeLink},
            originSource: {_uri: balanceStore.balanceSource}
        }
        let saveParams
        const nextBalanceCount = balanceStore.balanceChangeLogCount + 1

        // Check if the payment method is valid
        // and if the address ID is valid
        // const msg = getPaymentMethodIdAndCheckAddressIdValidationMsg(
        //     payeeLink,
        //     balanceStore.balanceSource,
        //     [...parties, ...beneficiaries],
        //     store
        // )
        // setPaymentMethodErrorMsg(msg ?? '')
        // if (msg) {
        //     return
        // }

        if (!values.isCompensateWithholdingsSelected) {
            setUnderPaymentAmount(values.amount)
            saveParams = {...params, underpaymentAmount: values.amount}
            balanceStore.payUnderpayment(saveParams).subscribe(either => {
                closeDrawer()
                const r = either.get()
                getBalanceChangeLog(nextBalanceCount)
                setTimeout(() => {
                    paymentsStore.loadCurrentPayments(
                        {
                            rootId: eventCase._key.rootId,
                            revisionNo: eventCase._key.revisionNo,
                            modelName: eventCase._modelName
                        },
                        '',
                        [`gentity://CapPayment/PaymentDefinition/underpayment/${r._key?.rootId}/${r._key?.revisionNo}`],
                        ['case_underpayment_approval']
                    )
                }, 5000)
            })
        } else {
            if (!values.selectedPayments?.length) {
                return
            }
            const paymentLinkList: CapAdjusterPaymentDefinitionEntityLink[] = []
            paymentsStore.payments.forEach(v => {
                values.selectedPayments.forEach(s => {
                    if (v._key?.id === s) {
                        if (v._variation === TRANSACTIONTYPE.UNDERPAYMENT.toLowerCase()) {
                            paymentLinkList.push({
                                _uri: `gentity://CapPayment/PaymentDefinition/underpayment/${v._key?.id}/${v._key?.revisionNo}`
                            })
                        }
                        if (v._variation === TRANSACTIONTYPE.RECOVERY.toLowerCase()) {
                            paymentLinkList.push({
                                _uri: `gentity://CapPayment/PaymentDefinition/recovery/${v._key?.id}/${v._key?.revisionNo}`
                            })
                        }
                        if (v._variation === TRANSACTIONTYPE.PAYMENT.toLowerCase()) {
                            paymentLinkList.push({
                                _uri: `gentity://CapPayment/PaymentDefinition/payment/${v._key?.id}/${v._key?.revisionNo}`
                            })
                        }
                    }
                })
            })
            saveParams = {...params, payments: paymentLinkList}
            balanceStore.generateWithholdingUnderpayment(saveParams).subscribe(() => {
                paymentsStore.loadCurrentPayments({
                    rootId: eventCase._key.rootId,
                    revisionNo: eventCase._key.revisionNo,
                    modelName: eventCase._modelName
                })
                getBalanceChangeLog(nextBalanceCount)
                closeDrawer()
            })
        }
    }

    return (
        <ActionFormBase
            uiEngineProps={{
                formId: BALANCE_ACTION_FORM_ID,
                config: payUnderPaymentConfig,
                slotComponents: {
                    PAYMENT_APPLIED_WITHHOLDINGS: getPaymentAppliedWithholdingsSlot(
                        issuedPaymentsWhichContainsWithholdings
                    )
                },
                initialValues: {
                    selectedPayments: [],
                    payeeDisplay,
                    payeeDisabled: true,
                    isCompensateWithholdingsSelected: false,
                    totalBalance,
                    totalBalanceAmount: getTotalBalanceAmount(),
                    amount: underPaymentAmount,
                    withholdingsAmount: undefined,
                    action: key
                },
                onNext: onFormConfirm
            }}
            drawerActionProps={{
                drawerFormState: DrawerFormStateType.Create,
                handleFormCancel: closeDrawer,
                isLoading
            }}
        >
            {/* {paymentMethodErrorMsg ? (
                <Alert key={v4()} type='warning' message={paymentMethodErrorMsg} closable={false} />
            ) : null} */}
        </ActionFormBase>
    )
})
