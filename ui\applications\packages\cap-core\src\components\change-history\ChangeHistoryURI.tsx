/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {
    backofficeCustomerCommonService,
    backofficePolicyService,
    CapGenericSettlement,
    claimLossService,
    claimService,
    ClaimsPolicySearchPolicySummary,
    IOCustomer
} from '@eisgroup/cap-services'
import {promiseToRxResult} from '@eisgroup/common'
import {t} from '@eisgroup/i18n'
import {opt} from '@eisgroup/common-types'
import {Spin} from '@eisgroup/ui-kit'
import * as React from 'react'
import {Observable} from 'rxjs'
import {
    EntityLink,
    isCustomerUri,
    isIndividualCustomer,
    isLossUri,
    isPolicyUri,
    isSettlementUri,
    LifeClaimTypesMap,
    tryTranslateValue
} from '../..'
import {useLoadLinkEntities} from './useLoadLinkEntities'
import {ChangeHistorySettlementModelNamesLifeClaims, ClaimModelToType} from './utils/types'

interface ChangeHistoryURIProps {
    uri: string[]
}

type GetURIPropsCallback = () => {
    obs: Observable<any>
    transform: (...props: any) => string[]
}

const getClaimNCoverageValue = (settlement: CapGenericSettlement): string => {
    const isLifeSettlement = Object.keys(ChangeHistorySettlementModelNamesLifeClaims).includes(
        settlement?._modelName || ''
    )
    const claimNumber = opt(
        isLifeSettlement ? settlement?.settlementLossInfo?.claimNumber : settlement?.settlementLossInfo?.lossNumber
    ).orElse(t('cap-core:not_available'))
    const claimCoveragePrefix = settlement?.claimCoveragePrefix ? ` - ${settlement?.claimCoveragePrefix}` : ''

    const coverageName = settlement?.claimCoverageName
        ? `${tryTranslateValue(settlement?.claimCoverageName)}${claimCoveragePrefix}`
        : settlement?.settlementLossInfo?.coverageType || ''

    return `${claimNumber} ${coverageName}`
}

export const ChangeHistoryURI: React.FC<ChangeHistoryURIProps> = ({uri}) => {
    const getURIProps = React.useCallback<GetURIPropsCallback>(() => {
        const rootIds = uri.map(singleUri => EntityLink.from(singleUri).rootId)
        if (isPolicyUri(uri)) {
            return {
                obs: promiseToRxResult(() => backofficePolicyService().searchPoliciesByRootIds(rootIds)),
                transform: (policies: ClaimsPolicySearchPolicySummary[]) => {
                    return policies.map(p => p.policyNumber)
                }
            }
        }

        if (isCustomerUri(uri)) {
            const returnCustomerInfo = (customer: IOCustomer) => {
                return isIndividualCustomer(customer)
                    ? `${customer.details.person.firstName} ${customer.details.person.lastName} ${customer.customerNumber}`
                    : `${customer.details.legalEntity.legalName}`
            }
            return {
                obs: promiseToRxResult(() => backofficeCustomerCommonService().searchCustomersByRootId(rootIds)),
                transform: (customers: IOCustomer[]) => customers.map((c: IOCustomer) => returnCustomerInfo(c))
            }
        }

        if (isLossUri(uri)) {
            return {
                obs: claimService.searchClaimsByRootId(rootIds),
                transform: (claims: any[]) => {
                    return claims.map(c => {
                        let lossModelName = c.claimModelName
                        if (Object.keys(LifeClaimTypesMap).includes(c.claimType)) {
                            lossModelName = c.claimType
                        }
                        return lossModelName && c.claimNumber
                            ? `${ClaimModelToType[lossModelName]} ${c.claimNumber}`
                            : (c.claimSystemId ?? c.caseNumber)
                    })
                }
            }
        }

        if (isSettlementUri(uri)) {
            return {
                obs: claimLossService.searchSettlementsByRootIds(rootIds),
                transform: (settlements: any[]) => {
                    return settlements.map(getClaimNCoverageValue)
                }
            }
        }
        return {
            obs: Observable.from([]),
            transform: () => []
        }
    }, [uri])

    const {obs, transform} = getURIProps()
    const {loaded, data, error} = useLoadLinkEntities(uri, obs, transform)

    if (!loaded) {
        return <Spin size='small' />
    }

    const formattedData = data?.length ? data.join(', ') : null

    return <div>{error || !formattedData ? uri : formattedData}</div>
}
