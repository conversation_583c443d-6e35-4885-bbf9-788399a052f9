/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {FC, useEffect, useState} from 'react'
import {Col, Row} from '@eisgroup/ui-kit'
import {withForm<PERSON>ield} from '@eisgroup/form'
import {CheckBoxCardsCodeValue} from '@eisgroup/cap-services'
import {RADIO_CARDS_GROUP, RADIO_CARDS_GROUP_CONTENT, RADIO_CARDS_GROUP_ITEMS} from '../..'
import {CheckBoxCard} from './CheckBoxCard'

export interface CheckBoxCardsGroupProps {
    items: CheckBoxCardsCodeValue[]
    multiSelection?: boolean
    onChange?: (selected: string | string[]) => void
    onStoreChange?: (selected: string | string[]) => void
    itemsSelected: string | string[]
    getDisabled?: (itemCode?: string) => boolean
    getPreSelected?: (itemCode?: string) => boolean | undefined
    getCardContent: (item?: CheckBoxCardsCodeValue) => React.ReactNode
    cardSpan?: number
    isRadioType?: boolean
    presentationalRule?: any
}
export const CheckBoxCardsGroup: FC<CheckBoxCardsGroupProps> = ({items, ...props}) => {
    const {onChange, onStoreChange, presentationalRule} = props
    const [selected, setSelected] = useState<string | undefined>('')
    const initMultiSelection: string[] = typeof props.itemsSelected === 'object' ? props.itemsSelected : []
    const [multiSelected, updateSelected] = useState<string[]>(initMultiSelection)

    useEffect(() => {
        if (props.multiSelection) {
            updateSelected([...props.itemsSelected])
        } else {
            setSelected(props.itemsSelected as string)
        }
    }, [props.itemsSelected])

    const onFuncChange = key => {
        if (onStoreChange) {
            onStoreChange(key)
        }
        if (onChange) {
            onChange(key)
        }
    }

    /** *  change both value and selected */
    const onSelect = key => {
        if (!props.multiSelection) {
            onFuncChange(key !== selected ? key : undefined)
            if (key !== selected) {
                setSelected(key)
            } else {
                setSelected(undefined)
            }
        } else {
            const withoutUnselected = multiSelected.filter(v => v !== key)
            if (!multiSelected.includes(key)) {
                onFuncChange([...multiSelected, key])
                updateSelected([...multiSelected, key])
            } else {
                onFuncChange(withoutUnselected)
                updateSelected(withoutUnselected)
            }
        }
        if (presentationalRule) {
            setTimeout(() => {
                presentationalRule()
            }, 350)
        }
    }

    const getSelected = (item?: string): boolean => {
        if (props.getPreSelected) {
            const preSelected = props.getPreSelected(item)
            if (preSelected) {
                return preSelected
            }
        }
        return selected === item || (!!item && multiSelected.includes(item))
    }

    const compare = property => {
        return (obj1, obj2) => {
            return obj1[property] - obj2[property]
        }
    }

    const renderCards = () => {
        return (
            <div className={RADIO_CARDS_GROUP}>
                <Row className={RADIO_CARDS_GROUP_CONTENT} gutter={16}>
                    {!!items &&
                        items.length > 0 &&
                        items.sort(compare('orderNo')).map(item => (
                            <Col span={props.cardSpan ?? 6} className={RADIO_CARDS_GROUP_ITEMS} key={item.orderNo}>
                                <CheckBoxCard
                                    item={item}
                                    onSelect={onSelect}
                                    isSelected={getSelected(item.code)}
                                    getCardContent={props.getCardContent}
                                    disabled={props.getDisabled ? props.getDisabled(item.code) : undefined}
                                    isRadioType={props.isRadioType}
                                />
                            </Col>
                        ))}
                </Row>
            </div>
        )
    }
    return renderCards()
}
export const CheckBoxCards = withFormField<CheckBoxCardsGroupProps>()(CheckBoxCardsGroup)
