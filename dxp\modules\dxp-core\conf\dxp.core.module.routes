# Base Core APIs (swagger configuration and version services)
-> /core/v1/ core.v1.Routes
-> /core/v1/ core.v1.logs.Routes

# Defines Swagger groups for APIs filtering in Swagger UI
GET    /core/v1/configuration/swagger/groups    core.controllers.ConfigurationApiController.getAllSwaggerGroupsNames

# Swagger Plugin routes
-> / swagger.Routes

# Map static resources from the /public folder to the /assets URL path (for Swagger UI)
GET    /assets/*file    controllers.Assets.versioned(path="/public", file: Asset)

# Swagger UI redirect
GET       /swagger-ui         controllers.Default.redirect(to="/core/swagger/index.html")
GET       /swagger-ui.html    controllers.Default.redirect(to="/core/swagger/index.html")
GET       /openapi            controllers.Default.redirect(to="/core/swagger/openapi.html")
GET       /openapi.html       controllers.Default.redirect(to="/core/swagger/openapi.html")